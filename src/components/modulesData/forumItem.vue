<template>
  <div class="module-field-forum-item">
    <el-input
        v-if="moduleField.type==='textarea'"
        v-model="tempValue"
        :style="{width: width}"
        type="textarea"
        @input="handleTextInput">
    </el-input>
    <el-input
        v-else-if="moduleField.type==='number'"
        v-model.trim="tempValue"
        type="number"
        @input="handleTextInput">
    </el-input>
    <el-date-picker
        v-else-if="moduleField.type==='date'"
        v-model="tempValue"
        type="date"
        value-format="YYYY-MM-DD"
        @change="handleDateChange">
    </el-date-picker>
    <el-date-picker
        v-else-if="moduleField.type==='datetime'"
        v-model="tempValue"
        type="datetime"
        value-format="YYYY-MM-DD HH:mm:ss"
        @change="handleDateChange">
    </el-date-picker>
    <gf-ueditor
        v-else-if="moduleField.type==='richtext'"
        v-model="tempValue"
        :editorId="'module-filed-data'+(new Date()).getTime()"
        :tool-bars="cmsToolbar"
        @setEditContent="setEditContent">
    </gf-ueditor>
    <el-checkbox-group v-else-if="moduleField.type==='checkbox'" v-model="tempValue">
      <el-checkbox
          v-for="(item,index) in moduleFieldOptions"
          :key="index"
          :label="item.value">
        {{item.label}}
      </el-checkbox>
    </el-checkbox-group>
    <el-radio-group v-else-if="moduleField.type==='radio'" v-model="tempValue">
      <el-radio
          v-for="(item,index) in moduleFieldOptions"
          :key="index"
          :label="item.value">
        {{item.label}}
      </el-radio>
    </el-radio-group>
    <el-select v-else-if="moduleField.type==='select'" v-model="tempValue">
      <el-option
          v-for="(item,index) in moduleFieldOptions"
          :key="index"
          :label="item.label"
          :value="item.value">
      </el-option>
    </el-select>
    <upload-img
        v-else-if="moduleField.type==='image'"
        v-model="tempValue"
        :limit="1"
        :action="baseURL+'api/v1/system/upload/singleImg'"
        @uploadData="setUpImgList">
    </upload-img>
    <upload-img
        v-else-if="moduleField.type==='images'"
        v-model="tempValue"
        :limit="10"
        :action="baseURL+'api/v1/system/upload/singleImg'"
        @uploadData="setUpImgList">
    </upload-img>
    <upload-file
        v-else-if="moduleField.type==='file'"
        v-model="tempValue"
        :drag="false"
        :action="baseURL+'api/v1/system/upload/singleFile'"
        :limit="1"
        @upFileData="handleUpFile">
      <el-button type="primary" :icon="Upload">点击上传文件</el-button>
    </upload-file>
    <upload-file
        v-else-if="moduleField.type==='files'"
        v-model="tempValue"
        :drag="false"
        :action="baseURL+'api/v1/system/upload/singleFile'"
        :limit="10"
        @upFileData="handleUpFileList">
      <el-button type="primary" :icon="Upload">点击上传文件</el-button>
    </upload-file>
    <el-input v-else v-model.trim="tempValue" @input="handleTextInput"></el-input>
  </div>
</template>

<script lang="ts">
import {defineComponent, getCurrentInstance, PropType, reactive, ref} from "vue";
import {ModulesFieldInfoData} from "/@/views/system/modulesField/list/component/model";
import {cmsToolbar} from "/@/components/ueditor/model";
import {ElMessage, UploadProps} from "element-plus";
import {getToken} from "/@/utils/gfast";
import uploadImg from "/@/components/uploadImg/index.vue";
import uploadFile from "/@/components/uploadFile/index.vue";
import GfUeditor from "/@/components/ueditor/index.vue";
import {Upload} from "@element-plus/icons-vue";

export interface ModuleFieldOptions {
  label: string;
  value: any;
}

export default defineComponent({
  name: "moduleDataFormItem",
  components: {
    uploadImg,
    uploadFile,
    GfUeditor
  },
  props: {
    width: {
      type: String,
      default: ""
    },
    moduleField: Object as PropType<ModulesFieldInfoData>,
    modelValue: null
  },
  setup(props, {emit}) {
    const baseURL: string | undefined | boolean = import.meta.env.VITE_API_URL;
    const {proxy} = <any>getCurrentInstance();
    //图片上传地址
    const imageUrlThumb = ref("");
    //上传加载
    const upLoadingThumb = ref(false);
    const tempValue = ref(props.modelValue);
    const setEditContent = (data: string) => {
      emit("update:modelValue", data);
    };
    const moduleFieldOptions = ref<ModuleFieldOptions[]>([]);
    if(props.moduleField?.type && ["select","radio","checkbox"].includes(props.moduleField?.type)) {
      if (props.moduleField?.options) {
        props.moduleField?.options.split(",").forEach((item: any) => {
          const tItem = item.split(":")
          if (Array.isArray(tItem) && tItem.length === 2) {
            moduleFieldOptions.value.push({label: tItem[0], value: tItem[1]})
          }
        })
      }
    }
    //单图上传缩略图
    const handleAvatarSuccessThumb: UploadProps["onSuccess"] = (res, file) => {
      if (res.code === 0) {
        imageUrlThumb.value = URL.createObjectURL(file.raw!);
        emit("update:modelValue", res.data.path);
      } else {
        ElMessage.error(res.msg);
      }
      upLoadingThumb.value = false;
    };
    const beforeAvatarUploadThumb = () => {
      upLoadingThumb.value = true;
      return true;
    };
    const setUpData = () => {
      return {token: getToken()};
    };
    const setUpImgList = (val: any) => {
      emit("update:modelValue",val)
    };
    const handleDateChange = (value: any) => {
      emit("update:modelValue",value)
    };
    const handleUpFile = (data: any) => {
      emit("update:modelValue", data);
    };
    const handleUpFileList = (data: any) => {
      emit("update:modelValue", data);
    };
    const handleTextInput = (value:any)=>{
      emit("update:modelValue",value)
    }
    return {
      proxy,
      Upload,
      moduleFieldOptions,
      handleUpFile,
      handleTextInput,
      handleUpFileList,
      setUpImgList,
      setEditContent,
      handleDateChange,
      imageUrlThumb,
      upLoadingThumb,
      handleAvatarSuccessThumb,
      beforeAvatarUploadThumb,
      setUpData,
      baseURL,
      tempValue,
      cmsToolbar: reactive(cmsToolbar)
    };
  },
  watch:{
    "tempValue":function(newValue:any){
      this.$emit("update:modelValue",newValue)
    }
  }
});
</script>

<style scoped>
.module-field-forum-item :deep(.avatar-uploader .avatar) {
  max-width: 178px;
  max-height: 178px;
  display: block;
}

.module-field-forum-item :deep(.avatar-uploader .el-upload) {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.module-field-forum-item :deep(.avatar-uploader .el-upload:hover) {
  border-color: var(--el-color-primary);
}

.module-field-forum-item :deep(.el-icon.avatar-uploader-icon) {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
}

</style>
<style>
.el-select__popper {
  z-index: 99999!important;
}
</style>
