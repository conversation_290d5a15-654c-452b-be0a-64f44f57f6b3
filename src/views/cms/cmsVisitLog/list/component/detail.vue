<template>
  <!-- 访问日志详情抽屉 -->  
  <div class="cms-cmsVisitLog-detail">
    <el-drawer v-model="isShowDialog" size="80%" direction="ltr">
      <template #header>
        <h4>访问日志详情</h4>
      </template>
      <el-descriptions
              class="margin-top"
              :column="3"
              border
              style="margin: 8px;"
      >        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  ID
                </div>
              </template>
              {{ formData.id }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  访问IP
                </div>
              </template>
              {{ formData.ip }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  URL
                </div>
              </template>
              {{ formData.url }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  AGENT
                </div>
              </template>
              {{ formData.agent }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  文章id
                </div>
              </template>
              {{ formData.articleId }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">            
              <template #label>
                <div class="cell-item">
                  分类id
                </div>
              </template>
              {{ formData.categoryId }}            
          </el-descriptions-item>        
          <el-descriptions-item :span="1">
            <template #label>
              <div class="cell-item">
                访问时间
              </div>
            </template>
            {{ proxy.parseTime(formData.createdAt, '{y}-{m}-{d} {h}:{i}:{s}') }}
          </el-descriptions-item>        
      </el-descriptions>
    </el-drawer>
  </div>
</template>
<script setup lang="ts">
  import { reactive, toRefs, defineComponent,ref,unref,getCurrentInstance,computed } from 'vue';
  import {ElMessageBox, ElMessage, FormInstance,UploadProps} from 'element-plus';  
  import {
    listCmsVisitLog,
    getCmsVisitLog,
    delCmsVisitLog,
    addCmsVisitLog,
    updateCmsVisitLog,    
  } from "/@/api/cms/cmsVisitLog";  
  import {
    CmsVisitLogTableColumns,
    CmsVisitLogInfoData,
    CmsVisitLogTableDataState,
    CmsVisitLogEditState
  } from "/@/views/cms/cmsVisitLog/list/component/model"
  defineOptions({ name: "ApiV1CmsCmsVisitLogDetail"})  
  const {proxy} = <any>getCurrentInstance()
  const formRef = ref<HTMLElement | null>(null);
  const menuRef = ref();  
  const state = reactive<CmsVisitLogEditState>({
    loading:false,
    isShowDialog: false,
    formData: {      
      id: undefined,      
      ip: undefined,      
      url: undefined,      
      agent: undefined,      
      articleId: undefined,      
      categoryId: undefined,      
      createdAt: undefined,      
    },
    // 表单校验
    rules: {      
      id : [
          { required: true, message: "ID不能为空", trigger: "blur" }
      ],      
    }
  });
  const { isShowDialog,formData } = toRefs(state);
  // 打开弹窗
  const openDialog = (row?: CmsVisitLogInfoData) => {
    resetForm();
    if(row) {
      getCmsVisitLog(row.id!).then((res:any)=>{
        const data = res.data;        
        state.formData = data;
      })
    }
    state.isShowDialog = true;
  };
  // 关闭弹窗
  const closeDialog = () => {
    state.isShowDialog = false;
  };
  defineExpose({
    openDialog,
  });
  // 取消
  const onCancel = () => {
    closeDialog();
  };
  const resetForm = ()=>{
    state.formData = {      
      id: undefined,      
      ip: undefined,      
      url: undefined,      
      agent: undefined,      
      articleId: undefined,      
      categoryId: undefined,      
      createdAt: undefined,      
    }
  };  
</script>
<style scoped>  
  .cms-cmsVisitLog-detail :deep(.el-form-item--large .el-form-item__label){
    font-weight: bolder;
  }
  .pic-block{
    margin-right: 8px;
  }
  .file-block{
    width: 100%;
    border: 1px solid var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
    margin-bottom: 5px;
    padding: 3px 6px;
  }
  .ml-2{margin-right: 5px;}
</style>