// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2023-01-31 09:13:21
// 生成路径: internal/app/cms/model/entity/cms_config.go
// 生成人：yxf
// desc:CMS配置
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/util/gmeta"
)

// CmsConfig is the golang structure for table cms_config.
type CmsConfig struct {
	gmeta.Meta  `orm:"table:cms_config"`
	Id          uint   `orm:"id,primary" json:"id"`           // ID
	Title       string `orm:"title" json:"title"`             // 标签
	Name        string `orm:"name" json:"name"`               // 名称
	Value       string `orm:"value" json:"value"`             // 内容
	Description string `orm:"description" json:"description"` // 说明
	Type        string `orm:"type" json:"type"`               // 类型
}
