// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2023-02-01 15:22:21
// 生成路径: api/v1/system/modules_field.go
// 生成人：yxf
// desc:模型字段相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package system

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/system/model"
)

// ModulesFieldSearchReq 分页请求参数
type ModulesFieldSearchReq struct {
	g.Meta   `path:"/list" tags:"系统后台/模型字段" method:"get" summary:"模型字段列表"`
	ModuleId uint `p:"moduleId"`
	commonApi.PageReq
	commonApi.Author
}

// ModulesFieldSearchRes 列表返回结果
type ModulesFieldSearchRes struct {
	g.Meta `mime:"application/json"`
	commonApi.ListRes
	List []*model.ModulesFieldListRes `json:"list"`
}

// ModulesFieldAddReq 添加操作请求参数
type ModulesFieldAddReq struct {
	g.Meta `path:"/add" tags:"系统后台/模型字段" method:"post" summary:"模型字段添加"`
	commonApi.Author
	Label       string `p:"label" v:"required#字段名称不能为空"`
	Name        string `p:"name" v:"required#调用名称不能为空"`
	Type        string `p:"type" `
	ModuleId    uint   `p:"moduleId"`
	Description string `p:"description" `
	Default     string `p:"default" `
	Sort        int    `p:"sort" `
	Options     string `p:"options" `
	FieldLength int    `p:"fieldLength"`
}

// ModulesFieldAddRes 添加操作返回结果
type ModulesFieldAddRes struct {
	commonApi.EmptyRes
}

// ModulesFieldEditReq 修改操作请求参数
type ModulesFieldEditReq struct {
	g.Meta `path:"/edit" tags:"系统后台/模型字段" method:"put" summary:"模型字段修改"`
	commonApi.Author
	Id          uint   `p:"id" v:"required#主键ID不能为空"`
	Label       string `p:"label" v:"required#字段名称不能为空"`
	Name        string `p:"name" v:"required#调用名称不能为空"`
	Type        string `p:"type" `
	Description string `p:"description" `
	Default     string `p:"default" `
	Options     string `p:"options" `
	FieldLength int    `p:"fieldLength"`
}

// ModulesFieldEditRes 修改操作返回结果
type ModulesFieldEditRes struct {
	commonApi.EmptyRes
}

// ModulesFieldGetReq 获取一条数据请求
type ModulesFieldGetReq struct {
	g.Meta `path:"/get" tags:"系统后台/模型字段" method:"get" summary:"获取模型字段信息"`
	commonApi.Author
	Id uint `p:"id" v:"required#主键必须"` //通过主键获取
}

// ModulesFieldGetRes 获取一条数据结果
type ModulesFieldGetRes struct {
	g.Meta `mime:"application/json"`
	*model.ModulesFieldInfoRes
}

// ModulesFieldDeleteReq 删除数据请求
type ModulesFieldDeleteReq struct {
	g.Meta `path:"/delete" tags:"系统后台/模型字段" method:"delete" summary:"删除模型字段"`
	commonApi.Author
	Ids []uint `p:"ids" v:"required#主键必须"` //通过主键删除
}

// ModulesFieldDeleteRes 删除数据返回
type ModulesFieldDeleteRes struct {
	commonApi.EmptyRes
}

// ModulesFieldUpdateSortReq 字段排序请求
type ModulesFieldUpdateSortReq struct {
	g.Meta `path:"/updateSort" tags:"系统后台/模型字段" method:"put" summary:"模型字段排序"`
	Data   []*model.ModulesFieldUpdateSort `p:"data"`
}

// ModulesFieldUpdateSortRes 字段排序返回
type ModulesFieldUpdateSortRes struct {
	commonApi.EmptyRes
}
