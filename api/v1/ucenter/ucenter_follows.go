/**
 * @Company: 云南奇讯科技有限公司
 * @Author: yxf
 * @Description:
 * @Version: 1.0.0
 * @Date: 2023/3/10 15:25
 */

package ucenter

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ucenter/model"
)

// UCenterFollowsAddReq 关注用户请求
type UCenterFollowsAddReq struct {
	g.Meta     `path:"/add" tags:"ucenter前台/关注用户" method:"post" summary:"关注用户"`
	FollowerId uint64 `p:"followerId"`
}

// UCenterFollowsAddRes 关注用户返回
type UCenterFollowsAddRes struct {
	common.EmptyRes
}

// UCenterFollowsDeleteReq 取消关注
type UCenterFollowsDeleteReq struct {
	g.Meta     `path:"/delete" tags:"ucenter前台/关注用户" method:"delete" summary:"删除关注用户"`
	FollowerId uint64 `p:"followerId"`
}

// UCenterFollowsDeleteRes 取消关注用户返回
type UCenterFollowsDeleteRes struct {
	common.EmptyRes
}

// UCenterFollowsListReq 获取关注用户列表
type UCenterFollowsListReq struct {
	g.Meta   `path:"/list" tags:"ucenter前台/关注用户" method:"get" summary:"获取关注用户列表"`
	MemberId uint64 `p:"memberId"`
	common.PageReq
}

// UCenterEditCoverRes 获取关注用户列表返回
type UCenterFollowsListRes struct {
	g.Meta `mime:"application/json"`
	common.ListRes
	List []*model.UCenterFollowsListRes `json:"list"`
}
