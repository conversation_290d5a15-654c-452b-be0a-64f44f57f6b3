<div class="member-form-wrap member-form-login">
    <div class="member-form-inner">
        <div class="member-form-head">
            <div class="member-form-head">
                <a class="member-form-logo" href="${.cmsConfig.site_url}">
                    <img src="/cms/default/images/cms_logo.png">
                </a>
            </div>
        </div>
        <ul class="member-form-tab">
            <li class="active"><a data-type="1" href="#">通过邮箱找回密码</a></li>
            <li><a data-type="2" href="#">通过手机号找回密码</a></li>
        </ul>
        <form class="member-form j-member-form" id="login-form" method="post" action="">
            <input type="hidden" id="reg_type" value="username">
            <div class="member-form-items">
                <div class="form-group">
                    <label>
                        <i class="iconfont icon-user hotnews-icon"></i>
                        <input class="form-input require" id="username" name="username" placeholder="请输入账号" type="text">
                    </label>
                </div>
                <div class="form-group login-item login-1">
                    <label>
                        <i class="iconfont icon-mail hotnews-icon"></i>
                        <input class="form-input require" id="email" name="email" placeholder="请输入注册时的电子邮箱" type="text">
                    </label>
                </div>
                
                <div class="form-group login-item login-2">
                    <label>
                        <i class="iconfont icon-phone hotnews-icon"></i>
                        <input class="form-input is-input require" data-label="手机号码" data-rule="mobile" id="mobile" name="mobile" placeholder="请输入手机号码" type="text">
                    </label>
                </div>
                <div class="form-group captcha">
                    <label>
                        <i class="iconfont icon-safetycertificate hotnews-icon"></i>
                        <input class="form-input require captcha-code" id="captcha_code" name="verifyCode" placeholder="请输入验证码" type="text">
                    </label>
                    <div class="captcha-img">
                        <img id="captcha_img" src="">
                        <input id="captcha_key" name="verifyKey" value="" type="hidden">
                    </div>
                </div>
                <div class="form-group login-item login-1">
                    <div class="form-group sms-code">
                        <label>
                            <i class="iconfont icon-safetycertificate hotnews-icon"></i>
                            <input autocomplete="off" class="form-input is-input require" id="email_code" name="emailCode" placeholder="请输入邮箱验证码" type="text">
                        </label>
                        <div class="btn btn-lg send-sms-code j-send-sms-code" id="sendEmailCode">发送验证码</div>
                    </div>
                </div>
                <div class="login-item login-2">
                    <div class="form-group sms-code">
                        <label>
                            <i class="iconfont icon-safetycertificate hotnews-icon"></i>
                            <input autocomplete="off" class="form-input is-input require" data-label="验证码" data-rule="sms_code:user_phone" id="sms_code" name="smsCode"
                                    placeholder="请输入短信验证码" type="text">
                        </label>
                        <div class="btn btn-lg send-sms-code j-send-sms-code" data-target="user_phone" id="sendMobileCode">发送验证码</div>
                    </div>
                </div>
            </div>
            <button class="btn btn-primary btn-block btn-lg" type="button" onclick="submitForgot()">提交</button>
        </form>
        <div class="member-form-footer" style="display:block;text-align: center;">
            <span class="member-switch"><a href="${.cmsConfig.site_url}/account/login">返回登录</a></span>
            <span class="member-switch" style="margin-left: 20px;"><a href="${.cmsConfig.site_url}/account/register">立即注册</a></span>
            <span class="member-switch" style="margin-left: 20px;"><a href="${.cmsConfig.site_url}">首页</a></span>
        </div>
    </div>
</div>
<script>
    function submitForgot(){
        var username = $("#username").val()
        var regType = $("#reg_type").val()
        var email = $("#email").val()
        var mobile = $("#mobile").val()
        var verifyCode = $("#email_code").val()
        if(regType ==='mobile') {
            verifyCode = $("#sms_code").val()
        }
        $.post(APIBASE+"ucenter/account/forgot",{username,regType,email,mobile,verifyCode},function (res){
            if(res.code === 0) {
                window.location.href = '${.cmsConfig.site_url}/account/resetPwd?key='+res.data.forgotKey
            } else {
                layer.alert(res.message,{icon:2})
            }
        })
    }
    
    $(function () {
    
    })
</script>