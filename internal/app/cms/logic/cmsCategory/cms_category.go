// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2023-01-31 09:33:48
// 生成路径: internal/app/cms/logic/cms_category.go
// 生成人：yxf
// desc:文章分类
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/api/v1/cms"
	"github.com/tiger1103/gfast/v3/internal/app/cms/dao"
	"github.com/tiger1103/gfast/v3/internal/app/cms/model"
	"github.com/tiger1103/gfast/v3/internal/app/cms/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/cms/service"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/libCms"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterCmsCategory(New())
}

func New() *sCmsCategory {
	return &sCmsCategory{}
}

type sCmsCategory struct{}

func (s *sCmsCategory) List(ctx context.Context, req *cms.CmsCategorySearchReq) (listRes *cms.CmsCategorySearchRes, err error) {
	listRes = new(cms.CmsCategorySearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.CmsCategory.Ctx(ctx).WithAll()
		if req.Name != "" {
			m = m.Where(dao.CmsCategory.Columns().Name+" like ?", "%"+req.Name+"%")
		}
		if req.Status != "" {
			m = m.Where(dao.CmsCategory.Columns().Status+"=?", req.Status)
		}
		if req.ParentId != "" && !req.ChildNode {
			m = m.Where(dao.CmsCategory.Columns().ParentId+"=?", req.ParentId)
		}
		if req.Type != "" {
			m = m.Where(dao.CmsCategory.Columns().Type+"=?", req.Type)
		}
		// 获取所有子分类包括自己
		if req.ChildNode {
			childIds, err := s.GetChildrenIds(ctx, []uint{gconv.Uint(req.ParentId)})
			liberr.ErrIsNil(ctx, err, "获取子分类失败")
			m = m.WhereIn(dao.CmsCategory.Columns().Id, childIds)
		}
		//判断是否有管理所有部门权限
		userId := sysService.Context().GetUserId(ctx)
		if !sysService.SysUser().AccessRule(ctx, userId, "api/v1/cms/cmsCategory/all") {
			//获取当前用户角色的分类权限
			superAdmin, categoryIds, err := s.GetCategoryIdsByRoleId(ctx)
			//查找出所有父级
			categoryIds, _ = s.GetParentIds(ctx, categoryIds)
			liberr.ErrIsNil(ctx, err, "获取数据失败")
			if !superAdmin {
				m = m.WhereIn(dao.CmsCategory.Columns().Id, categoryIds)
			}
		}
		order := "sort asc, id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.CmsCategoryInfoRes
		err = m.Fields(cms.CmsCategorySearchRes{}).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.CmsCategoryListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.CmsCategoryListRes{
				Id:             v.Id,
				Name:           v.Name,
				ParentId:       v.ParentId,
				Type:           v.Type,
				ModuleId:       v.ModuleId,
				LinkedModuleId: v.LinkedModuleId,
				Sort:           v.Sort,
				Thumb:          v.Thumb,
				Status:         v.Status,
				Alias:          v.Alias,
				JumpUrl:        v.JumpUrl,
				Url:            libCms.GetCmsUrl(v.Type, v.Url, gconv.Int(v.Id)),
				UpdatedAt:      v.UpdatedAt,
				Nav:            v.Nav,
			}
		}
	})
	return
}

// 获取所有分类
func (s *sCmsCategory) all(ctx context.Context, req *cms.CmsCategorySearchReq) (listRes *cms.CmsCategorySearchRes, err error) {
	listRes = new(cms.CmsCategorySearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.CmsCategory.Ctx(ctx)
		var res []*model.CmsCategoryInfoRes
		err = m.Fields(cms.CmsCategorySearchRes{}).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.CmsCategoryListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.CmsCategoryListRes{
				Id:             v.Id,
				Name:           v.Name,
				ParentId:       v.ParentId,
				Type:           v.Type,
				ModuleId:       v.ModuleId,
				LinkedModuleId: v.LinkedModuleId,
				Sort:           v.Sort,
				Thumb:          v.Thumb,
				Status:         v.Status,
				Alias:          v.Alias,
				JumpUrl:        v.JumpUrl,
				Url:            v.Url,
			}
		}
	})
	return
}

func (s *sCmsCategory) GetById(ctx context.Context, id uint) (res *model.CmsCategoryInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.CmsCategory.Ctx(ctx).WithAll().Where(dao.CmsCategory.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sCmsCategory) Add(ctx context.Context, req *cms.CmsCategoryAddReq) (err error) {
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		err = g.Try(ctx, func(ctx context.Context) {
			for _, obj := range req.Banner {
				obj.Url, err = libUtils.GetFilesPath(ctx, obj.Url)
				liberr.ErrIsNil(ctx, err)
			}
			categoryId, err := dao.CmsCategory.Ctx(ctx).InsertAndGetId(do.CmsCategory{
				Name:            req.Name,
				Type:            req.Type,
				SeoTitle:        req.SeoTitle,
				Keywords:        req.Keywords,
				Description:     req.Description,
				Content:         req.Content,
				ParentId:        req.ParentId,
				ModuleId:        req.ModuleId,
				Sort:            req.Sort,
				Template:        req.Template,
				ContentTemplate: req.ContentTemplate,
				IsInherit:       req.IsInherit,
				Thumb:           req.Thumb,
				Banner:          req.Banner,
				Status:          req.Status,
				PageTemplate:    req.PageTemplate,
				ChannelTemplate: req.ChannelTemplate,
				JumpUrl:         req.JumpUrl,
				Alias:           req.Alias,
				Nav:             req.Nav,
			})
			liberr.ErrIsNil(ctx, err, "添加失败")
			//设置角色分类权限
			err = s.saveRolePermission(ctx, req.Permissions, uint(categoryId))
			liberr.ErrIsNil(ctx, err, "设置角色分类权限失败")
			//isInherit 所有子级继承本模版
			if req.IsInherit == "1" {
				childCategoryIds, _ := s.GetChildrenIds(ctx, []uint{uint(categoryId)})
				_, err = dao.CmsCategory.Ctx(ctx).WhereIn(dao.CmsCategory.Columns().Id, childCategoryIds).Update(do.CmsCategory{
					Template:        req.Template,
					ContentTemplate: req.ContentTemplate,
				})
				liberr.ErrIsNil(ctx, err, "子级继承本模版失败")
			}
		})
		return err
	})
	return
}

func (s *sCmsCategory) Edit(ctx context.Context, req *cms.CmsCategoryEditReq) (err error) {
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		err = g.Try(ctx, func(ctx context.Context) {
			for _, obj := range req.Banner {
				obj.Url, err = libUtils.GetFilesPath(ctx, obj.Url)
				liberr.ErrIsNil(ctx, err)
			}
			_, err = dao.CmsCategory.Ctx(ctx).WherePri(req.Id).Update(do.CmsCategory{
				Name:            req.Name,
				Type:            req.Type,
				SeoTitle:        req.SeoTitle,
				Keywords:        req.Keywords,
				Description:     req.Description,
				Content:         req.Content,
				ParentId:        req.ParentId,
				ModuleId:        req.ModuleId,
				Sort:            req.Sort,
				Template:        req.Template,
				ContentTemplate: req.ContentTemplate,
				IsInherit:       req.IsInherit,
				Thumb:           req.Thumb,
				Banner:          req.Banner,
				Status:          req.Status,
				PageTemplate:    req.PageTemplate,
				ChannelTemplate: req.ChannelTemplate,
				JumpUrl:         req.JumpUrl,
				Alias:           req.Alias,
				Nav:             req.Nav,
			})
			liberr.ErrIsNil(ctx, err, "修改失败")
			//设置角色分类权限
			err = s.saveRolePermission(ctx, req.Permissions, req.Id)
			liberr.ErrIsNil(ctx, err, "设置角色分类权限失败")
			//isInherit 所有子级继承本模版
			if req.IsInherit == "1" {
				childCategoryIds, _ := s.GetChildrenIds(ctx, []uint{req.Id})
				_, err = dao.CmsCategory.Ctx(ctx).WhereIn(dao.CmsCategory.Columns().Id, childCategoryIds).Update(do.CmsCategory{
					Template:        req.Template,
					ContentTemplate: req.ContentTemplate,
				})
				liberr.ErrIsNil(ctx, err, "子级继承本模版失败")
			}
		})
		return err
	})
	return
}

func (s *sCmsCategory) Delete(ctx context.Context, ids []uint) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var childrenIds []uint
		childrenIds, err = s.GetChildrenIds(ctx, ids)
		liberr.ErrIsNil(ctx, err)
		ids = append(ids, childrenIds...)
		_, err = dao.CmsCategory.Ctx(ctx).Delete(dao.CmsCategory.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

// GetChildrenIds 通过ID获取子级ID
func (s *sCmsCategory) GetChildrenIds(ctx context.Context, ids []uint) (returnIds []uint, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		//获取所有
		var all *cms.CmsCategorySearchRes
		all, err = s.all(ctx, &cms.CmsCategorySearchReq{})
		liberr.ErrIsNil(ctx, err)
		list := make([]map[string]interface{}, len(all.List))
		for k, info := range all.List {
			list[k] = gconv.Map(info)
		}
		returnIds = ids
		for _, id := range ids {
			children := libUtils.FindSonByParentId(list, id, "parentId", "id")
			for _, cid := range children {
				returnIds = append(returnIds, cid["id"].(uint))
			}
		}
		//数组元素去重
		returnIds = libUtils.SliceUnique(returnIds)
	})
	return
}

// GetParentIds 通过ID获取父级ID
func (s *sCmsCategory) GetParentIds(ctx context.Context, ids []uint) (returnIds []uint, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		//获取所有
		var all *cms.CmsCategorySearchRes
		all, err = s.all(ctx, &cms.CmsCategorySearchReq{})
		liberr.ErrIsNil(ctx, err)
		list := make([]map[string]interface{}, len(all.List))
		for k, info := range all.List {
			list[k] = gconv.Map(info)
		}
		returnIds = ids
		for _, id := range ids {
			children := libUtils.FindParentBySonPid(list, gconv.Int(id), "", "parentId", "", "id")
			for _, cid := range children {
				returnIds = append(returnIds, cid["id"].(uint))
			}
		}
		//数组去重
		returnIds = libUtils.SliceUnique(returnIds)
	})
	return
}

func (s *sCmsCategory) ListPermissions(ctx context.Context, req *cms.CmsCategoryPermissionsReq) (res *cms.CmsCategoryPermissionsRes, err error) {
	res = new(cms.CmsCategoryPermissionsRes)
	err = g.Try(ctx, func(ctx context.Context) {
		list := []*model.CmsCategoryPermissionsRes{}
		err = dao.CmsCategoryPermissions.Ctx(ctx).WithAll().Where(dao.CmsCategoryPermissions.Columns().RoleId, req.RoleId).Scan(&list)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
		res.List = make([]*cms.CategoryPermissions, len(list))
		for k, v := range list {
			res.List[k] = &cms.CategoryPermissions{
				CategoryId:  v.CategoryId,
				Permissions: v.Permissions,
			}
		}
	})
	return
}

func (s *sCmsCategory) SavePermissions(ctx context.Context, req *cms.CmsCategorySavePermissionsReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		//先删除该角色下的权限
		dao.CmsCategoryPermissions.Ctx(ctx).Where(dao.CmsCategoryPermissions.Columns().RoleId, req.RoleId).Delete()
		for _, v := range req.Permissions {
			if v.Permissions != "" {
				_, err := dao.CmsCategoryPermissions.Ctx(ctx).Insert(do.CmsCategoryPermissions{
					RoleId:      req.RoleId,
					CategoryId:  v.CategoryId,
					Permissions: v.Permissions,
				})
				liberr.ErrIsNil(ctx, err, "添加失败")
			}
		}
	})
	return
}

func (s *sCmsCategory) saveRolePermission(ctx context.Context, permissions []*cms.RolePermissions, categoryId uint) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		//先删除该角色下的权限
		dao.CmsCategoryPermissions.Ctx(ctx).Where(dao.CmsCategoryPermissions.Columns().CategoryId, categoryId).Delete()
		for _, v := range permissions {
			if v.Permissions != "" {
				_, err := dao.CmsCategoryPermissions.Ctx(ctx).Insert(do.CmsCategoryPermissions{
					RoleId:      v.RoleId,
					CategoryId:  categoryId,
					Permissions: v.Permissions,
				})
				liberr.ErrIsNil(ctx, err)
			}
		}
	})
	return
}

// GetCategoryIdsByRoleId 通过角色ID获取权限内的分类IDS(列表权限)
func (s *sCmsCategory) GetCategoryIdsByRoleId(ctx context.Context) (superAdmin bool, categoryIds []uint, err error) {
	userId := sysService.Context().GetUserId(ctx)
	if !sysService.SysUser().IsSupperAdmin(ctx, userId) {
		//获取用户的分类权限
		roleIds, _ := sysService.SysUser().GetAdminRoleIds(ctx, userId, false)
		categorys, _ := dao.CmsCategoryPermissions.Ctx(ctx).
			Fields("category_id").
			WhereIn(dao.CmsCategoryPermissions.Columns().RoleId, roleIds).
			Where(" find_in_set(?,`"+dao.CmsCategoryPermissions.Columns().Permissions+"`)", "list").
			Array()
		for _, v := range categorys {
			categoryIds = append(categoryIds, gconv.Uint(v))
		}
		superAdmin = false
	} else {
		categorys, _ := dao.CmsCategoryPermissions.Ctx(ctx).
			Fields("category_id").
			Where(" find_in_set(?,`"+dao.CmsCategoryPermissions.Columns().Permissions+"`)", "list").
			Array()
		for _, v := range categorys {
			categoryIds = append(categoryIds, gconv.Uint(v))
		}
		superAdmin = true
	}
	return
}

// 检测按钮权限
func (s *sCmsCategory) CheckPermission(ctx context.Context, req *cms.CmsCategoryCheckPermissionReq) (res bool, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		//判断是否开启三审
		//是否超级管理员
		userId := sysService.Context().GetUserId(ctx)
		if !sysService.SysUser().IsSupperAdmin(ctx, userId) {
			roleIds, _ := sysService.SysUser().GetAdminRoleIds(ctx, userId, false)
			permissions, _ := dao.CmsCategoryPermissions.Ctx(ctx).Fields("permissions").Where(dao.CmsCategoryPermissions.Columns().CategoryId, req.CategoryId).WhereIn(dao.CmsCategoryPermissions.Columns().RoleId, roleIds).Array()
			for _, v := range permissions {
				//v是,分隔的字符串,处理成数组
				pers := gstr.Explode(",", gconv.String(v))
				for _, p := range pers {
					if req.Permission == p {
						res = true
						return
					}
				}
			}
		} else {
			//当为超级管理员并且该栏目类型为list时,直接返回true
			category, _ := dao.CmsCategory.Ctx(ctx).Where(dao.CmsCategory.Columns().Id, req.CategoryId).Where(dao.CmsCategory.Columns().Type, "list").One()
			if category == nil {
				res = false
			} else {
				res = true
			}
			return
		}
		res = false
		return
	})
	return
}

// 根据分类id获取具有对应权限的同一模型的分类
func (s *sCmsCategory) GetPerCategorys(ctx context.Context, req *cms.CmsCategoryGetPerCategorysReq) (resList *cms.CmsCategoryGetPerCategorysRes, err error) {
	userId := sysService.Context().GetUserId(ctx)
	categoryId := req.CategoryId
	moduleId, err := dao.CmsCategory.Ctx(ctx).Where(dao.CmsCategory.Columns().Id, categoryId).Value(dao.CmsCategory.Columns().ModuleId)
	if err != nil {
		return nil, gerror.Wrap(err, "获取模块ID失败")
	}
	// 增加对 moduleId 的非空检查
	if moduleId == nil {
		return nil, gerror.New("分类不存在")
	}

	// 初始化 resList，避免空指针异常
	resList = &cms.CmsCategoryGetPerCategorysRes{
		List: []*model.CmsCategoryListRes{},
	}
	var res []*model.CmsCategoryListRes
	//所有频道和列表分类
	err = dao.CmsCategory.Ctx(ctx).Where(dao.CmsCategory.Columns().Status, 1).
		WhereIn(dao.CmsCategory.Columns().Type, []string{"list", "channel"}).
		Scan(&res)
	if err != nil {
		return nil, gerror.Wrap(err, "获取分类失败")
	}
	// 超级管理员时,直接返回所有分类
	if sysService.SysUser().IsSupperAdmin(ctx, userId) {
		for _, v := range res {
			if v.Type == "channel" || (v.Type == "list" && v.ModuleId == gconv.Uint(moduleId)) {
				resList.List = append(resList.List, v)
			}
		}
	} else {
		// 获取用户的分类权限
		roleIds, err := sysService.SysUser().GetAdminRoleIds(ctx, userId, false)
		if err != nil {
			return nil, gerror.Wrap(err, "获取角色ID失败")
		}
		categoryPermissons, err := dao.CmsCategoryPermissions.Ctx(ctx).Fields("category_id", "permissions").
			WhereIn(dao.CmsCategoryPermissions.Columns().RoleId, roleIds).All()
		if err != nil {
			return nil, gerror.Wrap(err, "获取分类权限失败")
		}
		categoryIds := []uint64{}
		for _, v := range categoryPermissons {
			// 将 *gvar.Var 转换为 map[string]interface{}
			vMap := v.Map()
			categoryId := gconv.Uint64(vMap["category_id"])
			if categoryId > 0 {
				categoryIds = append(categoryIds, categoryId)
			}
		}

		var result gdb.Result
		result, err = dao.CmsCategory.Ctx(ctx).
			Where(dao.CmsCategory.Columns().Status, "1").
			Where(dao.CmsCategory.Columns().ModuleId, moduleId).
			WhereIn(dao.CmsCategory.Columns().Id, categoryIds).
			WhereIn(dao.CmsCategory.Columns().Type, []string{"list", "channel"}).
			All()
		if err != nil {
			return nil, gerror.Wrap(err, "获取分类失败")
		}

		for _, v := range result {
			vMap := v.Map()
			for _, p := range categoryPermissons {
				// v是,分隔的字符串,处理成数组
				pers := gstr.Explode(",", gconv.String(p.Map()["permissions"]))
				for _, n := range pers {
					if req.Permission == n && gconv.Uint(vMap["id"]) == gconv.Uint(p.Map()["category_id"]) {
						// 转换当前项为所需类型
						var currentCategory *model.CmsCategoryListRes
						if err = gconv.Struct(vMap, &currentCategory); err != nil {
							return nil, gerror.Wrap(err, "数据转换失败")
						}
						// 将 gdb.Result 转换为 []map[string]interface{}
						resultList := make([]map[string]interface{}, len(res))
						for i, r := range res {
							resultList[i] = gconv.Map(r)
						}
						// 查找并转换父级
						parentList := libUtils.FindParentBySonPid(resultList, gconv.Int(vMap["id"]), "", "parentId", "", "id")
						for _, parent := range parentList {
							var parentCategory *model.CmsCategoryListRes
							if err = gconv.Struct(parent, &parentCategory); err != nil {
								return nil, gerror.Wrap(err, "父级数据转换失败")
							}
							resList.List = append(resList.List, parentCategory)
						}
					}
				}
			}
		}
		//数组去重
		resList.List = RemoveDuplicatesByKey(resList.List, func(l *model.CmsCategoryListRes) uint {
			return l.Id
		})
	}
	return
}

// 泛型去重函数：根据用户提供的字段提取 Key
func RemoveDuplicatesByKey[T any, K comparable](slice []T, getKey func(T) K) []T {
	seen := make(map[K]bool)
	result := []T{}
	for _, item := range slice {
		key := getKey(item)
		if !seen[key] {
			seen[key] = true
			result = append(result, item)
		}
	}
	return result
}
