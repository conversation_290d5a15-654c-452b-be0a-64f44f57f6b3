// =================================================================================
// Code generated by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// ModulesInfo is the golang structure for table modules_info.
type ModulesInfo struct {
	Id        uint        `json:"id"        description:"ID"`
	Name      string      `json:"name"      description:"模型名称"`
	Sort      int         `json:"sort"      description:"排序"`
	TableName string      `json:"tableName" description:"数据表名"`
	CreatedAt *gtime.Time `json:"createdAt" description:"创建日期"`
	UpdatedAt *gtime.Time `json:"updatedAt" description:"更新日期"`
	Status    bool        `json:"status"    description:""`
	Fields    string      `json:"fields"    description:"字段信息"`
}
