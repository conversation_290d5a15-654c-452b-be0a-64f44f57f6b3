<template>
  <!-- 系统访问记录详情抽屉 -->  
  <div class="ucenter-ucenterLoginLog-detail">
    <el-drawer v-model="isShowDialog" size="80%" direction="ltr">
      <template #header>
        <h4>系统访问记录详情</h4>
      </template>
      <el-form ref="formRef" :model="formData" label-width="100px">          
        <el-row>        
          <el-col :span="12">          
            <el-form-item label="访问ID">{{ formData.id }}</el-form-item>          
          </el-col>        
          <el-col :span="12">          
            <el-form-item label="登录账号">{{ formData.loginName }}</el-form-item>          
          </el-col>        
          <el-col :span="12">          
            <el-form-item label="登录IP地址">{{ formData.ipaddr }}</el-form-item>          
          </el-col>        
          <el-col :span="12">          
            <el-form-item label="登录地点">{{ formData.loginLocation }}</el-form-item>          
          </el-col>        
          <el-col :span="12">          
            <el-form-item label="浏览器类型">{{ formData.browser }}</el-form-item>          
          </el-col>        
          <el-col :span="12">          
            <el-form-item label="操作系统">{{ formData.os }}</el-form-item>          
          </el-col>        
          <el-col :span="12">          
            <el-form-item label="登录状态（0成功 1失败）">{{ formData.status }}</el-form-item>          
          </el-col>        
          <el-col :span="12">          
            <el-form-item label="提示消息">{{ formData.msg }}</el-form-item>          
          </el-col>        
          <el-col :span="12">
            <el-form-item label="登录时间">{{ proxy.parseTime(formData.loginTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-form-item>
          </el-col>      
        </el-row>      
      </el-form>
    </el-drawer>
  </div>
</template>
<script lang="ts">
  import { reactive, toRefs, defineComponent,ref,unref,getCurrentInstance,computed } from 'vue';
  import {ElMessageBox, ElMessage, FormInstance,UploadProps} from 'element-plus';  
  import {
    listUcenterLoginLog,
    getUcenterLoginLog,
    delUcenterLoginLog,
    addUcenterLoginLog,
    updateUcenterLoginLog,    
  } from "/@/api/ucenter/ucenterLoginLog";  
  import {
    UcenterLoginLogTableColumns,
    UcenterLoginLogInfoData,
    UcenterLoginLogTableDataState,
    UcenterLoginLogEditState,    
  } from "/@/views/ucenter/ucenterLoginLog/list/component/model"
  export default defineComponent({
    name:"apiV1UcenterUcenterLoginLogDetail",
    components:{      
    },
    props:{      
    },
    setup(props,{emit}) {      
      const {proxy} = <any>getCurrentInstance()
      const formRef = ref<HTMLElement | null>(null);
      const menuRef = ref();      
      const state = reactive<UcenterLoginLogEditState>({
        loading:false,
        isShowDialog: false,
        formData: {          
          id: undefined,          
          loginName: undefined,          
          ipaddr: undefined,          
          loginLocation: undefined,          
          browser: undefined,          
          os: undefined,          
          status: undefined,          
          msg: undefined,          
          loginTime: undefined,          
        },
        // 表单校验
        rules: {          
          id : [
              { required: true, message: "访问ID不能为空", trigger: "blur" }
          ],          
          loginName : [
              { required: true, message: "登录账号不能为空", trigger: "blur" }
          ],          
        }
      });
        // 打开弹窗
        const openDialog = (row?: UcenterLoginLogInfoData) => {
          resetForm();
          if(row) {
            getUcenterLoginLog(row.id!).then((res:any)=>{
              const data = res.data;              
              state.formData = data;              
            })
          }
          state.isShowDialog = true;
        };
        // 关闭弹窗
        const closeDialog = () => {
          state.isShowDialog = false;
        };
        // 取消
        const onCancel = () => {
          closeDialog();
        };
        const resetForm = ()=>{
          state.formData = {            
            id: undefined,            
            loginName: undefined,            
            ipaddr: undefined,            
            loginLocation: undefined,            
            browser: undefined,            
            os: undefined,            
            status: undefined,            
            msg: undefined,            
            loginTime: undefined,            
          }
        };        
        return {
          proxy,
          openDialog,
          closeDialog,
          onCancel,
          menuRef,
          formRef,          
          ...toRefs(state),
        };
      }
  })
</script>
<style scoped>  
  .ucenter-ucenterLoginLog-detail :deep(.el-form-item--large .el-form-item__label){
    font-weight: bolder;
  }
  .pic-block{
    margin-right: 8px;
  }
  .file-block{
    width: 100%;
    border: 1px solid var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
    margin-bottom: 5px;
    padding: 3px 6px;
  }
  .ml-2{margin-right: 5px;}
</style>