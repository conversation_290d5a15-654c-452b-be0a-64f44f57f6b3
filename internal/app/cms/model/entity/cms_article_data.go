// =================================================================================
// Code generated by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import "github.com/gogf/gf/v2/os/gtime"

// CmsArticleData is the golang structure for table cms_article_data.
type CmsArticleData struct {
	Aid       uint64      `json:"aid"     description:"ID"`
	Content   string      `json:"content" description:"内容"`
	DeletedAt *gtime.Time `orm:"deleted_at" json:"deletedAt" description:"删除日期"` // 删除日期
}
