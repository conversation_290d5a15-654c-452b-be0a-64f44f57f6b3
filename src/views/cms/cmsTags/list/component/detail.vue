<template>
  <!-- CMS标签详情抽屉 -->  
  <div class="cms-cmsTags-detail">
    <el-drawer v-model="isShowDialog" size="80%" direction="ltr">
      <template #header>
        <h4>CMS标签详情</h4>
      </template>
      <el-form ref="formRef" :model="formData" label-width="100px">          
        <el-row>        
          <el-col :span="12">          
            <el-form-item label="ID">{{ formData.id }}</el-form-item>          
          </el-col>        
          <el-col :span="12">          
            <el-form-item label="标签名称">{{ formData.name }}</el-form-item>          
          </el-col>      
        </el-row>      
      </el-form>
    </el-drawer>
  </div>
</template>
<script lang="ts">
  import { reactive, toRefs, defineComponent,ref,unref,getCurrentInstance,computed } from 'vue';
  import {ElMessageBox, ElMessage, FormInstance,UploadProps} from 'element-plus';  
  import {
    listCmsTags,
    getCmsTags,
    delCmsTags,
    addCmsTags,
    updateCmsTags,    
  } from "/@/api/cms/cmsTags";  
  import {
    CmsTagsTableColumns,
    CmsTagsInfoData,
    CmsTagsTableDataState,
    CmsTagsEditState
  } from "/@/views/cms/cmsTags/list/component/model"
  export default defineComponent({
    name:"apiV1CmsCmsTagsDetail",
    components:{      
    },
    props:{      
    },
    setup(props,{emit}) {      
      const {proxy} = <any>getCurrentInstance()
      const formRef = ref<HTMLElement | null>(null);
      const menuRef = ref();      
      const state = reactive<CmsTagsEditState>({
        loading:false,
        isShowDialog: false,
        formData: {          
          id: undefined,          
          name: undefined,          
          createdAt: undefined,          
          updatedAt: undefined,          
          sort: undefined,          
        },
        // 表单校验
        rules: {          
          name : [
              { required: true, message: "标签名称不能为空", trigger: "blur" }
          ],          
        }
      });
        // 打开弹窗
        const openDialog = (row?: CmsTagsInfoData) => {
          resetForm();
          if(row) {
            getCmsTags(row.id!).then((res:any)=>{
              const data = res.data;              
              state.formData = data;
            })
          }
          state.isShowDialog = true;
        };
        // 关闭弹窗
        const closeDialog = () => {
          state.isShowDialog = false;
        };
        // 取消
        const onCancel = () => {
          closeDialog();
        };
        const resetForm = ()=>{
          state.formData = {            
            id: undefined,            
            name: undefined,            
            createdAt: undefined,            
            updatedAt: undefined,            
            sort: undefined,            
          }
        };        
        return {
          proxy,
          openDialog,
          closeDialog,
          onCancel,
          menuRef,
          formRef,          
          ...toRefs(state),
        };
      }
  })
</script>
<style scoped>  
  .cms-cmsTags-detail :deep(.el-form-item--large .el-form-item__label){
    font-weight: bolder;
  }
  .pic-block{
    margin-right: 8px;
  }
  .file-block{
    width: 100%;
    border: 1px solid var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
    margin-bottom: 5px;
    padding: 3px 6px;
  }
  .ml-2{margin-right: 5px;}
</style>