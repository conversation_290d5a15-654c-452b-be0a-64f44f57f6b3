<template>
  <div class="cms-cmsCategory-container">
    <el-card shadow="hover">
      <div class="cms-cmsCategory-content">
        <!-- 左侧树形 -->
        <div class="left-tree">
          <div class="tree-header">
            <div class="search-box">
              <el-input
                  v-model="treeFilterText"
                  placeholder="输入关键字搜索"
                  clearable
                  class="tree-filter"
                  @keyup.enter="handleTreeSearch"
              >
                <template #append>
                  <el-button @click="handleTreeSearch">
                    <el-icon><ele-Search /></el-icon>
                  </el-button>
                </template>
              </el-input>
            </div>
            <div class="tree-buttons">
              <div class="left-buttons">
                <el-button link @click="refreshTree">
                  <el-icon><ele-Refresh /></el-icon>
                  刷新
                </el-button>
              </div>
              <div class="right-buttons">
                <el-button link @click="toggleExpandAll">
                  <el-icon><ele-ArrowDown v-if="!isExpandAll" /><ele-ArrowUp v-else /></el-icon>
                  {{ isExpandAll ? '收缩' : '展开' }}
                </el-button>
              </div>
            </div>
          </div>
          <el-tree
              ref="treeRef"
              :data="treeData"
              :props="{
              label: 'name',
              children: 'children'
            }"
              node-key="id"
              highlight-current
              :filter-node-method="filterNode"
              @node-click="handleNodeClick"
              :expand-on-click-node="false"
              @expand-change="handleExpandChange"
          />
        </div>

        <!-- 右侧表格 -->
        <div class="right-content">
          <div class="cms-cmsCategory-search mb15">
            <el-form ref="queryRef" :inline="true" :model="tableData.param" label-width="80px">
              <el-row>
                <el-form-item label="分类名称" prop="name">
                  <el-input v-model.trim="tableData.param.name" clearable placeholder="请输入名称" @keyup.enter.native="handleSearch"/>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="handleSearch">
                    <el-icon><ele-Search/></el-icon>
                    搜索
                  </el-button>
                  <el-button @click="resetQuery(queryRef)">
                    <el-icon><ele-Refresh/></el-icon>
                    重置
                  </el-button>
                </el-form-item>
              </el-row>
            </el-form>
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button v-auth="'api/v1/cms/cmsCategory/add'" type="primary" @click="handleAdd">
                  <el-icon><ele-Plus/></el-icon>
                  新增
                </el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button v-auth="'api/v1/cms/cmsCategory/delete'" :disabled="multiple" type="danger" @click="handleDelete(null)">
                  <el-icon><ele-Delete /></el-icon>
                  删除
                </el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button v-auth="'api/v1/cms/cmsCategory/savePermissions'" type="primary" @click="categoryPermissionDialogVisible = true">
                  <el-icon><ele-Key /></el-icon>
                  分类权限
                </el-button>
              </el-col>
            </el-row>

            <CategoryPermission v-model="categoryPermissionDialogVisible" />
          </div>

          <el-table
              border
              v-loading="loading"
              :data="tableList"
              row-key="id"
              @selection-change="handleSelectionChange"
              @row-click="handleRowClick">
            <el-table-column align="center" type="selection" width="55"/>
            <el-table-column label="分类名称" min-width="120px" prop="name">
              <template #default="{row}">
                <span><a :href="row.url" target="_blank">{{row.name}} <span v-if="row.alias"> - ({{row.alias}})</span></a></span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="分类类型" prop="type" width="100px" :formatter="typeFormat"/>
            <el-table-column label="ID" prop="id" width="80px"/>
            <el-table-column align="center" label="内容模型" prop="linkedModuleId.name" width="100px" />
            <el-table-column align="center" label="排序" prop="sort" width="80px"/>
            <el-table-column :formatter="statusFormat" align="center" label="状态" prop="status" width="80px"/>
            <el-table-column align="center" class-name="small-padding" fixed="right" label="操作" width="200px">
              <template #default="scope">
                <el-button v-auth="'api/v1/cms/cmsCategory/edit'" link type="primary" @click="handleUpdate(scope.row)">
                  修改
                </el-button>
                <el-button v-if="scope.row.type==='channel'" v-auth="'api/v1/cms/cmsCategory/edit'" link type="primary" @click="handleAdd($event,scope.row.id)">
                  增加子类
                </el-button>
                <el-button v-auth="'api/v1/cms/cmsCategory/delete'" link type="primary" @click="handleDelete(scope.row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-card>
    <apiV1CmsCmsCategoryEdit
        ref="editRef"
        :isInheritOptions="sys_yes_no"
        :moduleIdOptions="moduleIdOptions"
        :parentIdOptions="parentIdOptions"
        :statusOptions="sys_normal_disable"
        @cmsCategoryList="cmsCategoryList"
        @getCmsCategoryItemsParentId="getCmsCategoryItemsParentId"
        @getModulesInfoItemsModuleId="getModulesInfoItemsModuleId"
    ></apiV1CmsCmsCategoryEdit>
  </div>
</template>
<script lang="ts">
import {ItemOptions} from "/@/api/items";
import {computed, defineComponent, getCurrentInstance, onMounted, reactive, ref, toRaw, toRefs, shallowRef, watch} from "vue";
import {ElMessage, ElMessageBox, FormInstance} from "element-plus";
import {delCmsCategory, listCmsCategory, listModulesInfo} from "/@/api/cms/cmsCategory";
import {CmsCategoryInfoData, CmsCategoryTableColumns, CmsCategoryTableDataState, CmsCategoryTypeOptions} from "/@/views/cms/cmsCategory/list/component/model";

interface PermissionOption {
  value: string;
  label: string;
}

import apiV1CmsCmsCategoryEdit from "/@/views/cms/cmsCategory/list/component/edit.vue";
import Permission from './component/permission.vue';

interface TreeNode extends CmsCategoryTableColumns {
  children?: TreeNode[];
  expanded?: boolean;
}

export default defineComponent({
  name: "CmsCategory",
  components: {
    apiV1CmsCmsCategoryEdit,
    CategoryPermission: Permission
  },
  setup() {
    const {proxy} = <any>getCurrentInstance();
    const loading = ref(false);
    const queryRef = ref<FormInstance>();
    const editRef = ref();
    const treeFilterText = ref('');
    const treeRef = ref();

    // 监听搜索文本变化
    watch(treeFilterText, (val) => {
      treeRef.value?.filter(val);
    });

    // 树节点过滤方法
    const filterNode = (value: string, data: any) => {
      if (!value) return true;
      return data.name.toLowerCase().includes(value.toLowerCase());
    };

    // 是否显示所有搜索选项
    const showAll = ref(false);
    // 非单个禁用
    const single = ref(true);
    const parentId = ref(0);
    // 非多个禁用
    const multiple = ref(true);
    const word = computed(() => {
      if (showAll.value === false) {
        //对文字进行处理
        return "展开搜索";
      } else {
        return "收起搜索";
      }
    });
    // 字典选项数据
    const {
      sys_yes_no,
      sys_normal_disable
    } = proxy.useDict(
        "sys_yes_no",
        "sys_normal_disable"
    );
    // parentIdOptions关联表数据
    const parentIdOptions = ref<Array<ItemOptions>>([]);
    // moduleIdOptions关联表数据
    const moduleIdOptions = ref<Array<ItemOptions>>([]);
    const state = reactive<CmsCategoryTableDataState>({
      ids: [],
      tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
          pageNum: 1,
          pageSize: 50,
          name: undefined
        }
      }
    });
    // 添加树形数据相关的响应式变量
    const treeData = ref<TreeNode[]>([]);
    const currentNode = ref<TreeNode | null>(null);
    const tableList = ref<CmsCategoryTableColumns[]>([]);
    const expanded = ref(false);
    const isExpandAll = ref(false);

    // 控制栏目权限对话框的显示状态
    const categoryPermissionDialogVisible = ref(false);

    // 页面加载时
    onMounted(() => {
      initTableData();
    });
    // 初始化表格数据
    const initTableData = async () => {
      loading.value = true;
      try {
        const res = await listCmsCategory({ pageSize: 10000 });
        const list = res.data.list || [];
        // 处理树形数据
        treeData.value = processTreeData(list);
        // 初始化右表为空数据
        tableList.value = [];
        state.tableData.data = list;
        loading.value = false;
      } catch (error) {
        console.error(error);
        loading.value = false;
      }
    };
    const typeFormat = (row: CmsCategoryTableColumns) => {
      for (const i in CmsCategoryTypeOptions) {
        if(CmsCategoryTypeOptions[i].value===row.type){
          return CmsCategoryTypeOptions[i].label
        }
      }
    }
    /** 重置按钮操作 */
    const resetQuery = (formEl: FormInstance | undefined) => {
      if (!formEl) return;
      formEl.resetFields();
      // 如果没有选中节点，清空表格数据
      if (!currentNode.value) {
        tableList.value = [];
        return;
      }
      handleSearch();
    };
    // 获取列表数据
    const cmsCategoryList = async () => {
      loading.value = true;
      try {
        const res = await listCmsCategory(state.tableData.param);

        // 使用 requestAnimationFrame 分批处理数据
        requestAnimationFrame(() => {
          const processedData = processTreeData(res.data.list || []);
          // 使用 shallowRef 减少响应式开销
          state.tableData.data = shallowRef(processedData).value;
          state.tableData.total = res.data.total;
          loading.value = false;
        });
      } catch (error) {
        console.error(error);
        loading.value = false;
      }
    };
    const toggleSearch = () => {
      showAll.value = !showAll.value;
    };
    //关联cms_category表选项
    const getCmsCategoryItemsParentId = () => {
      if (parentIdOptions.value && parentIdOptions.value.length > 0) {
        return;
      }
      proxy.getItems(listCmsCategory, {pageSize: 10000}).then((res: any) => {
        parentIdOptions.value = proxy.setItems(res, "id", "name");
      });
    };
    //关联modules_info表选项
    const getModulesInfoItemsModuleId = () => {
      if (moduleIdOptions.value && moduleIdOptions.value.length > 0) {
        return;
      }
      proxy.getItems(listModulesInfo, {pageSize: 10000}).then((res: any) => {
        moduleIdOptions.value = proxy.setItems(res, "id", "name");
      });
    };
    // 继承模板字典翻译
    const isInheritFormat = (row: CmsCategoryTableColumns) => {
      return proxy.selectDictLabel(sys_yes_no.value, row.isInherit);
    };
    // 状态字典翻译
    const statusFormat = (row: CmsCategoryTableColumns) => {
      return proxy.selectDictLabel(sys_normal_disable.value, row.status);
    };
    // 多选框选中数据
    const handleSelectionChange = (selection: Array<CmsCategoryInfoData>) => {
      state.ids = selection.map(item => item.id);
      single.value = selection.length != 1;
      multiple.value = !selection.length;
    };
    const handleAdd = (evt: Event, id?: number) => {
      // 如果没有传入 id 且有选中的树节点，使用树节点的 id
      if (!id && currentNode.value) {
        id = currentNode.value.id;
      }
      editRef.value.openDialog(null, id);
    };
    const handleUpdate = (row: CmsCategoryTableColumns) => {
      console.log(row);
      if (!row) {
        row = state.tableData.data.find((item: CmsCategoryTableColumns) => {
          return item.id === state.ids[0];
        }) as CmsCategoryTableColumns;
      }
      editRef.value.openDialog(toRaw(row));
    };
    const handleDelete = (row: CmsCategoryTableColumns | null) => {
      let msg = "你确定要删除所选数据？";
      let id: number[] = [];
      if (row) {
        msg = `此操作将永久删除数据，是否继续?`;
        id = [row.id];
      } else {
        id = state.ids;
      }
      if (id.length === 0) {
        ElMessage.error("请选择要删除的数据。");
        return;
      }
      ElMessageBox.confirm(msg, "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      })
          .then(() => {
            delCmsCategory(id).then(() => {
              ElMessage.success("删除成功");
              cmsCategoryList();
            });
          })
          .catch(() => {
          });
    };
    // 添加懒加载节点方法
    const loadNode = async (row: any, treeNode: any, resolve: (data: any[]) => void) => {
      try {
        const res = await listCmsCategory({
          parentId: row.id,
          pageSize: 50
        });
        resolve(res.data.list || []);
      } catch (error) {
        console.error(error);
        resolve([]);
      }
    };
    // 获取节点及其子节点的数据
    const getNodeAndChildren = (node: any): any[] => {
      if (!node) return [];

      const result: any[] = [];

      // 递归处理节点
      const processNode = (currentNode: any) => {
        if (!currentNode) return;

        // 添加当前节点，保持children属性
        const nodeData = { ...currentNode };

        // 如果有子节点，递归处理子节点
        if (currentNode.children && currentNode.children.length > 0) {
          nodeData.children = currentNode.children.map((child: CmsCategoryTableColumns) => ({ ...child }));
        }

        result.push(nodeData);
      };

      processNode(node);
      return result;
    };
    // 修改 handleNodeClick 方法
    const handleNodeClick = (node: any) => {
      currentNode.value = node;
      // 显示当前节点及其所有子节点数据
      tableList.value = [node];

      // 重置搜索条件
      state.tableData.param.name = undefined;
      if (queryRef.value) {
        queryRef.value.resetFields();
      }
    };
    // 添加表格行点击事件
    const handleRowClick = (row: any) => {
      // 显示当前节点及其子节点
      // tableList.value = getNodeAndChildren(row);
    };
    // 修改 handleSearch 方法，直接使用 getNodeAndChildren
    const handleSearch = () => {
      // 如果没有选中节点，不执行搜索
      if (!currentNode.value) {
        tableList.value = [];
        return;
      }

      const searchText = state.tableData.param.name?.toLowerCase();
      return proxy.useThrottle(handleSearch, 300);
    };
    // 处理树节点展开/折叠状态
    const handleExpandChange = (data: TreeNode, node: { expanded: boolean }) => {
      data.expanded = node.expanded;
      // 更新展开状态
      expanded.value = treeData.value.some(node => node.expanded);
    };

    // 处理树形数据
    const processTreeData = (data: CmsCategoryTableColumns[]): TreeNode[] => {
      // 使用 Map 优化查找性能
      const map = new Map<number, TreeNode>();
      data.forEach(item => map.set(item.id, { ...item, children: [] }));

      const result: TreeNode[] = [];
      data.forEach(item => {
        const node = map.get(item.id);
        if (item.parentId && map.has(item.parentId)) {
          const parent = map.get(item.parentId);
          if (parent && node) {
            parent.children?.push(node);
          }
        } else if (node) {
          result.push(node);
        }
      });

      return result;
    };
    // 添加计算属性优化渲染
    const displayData = computed(() => {
      const searchText = state.tableData.param.name?.toLowerCase();
      if (!searchText) return state.tableData.data;

      const searchTree = (nodes: any[]): any[] => {
        return nodes.reduce((acc, node) => {
          const matched = node.name.toLowerCase().includes(searchText);
          const childMatches = node.children ? searchTree(node.children) : [];

          if (matched || childMatches.length) {
            acc.push({
              ...node,
              children: childMatches
            });
          }
          return acc;
        }, []);
      };

      return searchTree(state.tableData.data);
    });
    // 添加防抖处理
    const debouncedSearch = computed(() => {
      return proxy.useDebounce(() => {
        state.tableData.param.name = state.tableData.param.name?.trim();
      }, 300);
    });
    const refreshTree = async () => {
      try {
        const res = await listCmsCategory({ pageSize: 10000 });
        const list = res.data.list ?? [];
        treeData.value = processTreeData(list);
        // 如果当前有选中节点，需要重新选中
        if (currentNode.value) {
          const node = treeRef.value.getNode(currentNode.value.id);
          if (node) {
            treeRef.value.setCurrentKey(currentNode.value.id);
          } else {
            currentNode.value = null;
          }
        }
      } catch (error) {
        console.error(error);
      }
    };

    // 树节点搜索方法
    const handleTreeSearch = () => {
      treeRef.value?.filter(treeFilterText.value);
    };

    // 展开/收缩所有节点
    const toggleExpandAll = () => {
      if (!treeRef.value) return;

      isExpandAll.value = !isExpandAll.value;

      if (isExpandAll.value) {
        // 展开所有节点
        expandAllNodes(treeData.value);
      } else {
        // 收缩所有节点
        collapseAllNodes();
      }
    };

    // 展开所有节点的递归方法
    const expandAllNodes = (nodes: TreeNode[]) => {
      if (!nodes || !treeRef.value) return;

      nodes.forEach(node => {
        // 展开当前节点
        treeRef.value.store.nodesMap[node.id].expanded = true;

        // 如果有子节点，递归展开
        if (node.children && node.children.length > 0) {
          expandAllNodes(node.children);
        }
      });
    };

    // 收缩所有节点
    const collapseAllNodes = () => {
      if (!treeRef.value) return;

      // 遍历所有节点并收缩
      Object.keys(treeRef.value.store.nodesMap).forEach(key => {
        treeRef.value.store.nodesMap[key].expanded = false;
      });
    };

    return {
      proxy,
      editRef,
      showAll,
      loading,
      single,
      multiple,
      word,
      queryRef,
      parentId,
      treeFilterText,
      filterNode,
      resetQuery,
      typeFormat,
      cmsCategoryList,
      toggleSearch,
      //关联表数据选项
      parentIdOptions,
      //关联cms_category表选项获取数据方法
      getCmsCategoryItemsParentId,
      //关联表数据选项
      moduleIdOptions,
      //关联modules_info表选项获取数据方法
      getModulesInfoItemsModuleId,
      isInheritFormat,
      sys_yes_no,
      statusFormat,
      sys_normal_disable,
      handleSelectionChange,
      handleAdd,
      handleUpdate,
      handleDelete,
      ...toRefs(state),
      displayData,
      treeRef,
      treeData,
      tableList,
      handleNodeClick,
      handleSearch,
      handleRowClick,
      handleExpandChange,
      refreshTree,
      // 新增的变量和方法
      categoryPermissionDialogVisible,
      isExpandAll,
      toggleExpandAll,
      // 移除不需要的变量和方法
      // categoryPermissionsList,
      // categoryLoading,
      // permissionOptions,
      // showCategoryPermissionDialog,
      // saveCategoryPermission,
      handleTreeSearch,
    };
  }
});
</script>
<style lang="scss" scoped>
.colBlock {
  display: block;
}

.colNone {
  display: none;
}

.cms-cmsCategory-container {
  height: 100%;

  .cms-cmsCategory-content {
    display: flex;
    height: calc(100vh - 200px);

    .left-tree {
      width: 250px;
      padding: 10px;
      border-right: 1px solid #dcdfe6;
      overflow: auto;

      .tree-header {
        padding-bottom: 8px;
        margin-bottom: 8px;
        border-bottom: 1px solid #eee;

        .search-box {
          display: flex;
          align-items: center;
          margin-bottom: 8px;

          .tree-filter {
            flex: 1;
          }
        }

        .tree-buttons {
          display: flex;
          justify-content: space-between;
          width: 100%;

          .left-buttons, .right-buttons {
            display: flex;
          }

          .left-buttons {
            justify-content: flex-start;
          }

          .right-buttons {
            justify-content: flex-end;
          }

          .el-button {
            padding: 4px 0;

            .el-icon {
              margin-right: 4px;
            }
          }
        }
      }
    }

    .right-content {
      flex: 1;
      padding: 10px;
      overflow: auto;
    }
  }
}

.cms-cmsCategory-container a {
  text-decoration: none; /* 去除下划线 */
  color: inherit; /* 保持链接颜色不变 */
}

.cms-cmsCategory-container a:visited {
  color: inherit; /* 访问后的链接颜色不变 */
}
</style>
