import request from '/@/utils/request'
// 查询用户积分列表
export function listUcenterScores(query:object) {
  return request({
    url: '/api/v1/ucenter/admin/ucenterScores/list',
    method: 'get',
    params: query
  })
}
// 查询用户积分详细
export function getUcenterScores(id:number) {
  return request({
    url: '/api/v1/ucenter/admin/ucenterScores/get',
    method: 'get',
    params: {
      id: id.toString()
    }
  })
}
// 新增用户积分
export function addUcenterScores(data:object) {
  return request({
    url: '/api/v1/ucenter/admin/ucenterScores/add',
    method: 'post',
    data: data
  })
}
// 修改用户积分
export function updateUcenterScores(data:object) {
  return request({
    url: '/api/v1/ucenter/admin/ucenterScores/edit',
    method: 'put',
    data: data
  })
}
// 删除用户积分
export function delUcenterScores(ids:number[]) {
  return request({
    url: '/api/v1/ucenter/admin/ucenterScores/delete',
    method: 'delete',
    data:{
      ids:ids
    }
  })
}
