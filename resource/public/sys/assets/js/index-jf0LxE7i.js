import{P as u,S as d}from"./splitpanes-BaxfhuKo.js";import{d as f,X as _,ad as c,ag as i,c as z,o as C,P as o,H as e,L as r}from"./@vue-C21YZbHS.js";import{a as x}from"./index-BGmsw1a8.js";import"./pinia-DwJf82dV.js";import"./vue-router-BDT9dCQ7.js";import"./js-cookie-Cz0CWeBA.js";import"./@element-plus-_Cc-TEQX.js";import"./nprogress-Scw6VIZr.js";import"./@intlify-D8kAWvSi.js";import"./axios-B62VicFi.js";import"./qs-D41HS0uK.js";import"./side-channel-DytTmCZg.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-DbsE5JW_.js";import"./crypto-js-oM26lpET.js";import"./side-channel-list-C_7Uk4RP.js";import"./side-channel-map-BUokNT8A.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-bSkmVQJL.js";import"./element-plus-CUmVNDWO.js";import"./lodash-es-S0Y0Up6J.js";import"./@vueuse-D_5IXmcI.js";import"./@popperjs-D_chPuIy.js";import"./@ctrl-r5W6hzzQ.js";import"./dayjs-CepoAVPu.js";import"./async-validator-9PlIezaS.js";import"./memoize-one-BdPwpGay.js";import"./normalize-wheel-es-BQoi3Ox2.js";import"./@floating-ui-DwceP2Gb.js";import"./vue-i18n-BkLk9Ti1.js";import"./vue-iNFchrNl.js";import"./vue-clipboard3-Dr8V5xR7.js";import"./clipboard-DzJOpNE6.js";import"./mitt-DJ65BbbF.js";import"./vue-grid-layout-CIPjP0XN.js";import"./vue-simple-uploader-j6sX1TKo.js";import"./vue-ueditor-wrap-CJ5gjsd3.js";const F=f({name:"funSplitpanes",components:{Splitpanes:d,Pane:u},setup(){const s=_({paneSize:50});return{...c(s)}}}),S={class:"splitpanes-container"};function v(s,t,P,$,b,g){const a=i("el-alert"),p=i("pane"),m=i("splitpanes"),n=i("el-card");return C(),z("div",S,[o(n,{shadow:"hover",header:"splitpanes 窗格拆分器"},{default:e(()=>[o(a,{title:"感谢优秀的 `splitpanes`，项目地址：https://github.com/antoniandre/splitpanes",type:"success",closable:!1,class:"mb15"}),o(m,{class:"default-theme",onResize:t[0]||(t[0]=l=>s.paneSize=l[0].size),style:{height:"500px"}},{default:e(()=>[o(p,{size:32},{default:e(()=>t[1]||(t[1]=[r(" 1 ")])),_:1}),o(p,{size:36},{default:e(()=>[o(m,{class:"default-theme",horizontal:!0},{default:e(()=>[o(p,{size:100},{default:e(()=>t[2]||(t[2]=[r(" 2 ")])),_:1}),o(p,{size:100},{default:e(()=>t[3]||(t[3]=[r(" 3 ")])),_:1})]),_:1})]),_:1}),o(p,{size:32},{default:e(()=>t[4]||(t[4]=[r(" 4 ")])),_:1})]),_:1})]),_:1})])}const St=x(F,[["render",v],["__scopeId","data-v-592261de"]]);export{St as default};
