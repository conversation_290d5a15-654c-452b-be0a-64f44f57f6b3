// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2023-01-31 09:44:00
// 生成路径: internal/app/cms/dao/internal/cms_article.go
// 生成人：yxf
// desc:文章
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// CmsArticleDao is the manager for logic model data accessing and custom defined data operations functions management.
type CmsArticleDao struct {
	table   string            // Table is the underlying table name of the DAO.
	group   string            // Group is the database configuration group name of current DAO.
	columns CmsArticleColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// CmsArticleColumns defines and stores column names for table cms_article.
type CmsArticleColumns struct {
	Id              string // ID
	Title           string // 标题
	SubTitle        string // 副标题
	SeoTitle        string // SEO标题
	CategoryId      string // 分类
	ModuleId        string // 模型
	Keywords        string // 关键字
	Description     string // 描述
	Hits            string // 点击数
	Flag            string // 推荐属性
	CommentCount    string // 评论数
	FavoriteCount   string // 收藏数
	LikeCount       string // 点赞数
	Writer          string // 作者
	Thumb           string // 缩略图
	Template        string // 模板
	Status          string // 状态
	UserId          string // 用户ID
	ReadPermissions string // 阅读权限
	OriginUrl       string // 来源地址
	OriginTitle     string // 来源名称
	Summary         string // 来源名称
	CreatedAt       string // 创建日期
	UpdatedAt       string // 更新日期
	DeletedAt       string // 删除日期
	PublishedAt     string // 发布日期
	IsJump          string
	AllowComment    string // 允许评论
	JumpUrl         string
}

var cmsArticleColumns = CmsArticleColumns{
	Id:              "id",
	Title:           "title",
	SeoTitle:        "seo_title",
	SubTitle:        "sub_title",
	CategoryId:      "category_id",
	ModuleId:        "module_id",
	Keywords:        "keywords",
	Description:     "description",
	Hits:            "hits",
	Flag:            "flag",
	CommentCount:    "comment_count",
	FavoriteCount:   "favorite_count",
	LikeCount:       "like_count",
	Writer:          "writer",
	Thumb:           "thumb",
	Template:        "template",
	Status:          "status",
	UserId:          "user_id",
	ReadPermissions: "read_permissions",
	OriginUrl:       "origin_url",
	OriginTitle:     "origin_title",
	Summary:         "summary",
	CreatedAt:       "created_at",
	UpdatedAt:       "updated_at",
	DeletedAt:       "deleted_at",
	PublishedAt:     "published_at",
	IsJump:          "is_jump",
	JumpUrl:         "jump_url",
	AllowComment:    "allow_comment",
}

// NewCmsArticleDao creates and returns a new DAO object for table data access.
func NewCmsArticleDao() *CmsArticleDao {
	return &CmsArticleDao{
		group:   "default",
		table:   "cms_article",
		columns: cmsArticleColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *CmsArticleDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *CmsArticleDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *CmsArticleDao) Columns() CmsArticleColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *CmsArticleDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *CmsArticleDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *CmsArticleDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
