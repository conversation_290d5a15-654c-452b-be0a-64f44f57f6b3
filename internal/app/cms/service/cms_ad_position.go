// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2023-03-02 08:45:44
// 生成路径: internal/app/cms/service/cms_ad_position.go
// 生成人：yxf
// desc:广告位置
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"
	"github.com/tiger1103/gfast/v3/api/v1/cms"
	"github.com/tiger1103/gfast/v3/internal/app/cms/model"
)

type ICmsAdPosition interface {
	List(ctx context.Context, req *cms.CmsAdPositionSearchReq) (res *cms.CmsAdPositionSearchRes, err error)
	GetById(ctx context.Context, Id uint) (res *model.CmsAdPositionInfoRes, err error)
	Add(ctx context.Context, req *cms.CmsAdPositionAddReq) (err error)
	Edit(ctx context.Context, req *cms.CmsAdPositionEditReq) (err error)
	Delete(ctx context.Context, Id []uint) (err error)
}

var localCmsAdPosition ICmsAdPosition

func CmsAdPosition() ICmsAdPosition {
	if localCmsAdPosition == nil {
		panic("implement not found for interface ICmsAdPosition, forgot register?")
	}
	return localCmsAdPosition
}

func RegisterCmsAdPosition(i ICmsAdPosition) {
	localCmsAdPosition = i
}
