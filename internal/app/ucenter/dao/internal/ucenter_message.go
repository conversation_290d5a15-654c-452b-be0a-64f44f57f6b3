// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2023-03-09 16:51:57
// 生成路径: internal/app/ucenter/dao/internal/ucenter_message.go
// 生成人：yxf
// desc:用户消息
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// UCenterMessageDao is the manager for logic model data accessing and custom defined data operations functions management.
type UCenterMessageDao struct {
	table   string                // Table is the underlying table name of the DAO.
	group   string                // Group is the database configuration group name of current DAO.
	columns UCenterMessageColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// UCenterMessageColumns defines and stores column names for table ucenter_message.
type UCenterMessageColumns struct {
	Id        string // ID
	Sender    string // 发送人member_id
	Receiver  string // 收信人member_id
	Content   string // 内容
	CreatedAt string // 创建时间
	UpdatedAt string // 更新时间
	Status    string // 状态:{0:未读,1:已读,2:已回复}
	Pid       string // 父级消息ID
}

var ucenterMessageColumns = UCenterMessageColumns{
	Id:        "id",
	Sender:    "sender",
	Receiver:  "receiver",
	Content:   "content",
	CreatedAt: "created_at",
	UpdatedAt: "updated_at",
	Status:    "status",
	Pid:       "pid",
}

// NewUCenterMessageDao creates and returns a new DAO object for table data access.
func NewUCenterMessageDao() *UCenterMessageDao {
	return &UCenterMessageDao{
		group:   "default",
		table:   "ucenter_message",
		columns: ucenterMessageColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *UCenterMessageDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *UCenterMessageDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *UCenterMessageDao) Columns() UCenterMessageColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *UCenterMessageDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *UCenterMessageDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *UCenterMessageDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
