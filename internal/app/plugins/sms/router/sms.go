// ==========================================================================
// GFast自动生成router操作代码。
// 生成日期：2023-12-01 14:49:39
// 生成路径: internal/app/plugins/sms/router/plugin_sms_config.go
// 生成人：gfast
// desc:短信配置
// company:云南奇讯科技有限公司
// ==========================================================================

package router

import (
	"context"

	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/internal/app/plugins/sms/controller"
)

func (router *Router) BindSmsDemoController(ctx context.Context, group *ghttp.RouterGroup) {
	group.Group("/demo", func(group *ghttp.RouterGroup) {
		group.Bind(
			controller.Sms,
		)
	})
}
