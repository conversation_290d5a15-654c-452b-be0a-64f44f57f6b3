<template>
  <div class="audit-cmsAuditUser-container">
    <el-card shadow="hover">
      <div class="audit-cmsAuditUser-content">
        <!-- 左侧角色树 -->
        <LeftTree
          @node-click="handleNodeClick"
          @user-node-click="handleUserNodeClick"
        />

        <!-- 右侧内容 -->
        <RightList
          :selected-user-id="selectedUserId"
        />
      </div>
    </el-card>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import LeftTree from "./component/leftTree.vue";
import RightList from "./component/rightList.vue";

defineOptions({ name: "apiV1CmsAuditCmsAuditUserList"});

// 当前选中的用户 ID
const selectedUserId = ref<number | undefined>(undefined);

// 处理节点点击
const handleNodeClick = (node: any) => {
  // 角色节点不允许选取，只处理展开/收起
};

// 处理用户节点点击
const handleUserNodeClick = (userId: number) => {
  selectedUserId.value = userId;
};


</script>
<style lang="scss" scoped>
.audit-cmsAuditUser-container {
  height: 100%;

  .audit-cmsAuditUser-content {
    display: flex;
    height: calc(100vh - 200px);
  }
}
</style>