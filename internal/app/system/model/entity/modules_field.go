// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2023-02-01 15:22:21
// 生成路径: internal/app/system/model/entity/modules_field.go
// 生成人：yxf
// desc:模型字段
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/util/gmeta"
)

// ModulesField is the golang structure for table modules_field.
type ModulesField struct {
	gmeta.Meta  `orm:"table:modules_field"`
	Id          uint   `orm:"id,primary" json:"id"`            // ID
	Label       string `orm:"label" json:"label"`              // 字段名称
	Name        string `orm:"name" json:"name"`                // 调用名称
	Type        string `orm:"type" json:"type"`                // 类型
	Description string `orm:"description" json:"description"`  // 描述
	Default     string `orm:"default" json:"default"`          // 默认值
	Sort        int    `orm:"sort" json:"sort"`                // 排序
	Options     string `orm:"options" json:"options"`          // 选项数据
	ModuleId    uint   `orm:"module_id" json:"moduleId"`       // 模型ID
	FieldLength int    `orm:"field_length" json:"fieldLength"` // 字段长度
}
