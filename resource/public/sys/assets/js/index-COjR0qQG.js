import{d as Ce,f as xe,r as k,k as De,X as he,ad as Ve,h as Je,ag as n,aq as W,c as N,o as u,P as e,H as l,a as Se,I as m,u as s,a4 as ze,O as Y,a6 as Z,G as _,D as O,L as d,M as ee,Q as x,ab as te}from"./@vue-C21YZbHS.js";import{l as Ie,r as Be,c as Ne,s as Oe,e as Pe}from"./sysJob-XIubHjsf.js";import Re from"./edit-9oUXlSLM.js";import Ge from"./detail-DzpsR0aY.js";import{E as P,a as h}from"./element-plus-CUmVNDWO.js";import{a as Ue}from"./index-BGmsw1a8.js";import"./lodash-es-S0Y0Up6J.js";import"./@vueuse-D_5IXmcI.js";import"./@element-plus-_Cc-TEQX.js";import"./@popperjs-D_chPuIy.js";import"./@ctrl-r5W6hzzQ.js";import"./dayjs-CepoAVPu.js";import"./@intlify-D8kAWvSi.js";import"./async-validator-9PlIezaS.js";import"./memoize-one-BdPwpGay.js";import"./normalize-wheel-es-BQoi3Ox2.js";import"./@floating-ui-DwceP2Gb.js";import"./pinia-DwJf82dV.js";import"./vue-router-BDT9dCQ7.js";import"./js-cookie-Cz0CWeBA.js";import"./nprogress-Scw6VIZr.js";import"./axios-B62VicFi.js";import"./qs-D41HS0uK.js";import"./side-channel-DytTmCZg.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-DbsE5JW_.js";import"./crypto-js-oM26lpET.js";import"./side-channel-list-C_7Uk4RP.js";import"./side-channel-map-BUokNT8A.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-bSkmVQJL.js";import"./vue-i18n-BkLk9Ti1.js";import"./vue-iNFchrNl.js";import"./vue-clipboard3-Dr8V5xR7.js";import"./clipboard-DzJOpNE6.js";import"./mitt-DJ65BbbF.js";import"./vue-grid-layout-CIPjP0XN.js";import"./vue-simple-uploader-j6sX1TKo.js";import"./vue-ueditor-wrap-CJ5gjsd3.js";const Ee={class:"system-sysJob-container"},Te={class:"system-sysJob-search mb15"},$e=Ce({name:"apiV1SystemSysJobList",__name:"index",setup(Le){const{proxy:V}=xe(),J=k(!1),S=k(),z=k(),R=k(),c=k(!1),G=k(!0),U=k(!0),E=De(()=>c.value===!1?"展开搜索":"收起搜索"),{sys_job_group:D,sys_job_policy:I,sys_job_status:B}=V.useDict("sys_job_group","sys_job_policy","sys_job_status"),y=he({jobIds:[],tableData:{data:[],total:0,loading:!1,param:{pageNum:1,pageSize:10,jobName:void 0,jobGroup:void 0,status:void 0}}}),{tableData:r}=Ve(y);Je(()=>{le()});const le=()=>{f()},T=o=>{o&&(o.resetFields(),f())},f=()=>{J.value=!0,Ie(y.tableData.param).then(o=>{let t=o.data.list??[];y.tableData.data=t,y.tableData.total=o.data.total,J.value=!1})},$=()=>{c.value=!c.value},oe=o=>V.selectDictLabel(D.value,o.jobGroup),ae=o=>V.selectDictLabel(I.value,o.misfirePolicy),ne=o=>{y.jobIds=o.map(t=>t.jobId),G.value=o.length!=1,U.value=!o.length},se=()=>{z.value.openDialog()},L=o=>{o||(o=y.tableData.data.find(t=>t.jobId===y.jobIds[0])),z.value.openDialog(te(o))},ie=o=>{const t=o.jobId||0;P.confirm("是否确认立即执行一次该任务?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){return Be(t)}).then(()=>{h.success("执行成功")}).catch(function(){})},A=o=>{let t="你确定要删除所选数据？",j=[];if(o?(t="此操作将永久删除数据，是否继续?",j=[o.jobId]):j=y.jobIds,j.length===0){h.error("请选择要删除的数据。");return}P.confirm(t,"提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(()=>{Ne(j).then(()=>{h.success("删除成功"),f()})}).catch(()=>{})},re=o=>{R.value.openDialog(te(o))},pe=o=>{let t=o.status===0?"启用":"停用";P.confirm('确认要"'+t+'""'+o.jobName+'"任务吗?',"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){return o.status===0?Oe(o.jobId):Pe(o.jobId)}).then(()=>{h.success(t+"成功")}).catch(function(){o.status=o.status===0?1:0})};return(o,t)=>{const j=n("el-input"),C=n("el-form-item"),v=n("el-col"),F=n("el-option"),M=n("el-select"),q=n("ele-Search"),i=n("el-icon"),p=n("el-button"),K=n("ele-Refresh"),Q=n("ele-ArrowUp"),H=n("ele-ArrowDown"),X=n("el-row"),ue=n("el-form"),me=n("ele-Plus"),de=n("ele-Edit"),ce=n("ele-Delete"),b=n("el-table-column"),_e=n("el-switch"),fe=n("ele-View"),be=n("ele-EditPen"),ye=n("ele-CaretRight"),ve=n("ele-DeleteFilled"),ge=n("el-table"),ke=n("pagination"),je=n("el-card"),g=W("auth"),we=W("loading");return u(),N("div",Ee,[e(je,{shadow:"hover"},{default:l(()=>[Se("div",Te,[e(ue,{model:s(r).param,ref_key:"queryRef",ref:S,inline:!0,"label-width":"100px"},{default:l(()=>[e(X,null,{default:l(()=>[e(v,{span:8,class:"colBlock"},{default:l(()=>[e(C,{label:"任务名称",prop:"jobName"},{default:l(()=>[e(j,{modelValue:s(r).param.jobName,"onUpdate:modelValue":t[0]||(t[0]=a=>s(r).param.jobName=a),placeholder:"请输入任务名称",clearable:"",onKeyup:ze(f,["enter","native"])},null,8,["modelValue"])]),_:1})]),_:1}),e(v,{span:8,class:"colBlock"},{default:l(()=>[e(C,{label:"任务组名",prop:"jobGroup"},{default:l(()=>[e(M,{modelValue:s(r).param.jobGroup,"onUpdate:modelValue":t[1]||(t[1]=a=>s(r).param.jobGroup=a),placeholder:"请选择任务组名",clearable:"",style:{width:"160px"}},{default:l(()=>[(u(!0),N(Y,null,Z(s(D),a=>(u(),_(F,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(v,{span:8,class:O(c.value?"colNone":"colBlock")},{default:l(()=>[e(C,null,{default:l(()=>[e(p,{type:"primary",size:"small",onClick:f},{default:l(()=>[e(i,null,{default:l(()=>[e(q)]),_:1}),t[9]||(t[9]=d("搜索"))]),_:1}),e(p,{size:"small",onClick:t[2]||(t[2]=a=>T(S.value))},{default:l(()=>[e(i,null,{default:l(()=>[e(K)]),_:1}),t[10]||(t[10]=d("重置"))]),_:1}),e(p,{type:"primary",link:"",size:"small",onClick:$},{default:l(()=>[d(ee(E.value)+" ",1),m(e(i,null,{default:l(()=>[e(Q)]),_:1},512),[[x,c.value]]),m(e(i,null,{default:l(()=>[e(H)]),_:1},512),[[x,!c.value]])]),_:1})]),_:1})]),_:1},8,["class"]),e(v,{span:8,class:O(c.value?"colBlock":"colNone")},{default:l(()=>[e(C,{label:"状态",prop:"status"},{default:l(()=>[e(M,{modelValue:s(r).param.status,"onUpdate:modelValue":t[3]||(t[3]=a=>s(r).param.status=a),placeholder:"请选择状态",clearable:"",style:{width:"160px"}},{default:l(()=>[(u(!0),N(Y,null,Z(s(B),a=>(u(),_(F,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["class"]),e(v,{span:8,class:O(c.value?"colBlock":"colNone")},{default:l(()=>[e(C,null,{default:l(()=>[e(p,{type:"primary",size:"small",onClick:f},{default:l(()=>[e(i,null,{default:l(()=>[e(q)]),_:1}),t[11]||(t[11]=d("搜索"))]),_:1}),e(p,{size:"small",onClick:t[4]||(t[4]=a=>T(S.value))},{default:l(()=>[e(i,null,{default:l(()=>[e(K)]),_:1}),t[12]||(t[12]=d("重置"))]),_:1}),e(p,{type:"primary",link:"",size:"small",onClick:$},{default:l(()=>[d(ee(E.value)+" ",1),m(e(i,null,{default:l(()=>[e(Q)]),_:1},512),[[x,c.value]]),m(e(i,null,{default:l(()=>[e(H)]),_:1},512),[[x,!c.value]])]),_:1})]),_:1})]),_:1},8,["class"])]),_:1})]),_:1},8,["model"]),e(X,{gutter:10,class:"mb8"},{default:l(()=>[e(v,{span:1.5},{default:l(()=>[m((u(),_(p,{type:"primary",size:"small",onClick:se},{default:l(()=>[e(i,null,{default:l(()=>[e(me)]),_:1}),t[13]||(t[13]=d("新增"))]),_:1})),[[g,"api/v1/system/sysJob/add"]])]),_:1}),e(v,{span:1.5},{default:l(()=>[m((u(),_(p,{type:"success",size:"small",disabled:G.value,onClick:t[5]||(t[5]=a=>L(null))},{default:l(()=>[e(i,null,{default:l(()=>[e(de)]),_:1}),t[14]||(t[14]=d("修改"))]),_:1},8,["disabled"])),[[g,"api/v1/system/sysJob/edit"]])]),_:1}),e(v,{span:1.5},{default:l(()=>[m((u(),_(p,{type:"danger",size:"small",disabled:U.value,onClick:t[6]||(t[6]=a=>A(null))},{default:l(()=>[e(i,null,{default:l(()=>[e(ce)]),_:1}),t[15]||(t[15]=d("删除"))]),_:1},8,["disabled"])),[[g,"api/v1/system/sysJob/delete"]])]),_:1})]),_:1})]),m((u(),_(ge,{data:s(r).data,onSelectionChange:ne},{default:l(()=>[e(b,{type:"selection",width:"55",align:"center"}),e(b,{label:"任务ID",align:"center",prop:"jobId","min-width":"100px"}),e(b,{label:"任务名称",align:"center",prop:"jobName","min-width":"100px"}),e(b,{label:"任务组名",align:"center",prop:"jobGroup",formatter:oe,"min-width":"100px"}),e(b,{label:"任务方法",align:"center",prop:"invokeTarget","min-width":"100px"}),e(b,{label:"cron执行表达式",align:"center",prop:"cronExpression","min-width":"100px"}),e(b,{label:"计划执行策略",align:"center",prop:"misfirePolicy",formatter:ae,"min-width":"100px"}),e(b,{label:"状态",align:"center"},{default:l(a=>[e(_e,{modelValue:a.row.status,"onUpdate:modelValue":w=>a.row.status=w,"active-value":0,"inactive-value":1,onChange:w=>pe(a.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),e(b,{label:"操作",align:"center","class-name":"small-padding","min-width":"200px",fixed:"right"},{default:l(a=>[m((u(),_(p,{size:"small",type:"primary",link:"",onClick:w=>re(a.row)},{default:l(()=>[e(i,null,{default:l(()=>[e(fe)]),_:1}),t[16]||(t[16]=d("详情"))]),_:2},1032,["onClick"])),[[g,"api/v1/system/sysJob/view"]]),m((u(),_(p,{size:"small",type:"primary",link:"",onClick:w=>L(a.row)},{default:l(()=>[e(i,null,{default:l(()=>[e(be)]),_:1}),t[17]||(t[17]=d("修改"))]),_:2},1032,["onClick"])),[[g,"api/v1/system/sysJob/edit"]]),m((u(),_(p,{size:"small",type:"primary",link:"",onClick:w=>ie(a.row)},{default:l(()=>[e(i,null,{default:l(()=>[e(ye)]),_:1}),t[18]||(t[18]=d("执行一次"))]),_:2},1032,["onClick"])),[[g,"api/v1/system/sysJob/run"]]),m((u(),_(p,{size:"small",type:"primary",link:"",onClick:w=>A(a.row)},{default:l(()=>[e(i,null,{default:l(()=>[e(ve)]),_:1}),t[19]||(t[19]=d("删除"))]),_:2},1032,["onClick"])),[[g,"api/v1/system/sysJob/delete"]])]),_:1})]),_:1},8,["data"])),[[we,J.value]]),m(e(ke,{total:s(r).total,page:s(r).param.pageNum,"onUpdate:page":t[7]||(t[7]=a=>s(r).param.pageNum=a),limit:s(r).param.pageSize,"onUpdate:limit":t[8]||(t[8]=a=>s(r).param.pageSize=a),onPagination:f},null,8,["total","page","limit"]),[[x,s(r).total>0]])]),_:1}),e(Re,{ref_key:"editRef",ref:z,jobGroupOptions:s(D),misfirePolicyOptions:s(I),statusOptions:s(B),onSysJobList:f},null,8,["jobGroupOptions","misfirePolicyOptions","statusOptions"]),e(Ge,{ref_key:"detailRef",ref:R,jobGroupOptions:s(D),misfirePolicyOptions:s(I),statusOptions:s(B),onSysJobList:f},null,8,["jobGroupOptions","misfirePolicyOptions","statusOptions"])])}}}),Et=Ue($e,[["__scopeId","data-v-7347d9e0"]]);export{Et as default};
