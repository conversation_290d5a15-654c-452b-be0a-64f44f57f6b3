import{a as S}from"./cmsCategory-MP-V5q2B.js";import{m as R}from"./cmsArticle-DSySRWLJ.js";import{e as V}from"./eventBus-BLgm5ZO3.js";import{a as m}from"./element-plus-CUmVNDWO.js";import{d as F,f as A,r as v,X as B,ad as M,ag as p,c as I,o as h,P as l,H as i,a as u,D as $,L as y,J as k,M as N,V as T}from"./@vue-C21YZbHS.js";import{a as O}from"./index-BGmsw1a8.js";import"./mitt-DJ65BbbF.js";import"./lodash-es-S0Y0Up6J.js";import"./@vueuse-D_5IXmcI.js";import"./@element-plus-_Cc-TEQX.js";import"./@popperjs-D_chPuIy.js";import"./@ctrl-r5W6hzzQ.js";import"./dayjs-CepoAVPu.js";import"./@intlify-D8kAWvSi.js";import"./async-validator-9PlIezaS.js";import"./memoize-one-BdPwpGay.js";import"./normalize-wheel-es-BQoi3Ox2.js";import"./@floating-ui-DwceP2Gb.js";import"./pinia-DwJf82dV.js";import"./vue-router-BDT9dCQ7.js";import"./js-cookie-Cz0CWeBA.js";import"./nprogress-Scw6VIZr.js";import"./axios-B62VicFi.js";import"./qs-D41HS0uK.js";import"./side-channel-DytTmCZg.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-DbsE5JW_.js";import"./crypto-js-oM26lpET.js";import"./side-channel-list-C_7Uk4RP.js";import"./side-channel-map-BUokNT8A.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-bSkmVQJL.js";import"./vue-i18n-BkLk9Ti1.js";import"./vue-iNFchrNl.js";import"./vue-clipboard3-Dr8V5xR7.js";import"./clipboard-DzJOpNE6.js";import"./vue-grid-layout-CIPjP0XN.js";import"./vue-simple-uploader-j6sX1TKo.js";import"./vue-ueditor-wrap-CJ5gjsd3.js";const w=F({name:"MoveDialog",setup(){const{proxy:a}=A(),t=v(),c=v([]),f=v(null),o=B({loading:!1,isShowDialog:!1,formData:{ids:[],categoryId:void 0,sourceCategoryId:void 0},rules:{categoryId:[{required:!0,message:"请选择目标栏目",trigger:"change"}]}}),b=()=>{if(!o.formData.sourceCategoryId){c.value=[];return}S({categoryId:o.formData.sourceCategoryId,permission:"move"}).then(e=>{const s=e.data.list||[];s.forEach(_=>{_.id===o.formData.sourceCategoryId&&(_.disabled=!0)}),c.value=a.handleTree(s,"id","parentId")})},g=(e,s)=>{C(),o.formData.ids=e,s?(o.formData.sourceCategoryId=s,b()):m.warning("缺少源栏目 ID，无法获取目标栏目"),o.isShowDialog=!0},d=()=>{o.isShowDialog=!1},D=()=>{d()},C=()=>{o.formData={ids:[],categoryId:void 0,sourceCategoryId:void 0},f.value=null},n=e=>{var s;!e||e.type!=="list"||e.id!==o.formData.sourceCategoryId&&(f.value=e.id,o.formData.categoryId=e.id,(s=t.value)==null||s.setCurrentRow(e))};return{treeTableRef:t,cmsCategoryOptions:c,selectedCategoryId:f,setSelectedCategory:n,handleCategorySelect:e=>{e&&e.type==="list"&&n(e)},handleRadioClick:e=>{e&&e.type==="list"&&n(e)},openDialog:g,closeDialog:d,onCancel:D,onSubmit:()=>{if(o.loading=!0,!o.formData.categoryId){m.error("请选择目标栏目"),o.loading=!1;return}if(o.formData.categoryId===o.formData.sourceCategoryId){m.error("目标栏目不能与源栏目相同"),o.loading=!1;return}const e={ids:o.formData.ids,categoryId:o.formData.categoryId,sourceCategoryId:o.formData.sourceCategoryId};R(e).then(()=>{m.success("移动成功"),d(),V.emit("refreshArticleList")}).catch(()=>{m.error("移动失败")}).finally(()=>{o.loading=!1})},...M(o)}}}),L={class:"cms-article-move"},P={class:"move-category-container"},q={key:0,class:"disabled-text"},z=["onClick"],H={class:"dialog-footer"};function J(a,t,c,f,o,b){const g=p("el-table-column"),d=p("el-radio"),D=p("el-table"),C=p("el-button"),n=p("el-dialog");return h(),I("div",L,[l(n,{modelValue:a.isShowDialog,"onUpdate:modelValue":t[0]||(t[0]=r=>a.isShowDialog=r),"close-on-click-modal":!1,"destroy-on-close":!0,width:"600px"},{header:i(()=>t[1]||(t[1]=[u("div",null,"文章移动",-1)])),footer:i(()=>[u("div",H,[l(C,{type:"primary",loading:a.loading,onClick:a.onSubmit},{default:i(()=>t[4]||(t[4]=[y("确 定")])),_:1},8,["loading","onClick"]),l(C,{onClick:a.onCancel},{default:i(()=>t[5]||(t[5]=[y("取 消")])),_:1},8,["onClick"])])]),default:i(()=>[u("div",P,[t[3]||(t[3]=u("div",{class:"move-title"},"选择目标栏目",-1)),l(D,{ref:"treeTableRef",data:a.cmsCategoryOptions,"row-key":"id",border:"","default-expand-all":"","tree-props":{children:"children",hasChildren:"hasChildren"},"highlight-current-row":"",onRowClick:a.handleCategorySelect},{default:i(()=>[l(g,{prop:"name",label:"栏目名称"},{default:i(({row:r})=>[u("span",{class:$({"disabled-row":r.id===a.formData.sourceCategoryId})},[y(N(r.name)+" ",1),r.id===a.formData.sourceCategoryId?(h(),I("span",q,"(当前栏目)")):k("",!0)],2)]),_:1}),l(g,{label:"选择",width:"100",align:"center"},{default:i(({row:r})=>[r.type==="list"?(h(),I("div",{key:0,onClick:T(E=>a.handleRadioClick(r),["stop"])},[l(d,{"model-value":a.selectedCategoryId,label:r.id,class:"custom-radio",disabled:r.id===a.formData.sourceCategoryId},{default:i(()=>t[2]||(t[2]=[y(" ")])),_:2},1032,["model-value","label","disabled"])],8,z)):k("",!0)]),_:1})]),_:1},8,["data","onRowClick"])])]),_:1},8,["modelValue"])])}const Ho=O(w,[["render",J],["__scopeId","data-v-54408e60"]]);export{Ho as default};
