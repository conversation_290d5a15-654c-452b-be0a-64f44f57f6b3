// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2023-01-30 14:15:09
// 生成路径: api/v1/system/modules_info.go
// 生成人：yxf
// desc:模型信息相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package system

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/system/model"
)

// ModulesInfoSearchReq 分页请求参数
type ModulesInfoSearchReq struct {
	g.Meta `path:"/list" tags:"系统后台/模型信息" method:"get" summary:"模型信息列表"`
	Name   string `p:"name"`                           //模型名称
	Sort   string `p:"sort" v:"sort@integer#排序需为整数"`   //排序
	Status string `p:"status" v:"status@integer#需为整数"` // 状态
	commonApi.PageReq
	commonApi.Author
}

// ModulesInfoSearchRes 列表返回结果
type ModulesInfoSearchRes struct {
	g.Meta `mime:"application/json"`
	commonApi.ListRes
	List []*model.ModulesInfoListRes `json:"list"`
}

// ModulesInfoAddReq 添加操作请求参数
type ModulesInfoAddReq struct {
	g.Meta `path:"/add" tags:"系统后台/模型信息" method:"post" summary:"模型信息添加"`
	commonApi.Author
	Name      string `p:"name" v:"required#模型名称不能为空"`
	Sort      int    `p:"sort" `
	TableName string `p:"tableName" v:"required#数据表名不能为空"`
	Status    int    `p:"status" v:"required#不能为空"`
	Fields    string `p:"fields" `
}

// ModulesInfoAddRes 添加操作返回结果
type ModulesInfoAddRes struct {
	commonApi.EmptyRes
}

// ModulesInfoEditReq 修改操作请求参数
type ModulesInfoEditReq struct {
	g.Meta `path:"/edit" tags:"系统后台/模型信息" method:"put" summary:"模型信息修改"`
	commonApi.Author
	Id        uint   `p:"id" v:"required#主键ID不能为空"`
	Name      string `p:"name" v:"required#模型名称不能为空"`
	Sort      int    `p:"sort" `
	TableName string `p:"tableName" v:"required#数据表名不能为空"`
	Status    int    `p:"status" v:"required#不能为空"`
	Fields    string `p:"fields" `
}

// ModulesInfoEditRes 修改操作返回结果
type ModulesInfoEditRes struct {
	commonApi.EmptyRes
}

// ModulesInfoGetReq 获取一条数据请求
type ModulesInfoGetReq struct {
	g.Meta `path:"/get" tags:"系统后台/模型信息" method:"get" summary:"获取模型信息信息"`
	commonApi.Author
	Id uint `p:"id" v:"required#主键必须"` //通过主键获取
}

// ModulesInfoGetRes 获取一条数据结果
type ModulesInfoGetRes struct {
	g.Meta `mime:"application/json"`
	*model.ModulesInfoInfoRes
}

// ModulesInfoDeleteReq 删除数据请求
type ModulesInfoDeleteReq struct {
	g.Meta `path:"/delete" tags:"系统后台/模型信息" method:"delete" summary:"删除模型信息"`
	commonApi.Author
	Ids []uint `p:"ids" v:"required#主键必须"` //通过主键删除
}

// ModulesInfoDeleteRes 删除数据返回
type ModulesInfoDeleteRes struct {
	commonApi.EmptyRes
}

type ModuleInfoCreateReq struct {
	g.Meta `path:"/createModule" tags:"系统后台/模型信息" method:"put" summary:"生成模型"`
	Id     uint `p:"id" v:"required#模型ID不存在"`
}

type ModuleInfoCreateRes struct {
}
