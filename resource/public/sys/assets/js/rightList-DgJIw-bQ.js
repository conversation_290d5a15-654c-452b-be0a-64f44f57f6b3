import{l as X,e as te,d as ae,s as le}from"./cmsCategory-MP-V5q2B.js";import{g as oe}from"./index-BvZSjrda.js";import{a as P,E as ne}from"./element-plus-CUmVNDWO.js";import{d as ie,f as se,r as g,k as re,X as W,h as de,i as Y,ad as ue,ag as r,aq as Z,c as D,o as u,a as G,I as k,P as e,H as a,a4 as me,L as p,G as h,J as x,M as K,O as pe,a6 as ce}from"./@vue-C21YZbHS.js";import{a as fe}from"./index-BGmsw1a8.js";import"./lodash-es-S0Y0Up6J.js";import"./@vueuse-D_5IXmcI.js";import"./@element-plus-_Cc-TEQX.js";import"./@popperjs-D_chPuIy.js";import"./@ctrl-r5W6hzzQ.js";import"./dayjs-CepoAVPu.js";import"./@intlify-D8kAWvSi.js";import"./async-validator-9PlIezaS.js";import"./memoize-one-BdPwpGay.js";import"./normalize-wheel-es-BQoi3Ox2.js";import"./@floating-ui-DwceP2Gb.js";import"./pinia-DwJf82dV.js";import"./vue-router-BDT9dCQ7.js";import"./js-cookie-Cz0CWeBA.js";import"./nprogress-Scw6VIZr.js";import"./axios-B62VicFi.js";import"./qs-D41HS0uK.js";import"./side-channel-DytTmCZg.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-DbsE5JW_.js";import"./crypto-js-oM26lpET.js";import"./side-channel-list-C_7Uk4RP.js";import"./side-channel-map-BUokNT8A.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-bSkmVQJL.js";import"./vue-i18n-BkLk9Ti1.js";import"./vue-iNFchrNl.js";import"./vue-clipboard3-Dr8V5xR7.js";import"./clipboard-DzJOpNE6.js";import"./mitt-DJ65BbbF.js";import"./vue-grid-layout-CIPjP0XN.js";import"./vue-simple-uploader-j6sX1TKo.js";import"./vue-ueditor-wrap-CJ5gjsd3.js";const ye=ie({name:"CmsCategoryRightList",props:{selectedCategory:{type:Object,default:()=>null}},emits:["refresh-tree","open-edit","open-permission"],setup(t,{emit:l}){const{proxy:L}=se(),V=g(),E=g(!1),M=g(!0),R=g(!0),y=g([]),T=re(()=>(t==null?void 0:t.selectedCategory)&&t.selectedCategory.type!=="channel"),{cms_nav_position:f}=L.useDict("cms_nav_position"),i=W({tableData:{data:[],total:0,loading:!1,param:{name:void 0,parentId:void 0}}}),w=g(!1),C=W({roleId:void 0}),v=g([]),A=g([]),b=g([]),I=g(),$=o=>{const s=new Map;o.forEach(c=>s.set(c.id,{...c,children:[]}));const m=[];return o.forEach(c=>{const S=s.get(c.id);if(c.parentId&&s.has(c.parentId)){const J=s.get(c.parentId);J&&S&&J.children.push(S)}else S&&m.push(S)}),m},d=()=>{var s;E.value=!0;const o={...i.tableData.param,parentId:(s=t==null?void 0:t.selectedCategory)==null?void 0:s.id,childNode:1};X(o).then(m=>{if(m.code===0){let c=[];m.data&&Array.isArray(m.data)?c=m.data:m.data&&m.data.list&&Array.isArray(m.data.list)&&(c=m.data.list),i.tableData.data=$(c),i.tableData.total=c.length||0}else i.tableData.data=[],i.tableData.total=0}).finally(()=>{E.value=!1})};de(()=>{d()}),Y(()=>t==null?void 0:t.selectedCategory,o=>{d()},{immediate:!0});const F=()=>{i.tableData.param.name,d()},N=()=>{var o;(o=V.value)==null||o.resetFields(),i.tableData.param.name=void 0,d()},O=()=>{d()},U=()=>{var o;(o=V.value)==null||o.resetFields(),i.tableData.param.name=void 0,d()},q=o=>{y.value=o.map(s=>s.id),M.value=!o.length,R.value=o.length!==1},z=(o,s)=>{if(s){l("open-edit",{parentId:s});return}const m=t!=null&&t.selectedCategory&&t.selectedCategory.type==="channel"?t.selectedCategory.id:0;l("open-edit",{parentId:m})},H=o=>{if(!o){if(y.value.length!==1){P.warning("请选择一条要修改的数据");return}o=i.tableData.data.find(s=>s.id===y.value[0])}l("open-edit",{parentId:o.parentId,row:o})},Q=o=>{const s=o?[o.id]:y.value;if(!s||s.length===0){P.warning("请选择要删除的数据");return}ne.confirm("确认要删除选中的数据吗？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{te(s).then(()=>{P.success("删除成功"),d(),l("refresh-tree")})})},_=()=>{oe({}).then(o=>{v.value=o.data.list||[],v.value.length>0&&(C.roleId=v.value[0].id,n(C.roleId))})},j=()=>{X({}).then(o=>{A.value=o.data||[]})},n=o=>{ae({roleId:o}).then(s=>{b.value=s.data||[]})},B=()=>{l("open-permission")},ee=()=>{if(!C.roleId){P.warning("请选择角色");return}const o=I.value.getCheckedKeys(),s=I.value.getHalfCheckedKeys(),m=[...o,...s];le({roleId:C.roleId,categoryIds:m}).then(()=>{P.success("保存成功"),w.value=!1})};return Y(()=>C.roleId,o=>{o&&n(o)}),{queryRef:V,loading:E,multiple:M,single:R,addButtonDisabled:T,cms_nav_position:f,navPositionFormat:o=>L.selectDictLabel(f.value,o),...ue(i),ids:y,handleSearch:F,resetQuery:N,refreshList:O,handleRefresh:U,handleSelectionChange:q,handleAdd:z,handleUpdate:H,handleDelete:Q,categoryPermissionDialogVisible:w,categoryPermissionForm:C,roleOptions:v,categoryPermissionTreeData:A,checkedCategoryIds:b,permissionTreeRef:I,loadRoleList:_,getCategoryPermissionTree:j,handleOpenPermissionDialog:B,handleSavePermissions:ee}}}),ge={class:"right-content"},he={class:"cms-cmsCategory-search mb15"},Ce={key:0},_e={key:1},ve={key:0},be={key:1},ke={class:"dialog-footer"};function De(t,l,L,V,E,M){const R=r("el-input"),y=r("el-form-item"),T=r("ele-Search"),f=r("el-icon"),i=r("el-button"),w=r("ele-Refresh"),C=r("el-row"),v=r("el-form"),A=r("ele-Plus"),b=r("el-col"),I=r("ele-Delete"),$=r("ele-Key"),d=r("el-table-column"),F=r("el-tag"),N=r("ele-Edit"),O=r("ele-DeleteFilled"),U=r("el-table"),q=r("el-option"),z=r("el-select"),H=r("el-tree"),Q=r("el-dialog"),_=Z("auth"),j=Z("loading");return u(),D("div",ge,[G("div",he,[e(v,{ref:"queryRef",inline:!0,model:t.tableData.param,"label-width":"80px"},{default:a(()=>[e(C,null,{default:a(()=>[e(y,{label:"分类名称",prop:"name"},{default:a(()=>[e(R,{modelValue:t.tableData.param.name,"onUpdate:modelValue":l[0]||(l[0]=n=>t.tableData.param.name=n),modelModifiers:{trim:!0},clearable:"",placeholder:"请输入名称",onKeyup:me(t.handleSearch,["enter","native"])},null,8,["modelValue","onKeyup"])]),_:1}),e(y,null,{default:a(()=>[e(i,{type:"primary",onClick:t.handleSearch},{default:a(()=>[e(f,null,{default:a(()=>[e(T)]),_:1}),l[5]||(l[5]=p(" 搜索 "))]),_:1},8,["onClick"]),e(i,{onClick:t.resetQuery},{default:a(()=>[e(f,null,{default:a(()=>[e(w)]),_:1}),l[6]||(l[6]=p(" 重置 "))]),_:1},8,["onClick"])]),_:1})]),_:1})]),_:1},8,["model"]),e(C,{gutter:10,class:"mb8"},{default:a(()=>[e(b,{span:1.5},{default:a(()=>[k((u(),h(i,{type:"primary",onClick:t.handleAdd,disabled:t.addButtonDisabled},{default:a(()=>[e(f,null,{default:a(()=>[e(A)]),_:1}),l[7]||(l[7]=p(" 新增 "))]),_:1},8,["onClick","disabled"])),[[_,"api/v1/cms/cmsCategory/add"]])]),_:1}),e(b,{span:1.5},{default:a(()=>[k((u(),h(i,{disabled:t.multiple,type:"danger",onClick:l[1]||(l[1]=n=>t.handleDelete(null))},{default:a(()=>[e(f,null,{default:a(()=>[e(I)]),_:1}),l[8]||(l[8]=p(" 删除 "))]),_:1},8,["disabled"])),[[_,"api/v1/cms/cmsCategory/delete"]])]),_:1}),e(b,{span:1.5},{default:a(()=>[k((u(),h(i,{type:"primary",onClick:t.handleOpenPermissionDialog},{default:a(()=>[e(f,null,{default:a(()=>[e($)]),_:1}),l[9]||(l[9]=p(" 分类权限 "))]),_:1},8,["onClick"])),[[_,"api/v1/cms/cmsCategory/savePermissions"]])]),_:1}),e(b,{span:1.5},{default:a(()=>[e(i,{type:"warning",onClick:t.handleRefresh},{default:a(()=>[e(f,null,{default:a(()=>[e(w)]),_:1}),l[10]||(l[10]=p(" 刷新 "))]),_:1},8,["onClick"])]),_:1})]),_:1})]),k((u(),h(U,{data:t.tableData.data,onSelectionChange:t.handleSelectionChange,style:{width:"100%"},"row-key":"id","tree-props":{children:"children",hasChildren:"hasChildren"},"default-expand-all":""},{default:a(()=>[e(d,{type:"selection",width:"55",align:"center"}),e(d,{label:"ID",prop:"id",width:"80"}),e(d,{label:"分类名称",prop:"name","show-overflow-tooltip":!0,"tree-node":""},{default:a(n=>[G("span",null,[p(K(n.row.name)+" ",1),n.row.type==="channel"?(u(),h(F,{key:0,size:"small",type:"info"},{default:a(()=>l[11]||(l[11]=[p("频道")])),_:1})):n.row.type==="list"?(u(),h(F,{key:1,size:"small",type:"success"},{default:a(()=>l[12]||(l[12]=[p("列表")])),_:1})):x("",!0)])]),_:1}),e(d,{label:"模型",align:"center",width:"120"},{default:a(n=>[n.row.linkedModuleId&&n.row.linkedModuleId.name?(u(),D("span",Ce,K(n.row.linkedModuleId.name),1)):(u(),D("span",_e,"-"))]),_:1}),e(d,{label:"排序",prop:"sort",width:"80"}),e(d,{label:"导航",align:"center",width:"100"},{default:a(n=>[n.row.nav?(u(),D("span",ve,K(t.navPositionFormat(n.row.nav)),1)):(u(),D("span",be,"-"))]),_:1}),e(d,{label:"状态",align:"center",width:"100"},{default:a(n=>[e(F,{type:n.row.status==="1"?"success":"info"},{default:a(()=>[p(K(n.row.status==="1"?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),e(d,{label:"更新时间",align:"center",prop:"updatedAt",width:"180"}),e(d,{label:"操作",align:"center",width:"240","class-name":"small-padding fixed-width"},{default:a(n=>[k((u(),h(i,{type:"primary",link:"",onClick:B=>t.handleUpdate(n.row)},{default:a(()=>[e(f,null,{default:a(()=>[e(N)]),_:1}),l[13]||(l[13]=p("修改"))]),_:2},1032,["onClick"])),[[_,"api/v1/cms/cmsCategory/edit"]]),n.row.type==="channel"?k((u(),h(i,{key:0,link:"",type:"primary",onClick:B=>t.handleAdd(B,n.row.id)},{default:a(()=>l[14]||(l[14]=[p(" 增加子类 ")])),_:2},1032,["onClick"])),[[_,"api/v1/cms/cmsCategory/edit"]]):x("",!0),k((u(),h(i,{type:"primary",link:"",onClick:B=>t.handleDelete(n.row)},{default:a(()=>[e(f,null,{default:a(()=>[e(O)]),_:1}),l[15]||(l[15]=p("删除"))]),_:2},1032,["onClick"])),[[_,"api/v1/cms/cmsCategory/delete"]])]),_:1})]),_:1},8,["data","onSelectionChange"])),[[j,t.loading]]),e(Q,{modelValue:t.categoryPermissionDialogVisible,"onUpdate:modelValue":l[4]||(l[4]=n=>t.categoryPermissionDialogVisible=n),title:"分类权限",width:"600px"},{footer:a(()=>[G("div",ke,[e(i,{onClick:l[3]||(l[3]=n=>t.categoryPermissionDialogVisible=!1)},{default:a(()=>l[16]||(l[16]=[p("取消")])),_:1}),e(i,{type:"primary",onClick:t.handleSavePermissions},{default:a(()=>l[17]||(l[17]=[p("确定")])),_:1},8,["onClick"])])]),default:a(()=>[e(v,{model:t.categoryPermissionForm,"label-width":"100px"},{default:a(()=>[e(y,{label:"角色"},{default:a(()=>[e(z,{modelValue:t.categoryPermissionForm.roleId,"onUpdate:modelValue":l[2]||(l[2]=n=>t.categoryPermissionForm.roleId=n),placeholder:"请选择角色",style:{width:"100%"}},{default:a(()=>[(u(!0),D(pe,null,ce(t.roleOptions,n=>(u(),h(q,{key:n.id,label:n.roleName,value:n.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(y,{label:"分类权限"},{default:a(()=>[e(H,{ref:"permissionTreeRef",data:t.categoryPermissionTreeData,"show-checkbox":"","node-key":"id",props:{label:"name",children:"children"},"default-checked-keys":t.checkedCategoryIds},null,8,["data","default-checked-keys"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}const vt=fe(ye,[["render",De],["__scopeId","data-v-febe5644"]]);export{vt as default};
