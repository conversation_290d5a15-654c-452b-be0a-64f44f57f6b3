// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2023-03-09 16:49:02
// 生成路径: internal/app/ucenter/controller/ucenter_members.go
// 生成人：yxf
// desc:用户中心
// company:云南奇讯科技有限公司
// ==========================================================================

package admin

import (
	"context"
	apiAdmin "github.com/tiger1103/gfast/v3/api/v1/ucenter/admin"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
	ucenterConsts "github.com/tiger1103/gfast/v3/internal/app/ucenter/consts"
	"github.com/tiger1103/gfast/v3/internal/app/ucenter/service"
)

type ucenterMembersController struct {
	systemController.BaseController
}

var UCenterMembers = new(ucenterMembersController)

// List 列表
func (c *ucenterMembersController) List(ctx context.Context, req *apiAdmin.UCenterMembersSearchReq) (res *apiAdmin.UCenterMembersSearchRes, err error) {
	res, err = service.UCenterMembers().List(ctx, req)
	return
}

// Get 获取用户中心
func (c *ucenterMembersController) Get(ctx context.Context, req *apiAdmin.UCenterMembersGetReq) (res *apiAdmin.UCenterMembersGetRes, err error) {
	res = new(apiAdmin.UCenterMembersGetRes)
	res.UCenterMembersInfoRes, err = service.UCenterMembers().GetById(ctx, req.Id)
	return
}

// Add 添加用户中心
func (c *ucenterMembersController) Add(ctx context.Context, req *apiAdmin.UCenterMembersAddReq) (res *apiAdmin.UCenterMembersAddRes, err error) {
	err = service.UCenterMembers().Add(ctx, req)
	return
}

// Edit 修改用户中心
func (c *ucenterMembersController) Edit(ctx context.Context, req *apiAdmin.UCenterMembersEditReq) (res *apiAdmin.UCenterMembersEditRes, err error) {
	err = service.UCenterMembers().Edit(ctx, req)
	return
}

// ResetPwd 重置密码
func (c *ucenterMembersController) ResetPwd(ctx context.Context, req *apiAdmin.UCenterMembersResetPwdReq) (res *apiAdmin.UCenterMembersEditRes, err error) {
	err = service.UCenterMembers().ResetPwd(ctx, req)
	return
}

// Delete 删除用户中心
func (c *ucenterMembersController) Delete(ctx context.Context, req *apiAdmin.UCenterMembersDeleteReq) (res *apiAdmin.UCenterMembersDeleteRes, err error) {
	err = service.UCenterMembers().Delete(ctx, req.Ids)
	commonService.EventBus().Publish(ucenterConsts.UCENTER_DELETED, req.Ids)
	return
}
