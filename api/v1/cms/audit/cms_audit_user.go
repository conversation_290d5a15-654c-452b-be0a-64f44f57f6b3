// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-05-07 08:32:03
// 生成路径: api/v1/audit/cms_audit_user.go
// 生成人：张徽
// desc:管理员审核设置相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package audit

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/cms/audit/model"
)

// CmsAuditUserSearchReq 分页请求参数
type CmsAuditUserSearchReq struct {
	g.Meta `path:"/list" tags:"cms后台/审核管理/审核人员设置" method:"get" summary:"审核人员设置列表"`
	commonApi.Author
	model.CmsAuditUserSearchReq
}

// CmsAuditUserSearchRes 列表返回结果
type CmsAuditUserSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.CmsAuditUserSearchRes
}

// CmsAuditUserAddReq 添加操作请求参数
type CmsAuditUserAddReq struct {
	g.Meta `path:"/add" tags:"cms后台/审核管理/审核人员设置" method:"post" summary:"审核人员设置添加"`
	commonApi.Author
	*model.CmsAuditUserAddReq
}

// CmsAuditUserAddRes 添加操作返回结果
type CmsAuditUserAddRes struct {
	commonApi.EmptyRes
}

// CmsAuditUserEditReq 修改操作请求参数
type CmsAuditUserEditReq struct {
	g.Meta `path:"/edit" tags:"cms后台/审核管理/审核人员设置" method:"put" summary:"审核人员设置修改"`
	commonApi.Author
	*model.CmsAuditUserEditReq
}

// CmsAuditUserEditRes 修改操作返回结果
type CmsAuditUserEditRes struct {
	commonApi.EmptyRes
}

// CmsAuditUserGetReq 获取一条数据请求
type CmsAuditUserGetReq struct {
	g.Meta `path:"/get" tags:"cms后台/审核管理/审核人员设置" method:"get" summary:"获取审核人员设置信息"`
	commonApi.Author
	Id uint `p:"id" v:"required#主键必须"` //通过主键获取
}

// CmsAuditUserGetRes 获取一条数据结果
type CmsAuditUserGetRes struct {
	g.Meta `mime:"application/json"`
	*model.CmsAuditUserInfoRes
}

// CmsAuditUserDeleteReq 删除数据请求
type CmsAuditUserDeleteReq struct {
	g.Meta `path:"/delete" tags:"cms后台/审核管理/审核人员设置" method:"delete" summary:"删除审核人员设置"`
	commonApi.Author
	Ids []uint `p:"ids" v:"required#主键必须"` //通过主键删除
}

// CmsAuditUserDeleteRes 删除数据返回
type CmsAuditUserDeleteRes struct {
	commonApi.EmptyRes
}
