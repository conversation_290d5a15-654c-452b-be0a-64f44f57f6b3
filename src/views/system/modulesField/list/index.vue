<template>
  <div class="system-modulesField-container">
    <el-card shadow="hover">
      <div class="system-modulesField-search mb15">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-auth="'api/v1/system/modulesField/add'" type="primary" @click="handleAdd">
              <el-icon>
                <ele-Plus/>
              </el-icon>
              新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-auth="'api/v1/system/modulesField/delete'" :disabled="multiple" type="danger" @click="handleDelete(null)">
              <el-icon>
                <ele-Delete/>
              </el-icon>
              删除
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" @click="modulesFieldList">
              <el-icon>
                <ele-Refresh/>
              </el-icon>
              刷新
            </el-button>
          </el-col>
        </el-row>
      </div>
      <el-table row-key="id" border v-tableDragable="dragOptions" v-loading="loading" :data="tableData.data" @selection-change="handleSelectionChange">
        <el-table-column width="40" class-name="sortable-handle">
          <template #default>
            <el-icon><ele-Rank /></el-icon>
          </template>
        </el-table-column>
        <el-table-column type="selection" width="55"/>
        <el-table-column label="排序" prop="sort" width="55" />
        <el-table-column label="字段名称" width="120px" prop="label">
          <template #default="scope">
            <a :href="scope.row.fullUrl" target="_blank">{{ scope.row.label }}</a>
          </template>
        </el-table-column>
        <el-table-column label="调用名称" width="120px" prop="name"/>
        <el-table-column label="类型" width="100px" :formatter="handleModuleInfoFieldTypeFormatter" prop="type"></el-table-column>
        <el-table-column label="默认值" min-width="100px" prop="default"/>
        <el-table-column label="描述" min-width="100px" prop="description"/>
        <el-table-column align="center" class-name="small-padding" fixed="right" label="操作" width="160px">
          <template #default="scope">
            <el-button v-auth="'api/v1/system/modulesField/edit'" link type="primary" @click="handleUpdate(scope.row)">
              <el-icon>
                <ele-EditPen/>
              </el-icon>
              修改
            </el-button>
            <el-button v-auth="'api/v1/system/modulesField/delete'" link type="primary" @click="handleDelete(scope.row)">
              <el-icon>
                <ele-DeleteFilled/>
              </el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <apiV1SystemModulesFieldEdit
        ref="editRef"
        :module-id="moduleId"
        @modulesFieldList="modulesFieldList"
    ></apiV1SystemModulesFieldEdit>
  </div>
</template>
<script lang="ts">
import {computed, defineComponent, getCurrentInstance, onMounted, reactive, ref, toRaw, toRefs} from "vue";
import {ElMessage, ElMessageBox, FormInstance} from "element-plus";
import {delModulesField, listModulesField, updateModulesFieldSort} from "/@/api/system/modulesField";
import {
  ModulesFieldInfoData,
  ModulesFieldTableColumns,
  ModulesFieldTableDataState,
  modulesInfoFieldOptions
} from "/@/views/system/modulesField/list/component/model";
import apiV1SystemModulesFieldEdit from "/@/views/system/modulesField/list/component/edit.vue";
import {useRoute} from "vue-router";
// @ts-ignore
import {vDragable} from "element-plus-table-dragable"

export default defineComponent({
  name: "apiV1SystemModulesFieldList",
  components: {
    apiV1SystemModulesFieldEdit
  },
  // 表格拖动排序
  directives:{
    tableDragable: vDragable
  },
  setup() {
    const route = useRoute();
    const moduleId = route.query.id ? Number(route.query.id) : 0;
    const {proxy} = <any>getCurrentInstance();
    const loading = ref(false);
    const queryRef = ref();
    const editRef = ref();
    // 是否显示所有搜索选项
    const showAll = ref(false);
    // 非单个禁用
    const single = ref(true);
    // 非多个禁用
    const multiple = ref(true);
    const word = computed(() => {
      if (showAll.value === false) {
        //对文字进行处理
        return "展开搜索";
      } else {
        return "收起搜索";
      }
    });

    const dragOptions = [
      {
        selector: "tbody", // add drag support for row
        option: {
          handle: '.sortable-handle',
          animation: 150,
          onEnd: (evt:any) => {
            const sortData:object[] = []
            state.tableData.data.splice(evt.newIndex,0,state.tableData.data.splice(evt.oldIndex,1)[0])
            state.tableData.data.forEach((item:ModulesFieldInfoData,index:number)=>{
              item.sort = index
              sortData.push({id:item.id,sort:item.sort})
            })
            // 更新排序到服务器
            updateModulesFieldSort(sortData)
          },
        },
      },
    ];

    const state = reactive<ModulesFieldTableDataState>({
      ids: [],
      tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
          moduleId: moduleId,
          pageNum: 1,
          pageSize: 100
        }
      }
    });
    // 页面加载时
    onMounted(() => {
      initTableData();
    });
    // 初始化表格数据
    const initTableData = () => {
      modulesFieldList();
    };
    /** 重置按钮操作 */
    const resetQuery = (formEl: FormInstance | undefined) => {
      if (!formEl) return;
      formEl.resetFields();
      modulesFieldList();
    };
    // 获取列表数据
    const modulesFieldList = () => {
      loading.value = true;
      listModulesField(state.tableData.param).then((res: any) => {
        let list = res.data.list ?? [];
        state.tableData.data = list;
        state.tableData.total = res.data.total;
        loading.value = false;
      });
    };
    const toggleSearch = () => {
      showAll.value = !showAll.value;
    };
    // 多选框选中数据
    const handleSelectionChange = (selection: Array<ModulesFieldInfoData>) => {
      state.ids = selection.map(item => item.id);
      single.value = selection.length != 1;
      multiple.value = !selection.length;
    };
    const handleAdd = () => {
      editRef.value.openDialog();
    };
    const handleUpdate = (row: ModulesFieldTableColumns) => {
      if (!row) {
        row = state.tableData.data.find((item: ModulesFieldTableColumns) => {
          return item.id === state.ids[0];
        }) as ModulesFieldTableColumns;
      }
      editRef.value.openDialog(toRaw(row));
    };
    const handleDelete = (row: ModulesFieldTableColumns) => {
      let msg = "你确定要删除所选数据？";
      let id: number[] = [];
      if (row) {
        msg = `此操作将永久删除数据，是否继续?`;
        id = [row.id];
      } else {
        id = state.ids;
      }
      if (id.length === 0) {
        ElMessage.error("请选择要删除的数据。");
        return;
      }
      ElMessageBox.confirm(msg, "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      })
          .then(() => {
            delModulesField(id).then(() => {
              ElMessage.success("删除成功");
              modulesFieldList();
            });
          })
          .catch(() => {
          });
    };
    // 格式化输出字段类型
    const handleModuleInfoFieldTypeFormatter = (row:ModulesFieldInfoData)=>{
      for (const item of modulesInfoFieldOptions) {
        if(row.type === item.value) {
          return item.label
        }
      }
      return '未知'
    }
    return {
      proxy,
      editRef,
      showAll,
      loading,
      single,
      multiple,
      word,
      queryRef,
      moduleId,
      modulesFieldOptions: reactive(modulesInfoFieldOptions),
      resetQuery,
      modulesFieldList,
      toggleSearch,
      handleModuleInfoFieldTypeFormatter,
      handleSelectionChange,
      handleAdd,
      handleUpdate,
      handleDelete,
      vDragable,
      dragOptions,
      ...toRefs(state)
    };
  }
});
</script>
<style lang="scss" scoped>
.colBlock {
  display: block;
}

.colNone {
  display: none;
}
:deep(.sortable-handle) {
  cursor: move;
}
</style>
