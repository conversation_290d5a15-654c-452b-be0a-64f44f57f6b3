// =================================================================================
// Code generated by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// ModulesInfo is the golang structure of table modules_info for DAO operations like Where/Data.
type ModulesInfo struct {
	g.Meta    `orm:"table:modules_info, do:true"`
	Id        interface{} // ID
	Name      interface{} // 模型名称
	Sort      interface{} // 排序
	TableName interface{} // 数据表名
	CreatedAt *gtime.Time // 创建日期
	UpdatedAt *gtime.Time // 更新日期
	Status    interface{} // 状态
	Fields    interface{} // 字段信息
}
