<template>
  <div class="mail-mail-container">
    <el-card shadow="hover">
      <div class="mail-mail-search mb15">
        <el-form :model="tableData.param" ref="queryRef" :inline="true" label-width="100px">
          <el-row>
            <el-col :span="6">
              <el-form-item label="用户名称" prop="user">
                <el-input
                    v-model="tableData.param.user"
                    placeholder="请输入用户名称"
                    clearable
                    @keyup.enter.native="mailList"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6" >
              <el-form-item label="主机地址" prop="host">
                <el-input
                    v-model="tableData.param.host"
                    placeholder="请输入主机地址"
                    clearable
                    @keyup.enter.native="mailList"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="端口" prop="port">
                <el-input
                    v-model="tableData.param.port"
                    placeholder="请输入端口"
                    clearable
                    @keyup.enter.native="mailList"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6" >
              <el-form-item>
                <el-button type="primary" @click="mailList">
                  <el-icon>
                    <ele-Search/>
                  </el-icon>
                  搜索
                </el-button>
                <el-button @click="resetQuery(queryRef)">
                  <el-icon>
                    <ele-Refresh/>
                  </el-icon>
                  重置
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
                type="primary"
                @click="handleAdd"
                v-auth="'api/v1/mail/mail/add'"
            >
              <el-icon>
                <ele-Plus/>
              </el-icon>
              新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="success"
                :disabled="single"
                @click="handleUpdate(null)"
                v-auth="'api/v1/mail/mail/edit'"
            >
              <el-icon>
                <ele-Edit/>
              </el-icon>
              修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="danger"
                :disabled="multiple"
                @click="handleDelete(null)"
                v-auth="'api/v1/mail/mail/delete'"
            >
              <el-icon>
                <ele-Delete/>
              </el-icon>
              删除
            </el-button>
          </el-col>
        </el-row>
      </div>
      <el-table v-loading="loading" :data="tableData.data" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center"/>
        <el-table-column label="" align="center" prop="id" min-width="150px"/>
        <el-table-column label="邮箱类型" align="center" prop="mType" min-width="150px"/>
        <el-table-column label="用户名称" align="center" prop="user" min-width="150px"/>
        <el-table-column label="密码" align="center" prop="pass" min-width="150px"/>
        <el-table-column label="主机地址" align="center" prop="host" min-width="150px"/>
        <el-table-column label="端口" align="center" prop="port" min-width="150px"/>
        <el-table-column label="状态 " align="center" prop="status" min-width="150px">
          <template #default="scope">
            <el-tag v-if="scope.row.status==1" type="success">启用</el-tag>
            <el-tag v-else type="danger">禁用</el-tag>
          </template>
        </el-table-column>


        <el-table-column label="备注" align="center" prop="remark" min-width="150px"/>
        <el-table-column label="操作" align="center" class-name="small-padding" min-width="160px" fixed="right">
          <template #default="scope">
            <el-button
                type="primary"
                link
                @click="handleUpdate(scope.row)"
                v-auth="'api/v1/mail/mail/edit'"
            >
              <el-icon>
                <ele-EditPen/>
              </el-icon>
              修改
            </el-button>
            <el-button
                type="primary"
                link
                @click="handleDelete(scope.row)"
                v-auth="'api/v1/mail/mail/delete'"
            >
              <el-icon>
                <ele-DeleteFilled/>
              </el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
          v-show="tableData.total>0"
          :total="tableData.total"
          v-model:page="tableData.param.pageNum"
          v-model:limit="tableData.param.pageSize"
          @pagination="mailList"
      />
    </el-card>
    <apiV1MailPluginMailEdit
        ref="editRef"
        @mailList="mailList"
    ></apiV1MailPluginMailEdit>
    <apiV1MailPluginMailDetail
        ref="detailRef"
        @mailList="mailList"
    ></apiV1MailPluginMailDetail>
  </div>
</template>
<script setup lang="ts">
import {toRefs, reactive, onMounted, ref, defineComponent, computed, getCurrentInstance, toRaw} from 'vue';
import {ElMessageBox, ElMessage, FormInstance} from 'element-plus';
import {
  listPluginMail,
  getPluginMail,
  delPluginMail,
  addPluginMail,
  updatePluginMail,
} from "/@/api/plugins/mail/mail";
import {
  PluginMailTableColumns,
  PluginMailInfoData,
  PluginMailTableDataState,
} from "/@/views/plugins/mail/mail/list/component/model"
import apiV1MailPluginMailEdit from "/@/views/plugins/mail/mail/list/component/edit.vue"
import apiV1MailPluginMailDetail from "/@/views/plugins/mail/mail/list/component/detail.vue"
defineOptions({ name: "apiV1MailPluginMailList"})
const {proxy} = <any>getCurrentInstance()
const loading = ref(false)
const queryRef = ref()
const editRef = ref();
const detailRef = ref();
// 是否显示所有搜索选项
const showAll = ref(false)
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
const word = computed(() => {
  if (showAll.value === false) {
    //对文字进行处理
    return "展开搜索";
  } else {
    return "收起搜索";
  }
})
// 字典选项数据
const {} = proxy.useDict(
)
const state = reactive<PluginMailTableDataState>({
  ids: [],
  tableData: {
    data: [],
    total: 0,
    loading: false,
    param: {
      pageNum: 1,
      pageSize: 10,
      id: undefined,
      mType: undefined,
      user: undefined,
      pass: undefined,
      host: undefined,
      port: undefined,
      status: undefined,
      dateRange: []
    },
  },
});
const { tableData } = toRefs(state)
// 页面加载时
onMounted(() => {
  initTableData();
});
// 初始化表格数据
const initTableData = () => {
  mailList()
};
/** 重置按钮操作 */
const resetQuery = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  mailList()
};
// 获取列表数据
const mailList = () => {
  loading.value = true
  listPluginMail(state.tableData.param).then((res: any) => {
    let list = res.data.list ?? [];
    state.tableData.data = list;
    state.tableData.total = res.data.total;
    loading.value = false
  })
};
const toggleSearch = () => {
  showAll.value = !showAll.value;
}
// 多选框选中数据
const handleSelectionChange = (selection: Array<PluginMailInfoData>) => {
  state.ids = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}
const handleAdd = () => {
  editRef.value.openDialog()
}
const handleUpdate = (row: PluginMailTableColumns|null) => {
  if (!row) {
    row = state.tableData.data.find((item: PluginMailTableColumns) => {
      return item.id === state.ids[0]
    }) as PluginMailTableColumns
  }
  editRef.value.openDialog(toRaw(row));
};
const handleDelete = (row: PluginMailTableColumns|null) => {
  let msg = '你确定要删除所选数据？';
  let id: number[] = [];
  if (row) {
    msg = `此操作将永久删除数据，是否继续?`
    id = [row.id]
  } else {
    id = state.ids
  }
  if (id.length === 0) {
    ElMessage.error('请选择要删除的数据。');
    return
  }
  ElMessageBox.confirm(msg, '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  })
      .then(() => {
        delPluginMail(id).then(() => {
          ElMessage.success('删除成功');
          mailList();
        })
      })
      .catch(() => {
      });
}
const handleView = (row: PluginMailTableColumns) => {
  detailRef.value.openDialog(toRaw(row));
}
</script>
<style lang="scss" scoped>
.colBlock {
  display: block;
}

.colNone {
  display: none;
}

.ml-2 {
  margin: 3px;
}
</style>
