/*
* @desc:登录日志管理
* @company:云南奇讯科技有限公司
* @Author: yix<PERSON><PERSON>u
* @Date:   2022/4/24 22:14
 */

package admin

import (
	"context"
	"github.com/tiger1103/gfast/v3/api/v1/ucenter/admin"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
	"github.com/tiger1103/gfast/v3/internal/app/ucenter/service"
)

var UCenterLoginLog = ucenterLoginLogController{}

type ucenterLoginLogController struct {
	systemController.BaseController
}

func (c *ucenterLoginLogController) List(ctx context.Context, req *admin.UCenterLoginLogSearchReq) (res *admin.UCenterLoginLogSearchRes, err error) {
	res, err = service.UCenterLoginLog().List(ctx, req)
	return
}

func (c *ucenterLoginLogController) Delete(ctx context.Context, req *admin.UCenterLoginLogDeleteReq) (res *admin.UCenterLoginLogDeleteRes, err error) {
	err = service.UCenterLoginLog().Delete(ctx, req.Ids)
	return
}

func (c *ucenterLoginLogController) Clear(ctx context.Context, req *admin.UCenterLoginLogClearReq) (res *admin.UCenterLoginLogClearRes, err error) {
	err = service.UCenterLoginLog().Clear(ctx)
	return
}
