<div id="wrap">
    <div class="wrap container">
        <main class="main">
            <section class="slider-wrap clearfix">
                <div class="main-slider hotnews-slider swiper-container pull-left">
                    <!-- 获取幻灯片文章 -->
                    <ul class="swiper-wrapper">
                        ${range $key,$article := GetArticleList "flag:3 | pageSize:5"}
                        <li class="swiper-slide">
                            <a href="${$article.Url}" target="_blank">
                                <img alt="${$article.Title}" src="${$article.Thumb}">
                            </a>
                            <p class="slide-title"><a href="${$article.Url}" target="_blank">${$article.Title}</a></p>
                        </li>
                        ${end}
                    </ul>
                    <div class="swiper-pagination"></div>
                    <div class="swiper-button-prev"></div>
                    <div class="swiper-button-next"></div>
                </div>
                <ul class="feature-post pull-right">
                    ${range $ad := GetAdList "position:2"}
                    ${if eq $ad.Type "image"}
                    <li>
                        <a href="${$ad.Url}" rel="noopener" target="_blank">
                            ${$adImageList := maps $ad.Content}
                            ${range $img := $adImageList}
                            <img src="${CorrectionPath $img.url}" style="display: inline;">
                            ${end}
                        </a>
                        <span>${$ad.Name}</span>
                    </li>
                    ${else}
                    <a href="${$ad.Url}" rel="noopener" target="_blank">${$ad.Content}</a>
                    ${end}
                    ${end}
                </ul>
            </section>
            <section class="sec-panel main-list">
                <div class="sec-panel-head">
                    <ul class="list tabs j-newslist">
                        <li class="tab active">
                            <a data-id="0" href="javascript:">最新文章</a>
                            <i class="tab-underscore" style="transform: translateX(0px); width: 64px;"></i>
                        </li>
                        ${$tagList := GetArticleTags "pageSize:5"}
                        ${range $key,$tag := $tagList}
                        <li class="tab"><a data-id="${$tag.Id}" href="javascript:">${$tag.Name}</a></li>
                        ${end}
                    </ul>
                </div>
                <ul class="articles-list articles-list-default tab-wrap active">
                    ${range $key,$article := GetArticleList "orderBy:published_at desc | pageSize:10"}
                    <li class="item">
                        ${if ne $article.Thumb ""}
                        <div class="item-img">
                            <a class="item-img-inner" href="${$article.Url}" target="_blank" title="${$article.Title}">
                                <img class="attachment-default size-default  j-lazy" src="${$article.Thumb}" style="display: inline;">
                            </a>
                            <a class="item-category" href="${$.cmsConfig.site_url}/list/${$article.Category.Id}.html" target="_blank">${$article.Category.Name}</a>
                        </div>
                        ${end}
                        <div class="item-content">
                            <h2 class="item-title">
                                <a href="${$article.Url}" target="_blank">
                                    ${if eq $article.Flag 1}
                                    <span class="sticky-post">置顶</span>
                                    ${end}
                                    ${$article.Title}
                                </a>
                            </h2>
                            <div class="item-excerpt">
                                <p>${$article.Summary}</p>
                            </div>
                            <div class="item-meta">
                                <div class="item-meta-li author">
                                    <span>作者：<a href="${$.cmsConfig.site_url}/member/${$article.UserInfo.Id}">${$article.UserInfo.UserNickname}</a></span>
                                </div>
                                <span class="item-meta-li date">${DateTimeFormat $article.PublishedAt "Y年m月d"}</span>
                                <div class="item-meta-right">
                                    <span class="item-meta-li views" title="阅读数"><i class="iconfont icon-eye"></i> ${$article.Hits}</span>
                                    <a class="item-meta-li comments" href="${$article.Url}#comments" target="_blank" title="评论数">
                                        <i class="iconfont icon-comment"></i> ${$article.CommentCount}</a>
                                    <span class="item-meta-li stars" title="收藏数"><i class="iconfont icon-star"></i> ${$article.FavoriteCount}</span>
                                    <span class="item-meta-li likes" title="点赞数"><i class="iconfont icon-like"></i> ${$article.LikeCount}</span>
                                </div>
                            </div>
                        </div>
                    </li>
                    ${end}
                    <li class="load-more-wrap list-more-tag list-more-none">
                        <span>加载中....</span>
                    </li>
                    <li class="load-more-wrap">
                        <a class="btn load-more" href="javascript:;" onclick="handleMoreArticle(this,0)">点击查看更多</a>
                    </li>
                </ul>
                ${range $key,$tag := $tagList}
                <ul class="articles-list articles-list-default tab-wrap">
                    ${range $key,$article := GetArticleList (print "tagId:" $tag.Id " | orderBy:published_at desc | pageSize:10")}
                    <li class="item">
                        ${if ne $article.Thumb ""}
                        <div class="item-img">
                            <a class="item-img-inner" href="${$article.Url}" target="_blank" title="${$article.Title}">
                                <img class="attachment-default size-default  j-lazy" src="${$article.Thumb}" style="display: inline;">
                            </a>
                            <a class="item-category" href="${$.cmsConfig.site_url}/list/17.html" target="_blank">行业动态</a>
                        </div>
                        ${end}
                        <div class="item-content">
                            <h2 class="item-title">
                                <a href="${$article.Url}" target="_blank">
                                    ${if FindArticleAttr $article.Flag "置顶"}
                                    <span class="sticky-post">置顶</span>
                                    ${end}
                                    ${$article.Title}
                                </a>
                            </h2>
                            <div class="item-excerpt">
                                <p>${$article.Summary}</p>
                            </div>
                            <div class="item-meta">
                                <div class="item-meta-li author">
                                    <span>作者：<a href="${$.cmsConfig.site_url}/member/${$article.UserInfo.Id}">${$article.UserInfo.UserNickname}</a></span>
                                </div>
                                <span class="item-meta-li date">${DateTimeFormat $article.PublishedAt "Y年m月d"}</span>
                                <div class="item-meta-right">
                                    <span class="item-meta-li views" title="阅读数"><i class="iconfont icon-eye"></i> ${$article.Hits}</span>
                                    <a class="item-meta-li comments" href="${$article.Url}#comments" target="_blank" title="评论数">
                                        <i class="iconfont icon-comment"></i> ${$article.CommentCount}</a>
                                    <span class="item-meta-li stars" title="收藏数"><i class="iconfont icon-star"></i> ${$article.FavoriteCount}</span>
                                    <span class="item-meta-li likes" title="点赞数"><i class="iconfont icon-like"></i> ${$article.LikeCount}</span>
                                </div>
                            </div>
                        </div>
                    </li>
                    ${end}
                    <li class="load-more-wrap list-more-tag list-more-none">
                        <span>加载中....</span>
                    </li>
                    <li class="load-more-wrap">
                        <a class="btn load-more" href="javascript:;" onclick="handleMoreArticle(this,${$tag.Id})" >点击查看更多</a>
                    </li>
                </ul>
                ${end}
            </section>
        </main>
        <aside class="sidebar">
            ${range $ad := GetAdList "position:25"}
            <div class="widget widget_image_ad">
                <a href="${$ad.Url}" rel="noopener" target="_blank">
                    <img class="j-lazy" src="${$ad.Images}" style="display: inline;">
                </a>
            </div>
            ${end}
            <div class="widget widget_views">
                <h3 class="widget-title">热点资讯</h3>
                <ul>
                    ${range $key, $article := GetArticleList "orderBy:hits desc | pageSize:10"}
                    <li>
                        <a class="kx-title" href="${$article.Url}">${$article.Title}</a>
                    </li>
                    ${end}
                </ul>
            </div>
            <div class="widget">
                <h3 class="widget-title">推荐</h3>
                <ul>
                    ${range $key, $article := GetArticleList "flag:1 | pageSize:10"}
                    <li><a href="${$article.Url}" title="${$article.Title}">${$article.Title}</a></li>
                    ${end}
                </ul>
            </div>
        </aside>
    </div>
    <div class="container hidden-xs j-partner">
        <div class="sec-panel">
            <div class="sec-panel-head">
                <h3><span>相关站点</span></h3>
            </div>
            <div class="sec-panel-body">
                <!-- pageSize 14 -->
                <ul class="list list-partner">
                    ${range $ad := GetAdList "pageSize:14 | position:27"}
                    <li style="width:14.28%">
                        <img alt="" class="j-lazy" src="${$ad.Images}" style="display: block;">
                    </li>
                    ${end}
                </ul>
            </div>
        </div>
    </div>
    <script id="article-list-template" type="text/html">
        <% for(var i=0;i < list.length; i++) { %>
        <li class="item">
            <% if(list[i].thumb != "") { %>
            <div class="item-img">
                <a class="item-img-inner" href="<%=list[i].url%>" target="_blank" title="<%=list[i].title%>">
                    <img alt="<%=list[i].title%>" class="attachment-default size-default " src="<%=$imports.CorrectionPath(list[i].thumb)%>">
                </a>
            </div>
            <%}%>
            <div class="item-content">
                <h2 class="item-title">
                    <a href="<%=list[i].url%>" target="_blank">
                        <%=list[i].title%>
                    </a>
                </h2>
                <div class="item-excerpt">
                    <p><%=list[i].summary%></p>
                </div>
                <div class="item-meta">
                    <div class="item-meta-li author">
                        <span>作者：<a href="${$.cmsConfig.site_url}/member/<%=list[i].userInfo?list[i].userInfo.id:''%>"><%=list[i].userInfo?list[i].userInfo.userNickname:''%></span>
                    </div>
                    <span class="item-meta-li date"><%=list[i].publishedAt%></span>
                    <div class="item-meta-right">
                        <span class="item-meta-li views" title="阅读数"><i class="iconfont icon-eye"></i> <%=list[i].hits%></span>
                        <a class="item-meta-li comments" href="<%=list[i].url%>#comments" target="_blank" title="评论数">
                            <i class="iconfont icon-comment"></i> <%=list[i].comments%></a>
                        <span class="item-meta-li stars" title="收藏数"><i class="iconfont icon-star"></i> <%=list[i].favorites%></span>
                        <span class="item-meta-li likes" title="点赞数"><i class="iconfont icon-like"></i> <%=list[i].likes%></span>
                    </div>
                </div>
            </div>
        </li>
        <% } %>
    </script>
</div>
<script>
    var pageNum = {
        0:1,
        ${range $key,$tag := $tagList}
        ${$tag.Id}:1,
        ${end}
    }
    $(function (){
        // 轮播图片
        var mySwiper = new Swiper ('.swiper-container', {
            loop: true, // 循环模式选项
            pagination: {
                el: '.swiper-pagination',
            },
            autoplay:true,
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            }
        })
    })
    function handleMoreArticle(e,tagId){
        console.log('tagId===',tagId)
        pageNum[tagId]++
        let loadTag = $(e).parent().prev(".list-more-tag")
        loadTag.show()
        let query = "pageNum="+pageNum[tagId]+(tagId>0?'&tagId='+tagId:'')
        $.get(APIBASE+"home/list?"+query,function (res){
            loadTag.hide()
            if(res.code === 0 && res.data.articleList) {
                $(e).parent().before(template("article-list-template",{list:res.data.articleList}))
            }else{
                $(e).css("visibility","hidden")
            }
        })
    }
</script>