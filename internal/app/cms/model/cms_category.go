// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2023-01-31 09:33:48
// 生成路径: internal/app/cms/model/cms_category.go
// 生成人：yxf
// desc:文章分类
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// CmsCategoryInfoRes is the golang structure for table cms_category.
type CmsCategoryInfoRes struct {
	gmeta.Meta       `orm:"table:cms_category"`
	Id               uint                           `orm:"id,primary" json:"id"`           // ID
	Name             string                         `orm:"name" json:"name"`               // 名称
	Type             string                         `orm:"type" json:"type"`               // 类型
	SeoTitle         string                         `orm:"seo_title" json:"seoTitle"`      // SEO标题
	Keywords         string                         `orm:"keywords" json:"keywords"`       // SEO关键字
	Description      string                         `orm:"description" json:"description"` // SEO描述
	CreatedAt        *gtime.Time                    `orm:"created_at" json:"createdAt"`    // 创建日期
	UpdatedAt        *gtime.Time                    `orm:"updated_at" json:"updatedAt"`    // 更新日期
	DeletedAt        *gtime.Time                    `orm:"deleted_at" json:"deletedAt"`    // 删除日期
	Content          string                         `orm:"content" json:"content"`         // 内容
	ParentId         uint                           `orm:"parent_id" json:"parentId"`      // 父ID
	LinkedParentId   *LinkedCmsCategoryCmsCategory  `orm:"with:id=parent_id" json:"linkedParentId"`
	ModuleId         uint                           `orm:"module_id" json:"moduleId"` // 模型ID
	LinkedModuleId   *LinkedCmsCategoryModulesInfo  `orm:"with:id=module_id" json:"linkedModuleId"`
	Sort             int                            `orm:"sort" json:"sort"`                        // 排序
	Template         string                         `orm:"template" json:"template"`                // 模板
	ContentTemplate  string                         `orm:"content_template" json:"contentTemplate"` // 内容模板
	IsInherit        string                         `orm:"is_inherit" json:"isInherit"`             // 继承模板
	Thumb            string                         `orm:"thumb" json:"thumb"`                      // 缩略图
	Banner           string                         `orm:"banner" json:"banner"`                    // 顶部图片
	Status           string                         `orm:"status" json:"status"`                    // 状态
	PageTemplate     string                         `orm:"page_template" json:"pageTemplate"`       // 单页模板
	ChannelTemplate  string                         `orm:"channel_template" json:"channelTemplate"` // 单页模板
	JumpUrl          string                         `orm:"jump_url" json:"jumpUrl"`                 // 单页模板
	Alias            string                         `orm:"alias" json:"alias"`                      // 别名
	Url              string                         `orm:"url" json:"url"`                          // url
	LinkedPermission []*LinkedCmsCategoryPermission `orm:"with:category_id=id" json:"linkedPermissions"`
	Nav              string                         `orm:"nav" json:"nav"` // 导航
}

type LinkedCmsCategoryCmsCategory struct {
	gmeta.Meta      `orm:"table:cms_category"`
	Id              uint   `orm:"id" json:"id"`                            // ID
	Name            string `orm:"name" json:"name"`                        // 名称
	Template        string `orm:"template" json:"template"`                // 模板
	ContentTemplate string `orm:"content_template" json:"contentTemplate"` // 内容模板
}
type LinkedCmsCategoryModulesInfo struct {
	gmeta.Meta      `orm:"table:modules_info"`
	Id              uint   `orm:"id" json:"id"`                            // ID
	Name            string `orm:"name" json:"name"`                        // 模型名称
	Template        string `orm:"template" json:"template"`                // 模板
	ContentTemplate string `orm:"content_template" json:"contentTemplate"` // 内容模板
}

type CmsCategoryListRes struct {
	Id             uint                          `json:"id"`
	Name           string                        `json:"name"`
	ParentId       uint                          `json:"parentId"`
	Type           string                        `json:"type"`
	ModuleId       uint                          `json:"moduleId"`
	LinkedModuleId *LinkedCmsCategoryModulesInfo `orm:"with:id=module_id" json:"linkedModuleId"`
	Sort           int                           `json:"sort"`
	Thumb          string                        `json:"thumb"`
	Status         string                        `json:"status"`
	JumpUrl        string                        `json:"jumpUrl"`
	Description    string                        `json:"description"`
	Alias          string                        `json:"alias"`
	Url            string                        `json:"url"` // url
}

type LinkedCmsCategoryPermission struct {
	gmeta.Meta  `orm:"table:cms_category_permissions"`
	RoleId      uint   `orm:"rolw_id" json:"roleId"`          // ID
	CategoryId  uint   `orm:"category_id" json:"categoryId"`  // 分类ID
	Permissions string `orm:"permissions" json:"permissions"` // 权限
}
