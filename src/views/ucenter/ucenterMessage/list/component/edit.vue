<template>  
  <div class="ucenter-ucenterMessage-edit">
    <!-- 添加或修改用户消息对话框 -->
    <el-dialog v-model="isShowDialog" width="769px" :close-on-click-modal="false" :destroy-on-close="true">
      <template #header>
        <div v-drag="['.ucenter-ucenterMessage-edit .el-dialog', '.ucenter-ucenterMessage-edit .el-dialog__header']">{{(!formData.id || formData.id==0?'添加':'修改')+'用户消息'}}</div>
      </template>
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="90px">        
        <el-form-item label="发送人member_id" prop="sender">
          <el-input v-model="formData.sender" placeholder="请输入发送人member_id" />
        </el-form-item>        
        <el-form-item label="收信人member_id" prop="receiver">
          <el-input v-model="formData.receiver" placeholder="请输入收信人member_id" />
        </el-form-item>        
        <el-form-item label="内容" prop="content">
          <el-input v-model="formData.content" placeholder="请输入内容" />
        </el-form-item>          
        <el-form-item label="状态:{0:未读,1:已读,2:已回复}" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio>请选择字典生成</el-radio>
          </el-radio-group>
        </el-form-item>        
        <el-form-item label="父级消息ID" prop="pid">
          <el-input v-model="formData.pid" placeholder="请输入父级消息ID" />
        </el-form-item>       
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="onSubmit">确 定</el-button>
          <el-button @click="onCancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts">
import { reactive, toRefs, defineComponent,ref,unref,getCurrentInstance } from 'vue';
import {ElMessageBox, ElMessage, FormInstance,UploadProps} from 'element-plus';
import {
  listUcenterMessage,
  getUcenterMessage,
  delUcenterMessage,
  addUcenterMessage,
  updateUcenterMessage,  
} from "/@/api/ucenter/ucenterMessage";
import {
  UcenterMessageTableColumns,
  UcenterMessageInfoData,
  UcenterMessageTableDataState,
  UcenterMessageEditState,  
} from "/@/views/ucenter/ucenterMessage/list/component/model"
export default defineComponent({
  name:"apiV1UcenterUcenterMessageEdit",
  components:{    
  },
  props:{    
  },
  setup(props,{emit}) {    
    const {proxy} = <any>getCurrentInstance()
    const formRef = ref<HTMLElement | null>(null);
    const menuRef = ref();    
    const state = reactive<UcenterMessageEditState>({
      loading:false,
      isShowDialog: false,
      formData: {        
        id: undefined,        
        sender: undefined,        
        receiver: undefined,        
        content: undefined,        
        createdAt: undefined,        
        updatedAt: undefined,        
        status: false ,        
        pid: undefined,        
      },
      // 表单校验
      rules: {        
        id : [
            { required: true, message: "ID不能为空", trigger: "blur" }
        ],        
        status : [
            { required: true, message: "状态:{0:未读,1:已读,2:已回复}不能为空", trigger: "blur" }
        ],        
      }
    });
    // 打开弹窗
    const openDialog = (row?: UcenterMessageInfoData) => {
      resetForm();
      if(row) {        
        getUcenterMessage(row.id!).then((res:any)=>{
          const data = res.data;          
          state.formData = data;
      })
    }
      state.isShowDialog = true;
    };
    // 关闭弹窗
    const closeDialog = () => {
      state.isShowDialog = false;
    };
    // 取消
    const onCancel = () => {
      closeDialog();
    };
    // 提交
    const onSubmit = () => {
      const formWrap = unref(formRef) as any;
      if (!formWrap) return;
      formWrap.validate((valid: boolean) => {
        if (valid) {
          state.loading = true;
          if(!state.formData.id || state.formData.id===0){
            //添加
          addUcenterMessage(state.formData).then(()=>{
              ElMessage.success('添加成功');
              closeDialog(); // 关闭弹窗
              emit('ucenterMessageList')
            }).finally(()=>{
              state.loading = false;
            })
          }else{
            //修改
          updateUcenterMessage(state.formData).then(()=>{
              ElMessage.success('修改成功');
              closeDialog(); // 关闭弹窗
              emit('ucenterMessageList')
            }).finally(()=>{
              state.loading = false;
            })
          }
        }
      });
    };
    const resetForm = ()=>{
      state.formData = {        
        id: undefined,        
        sender: undefined,        
        receiver: undefined,        
        content: undefined,        
        createdAt: undefined,        
        updatedAt: undefined,        
        status: false ,        
        pid: undefined,        
      }
    };    
    return {
      proxy,
      openDialog,
      closeDialog,
      onCancel,
      onSubmit,
      menuRef,
      formRef,      
      ...toRefs(state),
    };
  }
})
</script>
<style scoped>  
</style>