// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2023-03-10 16:19:10
// 生成路径: api/v1/ucenter/ucenter_login_log.go
// 生成人：yxf
// desc:系统访问记录相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ucenter/model"
)

// UCenterLoginLogSearchReq 分页请求参数
type UCenterLoginLogSearchReq struct {
	g.Meta    `path:"/list" tags:"ucenter后台/系统访问记录" method:"get" summary:"系统访问记录列表"`
	LoginName string `p:"loginName"` //登录账号
	Ipaddr    string `p:"ipaddr"`    //登录IP地址
	commonApi.PageReq
	commonApi.Author
}

// UCenterLoginLogSearchRes 列表返回结果
type UCenterLoginLogSearchRes struct {
	g.Meta `mime:"application/json"`
	commonApi.ListRes
	List []*model.UCenterLoginLogListRes `json:"list"`
}

// UCenterLoginLogAddRes 添加操作返回结果
type UCenterLoginLogAddRes struct {
	commonApi.EmptyRes
}

// UCenterLoginLogEditRes 修改操作返回结果
type UCenterLoginLogEditRes struct {
	commonApi.EmptyRes
}

// UCenterLoginLogGetReq 获取一条数据请求
type UCenterLoginLogGetReq struct {
	g.Meta `path:"/get" tags:"ucenter后台/系统访问记录" method:"get" summary:"获取系统访问记录信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// UCenterLoginLogGetRes 获取一条数据结果
type UCenterLoginLogGetRes struct {
	g.Meta `mime:"application/json"`
	*model.UCenterLoginLogInfoRes
}

// UCenterLoginLogDeleteReq 删除数据请求
type UCenterLoginLogDeleteReq struct {
	g.Meta `path:"/delete" tags:"ucenter后台/系统访问记录" method:"delete" summary:"删除系统访问记录"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// UCenterLoginLogDeleteRes 删除数据返回
type UCenterLoginLogDeleteRes struct {
	commonApi.EmptyRes
}

// UCenterLoginLogParams 登录日志写入参数
type UCenterLoginLogParams struct {
	Status    int
	Username  string
	Ip        string
	UserAgent string
	Msg       string
	Module    string
}

type UCenterLoginLogClearReq struct {
	g.Meta `path:"/clear" tags:"ucenter后台/系统访问记录" method:"delete" summary:"清除日志"`
}

type UCenterLoginLogClearRes struct {
}
