// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2023-03-09 16:48:33
// 生成路径: internal/app/ucenter/logic/ucenter_config.go
// 生成人：yxf
// desc:用户中心配置
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	apiAdmin "github.com/tiger1103/gfast/v3/api/v1/ucenter/admin"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/internal/app/ucenter/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ucenter/model"
	"github.com/tiger1103/gfast/v3/internal/app/ucenter/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ucenter/service"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterUCenterConfig(New())
}

func New() *sUCenterConfig {
	return &sUCenterConfig{}
}

type sUCenterConfig struct{}

func (s *sUCenterConfig) List(ctx context.Context, req *apiAdmin.UCenterConfigSearchReq) (listRes *apiAdmin.UCenterConfigSearchRes, err error) {
	listRes = new(apiAdmin.UCenterConfigSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.UCenterConfig.Ctx(ctx).WithAll()
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.UCenterConfigInfoRes
		err = m.Fields(apiAdmin.UCenterConfigSearchRes{}).Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.UCenterConfigListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.UCenterConfigListRes{
				Id:    v.Id,
				Name:  v.Name,
				Value: v.Value,
				Type:  v.Type,
				Tips:  v.Tips,
			}
		}
	})
	return
}

func (s *sUCenterConfig) GetById(ctx context.Context, id uint) (res *model.UCenterConfigInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.UCenterConfig.Ctx(ctx).WithAll().Where(dao.UCenterConfig.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sUCenterConfig) Add(ctx context.Context, req *apiAdmin.UCenterConfigAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.UCenterConfig.Ctx(ctx).Insert(do.UCenterConfig{
			Name:  req.Name,
			Value: req.Value,
			Type:  req.Type,
			Tips:  req.Tips,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sUCenterConfig) Edit(ctx context.Context, req *apiAdmin.UCenterConfigEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.UCenterConfig.Ctx(ctx).WherePri(req.Id).Update(do.UCenterConfig{
			Name:  req.Name,
			Value: req.Value,
			Type:  req.Type,
			Tips:  req.Tips,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sUCenterConfig) Delete(ctx context.Context, ids []uint) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.UCenterConfig.Ctx(ctx).Delete(dao.UCenterConfig.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
