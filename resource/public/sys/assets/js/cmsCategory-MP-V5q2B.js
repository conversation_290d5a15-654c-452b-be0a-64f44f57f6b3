import{s as t}from"./index-BGmsw1a8.js";function r(e){return t({url:"/api/v1/cms/cmsCategory/list",method:"get",params:e})}function a(e){return e==null?(console.error("getCmsCategory called with undefined or null id"),Promise.reject(new Error("id is required"))):t({url:"/api/v1/cms/cmsCategory/get",method:"get",params:{id:e.toString()}})}function o(e){return t({url:"/api/v1/cms/cmsCategory/add",method:"post",data:e})}function m(e){return t({url:"/api/v1/cms/cmsCategory/edit",method:"put",data:e})}function i(e){return t({url:"/api/v1/cms/cmsCategory/delete",method:"delete",data:{ids:e}})}function n(e){return t({url:"/api/v1/system/modulesInfo/list",method:"get",params:e})}function u(e){return t({url:"/api/v1/cms/cmsCategory/listPermissions",method:"get",params:e})}function c(e){return t({url:"api/v1/cms/cmsCategory/savePermissions",method:"put",data:e})}function g(e){return t({url:"api/v1/cms/cmsCategory/checkPermission",params:e,method:"get"})}function l(e){return t({url:"/api/v1/cms/cmsCategory/getPerCatetorys",method:"get",params:e})}export{l as a,o as b,g as c,u as d,i as e,n as f,a as g,r as l,c as s,m as u};
