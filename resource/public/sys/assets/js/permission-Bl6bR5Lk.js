import{s as R,l as U,d as j}from"./cmsCategory-MP-V5q2B.js";import{g as G}from"./index-BvZSjrda.js";import{a as f}from"./element-plus-CUmVNDWO.js";import{d as H,f as J,r as g,i as q,k as z,ag as d,c as x,o as v,P as p,H as r,a as k,J as O,M as V,G as $,L as y,O as K,a6 as Q}from"./@vue-C21YZbHS.js";import{a as W}from"./index-BGmsw1a8.js";import"./lodash-es-S0Y0Up6J.js";import"./@vueuse-D_5IXmcI.js";import"./@element-plus-_Cc-TEQX.js";import"./@popperjs-D_chPuIy.js";import"./@ctrl-r5W6hzzQ.js";import"./dayjs-CepoAVPu.js";import"./@intlify-D8kAWvSi.js";import"./async-validator-9PlIezaS.js";import"./memoize-one-BdPwpGay.js";import"./normalize-wheel-es-BQoi3Ox2.js";import"./@floating-ui-DwceP2Gb.js";import"./pinia-DwJf82dV.js";import"./vue-router-BDT9dCQ7.js";import"./js-cookie-Cz0CWeBA.js";import"./nprogress-Scw6VIZr.js";import"./axios-B62VicFi.js";import"./qs-D41HS0uK.js";import"./side-channel-DytTmCZg.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-DbsE5JW_.js";import"./crypto-js-oM26lpET.js";import"./side-channel-list-C_7Uk4RP.js";import"./side-channel-map-BUokNT8A.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-bSkmVQJL.js";import"./vue-i18n-BkLk9Ti1.js";import"./vue-iNFchrNl.js";import"./vue-clipboard3-Dr8V5xR7.js";import"./clipboard-DzJOpNE6.js";import"./mitt-DJ65BbbF.js";import"./vue-grid-layout-CIPjP0XN.js";import"./vue-simple-uploader-j6sX1TKo.js";import"./vue-ueditor-wrap-CJ5gjsd3.js";const X=H({name:"CategoryPermission",props:{modelValue:{type:Boolean,default:!1}},emits:["update:modelValue"],setup(o,{emit:n}){const{proxy:P}=J(),C=g(o.modelValue),u=g([]),B=g(!1),D=g([]),m=g({roleId:void 0}),A=()=>{C.value=!0,_()},_=async()=>{try{const s=(await G({})).data.list||[];D.value=P.handleTree(s.map(l=>({id:l.id,name:l.name,parentId:l.pid,status:l.status,remark:l.remark,userCnt:l.userCnt,dataScope:l.dataScope})),"id","parentId")}catch{f.error("获取角色列表失败")}},h=async()=>{try{const s=(await U({})).data.list||[];return P.handleTree(s.map(l=>({id:l.id,name:l.name,type:l.type,parentId:l.parentId,permissions:[]})),"id","parentId")}catch{return f.error("获取栏目列表失败"),[]}},I=async e=>{try{return(await j({roleId:e})).data.list||[]}catch{return f.error("获取权限数据失败"),[]}},T=(e,s)=>{const l=new Map;s.forEach(i=>{l.set(i.categoryId,i.permissions?i.permissions.split(",").filter(Boolean):[])});const a=i=>{i.forEach(b=>{b.permissions=l.get(b.id)||[],b.children&&b.children.length>0&&a(b.children)})};return a(e),e},F=async e=>{if(!e){u.value=[];return}try{B.value=!0;const[s,l]=await Promise.all([h(),I(e)]);u.value=T(s,l)}catch{f.error("获取数据失败")}finally{B.value=!1}},L=e=>{F(e)},t=g([{value:"list",label:"查看"},{value:"add",label:"新增"},{value:"edit",label:"修改"},{value:"delete",label:"删除"},{value:"push",label:"推送"},{value:"move",label:"移动"},{value:"publish",label:"发布"},{value:"recycle",label:"回收站"}]);q(()=>o.modelValue,e=>{C.value=e,e?_():(m.value.roleId=void 0,u.value=[])});const c=async()=>{if(!m.value.roleId){f.warning("请先选择角色");return}try{const e=l=>{let a=[];return l.forEach(i=>{a.push({categoryId:i.id,permissions:i.permissions.join(",")}),i.children&&i.children.length>0&&(a=a.concat(e(i.children)))}),a},s=e(u.value);await R({roleId:m.value.roleId,permissions:s}),f.success("权限保存成功"),E()}catch{f.error("保存权限失败，请重试")}},E=()=>{C.value=!1,n("update:modelValue",!1),m.value.roleId=void 0,u.value=[]},S=e=>e.type==="channel"?e.permissions.includes("list"):e.type==="list"?t.value.every(s=>e.permissions.includes(s.value)):!1,N=e=>{e.type==="channel"?e.permissions=S(e)?[]:["list"]:e.type==="list"&&(S(e)?e.permissions=[]:e.permissions=t.value.map(s=>s.value))},M=z(()=>u.value.length?u.value.every(e=>e.type==="channel"?e.permissions.includes("list"):e.type==="list"?t.value.every(s=>e.permissions.includes(s.value)):!0):!1);return{dialogVisible:C,categoryPermissionsList:u,permissionOptions:t,handleSave:c,handleCancel:E,roleOptions:D,formData:m,handleRoleChange:L,isAllSelected:S,openDialog:A,handleToggleAll:N,isAllCategoriesSelected:M,handleToggleAllCategories:()=>{const e=(s,l)=>{s.forEach(a=>{a.type==="channel"?a.permissions=l?["list"]:[]:a.type==="list"&&(a.permissions=l?t.value.map(i=>i.value):[]),a.children&&a.children.length>0&&e(a.children,l)})};e(u.value,!M.value)}}}}),Y={key:0,class:"text-gray-400 ml-2"},Z={class:"flex items-center justify-between"},w={class:"flex items-center"},ee={class:"dialog-footer"};function le(o,n,P,C,u,B){const D=d("el-tree-select"),m=d("el-form-item"),A=d("el-form"),_=d("el-table-column"),h=d("el-button"),I=d("el-checkbox"),T=d("el-checkbox-group"),F=d("el-table"),L=d("el-dialog");return v(),x("div",null,[p(L,{title:"分类权限",modelValue:o.dialogVisible,"onUpdate:modelValue":n[1]||(n[1]=t=>o.dialogVisible=t),width:"1069px"},{footer:r(()=>[k("span",ee,[p(h,{onClick:o.handleCancel},{default:r(()=>n[4]||(n[4]=[y("取消")])),_:1},8,["onClick"]),p(h,{type:"primary",onClick:o.handleSave},{default:r(()=>n[5]||(n[5]=[y("保存")])),_:1},8,["onClick"])])]),default:r(()=>[p(A,{model:o.formData,"label-width":"80px",class:"mb-15"},{default:r(()=>[p(m,{label:"选择角色"},{default:r(()=>[p(D,{modelValue:o.formData.roleId,"onUpdate:modelValue":n[0]||(n[0]=t=>o.formData.roleId=t),data:o.roleOptions,"node-key":"id","check-strictly":"",props:{label:"name",value:"id",children:"children"},"render-after-expand":!1,placeholder:"请选择角色",clearable:"",onChange:o.handleRoleChange},{default:r(({data:t})=>[k("span",null,V(t.name),1),t.remark?(v(),x("span",Y,"("+V(t.remark)+")",1)):O("",!0)]),_:1},8,["modelValue","data","onChange"])]),_:1})]),_:1},8,["model"]),p(F,{data:o.categoryPermissionsList,"row-key":"id",border:"","default-expand-all":"","tree-props":{children:"children",hasChildren:"hasChildren"}},{default:r(()=>[p(_,{prop:"name",label:"栏目名称","min-width":"180"}),p(_,{label:"权限设置","min-width":"400"},{header:r(()=>[k("div",Z,[n[2]||(n[2]=k("span",null,"权限设置",-1)),p(h,{link:"",type:"primary",onClick:o.handleToggleAllCategories,style:{"margin-right":"10px"}},{default:r(()=>[y(V(o.isAllCategoriesSelected?"反选":"全选"),1)]),_:1},8,["onClick"])])]),default:r(({row:t})=>[k("div",w,[t.type==="list"||t.type==="channel"?(v(),$(h,{key:0,link:"",type:"primary",onClick:c=>o.handleToggleAll(t),style:{"margin-right":"10px"}},{default:r(()=>[y(V(o.isAllSelected(t)?"反选":"全选"),1)]),_:2},1032,["onClick"])):O("",!0),p(T,{modelValue:t.permissions,"onUpdate:modelValue":c=>t.permissions=c},{default:r(()=>[t.type==="channel"?(v(),$(I,{key:0,value:"list"},{default:r(()=>n[3]||(n[3]=[y(" 查看 ")])),_:1})):t.type==="list"?(v(!0),x(K,{key:1},Q(o.permissionOptions,(c,E)=>(v(),$(I,{key:E,value:c.value,style:{"margin-right":"20px"}},{default:r(()=>[y(V(c.label),1)]),_:2},1032,["value"]))),128)):O("",!0)]),_:2},1032,["modelValue","onUpdate:modelValue"])])]),_:1})]),_:1},8,["data"])]),_:1},8,["modelValue"])])}const Ze=W(X,[["render",le],["__scopeId","data-v-3fdea81e"]]);export{Ze as default};
