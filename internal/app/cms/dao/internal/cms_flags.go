// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-04-24 10:40:09
// 生成路径: internal/app/cms/dao/internal/cms_flags.go
// 生成人：zhanghui
// desc:推荐属性
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// CmsFlagsDao is the manager for logic model data accessing and custom defined data operations functions management.
type CmsFlagsDao struct {
	table   string          // Table is the underlying table name of the DAO.
	group   string          // Group is the database configuration group name of current DAO.
	columns CmsFlagsColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// CmsFlagsColumns defines and stores column names for table cms_flags.
type CmsFlagsColumns struct {
	Id          string // ID
	Name        string // 属性名称
	Sort        string // 排序
	CreatedAt   string // 创建时间
	CategoryIds string // 栏目ids
	UpdatedAt   string // 更新时间
	DeletedAt   string // 删除时间
}

var cmsFlagsColumns = CmsFlagsColumns{
	Id:          "id",
	Name:        "name",
	Sort:        "sort",
	CreatedAt:   "created_at",
	CategoryIds: "category_ids",
	UpdatedAt:   "updated_at",
	DeletedAt:   "deleted_at",
}

// NewCmsFlagsDao creates and returns a new DAO object for table data access.
func NewCmsFlagsDao() *CmsFlagsDao {
	return &CmsFlagsDao{
		group:   "default",
		table:   "cms_flags",
		columns: cmsFlagsColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *CmsFlagsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *CmsFlagsDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *CmsFlagsDao) Columns() CmsFlagsColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *CmsFlagsDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *CmsFlagsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *CmsFlagsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
