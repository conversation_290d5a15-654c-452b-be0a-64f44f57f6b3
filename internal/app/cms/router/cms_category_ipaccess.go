// ==========================================================================
// GFast自动生成router操作代码。
// 生成日期：2025-04-16 15:37:42
// 生成路径: internal/app/cms/router/cms_category_ipaccess.go
// 生成人：zhanghui
// desc:分类IP访问限制
// company:云南奇讯科技有限公司
// ==========================================================================

package router

import (
	"context"

	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/internal/app/cms/controller"
)

func (router *Router) BindCmsCategoryIpaccessController(ctx context.Context, group *ghttp.RouterGroup) {
	group.Group("/cmsCategoryIpaccess", func(group *ghttp.RouterGroup) {
		group.Bind(
			controller.CmsCategoryIpaccess,
		)
	})
}
