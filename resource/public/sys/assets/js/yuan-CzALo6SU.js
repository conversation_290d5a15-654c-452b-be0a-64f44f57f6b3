import{l as K,f as ye,e as _e}from"./cmsCategory-MP-V5q2B.js";import{a as be,C as X}from"./edit-vs37Y69y.js";import De from"./permission-Bl6bR5Lk.js";import{a as ee,E as ke}from"./element-plus-CUmVNDWO.js";import{d as Ie,f as we,r as d,i as Ee,k as W,X as Be,h as Fe,ad as Me,s as Ve,ab as Se,ag as r,aq as te,c as ae,o as h,P as a,H as n,a as g,a4 as oe,L as C,G as b,M as Y,I as F,J as le}from"./@vue-C21YZbHS.js";import{a as Ae}from"./index-BGmsw1a8.js";import"./index-D8PZCYUo.js";import"./lodash-BPJNOONf.js";import"./@intlify-D8kAWvSi.js";import"./modulesInfo-OqFq1r32.js";import"./index-BSsI7aMT.js";import"./cmsTemplate-IDwSlQj3.js";import"./index-BvZSjrda.js";import"./lodash-es-S0Y0Up6J.js";import"./@vueuse-D_5IXmcI.js";import"./@element-plus-_Cc-TEQX.js";import"./@popperjs-D_chPuIy.js";import"./@ctrl-r5W6hzzQ.js";import"./dayjs-CepoAVPu.js";import"./async-validator-9PlIezaS.js";import"./memoize-one-BdPwpGay.js";import"./normalize-wheel-es-BQoi3Ox2.js";import"./@floating-ui-DwceP2Gb.js";import"./pinia-DwJf82dV.js";import"./vue-router-BDT9dCQ7.js";import"./js-cookie-Cz0CWeBA.js";import"./nprogress-Scw6VIZr.js";import"./axios-B62VicFi.js";import"./qs-D41HS0uK.js";import"./side-channel-DytTmCZg.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-DbsE5JW_.js";import"./crypto-js-oM26lpET.js";import"./side-channel-list-C_7Uk4RP.js";import"./side-channel-map-BUokNT8A.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-bSkmVQJL.js";import"./vue-i18n-BkLk9Ti1.js";import"./vue-iNFchrNl.js";import"./vue-clipboard3-Dr8V5xR7.js";import"./clipboard-DzJOpNE6.js";import"./mitt-DJ65BbbF.js";import"./vue-grid-layout-CIPjP0XN.js";import"./vue-simple-uploader-j6sX1TKo.js";import"./vue-ueditor-wrap-CJ5gjsd3.js";const Te=Ie({name:"CmsCategory",components:{apiV1CmsCmsCategoryEdit:be,CategoryPermission:De},setup(){const{proxy:t}=we(),l=d(!1),A=d(),T=d(),P=d(""),f=d();Ee(P,e=>{var o;(o=f.value)==null||o.filter(e)});const R=(e,o)=>e?o.name.toLowerCase().includes(e.toLowerCase()):!0,p=d(!1),u=d(!0),N=d(0),V=d(!0),U=W(()=>p.value===!1?"展开搜索":"收起搜索"),{sys_yes_no:L,sys_normal_disable:O}=t.useDict("sys_yes_no","sys_normal_disable"),D=d([]),k=d([]),i=Be({ids:[],tableData:{data:[],total:0,loading:!1,param:{pageNum:1,pageSize:50,name:void 0}}}),I=d([]),m=d(null),w=d([]),q=d(!1),M=d(!1),v=d(!1);Fe(()=>{z()});const z=async()=>{l.value=!0;try{const o=(await K({pageSize:1e4})).data.list||[];I.value=J(o),w.value=[],i.tableData.data=o,l.value=!1}catch(e){console.error(e),l.value=!1}},G=e=>{for(const o in X)if(X[o].value===e.type)return X[o].label},Q=e=>{if(e){if(e.resetFields(),!m.value){w.value=[];return}H()}},y=async()=>{l.value=!0;try{const e=await K(i.tableData.param);requestAnimationFrame(()=>{const o=J(e.data.list||[]);i.tableData.data=Ve(o).value,i.tableData.total=e.data.total,l.value=!1})}catch(e){console.error(e),l.value=!1}},j=()=>{p.value=!p.value},s=()=>{D.value&&D.value.length>0||t.getItems(K,{pageSize:1e4}).then(e=>{D.value=t.setItems(e,"id","name")})},S=()=>{k.value&&k.value.length>0||t.getItems(ye,{pageSize:1e4}).then(e=>{k.value=t.setItems(e,"id","name")})},ne=e=>t.selectDictLabel(L.value,e.isInherit),se=e=>t.selectDictLabel(O.value,e.status),re=e=>{i.ids=e.map(o=>o.id),u.value=e.length!=1,V.value=!e.length},ie=(e,o)=>{!o&&m.value&&(o=m.value.id),T.value.openDialog(null,o)},de=e=>{console.log(e),e||(e=i.tableData.data.find(o=>o.id===i.ids[0])),T.value.openDialog(Se(e))},ue=e=>{let o="你确定要删除所选数据？",c=[];if(e?(o="此操作将永久删除数据，是否继续?",c=[e.id]):c=i.ids,c.length===0){ee.error("请选择要删除的数据。");return}ke.confirm(o,"提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(()=>{_e(c).then(()=>{ee.success("删除成功"),y()})}).catch(()=>{})},pe=e=>{m.value=e,w.value=[e],i.tableData.param.name=void 0,A.value&&A.value.resetFields()},me=e=>{},H=()=>{var e;if(!m.value){w.value=[];return}return(e=i.tableData.param.name)==null||e.toLowerCase(),t.useThrottle(H,300)},ce=(e,o)=>{e.expanded=o.expanded,q.value=I.value.some(c=>c.expanded)},J=e=>{const o=new Map;e.forEach(_=>o.set(_.id,{..._,children:[]}));const c=[];return e.forEach(_=>{var B;const E=o.get(_.id);if(_.parentId&&o.has(_.parentId)){const $=o.get(_.parentId);$&&E&&((B=$.children)==null||B.push(E))}else E&&c.push(E)}),c},fe=W(()=>{var c;const e=(c=i.tableData.param.name)==null?void 0:c.toLowerCase();if(!e)return i.tableData.data;const o=_=>_.reduce((E,B)=>{const $=B.name.toLowerCase().includes(e),x=B.children?o(B.children):[];return($||x.length)&&E.push({...B,children:x}),E},[]);return o(i.tableData.data)});W(()=>t.useDebounce(()=>{var e;i.tableData.param.name=(e=i.tableData.param.name)==null?void 0:e.trim()},300));const he=async()=>{try{const o=(await K({pageSize:1e4})).data.list??[];I.value=J(o),m.value&&(f.value.getNode(m.value.id)?f.value.setCurrentKey(m.value.id):m.value=null)}catch(e){console.error(e)}},ge=()=>{var e;(e=f.value)==null||e.filter(P.value)},Ce=()=>{f.value&&(M.value=!M.value,M.value?Z(I.value):ve())},Z=e=>{!e||!f.value||e.forEach(o=>{f.value.store.nodesMap[o.id].expanded=!0,o.children&&o.children.length>0&&Z(o.children)})},ve=()=>{f.value&&Object.keys(f.value.store.nodesMap).forEach(e=>{f.value.store.nodesMap[e].expanded=!1})};return{proxy:t,editRef:T,showAll:p,loading:l,single:u,multiple:V,word:U,queryRef:A,parentId:N,treeFilterText:P,filterNode:R,resetQuery:Q,typeFormat:G,cmsCategoryList:y,toggleSearch:j,parentIdOptions:D,getCmsCategoryItemsParentId:s,moduleIdOptions:k,getModulesInfoItemsModuleId:S,isInheritFormat:ne,sys_yes_no:L,statusFormat:se,sys_normal_disable:O,handleSelectionChange:re,handleAdd:ie,handleUpdate:de,handleDelete:ue,...Me(i),displayData:fe,treeRef:f,treeData:I,tableList:w,handleNodeClick:pe,handleSearch:H,handleRowClick:me,handleExpandChange:ce,refreshTree:he,categoryPermissionDialogVisible:v,isExpandAll:M,toggleExpandAll:Ce,handleTreeSearch:ge}}}),Pe={class:"cms-cmsCategory-container"},Re={class:"cms-cmsCategory-content"},Ne={class:"left-tree"},Le={class:"tree-header"},Oe={class:"search-box"},$e={class:"tree-buttons"},Ke={class:"left-buttons"},Ue={class:"right-buttons"},qe={class:"right-content"},ze={class:"cms-cmsCategory-search mb15"},Ge=["href"],Qe={key:0};function je(t,l,A,T,P,f){const R=r("ele-Search"),p=r("el-icon"),u=r("el-button"),N=r("el-input"),V=r("ele-Refresh"),U=r("ele-ArrowDown"),L=r("ele-ArrowUp"),O=r("el-tree"),D=r("el-form-item"),k=r("el-row"),i=r("el-form"),I=r("ele-Plus"),m=r("el-col"),w=r("ele-Delete"),q=r("ele-Key"),M=r("CategoryPermission"),v=r("el-table-column"),z=r("el-table"),G=r("el-card"),Q=r("apiV1CmsCmsCategoryEdit"),y=te("auth"),j=te("loading");return h(),ae("div",Pe,[a(G,{shadow:"hover"},{default:n(()=>[g("div",Re,[g("div",Ne,[g("div",Le,[g("div",Oe,[a(N,{modelValue:t.treeFilterText,"onUpdate:modelValue":l[0]||(l[0]=s=>t.treeFilterText=s),placeholder:"输入关键字搜索",clearable:"",class:"tree-filter",onKeyup:oe(t.handleTreeSearch,["enter"])},{append:n(()=>[a(u,{onClick:t.handleTreeSearch},{default:n(()=>[a(p,null,{default:n(()=>[a(R)]),_:1})]),_:1},8,["onClick"])]),_:1},8,["modelValue","onKeyup"])]),g("div",$e,[g("div",Ke,[a(u,{link:"",onClick:t.refreshTree},{default:n(()=>[a(p,null,{default:n(()=>[a(V)]),_:1}),l[6]||(l[6]=C(" 刷新 "))]),_:1},8,["onClick"])]),g("div",Ue,[a(u,{link:"",onClick:t.toggleExpandAll},{default:n(()=>[a(p,null,{default:n(()=>[t.isExpandAll?(h(),b(L,{key:1})):(h(),b(U,{key:0}))]),_:1}),C(" "+Y(t.isExpandAll?"收缩":"展开"),1)]),_:1},8,["onClick"])])])]),a(O,{ref:"treeRef",data:t.treeData,props:{label:"name",children:"children"},"node-key":"id","highlight-current":"","filter-node-method":t.filterNode,onNodeClick:t.handleNodeClick,"expand-on-click-node":!1,onExpandChange:t.handleExpandChange},null,8,["data","filter-node-method","onNodeClick","onExpandChange"])]),g("div",qe,[g("div",ze,[a(i,{ref:"queryRef",inline:!0,model:t.tableData.param,"label-width":"80px"},{default:n(()=>[a(k,null,{default:n(()=>[a(D,{label:"分类名称",prop:"name"},{default:n(()=>[a(N,{modelValue:t.tableData.param.name,"onUpdate:modelValue":l[1]||(l[1]=s=>t.tableData.param.name=s),modelModifiers:{trim:!0},clearable:"",placeholder:"请输入名称",onKeyup:oe(t.handleSearch,["enter","native"])},null,8,["modelValue","onKeyup"])]),_:1}),a(D,null,{default:n(()=>[a(u,{type:"primary",onClick:t.handleSearch},{default:n(()=>[a(p,null,{default:n(()=>[a(R)]),_:1}),l[7]||(l[7]=C(" 搜索 "))]),_:1},8,["onClick"]),a(u,{onClick:l[2]||(l[2]=s=>t.resetQuery(t.queryRef))},{default:n(()=>[a(p,null,{default:n(()=>[a(V)]),_:1}),l[8]||(l[8]=C(" 重置 "))]),_:1})]),_:1})]),_:1})]),_:1},8,["model"]),a(k,{gutter:10,class:"mb8"},{default:n(()=>[a(m,{span:1.5},{default:n(()=>[F((h(),b(u,{type:"primary",onClick:t.handleAdd},{default:n(()=>[a(p,null,{default:n(()=>[a(I)]),_:1}),l[9]||(l[9]=C(" 新增 "))]),_:1},8,["onClick"])),[[y,"api/v1/cms/cmsCategory/add"]])]),_:1}),a(m,{span:1.5},{default:n(()=>[F((h(),b(u,{disabled:t.multiple,type:"danger",onClick:l[3]||(l[3]=s=>t.handleDelete(null))},{default:n(()=>[a(p,null,{default:n(()=>[a(w)]),_:1}),l[10]||(l[10]=C(" 删除 "))]),_:1},8,["disabled"])),[[y,"api/v1/cms/cmsCategory/delete"]])]),_:1}),a(m,{span:1.5},{default:n(()=>[F((h(),b(u,{type:"primary",onClick:l[4]||(l[4]=s=>t.categoryPermissionDialogVisible=!0)},{default:n(()=>[a(p,null,{default:n(()=>[a(q)]),_:1}),l[11]||(l[11]=C(" 分类权限 "))]),_:1})),[[y,"api/v1/cms/cmsCategory/savePermissions"]])]),_:1})]),_:1}),a(M,{modelValue:t.categoryPermissionDialogVisible,"onUpdate:modelValue":l[5]||(l[5]=s=>t.categoryPermissionDialogVisible=s)},null,8,["modelValue"])]),F((h(),b(z,{border:"",data:t.tableList,"row-key":"id",onSelectionChange:t.handleSelectionChange,onRowClick:t.handleRowClick},{default:n(()=>[a(v,{align:"center",type:"selection",width:"55"}),a(v,{label:"分类名称","min-width":"120px",prop:"name"},{default:n(({row:s})=>[g("span",null,[g("a",{href:s.url,target:"_blank"},[C(Y(s.name)+" ",1),s.alias?(h(),ae("span",Qe," - ("+Y(s.alias)+")",1)):le("",!0)],8,Ge)])]),_:1}),a(v,{align:"center",label:"分类类型",prop:"type",width:"100px",formatter:t.typeFormat},null,8,["formatter"]),a(v,{label:"ID",prop:"id",width:"80px"}),a(v,{align:"center",label:"内容模型",prop:"linkedModuleId.name",width:"100px"}),a(v,{align:"center",label:"排序",prop:"sort",width:"80px"}),a(v,{formatter:t.statusFormat,align:"center",label:"状态",prop:"status",width:"80px"},null,8,["formatter"]),a(v,{align:"center","class-name":"small-padding",fixed:"right",label:"操作",width:"200px"},{default:n(s=>[F((h(),b(u,{link:"",type:"primary",onClick:S=>t.handleUpdate(s.row)},{default:n(()=>l[12]||(l[12]=[C(" 修改 ")])),_:2},1032,["onClick"])),[[y,"api/v1/cms/cmsCategory/edit"]]),s.row.type==="channel"?F((h(),b(u,{key:0,link:"",type:"primary",onClick:S=>t.handleAdd(S,s.row.id)},{default:n(()=>l[13]||(l[13]=[C(" 增加子类 ")])),_:2},1032,["onClick"])),[[y,"api/v1/cms/cmsCategory/edit"]]):le("",!0),F((h(),b(u,{link:"",type:"primary",onClick:S=>t.handleDelete(s.row)},{default:n(()=>l[14]||(l[14]=[C(" 删除 ")])),_:2},1032,["onClick"])),[[y,"api/v1/cms/cmsCategory/delete"]])]),_:1})]),_:1},8,["data","onSelectionChange","onRowClick"])),[[j,t.loading]])])])]),_:1}),a(Q,{ref:"editRef",isInheritOptions:t.sys_yes_no,moduleIdOptions:t.moduleIdOptions,parentIdOptions:t.parentIdOptions,statusOptions:t.sys_normal_disable,onCmsCategoryList:t.cmsCategoryList,onGetCmsCategoryItemsParentId:t.getCmsCategoryItemsParentId,onGetModulesInfoItemsModuleId:t.getModulesInfoItemsModuleId},null,8,["isInheritOptions","moduleIdOptions","parentIdOptions","statusOptions","onCmsCategoryList","onGetCmsCategoryItemsParentId","onGetModulesInfoItemsModuleId"])])}const Xt=Ae(Te,[["render",je],["__scopeId","data-v-eea40faa"]]);export{Xt as default};
