<template>
  <div class="cms-cmsConfig-edit">
    <!-- 添加或修改CMS配置对话框 -->
    <el-dialog v-model="isShowDialog" :close-on-click-modal="false" :destroy-on-close="true" width="769px">
      <template #header>
        <div v-drag="['.cms-cmsConfig-edit .el-dialog', '.cms-cmsConfig-edit .el-dialog__header']">
          {{ (!formData.id || formData.id == 0 ? '添加' : '修改') + 'CMS配置' }}
        </div>
      </template>
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="90px" >
        <el-form-item label="名称" prop="title">
          <el-input v-model.trim="formData.title" placeholder="请输入名称"/>
        </el-form-item>
        <el-form-item label="调用标签" prop="name">
          <el-input v-model.trim="formData.name" placeholder="只能以字母开头，只能输入字母和数字或下划线，用于在代码中调用"/>
        </el-form-item>
        <el-form-item label="说明" prop="description">
          <el-input v-model.trim="formData.description" placeholder="请输入配置说明"/>
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-tag v-if="formData.type==='text'" type="info">文本</el-tag>
          <el-tag v-else-if="formData.type==='image'" type="success">图片</el-tag>
          <el-tag v-else-if="formData.type==='rich'" type="warning">富文本</el-tag>
          <el-tag v-else-if="formData.type==='radio'" type="danger">单选</el-tag>
          <el-tag v-else-if="formData.type==='select'" type="primary">下拉选择</el-tag>
          <el-tag v-else>未知类型</el-tag>
        </el-form-item>
        <el-form-item v-if="formData.type==='text'" label="内容" prop="value">
          <el-input v-model.trim="formData.value" placeholder="请输入内容"/>
        </el-form-item>
        <el-form-item v-if="formData.type==='image'" label="图片上传" prop="value">
          <el-upload
              v-loading="upLoadingThumb"
              :action="baseURL+'api/v1/system/upload/singleImg'"
              :before-upload="beforeAvatarUploadThumb"
              :data="setUpData()"
              :on-success="handleAvatarSuccessThumb"
              :show-file-list="false"
              class="avatar-uploader"
              name="file"
          >
            <img v-if="!proxy.isEmpty(imageUrlThumb)" :src="imageUrlThumb" class="avatar">
            <el-icon v-else class="avatar-uploader-icon">
              <ele-Plus/>
            </el-icon>
          </el-upload>
        </el-form-item>
        <el-form-item v-if="formData.type==='rich'" label="内容" prop="value">
          <gf-ueditor editorId="cmsConfigValueEditor" :tool-bars="toolbar" v-model="formData.value" @setEditContent="setEditContent"></gf-ueditor>
        </el-form-item>
        <el-form-item v-if="formData.type==='radio'" label="内容" prop="value">
          <el-radio-group v-model="formData.value">
            <el-radio v-for="item in radioOptions" :key="item.key" :label="item.key">{{ item.value }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="formData.type==='select'" label="内容" prop="value">
          <el-select v-model="formData.value" placeholder="请选择">
            <el-option v-for="item in selectOptions" :key="item.key" :label="item.value" :value="item.key"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="onSubmit">确 定</el-button>
          <el-button @click="onCancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts">
import {defineComponent, getCurrentInstance, reactive, ref, toRefs, unref} from "vue";
import {ElMessage, UploadProps} from "element-plus";
import {addCmsConfig, getCmsConfig, updateCmsConfig} from "/@/api/cms/cmsConfig";
import {CmsConfigEditState, CmsConfigInfoData} from "/@/views/cms/cmsConfig/list/component/model";
import {getToken} from "/@/utils/gfast";
import GfUeditor from "/@/components/ueditor/index.vue"
import {minToolbar} from "/@/components/ueditor/model";

export default defineComponent({
  name: "apiV1CmsCmsConfigEdit",
  components: {GfUeditor},
  props: {},
  setup(props, {emit}) {
    const baseURL: string | undefined | boolean = import.meta.env.VITE_API_URL;
    const {proxy} = <any>getCurrentInstance();
    const formRef = ref<HTMLElement | null>(null);
    const menuRef = ref();
    //图片上传地址
    const imageUrlThumb = ref("");
    //上传加载
    const upLoadingThumb = ref(false);
    // radio选项
    const radioOptions = ref<{key: string, value: string}[]>([{key: '', value: ''}]);
    // select选项
    const selectOptions = ref<{key: string, value: string}[]>([{key: '', value: ''}]);
    const state = reactive<CmsConfigEditState>({
      loading: false,
      isShowDialog: false,
      formData: {
        id: undefined,
        title: undefined,
        name: undefined,
        value: undefined,
        description: undefined,
        type: 'text'
      },
      // 表单校验
      rules: {}
    });
    // 解析JSON数据到选项中
    const parseJsonToOptions = (jsonStr: string, type: 'radio' | 'select') => {
      try {
        const options = JSON.parse(jsonStr);
        const result: {key: string, value: string}[] = [];

        for (const key in options) {
          if (Object.prototype.hasOwnProperty.call(options, key)) {
            result.push({
              key: key,
              value: options[key]
            });
          }
        }

        if (result.length === 0) {
          result.push({key: '', value: ''});
        }

        // 预选中与 value 匹配的选项
        const selectedValue = state.formData.value;
        if (selectedValue) {
          // 将选中的选项放在最前面
          const selectedIndex = result.findIndex(item => item.key === selectedValue);
          if (selectedIndex > 0) {
            const selectedItem = result.splice(selectedIndex, 1)[0];
            result.unshift(selectedItem);
          }
        }

        if (type === 'radio') {
          radioOptions.value = result;
        } else {
          selectOptions.value = result;
        }
      } catch (e) {
        console.error('JSON解析错误:', e);
        if (type === 'radio') {
          radioOptions.value = [{key: '', value: ''}];
        } else {
          selectOptions.value = [{key: '', value: ''}];
        }
      }
    };

    // 打开弹窗
    const openDialog = (row?: CmsConfigInfoData) => {
      resetForm();
      if (row) {
        getCmsConfig(row.id!).then((res: any) => {
          const data = res.data;
          //单图地址赋值，如果是图片类型
          if(data.type==='image') {
            imageUrlThumb.value = data.thumb ? proxy.getUpFileUrl(data.thumb) : "";
          }

          // 如果是radio或select类型，解析setting字段的JSON数据
          if (data.type === 'radio' && data.setting) {
            parseJsonToOptions(data.setting, 'radio');
          } else if (data.type === 'select' && data.setting) {
            parseJsonToOptions(data.setting, 'select');
          }

          state.formData = data;
        });
      }
      state.isShowDialog = true;
    };
    // 关闭弹窗
    const closeDialog = () => {
      state.isShowDialog = false;
    };
    // 取消
    const onCancel = () => {
      closeDialog();
    };
    const setUpData = () => {
      return {token: getToken()};
    };
    // 添加radio选项
    const addOption = () => {
      radioOptions.value.push({key: '', value: ''});
    };

    // 移除radio选项
    const removeOption = (index: number) => {
      if (radioOptions.value.length > 1) {
        radioOptions.value.splice(index, 1);
      } else {
        ElMessage.warning('至少保留一个选项');
      }
    };

    // 添加select选项
    const addSelectOption = () => {
      selectOptions.value.push({key: '', value: ''});
    };

    // 移除select选项
    const removeSelectOption = (index: number) => {
      if (selectOptions.value.length > 1) {
        selectOptions.value.splice(index, 1);
      } else {
        ElMessage.warning('至少保留一个选项');
      }
    };

    // 获取选中的值
    const getSelectedValue = () => {
      // 直接返回用户选择的值，这个值已经绑定到 formData.value 上
      return state.formData.value;
    };

    // 在提交前检查值
    const validateValue = () => {
      // 不需要做任何特殊处理，因为值已经绑定到 formData.value 上
      return true;
    };

    // 提交
    const onSubmit = () => {
      const formWrap = unref(formRef) as any;
      if (!formWrap) return;
      formWrap.validate((valid: boolean) => {
        if (valid) {
          // 在提交前检查值
          validateValue();

          state.loading = true;
          if (!state.formData.id || state.formData.id === 0) {
            //添加
            addCmsConfig(state.formData).then(() => {
              ElMessage.success("添加成功");
              closeDialog(); // 关闭弹窗
              emit("cmsConfigList");
            }).finally(() => {
              state.loading = false;
            });
          } else {
            //修改
            updateCmsConfig(state.formData).then(() => {
              ElMessage.success("修改成功");
              closeDialog(); // 关闭弹窗
              emit("cmsConfigList");
            }).finally(() => {
              state.loading = false;
            });
          }
        }
      });
    };

    const resetForm = () => {
      state.formData = {
        id: undefined,
        title: undefined,
        name: undefined,
        value: undefined,
        description: undefined,
        type: 'text'
      };
      // 重置选项数据
      radioOptions.value = [{key: '', value: ''}];
      selectOptions.value = [{key: '', value: ''}];
    };

    //单图上传缩略图
    const handleAvatarSuccessThumb: UploadProps["onSuccess"] = (res, file) => {
      if (res.code === 0) {
        imageUrlThumb.value = URL.createObjectURL(file.raw!);
        state.formData.value = res.data.path;
      } else {
        ElMessage.error(res.msg);
      }
      upLoadingThumb.value = false;
    };
    const beforeAvatarUploadThumb = () => {
      upLoadingThumb.value = true;
      return true;
    };
    const setEditContent = (data:string)=>{
      state.formData.value = data
    }
    return {
      proxy,
      openDialog,
      closeDialog,
      onCancel,
      onSubmit,
      menuRef,
      formRef,
      //图片上传地址
      imageUrlThumb,
      //上传加载
      setUpData,
      upLoadingThumb,
      toolbar:reactive(minToolbar),
      handleAvatarSuccessThumb,
      beforeAvatarUploadThumb,
      baseURL,
      setEditContent,
      // 选项相关
      radioOptions,
      selectOptions,
      addOption,
      removeOption,
      addSelectOption,
      removeSelectOption,
      ...toRefs(state)
    };
  }
});
</script>
<style scoped>
.cms-cmsConfig-edit :deep(.avatar-uploader .avatar) {
  width: 178px;
  height: 178px;
  display: block;
}

.cms-cmsConfig-edit :deep(.avatar-uploader .el-upload) {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.cms-cmsConfig-edit :deep(.avatar-uploader .el-upload:hover) {
  border-color: var(--el-color-primary);
}

.cms-cmsConfig-edit :deep(.el-icon.avatar-uploader-icon) {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
}

.option-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.option-info {
  margin-top: 10px;
}

.el-radio-group {
  display: flex;
  flex-wrap: wrap;
}

.el-radio {
  margin-right: 20px;
  margin-bottom: 10px;
}
</style>
