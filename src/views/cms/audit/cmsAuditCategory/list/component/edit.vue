<template>
  <div class="audit-cmsAuditCategory-edit">
    <!-- 添加或修改栏目审核设置对话框 -->
    <el-dialog v-model="isShowDialog" width="800px" :close-on-click-modal="false" :destroy-on-close="true">
      <template #header>
        <div v-drag="['.audit-cmsAuditCategory-edit .el-dialog', '.audit-cmsAuditCategory-edit .el-dialog__header']">{{(!formData.id || formData.id==0?'添加':'修改')+'栏目审核设置'}}</div>
      </template>
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px">
        <el-form-item label="审核人员" prop="userIds">
          <select-user v-model="selectedUserIds"></select-user>
        </el-form-item>
        <el-form-item label="审核步骤" prop="stepId">
          <!-- 添加模式下可选择审核步骤 -->
          <el-select
            v-if="!formData.id"
            v-model="formData.stepId"
            placeholder="请选择审核步骤"
            style="width: 100%"
            :loading="stepLoading"
            @visible-change="handleStepSelectOpen"
          >
            <el-option
              v-for="item in stepOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
          <!-- 编辑模式下只读显示 -->
          <div v-else class="readonly-field">
            {{ getStepName(formData.stepId) }}
          </div>
        </el-form-item>
        <el-form-item label="分类" prop="categoryId">
          <!-- 只读模式下的分类显示 -->
          <div class="readonly-field">
            {{ getCategoryName(formData.categoryId) }}
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="onSubmit" :disabled="loading">确 定</el-button>
          <el-button @click="onCancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { reactive, toRefs, ref, unref, getCurrentInstance, computed, watch } from 'vue';
import { ElMessageBox, ElMessage, FormInstance, UploadProps } from 'element-plus';
import selectUser from "/@/components/selectUser/index.vue";
import {
  listCmsAuditCategory,
  getCmsAuditCategory,
  delCmsAuditCategory,
  addCmsAuditCategory,
  updateCmsAuditCategory,
} from "/@/api/cms/audit/cmsAuditCategory";
import { listCmsAuditStep } from "/@/api/cms/audit/cmsAuditStep";
import { listCmsCategory } from "/@/api/cms/cmsCategory";
import {
  CmsAuditCategoryTableColumns,
  CmsAuditCategoryInfoData,
  CmsAuditCategoryTableDataState,
  CmsAuditCategoryEditState
} from "/@/views/cms/audit/cmsAuditCategory/list/component/model"
defineOptions({ name: "ApiV1CmsAuditCmsAuditCategoryEdit"})
const emit = defineEmits(['cmsAuditCategoryList'])
const {proxy} = <any>getCurrentInstance()
const formRef = ref<HTMLElement | null>(null);
const menuRef = ref();

// 审核步骤相关变量
const stepOptions = ref<Array<{value: number, label: string, step: number}>>([]);
const stepLoading = ref(false);
const stepMap = ref<Map<number, string>>(new Map()); // 步骤ID和名称的映射

// 栏目相关变量
const categoryOptions = ref<Array<any>>([]);
const categoryLoading = ref(false);
const categoryMap = ref<Map<number, string>>(new Map()); // 分类ID和名称的映射
const state = reactive<CmsAuditCategoryEditState>({
  loading:false,
  isShowDialog: false,
  formData: {
    id: undefined,
    userIds: '', // 审核人员ID（逗号分隔的字符串）
    stepId: undefined,
    step: undefined,
    categoryId: undefined,
  },
  // 表单校验
  rules: {
    userIds : [
        { required: true, message: "审核人员不能为空", trigger: "blur" }
    ],
    stepId : [
        { required: true, message: "审核步骤不能为空", trigger: "change" }
    ],
    categoryId : [
        { required: true, message: "栏目不能为空", trigger: "change" }
    ],
  }
});
const { loading,isShowDialog,formData,rules } = toRefs(state);

// 监听 select-user 组件选中的用户ID数组
const selectedUserIds = ref<number[]>([]);

// 当选中的用户ID数组变化时，将其转换为逗号分隔的字符串
watch(() => selectedUserIds.value, (newVal) => {
  if (newVal && newVal.length > 0) {
    // 将数组转换为逗号分隔的字符串
    state.formData.userIds = newVal.join(',');
  } else {
    state.formData.userIds = '';
  }
}, { deep: true });

// 当 formData.userIds 字符串变化时，将其转换为数组传给 select-user 组件
watch(() => state.formData.userIds, (newVal) => {
  if (newVal && newVal.length > 0) {
    // 将逗号分隔的字符串转换为数组
    selectedUserIds.value = newVal.split(',').map(id => parseInt(id));
  } else {
    selectedUserIds.value = [];
  }
}, { immediate: true });

// 监听 stepId 变化
watch(() => state.formData.stepId, (newVal) => {
  if (newVal) {
    // 从步骤选项中查找对应的步骤值
    const selectedStep = stepOptions.value.find(option => option.value === newVal);
    if (selectedStep) {
      state.formData.step = selectedStep.step;
    }
  } else {
    state.formData.step = undefined;
  }
});

// 监听 categoryOptions 变化
// 当分类数据加载完成后，确保默认分类能正确显示
watch(() => categoryOptions.value, (newVal) => {
  if (newVal && newVal.length > 0 && state.formData.categoryId) {
    // 当分类数据加载完成且有默认分类ID时，强制更新一次数据绑定
    const currentCategoryId = state.formData.categoryId;
    // 先清空再设置，触发UI更新
    state.formData.categoryId = undefined;
    setTimeout(() => {
      state.formData.categoryId = currentCategoryId;
    }, 0);
  }
}, { deep: true });

// 根据步骤ID获取步骤名称
const getStepName = (stepId: number | undefined) => {
  if (!stepId) return '';

  // 如果步骤映射中有该步骤，直接返回
  if (stepMap.value.has(stepId)) {
    return stepMap.value.get(stepId);
  }

  // 如果步骤选项中有该步骤，更新映射并返回
  const stepOption = stepOptions.value.find(option => option.value === stepId);
  if (stepOption) {
    stepMap.value.set(stepId, stepOption.label);
    return stepOption.label;
  }

  // 如果没有找到步骤，返回步骤ID作为临时显示
  return `步骤ID: ${stepId}`;
};

// 加载审核步骤列表
const loadStepOptions = () => {
  stepLoading.value = true;
  listCmsAuditStep({
    pageNum: 1,
    pageSize: 1000
  }).then((res: any) => {
    if (res.code === 0 && res.data && res.data.list) {
      // 将审核步骤列表转换为选项格式
      stepOptions.value = res.data.list.map((step: any) => {
        const label = `${step.title} (第${step.step}审)`;

        // 更新步骤映射
        stepMap.value.set(step.id, label);

        return {
          value: step.id,
          label: label,
          step: step.step
        };
      });
    }
    stepLoading.value = false;
  }).catch(() => {
    stepLoading.value = false;
  });
};

// 处理审核步骤选择器打开事件
const handleStepSelectOpen = (opened: boolean) => {
  if (opened && stepOptions.value.length === 0) {
    loadStepOptions();
  }
};

// 分类路径映射，存储分类ID和完整路径的对应关系
const categoryPathMap = ref<Map<number, string>>(new Map());

// 根据分类ID获取分类完整路径名称
const getCategoryName = (categoryId: number | undefined) => {
  if (!categoryId) return '';

  // 如果分类路径映射中有该分类，直接返回完整路径
  if (categoryPathMap.value.has(categoryId)) {
    return categoryPathMap.value.get(categoryId);
  }

  // 如果分类映射中有该分类，返回分类名称
  if (categoryMap.value.has(categoryId)) {
    return categoryMap.value.get(categoryId);
  }

  // 如果没有找到分类，返回分类ID作为临时显示
  return `分类ID: ${categoryId}`;
};

// 递归遍历分类树，构建分类ID和名称的映射，以及完整路径映射
const buildCategoryMap = (categories: any[], parentPath: string = '') => {
  categories.forEach(category => {
    // 将当前分类添加到名称映射中
    categoryMap.value.set(category.id, category.name);

    // 构建当前分类的完整路径
    const currentPath = parentPath ? `${parentPath} / ${category.name}` : category.name;

    // 将当前分类的完整路径添加到路径映射中
    categoryPathMap.value.set(category.id, currentPath);

    // 如果有子分类，递归遍历，并传递当前路径
    if (category.children && category.children.length > 0) {
      buildCategoryMap(category.children, currentPath);
    }
  });
};

// 加载栏目列表
const loadCategoryOptions = () => {
  categoryLoading.value = true;
  listCmsCategory({
    pageNum: 1,
    pageSize: 1000,
    status: 1
  }).then((res: any) => {
    if (res.code === 0 && res.data && res.data.list) {
      // 将栏目列表转换为树形结构
      const list = res.data.list || [];

      // 标记非 list 类型的节点为禁用
      list.forEach((item: any) => {
        if (item.type !== 'list') {
          item.disabled = true;
        }

        // 将分类添加到映射中
        categoryMap.value.set(item.id, item.name);
      });

      categoryOptions.value = proxy.handleTree(list, "id", "parentId");

      // 构建分类ID和名称的映射
      buildCategoryMap(categoryOptions.value);

      // 如果有默认分类ID，确保其能正确显示
      if (state.formData.categoryId) {
        // 强制触发UI更新
        const currentCategoryId = state.formData.categoryId;
        state.formData.categoryId = undefined;
        setTimeout(() => {
          state.formData.categoryId = currentCategoryId;
        }, 0);
      }
    }
    categoryLoading.value = false;
  }).catch(() => {
    categoryLoading.value = false;
  });
};

// 加载分类数据
const loadCategoryData = () => {
  if (categoryOptions.value.length === 0) {
    loadCategoryOptions();
  }
};

// 打开弹窗
const openDialog = (row?: CmsAuditCategoryInfoData, defaultCategoryId?: number) => {
  resetForm();

  // 如果有默认分类ID，先设置到表单中
  if (defaultCategoryId) {
    state.formData.categoryId = defaultCategoryId;
  }

  // 加载审核步骤选项
  if (stepOptions.value.length === 0) {
    loadStepOptions();
  }

  // 加载分类数据
  loadCategoryData();

  if(row) {
    getCmsAuditCategory(row.id!).then((res:any)=>{
      const data = res.data;
      state.formData = data;
      // 处理审核人员数据
      if (data.userIds) {
        // 如果后端返回 userIds，直接使用
        state.formData.userIds = data.userIds;
      } else {
        state.formData.userIds = '';
      }
    })
  }
  state.isShowDialog = true;
};
// 关闭弹窗
const closeDialog = () => {
  state.isShowDialog = false;
};
defineExpose({
  openDialog,
});
// 取消
const onCancel = () => {
  closeDialog();
};
// 提交
const onSubmit = () => {
  const formWrap = unref(formRef) as any;
  if (!formWrap) return;
  formWrap.validate((valid: boolean) => {
    if (valid) {
      // 检查 categoryId 是否为 0
      if (!state.formData.categoryId || state.formData.categoryId === 0) {
        ElMessage.error('分类ID不能为0，请选择有效的分类');
        return;
      }

      state.loading = true;

      // 准备提交的数据
      const submitData = { ...state.formData };

      if(!state.formData.id || state.formData.id===0){
        //添加
      addCmsAuditCategory(submitData).then(()=>{
          ElMessage.success('添加成功');
          closeDialog(); // 关闭弹窗
          emit('cmsAuditCategoryList')
        }).finally(()=>{
          state.loading = false;
        })
      }else{
        //修改
      updateCmsAuditCategory(submitData).then(()=>{
          ElMessage.success('修改成功');
          closeDialog(); // 关闭弹窗
          emit('cmsAuditCategoryList')
        }).finally(()=>{
          state.loading = false;
        })
      }
    }
  });
};
const resetForm = ()=>{
  state.formData = {
    id: undefined,
    userIds: '', // 审核人员ID（逗号分隔的字符串）
    stepId: undefined,
    step: undefined,
    categoryId: undefined,
  }
};
</script>
<style scoped>
  .kv-label{margin-bottom: 15px;font-size: 14px;}
  .mini-btn i.el-icon{margin: unset;}
  .kv-row{margin-bottom: 12px;}

  /* 只读字段样式 */
  .readonly-field {
    padding: 0 15px;
    min-height: 32px;
    line-height: 32px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background-color: #f5f7fa;
    color: #606266;
    word-break: break-all;
    white-space: normal;
    overflow: hidden;
  }
</style>