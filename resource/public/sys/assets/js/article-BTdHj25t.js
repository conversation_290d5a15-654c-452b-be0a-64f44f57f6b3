import{d as w,f as x,r as c,X as S,h as k,ag as r,aq as A,c as C,o as u,P as t,H as n,I as _,G as D,L as I,M as N,Q as V}from"./@vue-C21YZbHS.js";import{A as z}from"./cmsVisit-Hye8gtb9.js";import{a as B}from"./index-BGmsw1a8.js";import"./pinia-DwJf82dV.js";import"./vue-router-BDT9dCQ7.js";import"./js-cookie-Cz0CWeBA.js";import"./@element-plus-_Cc-TEQX.js";import"./nprogress-Scw6VIZr.js";import"./@intlify-D8kAWvSi.js";import"./axios-B62VicFi.js";import"./qs-D41HS0uK.js";import"./side-channel-DytTmCZg.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-DbsE5JW_.js";import"./crypto-js-oM26lpET.js";import"./side-channel-list-C_7Uk4RP.js";import"./side-channel-map-BUokNT8A.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-bSkmVQJL.js";import"./element-plus-CUmVNDWO.js";import"./lodash-es-S0Y0Up6J.js";import"./@vueuse-D_5IXmcI.js";import"./@popperjs-D_chPuIy.js";import"./@ctrl-r5W6hzzQ.js";import"./dayjs-CepoAVPu.js";import"./async-validator-9PlIezaS.js";import"./memoize-one-BdPwpGay.js";import"./normalize-wheel-es-BQoi3Ox2.js";import"./@floating-ui-DwceP2Gb.js";import"./vue-i18n-BkLk9Ti1.js";import"./vue-iNFchrNl.js";import"./vue-clipboard3-Dr8V5xR7.js";import"./clipboard-DzJOpNE6.js";import"./mitt-DJ65BbbF.js";import"./vue-grid-layout-CIPjP0XN.js";import"./vue-simple-uploader-j6sX1TKo.js";import"./vue-ueditor-wrap-CJ5gjsd3.js";const P={class:"cms-visit-article-container"},T=w({name:"cmsVisitArticle",__name:"article",setup(q,{expose:v}){const{proxy:M}=x(),s=c(!1),p=c([]),a=c(0),i=S({pageNum:1,pageSize:10,title:void 0,categoryId:void 0,startTime:void 0,endTime:void 0}),d=async()=>{s.value=!0;try{const e=await z(i);e.data?(p.value=e.data.list||[],a.value=e.data.total||0):(p.value=[],a.value=0)}catch(e){console.error("获取文章访问统计失败:",e),p.value=[],a.value=0}finally{s.value=!1}};return k(()=>{}),v({getArticleVisitStats:d}),(e,l)=>{const m=r("el-table-column"),g=r("el-link"),f=r("el-table"),b=r("pagination"),h=r("el-card"),y=A("loading");return u(),C("div",P,[t(h,{shadow:"hover"},{default:n(()=>[_((u(),D(f,{data:p.value,border:""},{default:n(()=>[t(m,{label:"ID",prop:"id",width:"80",align:"center"}),t(m,{label:"文章标题",prop:"title","min-width":"250"},{default:n(({row:o})=>[t(g,{type:"primary",href:o.url,target:"_blank",underline:!1},{default:n(()=>[I(N(o.article.title),1)]),_:2},1032,["href"])]),_:1}),t(m,{label:"分类",prop:"category.name",width:"120",align:"center"}),t(m,{label:"访问量",prop:"count",width:"100",align:"center"})]),_:1},8,["data"])),[[y,s.value]]),_(t(b,{total:a.value,page:i.pageNum,"onUpdate:page":l[0]||(l[0]=o=>i.pageNum=o),limit:i.pageSize,"onUpdate:limit":l[1]||(l[1]=o=>i.pageSize=o),onPagination:d},null,8,["total","page","limit"]),[[V,a.value>0]])]),_:1})])}}}),zt=B(T,[["__scopeId","data-v-8272be47"]]);export{zt as default};
