<div id="wrap">
    <div class="wrap container">
        <div class="main main-full">
            <article class="post-18 page type-page status-publish main-content" id="post-18">
                <div class="entry">
                    <div class="entry-content">
                        <div class="member-account-wrap">
                            <!-- 左侧导航 -->
                            ${include "widget/profile_left.html" .}
                            <div class="member-account-content">
                                <h2 class="member-account-title">通知列表</h2>
                                <ul class="nav nav-tabs nav-pills" id="message_list_tab">
                                    <li class="nav-item">
                                        <a href="javascript:void(0);" class="nav-link active" data-id="recv_message">我收到的</a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="javascript:void(0);" class="nav-link" data-id="send_message">我发送的</a>
                                    </li>
                                </ul>
                                <div class="message-tab-content">
                                    <div class="tab-pane fade show in" id="recv_message">
                                        <div id="message_list_container_recv"></div>
                                        <div id="message_pagination_recv" style="margin-top: 10px;"></div>
                                    </div>
                                    <div class="tab-pane fade" id="send_message">
                                        <div id="message_list_container_send"></div>
                                        <div id="message_pagination_send" style="margin-top: 10px;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </article>
        </div>
    </div>
</div>
<script id="message-list-recv" type="text/html">
    <%if(list && list.length > 0){%>
    <ul class="comments-list ">
        <% for(var i=0;i < list.length;i++){ %>
        <li class="comment byuser odd alt thread-odd thread-alt depth-1 parent">
            <div class="comment-inner">
                <div class="comment-author vcard">
                    <img class="avatar avatar-60 photo" height="60" src="<%=list[i].sendMember.avatar?list[i].sendMember.avatar:'/static/images/avatar.jpg'%>" width="60">
                </div>
                <div class="comment-body">
                    <div class="nickname">
                        <a class="j-user-card" href="${.cmsConfig.site_url}/member/<%= list[i].sendMember.id %>" target="_blank">发送人：<%= list[i].sendMember.nickname %></a>
                        <span class="comment-time"><%= list[i].createdAt%></span>
                    </div>
                    <div class="comment-text">
                        <div class="comment-text-content"><%=list[i].content %></div>
                    </div>
                </div>
                <div class="reply reply-btn" style="visibility: unset">
                    <a href="javascript:void(0);" onclick="deleteMessage(<%=list[i].id%>)"><i class="iconfont icon-delete"></i>删除</a>
                </div>
            </div>
            <div class="comment-respond comment-reply-container" id="comment-reply-<%=i%>"></div>
        </li>
        <% } %>
    </ul>
    <%}else{%>
    <div class="empty-content-empty">没有数据</div>
    <%}%>
</script>
<script id="message-list-send" type="text/html">
    <%if(list && list.length > 0){%>
    <ul class="comments-list ">
        <% for(var i=0;i < list.length;i++){ %>
        <li class="comment byuser odd alt thread-odd thread-alt depth-1 parent">
            <div class="comment-inner">
                <div class="comment-author vcard">
                    <img class="avatar avatar-60 photo" height="60" src="<%=list[i].receiveMember.avatar?list[i].receiveMember.avatar:'/static/images/avatar.jpg'%>" width="60">
                </div>
                <div class="comment-body">
                    <div class="nickname">
                        <a class="j-user-card" href="${.cmsConfig.site_url}/member/<%= list[i].receiveMember.id %>" target="_blank">收信人：<%= list[i].receiveMember.nickname %></a>
                        <span class="comment-time"><%= list[i].createdAt%></span>
                    </div>
                    <div class="comment-text">
                        <div class="comment-text-content"><%=list[i].content %></div>
                    </div>
                </div>
            </div>
            <div class="comment-respond comment-reply-container" id="comment-reply-<%=i%>"></div>
        </li>
        <% } %>
    </ul>
    <%}else{%>
    <div class="empty-content-empty">没有数据</div>
    <%}%>
</script>
<script id="message-pagination-template" type="text/html">
    <ul class="pagination" >
        <li class="total">共<%=total%>条</li>
        <% if(page > 1){ %>
        <li class="prev">
            <a href="javascript:void(0)" onclick="messagePageClick(1)" class="page-numbers"><i class="iconfont icon-left"></i></a>
        </li>
        <%}%>
        <% for(var i=1;i <= totalPage; i++) { %>
        <li class="<%=page===i?'active':''%>">
            <a href="javascript:void(0)" onclick="messagePageClick(<%=i%>)" class="page-numbers"><%=i%></a>
        </li>
        <% } %>
        <% if(page < totalPage) { %>
        <li class="next">
            <a href="javascript:void(0)" onclick="messagePageClick(<%=totalPage%>)" class="page-numbers"><i class="iconfont icon-right"></i></a>
        </li>
        <%}%>
    </ul>
</script>
<script>
    var totalMessagePageRecv = 0;
    var messagePageSizeRecv = 10;
    var totalMessageRecv = 0;
    var totalMessagePageSend = 0;
    var messagePageSizeSend = 10;
    var totalMessageSend = 0;
    var msgType = 'recv'
    function getMessageListRecv(page){
        $.get(APIBASE+"ucenter/message/list",{pageSize:messagePageSizeRecv,pageNum:page,memberId:${.Session.memberInfo.Id},msgType:'recv'},function (res) {
            if(res.code === 0){
                totalMessagePageRecv = Math.ceil(res.data.total/messagePageSizeRecv)
                totalMessageRecv = res.data.total
                $("#message_list_container_recv").html(template("message-list-recv", res.data))
                if(res.data.total > messagePageSizeRecv) {
                    $("#message_pagination_recv").html(template("message-pagination-template",{page:res.data.currentPage,totalPage:totalMessagePageRecv,total:totalMessageRecv}))
                }
            }
        })
    }``
    function getMessageListSend(page){
        $.get(APIBASE+"ucenter/message/list",{pageSize:messagePageSizeSend,pageNum:page,memberId:${.Session.memberInfo.Id},msgType:'send'},function (res) {
            if(res.code === 0){
                totalMessagePageSend = Math.ceil(res.data.total/messagePageSizeSend)
                totalMessageSend = res.data.total
                $("#message_list_container_send").html(template("message-list-send", res.data))
                if(res.data.total > messagePageSizeSend) {
                    $("#message_pagination_send").html(template("message-pagination-template",{page:res.data.currentPage,totalPage:totalMessagePageSend,total:totalMessageSend}))
                }
            }
        })
    }
    function deleteMessage(id){
        $.ajax({
            url:APIBASE+'ucenter/message/delete',
            data:{id},
            method:'delete',
            success:function (res){
                if(res.code === 0){
                    layer.msg("操作成功",{icon:1})
                    setTimeout(function (){
                        window.location.reload()
                    },2000)
                } else {
                    layer.msg("操作失败",{icon:2})
                }
            }
        })
    }
    
    function messagePageClick(page){
        if(msgType==='recv'){
            getMessageListRecv(page)
        } else {
            getMessageListSend(page)
        }
    }
    
    $(function (){
        $("#message_list_tab li a").on('click',function (e){
            $("#message_list_tab li a").removeClass("active")
            $(e.currentTarget).addClass("active")
            $(".message-tab-content .tab-pane").removeClass("show").removeClass("in")
            $("#"+e.currentTarget.dataset.id).addClass("show").addClass("in")
            msgType = e.currentTarget.dataset.id.substring(0,4)
        })
        getMessageListRecv(1)
        getMessageListSend(1)
    })
</script>