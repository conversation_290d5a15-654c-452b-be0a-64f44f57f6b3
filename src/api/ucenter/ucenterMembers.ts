import request from '/@/utils/request'
// 查询用户中心列表
export function listUcenterMembers(query:object) {
  return request({
    url: '/api/v1/ucenter/admin/ucenterMembers/list',
    method: 'get',
    params: query
  })
}
// 查询用户中心详细
export function getUcenterMembers(id:number) {
  return request({
    url: '/api/v1/ucenter/admin/ucenterMembers/get',
    method: 'get',
    params: {
      id: id.toString()
    }
  })
}
// 新增用户中心
export function addUcenterMembers(data:object) {
  return request({
    url: '/api/v1/ucenter/admin/ucenterMembers/add',
    method: 'post',
    data: data
  })
}
// 修改用户中心
export function updateUcenterMembers(data:object) {
  return request({
    url: '/api/v1/ucenter/admin/ucenterMembers/edit',
    method: 'put',
    data: data
  })
}

// 重置用户密码
export function resetPwd(data:object) {
  return request({
    url: '/api/v1/ucenter/admin/ucenterMembers/resetPwd',
    method: 'put',
    data: data
  })
}

// 删除用户中心
export function delUcenterMembers(ids:number[]) {
  return request({
    url: '/api/v1/ucenter/admin/ucenterMembers/delete',
    method: 'delete',
    data:{
      ids:ids
    }
  })
}
// 关联ucenter_level表选项
export function listUcenterLevel(query:object){
   return request({
     url: '/api/v1/ucenter/admin/ucenterLevel/list',
     method: 'get',
     params: query
   })
}

export function getDeptTree(showOwner?:boolean) {
  return request({
    url: '/api/v1/system/dept/treeSelect',
    method: 'get',
    params:{showOwner}
  })
}
