@font-face {
  font-family: "iconfont"; /* Project id 2794522 */
  src: url('iconfont.woff2?t=1630981552527') format('woff2'),
       url('iconfont.woff?t=1630981552527') format('woff'),
       url('iconfont.ttf?t=1630981552527') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-file-ppt:before {
  content: "\e7e8";
}

.icon-database-fill:before {
  content: "\e8e8";
}

.icon-file-word:before {
  content: "\e7e9";
}

.icon-container-fill:before {
  content: "\e8e9";
}

.icon-file:before {
  content: "\e7ea";
}

.icon-sever-fill:before {
  content: "\e8ea";
}

.icon-file-zip:before {
  content: "\e7eb";
}

.icon-calendar-check-fill:before {
  content: "\e8eb";
}

.icon-file-text:before {
  content: "\e7ec";
}

.icon-image-fill:before {
  content: "\e8ec";
}

.icon-file-copy:before {
  content: "\e7ed";
}

.icon-idcard-fill:before {
  content: "\e8ed";
}

.icon-snippets:before {
  content: "\e7ee";
}

.icon-creditcard-fill:before {
  content: "\e8ee";
}

.icon-audit:before {
  content: "\e7ef";
}

.icon-fund-fill:before {
  content: "\e8ef";
}

.icon-diff:before {
  content: "\e7f0";
}

.icon-read-fill:before {
  content: "\e8f0";
}

.icon-Batchfolding:before {
  content: "\e7f1";
}

.icon-contacts-fill:before {
  content: "\e8f1";
}

.icon-securityscan:before {
  content: "\e7f2";
}

.icon-delete-fill:before {
  content: "\e8f2";
}

.icon-propertysafety:before {
  content: "\e7f3";
}

.icon-notification-fill:before {
  content: "\e8f3";
}

.icon-safetycertificate:before {
  content: "\e7f4";
}

.icon-flag-fill:before {
  content: "\e8f4";
}

.icon-insurance:before {
  content: "\e7f5";
}

.icon-moneycollect-fill:before {
  content: "\e8f5";
}

.icon-alert:before {
  content: "\e7f6";
}

.icon-medicinebox-fill:before {
  content: "\e8f6";
}

.icon-delete:before {
  content: "\e7f7";
}

.icon-rest-fill:before {
  content: "\e8f7";
}

.icon-hourglass:before {
  content: "\e7f8";
}

.icon-shopping-fill:before {
  content: "\e8f8";
}

.icon-bulb:before {
  content: "\e7f9";
}

.icon-skin-fill:before {
  content: "\e8f9";
}

.icon-experiment:before {
  content: "\e7fa";
}

.icon-video-fill:before {
  content: "\e8fa";
}

.icon-bell:before {
  content: "\e7fb";
}

.icon-sound-fill:before {
  content: "\e8fb";
}

.icon-trophy:before {
  content: "\e7fc";
}

.icon-bulb-fill:before {
  content: "\e8fc";
}

.icon-rest:before {
  content: "\e7fd";
}

.icon-bell-fill:before {
  content: "\e8fd";
}

.icon-USB:before {
  content: "\e7fe";
}

.icon-filter-fill:before {
  content: "\e8fe";
}

.icon-skin:before {
  content: "\e7ff";
}

.icon-fire-fill:before {
  content: "\e8ff";
}

.icon-home:before {
  content: "\e800";
}

.icon-funnelplot-fill:before {
  content: "\e900";
}

.icon-bank:before {
  content: "\e801";
}

.icon-gift-fill:before {
  content: "\e901";
}

.icon-filter:before {
  content: "\e802";
}

.icon-hourglass-fill:before {
  content: "\e902";
}

.icon-funnelplot:before {
  content: "\e803";
}

.icon-home-fill:before {
  content: "\e903";
}

.icon-like:before {
  content: "\e804";
}

.icon-trophy-fill:before {
  content: "\e904";
}

.icon-unlike:before {
  content: "\e805";
}

.icon-location-fill:before {
  content: "\e905";
}

.icon-unlock:before {
  content: "\e806";
}

.icon-cloud-fill:before {
  content: "\e906";
}

.icon-lock:before {
  content: "\e807";
}

.icon-customerservice-fill:before {
  content: "\e907";
}

.icon-customerservice:before {
  content: "\e808";
}

.icon-experiment-fill:before {
  content: "\e908";
}

.icon-flag:before {
  content: "\e809";
}

.icon-eye-fill:before {
  content: "\e909";
}

.icon-moneycollect:before {
  content: "\e80a";
}

.icon-like-fill:before {
  content: "\e90a";
}

.icon-medicinebox:before {
  content: "\e80b";
}

.icon-lock-fill:before {
  content: "\e90b";
}

.icon-shop:before {
  content: "\e80c";
}

.icon-unlike-fill:before {
  content: "\e90c";
}

.icon-rocket:before {
  content: "\e80d";
}

.icon-star-fill:before {
  content: "\e90d";
}

.icon-shopping:before {
  content: "\e80e";
}

.icon-unlock-fill:before {
  content: "\e90e";
}

.icon-folder:before {
  content: "\e80f";
}

.icon-alert-fill:before {
  content: "\e90f";
}

.icon-folder-open:before {
  content: "\e810";
}

.icon-api-fill:before {
  content: "\e910";
}

.icon-folder-add:before {
  content: "\e811";
}

.icon-highlight-fill:before {
  content: "\e911";
}

.icon-deploymentunit:before {
  content: "\e812";
}

.icon-phone-fill:before {
  content: "\e912";
}

.icon-accountbook:before {
  content: "\e813";
}

.icon-edit-fill:before {
  content: "\e913";
}

.icon-contacts:before {
  content: "\e814";
}

.icon-pushpin-fill:before {
  content: "\e914";
}

.icon-carryout:before {
  content: "\e815";
}

.icon-rocket-fill:before {
  content: "\e915";
}

.icon-calendar-check:before {
  content: "\e816";
}

.icon-thunderbolt-fill:before {
  content: "\e916";
}

.icon-calendar:before {
  content: "\e817";
}

.icon-tag-fill:before {
  content: "\e917";
}

.icon-scan:before {
  content: "\e818";
}

.icon-wrench-fill:before {
  content: "\e918";
}

.icon-select:before {
  content: "\e819";
}

.icon-tags-fill:before {
  content: "\e919";
}

.icon-boxplot:before {
  content: "\e81a";
}

.icon-bank-fill:before {
  content: "\e91a";
}

.icon-build:before {
  content: "\e81b";
}

.icon-camera-fill:before {
  content: "\e91b";
}

.icon-sliders:before {
  content: "\e81c";
}

.icon-error-fill:before {
  content: "\e91c";
}

.icon-laptop:before {
  content: "\e81d";
}

.icon-crown-fill:before {
  content: "\e91d";
}

.icon-barcode:before {
  content: "\e81e";
}

.icon-mail-fill:before {
  content: "\e91e";
}

.icon-camera:before {
  content: "\e81f";
}

.icon-car-fill:before {
  content: "\e91f";
}

.icon-cluster:before {
  content: "\e820";
}

.icon-printer-fill:before {
  content: "\e920";
}

.icon-gateway:before {
  content: "\e821";
}

.icon-shop-fill:before {
  content: "\e921";
}

.icon-car:before {
  content: "\e822";
}

.icon-setting-fill:before {
  content: "\e922";
}

.icon-printer:before {
  content: "\e823";
}

.icon-USB-fill:before {
  content: "\e923";
}

.icon-read:before {
  content: "\e824";
}

.icon-golden-fill:before {
  content: "\e924";
}

.icon-cloud-server:before {
  content: "\e825";
}

.icon-build-fill:before {
  content: "\e925";
}

.icon-cloud-upload:before {
  content: "\e826";
}

.icon-boxplot-fill:before {
  content: "\e926";
}

.icon-cloud:before {
  content: "\e827";
}

.icon-sliders-fill:before {
  content: "\e927";
}

.icon-cloud-download:before {
  content: "\e828";
}

.icon-alibaba:before {
  content: "\e928";
}

.icon-cloud-sync:before {
  content: "\e829";
}

.icon-antdesign:before {
  content: "\e929";
}

.icon-video:before {
  content: "\e82a";
}

.icon-ant-cloud:before {
  content: "\e92a";
}

.icon-notification:before {
  content: "\e82b";
}

.icon-behance:before {
  content: "\e92b";
}

.icon-sound:before {
  content: "\e82c";
}

.icon-googleplus:before {
  content: "\e92c";
}

.icon-radarchart:before {
  content: "\e82d";
}

.icon-medium:before {
  content: "\e92d";
}

.icon-qrcode:before {
  content: "\e82e";
}

.icon-google:before {
  content: "\e92e";
}

.icon-fund:before {
  content: "\e82f";
}

.icon-IE:before {
  content: "\e92f";
}

.icon-image:before {
  content: "\e830";
}

.icon-amazon:before {
  content: "\e930";
}

.icon-mail:before {
  content: "\e831";
}

.icon-slack:before {
  content: "\e931";
}

.icon-table:before {
  content: "\e832";
}

.icon-alipay:before {
  content: "\e932";
}

.icon-idcard:before {
  content: "\e833";
}

.icon-taobao:before {
  content: "\e933";
}

.icon-creditcard:before {
  content: "\e834";
}

.icon-zhihu:before {
  content: "\e934";
}

.icon-heart:before {
  content: "\e835";
}

.icon-HTML:before {
  content: "\e935";
}

.icon-block:before {
  content: "\e836";
}

.icon-linkedin:before {
  content: "\e936";
}

.icon-error:before {
  content: "\e837";
}

.icon-yahoo:before {
  content: "\e937";
}

.icon-star:before {
  content: "\e838";
}

.icon-facebook:before {
  content: "\e938";
}

.icon-gold:before {
  content: "\e839";
}

.icon-skype:before {
  content: "\e939";
}

.icon-heatmap:before {
  content: "\e83a";
}

.icon-CodeSandbox:before {
  content: "\e93a";
}

.icon-wifi:before {
  content: "\e83b";
}

.icon-chrome:before {
  content: "\e93b";
}

.icon-attachment:before {
  content: "\e83c";
}

.icon-codepen:before {
  content: "\e93c";
}

.icon-edit:before {
  content: "\e83d";
}

.icon-aliwangwang:before {
  content: "\e93d";
}

.icon-key:before {
  content: "\e83e";
}

.icon-apple:before {
  content: "\e93e";
}

.icon-api:before {
  content: "\e83f";
}

.icon-android:before {
  content: "\e93f";
}

.icon-disconnect:before {
  content: "\e840";
}

.icon-sketch:before {
  content: "\e940";
}

.icon-highlight:before {
  content: "\e841";
}

.icon-Gitlab:before {
  content: "\e941";
}

.icon-monitor:before {
  content: "\e842";
}

.icon-dribbble:before {
  content: "\e942";
}

.icon-link:before {
  content: "\e843";
}

.icon-instagram:before {
  content: "\e943";
}

.icon-man:before {
  content: "\e844";
}

.icon-reddit:before {
  content: "\e944";
}

.icon-percentage:before {
  content: "\e845";
}

.icon-windows:before {
  content: "\e945";
}

.icon-pushpin:before {
  content: "\e846";
}

.icon-yuque:before {
  content: "\e946";
}

.icon-phone:before {
  content: "\e847";
}

.icon-Youtube:before {
  content: "\e947";
}

.icon-shake:before {
  content: "\e848";
}

.icon-Gitlab-fill:before {
  content: "\e948";
}

.icon-tag:before {
  content: "\e849";
}

.icon-dropbox:before {
  content: "\e949";
}

.icon-wrench:before {
  content: "\e84a";
}

.icon-dingtalk:before {
  content: "\e94a";
}

.icon-tags:before {
  content: "\e84b";
}

.icon-android-fill:before {
  content: "\e94b";
}

.icon-scissor:before {
  content: "\e84c";
}

.icon-apple-fill:before {
  content: "\e94c";
}

.icon-mr:before {
  content: "\e84d";
}

.icon-HTML-fill:before {
  content: "\e94d";
}

.icon-share:before {
  content: "\e84e";
}

.icon-windows-fill:before {
  content: "\e94e";
}

.icon-branches:before {
  content: "\e84f";
}

.icon-QQ:before {
  content: "\e94f";
}

.icon-fork:before {
  content: "\e850";
}

.icon-twitter:before {
  content: "\e950";
}

.icon-shrink:before {
  content: "\e851";
}

.icon-skype-fill:before {
  content: "\e951";
}

.icon-arrawsalt:before {
  content: "\e852";
}

.icon-weibo:before {
  content: "\e952";
}

.icon-verticalright:before {
  content: "\e853";
}

.icon-yuque-fill:before {
  content: "\e953";
}

.icon-verticalleft:before {
  content: "\e854";
}

.icon-Youtube-fill:before {
  content: "\e954";
}

.icon-right:before {
  content: "\e855";
}

.icon-yahoo-fill:before {
  content: "\e955";
}

.icon-left:before {
  content: "\e856";
}

.icon-wechat-fill:before {
  content: "\e956";
}

.icon-up:before {
  content: "\e857";
}

.icon-chrome-fill:before {
  content: "\e957";
}

.icon-down:before {
  content: "\e858";
}

.icon-alipay-circle-fill:before {
  content: "\e958";
}

.icon-fullscreen:before {
  content: "\e859";
}

.icon-aliwangwang-fill:before {
  content: "\e959";
}

.icon-fullscreen-exit:before {
  content: "\e85a";
}

.icon-behance-circle-fill:before {
  content: "\e95a";
}

.icon-doubleleft:before {
  content: "\e85b";
}

.icon-amazon-circle-fill:before {
  content: "\e95b";
}

.icon-doubleright:before {
  content: "\e85c";
}

.icon-codepen-circle-fill:before {
  content: "\e95c";
}

.icon-arrowright:before {
  content: "\e85d";
}

.icon-CodeSandbox-circle-f:before {
  content: "\e95d";
}

.icon-arrowup:before {
  content: "\e85e";
}

.icon-dropbox-circle-fill:before {
  content: "\e95e";
}

.icon-arrowleft:before {
  content: "\e85f";
}

.icon-github-fill:before {
  content: "\e95f";
}

.icon-arrowdown:before {
  content: "\e860";
}

.icon-dribbble-circle-fill:before {
  content: "\e960";
}

.icon-upload:before {
  content: "\e861";
}

.icon-googleplus-circle-f:before {
  content: "\e961";
}

.icon-colum-height:before {
  content: "\e862";
}

.icon-medium-circle-fill:before {
  content: "\e962";
}

.icon-vertical-align-botto:before {
  content: "\e863";
}

.icon-QQ-circle-fill:before {
  content: "\e963";
}

.icon-vertical-align-middl:before {
  content: "\e864";
}

.icon-IE-circle-fill:before {
  content: "\e964";
}

.icon-totop:before {
  content: "\e865";
}

.icon-google-circle-fill:before {
  content: "\e965";
}

.icon-vertical-align-top:before {
  content: "\e866";
}

.icon-dingtalk-circle-fill:before {
  content: "\e966";
}

.icon-download:before {
  content: "\e867";
}

.icon-sketch-circle-fill:before {
  content: "\e967";
}

.icon-sort-descending:before {
  content: "\e868";
}

.icon-slack-circle-fill:before {
  content: "\e968";
}

.icon-sort-ascending:before {
  content: "\e869";
}

.icon-twitter-circle-fill:before {
  content: "\e969";
}

.icon-fall:before {
  content: "\e86a";
}

.icon-taobao-circle-fill:before {
  content: "\e96a";
}

.icon-swap:before {
  content: "\e86b";
}

.icon-weibo-circle-fill:before {
  content: "\e96b";
}

.icon-stock:before {
  content: "\e86c";
}

.icon-zhihu-circle-fill:before {
  content: "\e96c";
}

.icon-rise:before {
  content: "\e86d";
}

.icon-reddit-circle-fill:before {
  content: "\e96d";
}

.icon-indent:before {
  content: "\e86e";
}

.icon-alipay-square-fill:before {
  content: "\e96e";
}

.icon-outdent:before {
  content: "\e86f";
}

.icon-dingtalk-square-fill:before {
  content: "\e96f";
}

.icon-menu:before {
  content: "\e870";
}

.icon-CodeSandbox-square-f:before {
  content: "\e970";
}

.icon-unorderedlist:before {
  content: "\e871";
}

.icon-behance-square-fill:before {
  content: "\e971";
}

.icon-orderedlist:before {
  content: "\e872";
}

.icon-amazon-square-fill:before {
  content: "\e972";
}

.icon-align-right:before {
  content: "\e873";
}

.icon-codepen-square-fill:before {
  content: "\e973";
}

.icon-align-center:before {
  content: "\e874";
}

.icon-dribbble-square-fill:before {
  content: "\e974";
}

.icon-align-left:before {
  content: "\e875";
}

.icon-dropbox-square-fill:before {
  content: "\e975";
}

.icon-pic-center:before {
  content: "\e876";
}

.icon-facebook-fill:before {
  content: "\e976";
}

.icon-pic-right:before {
  content: "\e877";
}

.icon-googleplus-square-f:before {
  content: "\e977";
}

.icon-pic-left:before {
  content: "\e878";
}

.icon-google-square-fill:before {
  content: "\e978";
}

.icon-bold:before {
  content: "\e879";
}

.icon-instagram-fill:before {
  content: "\e979";
}

.icon-font-colors:before {
  content: "\e87a";
}

.icon-IE-square-fill:before {
  content: "\e97a";
}

.icon-exclaimination:before {
  content: "\e87b";
}

.icon-medium-square-fill:before {
  content: "\e97b";
}

.icon-font-size:before {
  content: "\e87c";
}

.icon-linkedin-fill:before {
  content: "\e97c";
}

.icon-check-circle:before {
  content: "\e77d";
}

.icon-infomation:before {
  content: "\e87d";
}

.icon-QQ-square-fill:before {
  content: "\e97d";
}

.icon-CI:before {
  content: "\e77e";
}

.icon-line-height:before {
  content: "\e87e";
}

.icon-reddit-square-fill:before {
  content: "\e97e";
}

.icon-Dollar:before {
  content: "\e77f";
}

.icon-strikethrough:before {
  content: "\e87f";
}

.icon-twitter-square-fill:before {
  content: "\e97f";
}

.icon-compass:before {
  content: "\e780";
}

.icon-underline:before {
  content: "\e880";
}

.icon-sketch-square-fill:before {
  content: "\e980";
}

.icon-close-circle:before {
  content: "\e781";
}

.icon-number:before {
  content: "\e881";
}

.icon-slack-square-fill:before {
  content: "\e981";
}

.icon-frown:before {
  content: "\e782";
}

.icon-italic:before {
  content: "\e882";
}

.icon-taobao-square-fill:before {
  content: "\e982";
}

.icon-info-circle:before {
  content: "\e783";
}

.icon-code:before {
  content: "\e883";
}

.icon-weibo-square-fill:before {
  content: "\e983";
}

.icon-left-circle:before {
  content: "\e784";
}

.icon-column-width:before {
  content: "\e884";
}

.icon-zhihu-square-fill:before {
  content: "\e984";
}

.icon-down-circle:before {
  content: "\e785";
}

.icon-check:before {
  content: "\e885";
}

.icon-zoomout:before {
  content: "\e985";
}

.icon-EURO:before {
  content: "\e786";
}

.icon-ellipsis:before {
  content: "\e886";
}

.icon-apartment:before {
  content: "\e986";
}

.icon-copyright:before {
  content: "\e787";
}

.icon-dash:before {
  content: "\e887";
}

.icon-audio:before {
  content: "\e987";
}

.icon-minus-circle:before {
  content: "\e788";
}

.icon-close:before {
  content: "\e888";
}

.icon-audio-fill:before {
  content: "\e988";
}

.icon-meh:before {
  content: "\e789";
}

.icon-enter:before {
  content: "\e889";
}

.icon-robot:before {
  content: "\e989";
}

.icon-plus-circle:before {
  content: "\e78a";
}

.icon-line:before {
  content: "\e88a";
}

.icon-zoomin:before {
  content: "\e98a";
}

.icon-play-circle:before {
  content: "\e78b";
}

.icon-minus:before {
  content: "\e88b";
}

.icon-robot-fill:before {
  content: "\e98b";
}

.icon-question-circle:before {
  content: "\e78c";
}

.icon-question:before {
  content: "\e88c";
}

.icon-bug-fill:before {
  content: "\e98c";
}

.icon-Pound:before {
  content: "\e78d";
}

.icon-rollback:before {
  content: "\e88d";
}

.icon-bug:before {
  content: "\e98d";
}

.icon-right-circle:before {
  content: "\e78e";
}

.icon-small-dash:before {
  content: "\e88e";
}

.icon-audiostatic:before {
  content: "\e98e";
}

.icon-smile:before {
  content: "\e78f";
}

.icon-pause:before {
  content: "\e88f";
}

.icon-comment:before {
  content: "\e98f";
}

.icon-trademark:before {
  content: "\e790";
}

.icon-bg-colors:before {
  content: "\e890";
}

.icon-signal-fill:before {
  content: "\e990";
}

.icon-time-circle:before {
  content: "\e791";
}

.icon-crown:before {
  content: "\e891";
}

.icon-verified:before {
  content: "\e991";
}

.icon-timeout:before {
  content: "\e792";
}

.icon-drag:before {
  content: "\e892";
}

.icon-shortcut-fill:before {
  content: "\e992";
}

.icon-earth:before {
  content: "\e793";
}

.icon-desktop:before {
  content: "\e893";
}

.icon-videocameraadd:before {
  content: "\e993";
}

.icon-YUAN:before {
  content: "\e794";
}

.icon-gift:before {
  content: "\e894";
}

.icon-switchuser:before {
  content: "\e994";
}

.icon-up-circle:before {
  content: "\e795";
}

.icon-stop:before {
  content: "\e895";
}

.icon-whatsapp:before {
  content: "\e995";
}

.icon-warning-circle:before {
  content: "\e796";
}

.icon-fire:before {
  content: "\e896";
}

.icon-appstoreadd:before {
  content: "\e996";
}

.icon-sync:before {
  content: "\e797";
}

.icon-thunderbolt:before {
  content: "\e897";
}

.icon-caret-down:before {
  content: "\e997";
}

.icon-transaction:before {
  content: "\e798";
}

.icon-check-circle-fill:before {
  content: "\e898";
}

.icon-backward:before {
  content: "\e998";
}

.icon-undo:before {
  content: "\e799";
}

.icon-left-circle-fill:before {
  content: "\e899";
}

.icon-caret-up:before {
  content: "\e999";
}

.icon-redo:before {
  content: "\e79a";
}

.icon-down-circle-fill:before {
  content: "\e89a";
}

.icon-caret-right:before {
  content: "\e99a";
}

.icon-reload:before {
  content: "\e79b";
}

.icon-minus-circle-fill:before {
  content: "\e89b";
}

.icon-caret-left:before {
  content: "\e99b";
}

.icon-reloadtime:before {
  content: "\e79c";
}

.icon-close-circle-fill:before {
  content: "\e89c";
}

.icon-fast-backward:before {
  content: "\e99c";
}

.icon-message:before {
  content: "\e79d";
}

.icon-info-circle-fill:before {
  content: "\e89d";
}

.icon-forward:before {
  content: "\e99d";
}

.icon-dashboard:before {
  content: "\e79e";
}

.icon-up-circle-fill:before {
  content: "\e89e";
}

.icon-fast-forward:before {
  content: "\e99e";
}

.icon-issuesclose:before {
  content: "\e79f";
}

.icon-right-circle-fill:before {
  content: "\e89f";
}

.icon-search:before {
  content: "\e99f";
}

.icon-poweroff:before {
  content: "\e7a0";
}

.icon-plus-circle-fill:before {
  content: "\e8a0";
}

.icon-retweet:before {
  content: "\e9a0";
}

.icon-logout:before {
  content: "\e7a1";
}

.icon-question-circle-fill:before {
  content: "\e8a1";
}

.icon-login:before {
  content: "\e9a1";
}

.icon-piechart:before {
  content: "\e7a2";
}

.icon-EURO-circle-fill:before {
  content: "\e8a2";
}

.icon-step-backward:before {
  content: "\e9a2";
}

.icon-setting:before {
  content: "\e7a3";
}

.icon-frown-fill:before {
  content: "\e8a3";
}

.icon-step-forward:before {
  content: "\e9a3";
}

.icon-eye:before {
  content: "\e7a4";
}

.icon-copyright-circle-fil:before {
  content: "\e8a4";
}

.icon-swap-right:before {
  content: "\e9a4";
}

.icon-location:before {
  content: "\e7a5";
}

.icon-CI-circle-fill:before {
  content: "\e8a5";
}

.icon-swap-left:before {
  content: "\e9a5";
}

.icon-edit-square:before {
  content: "\e7a6";
}

.icon-compass-fill:before {
  content: "\e8a6";
}

.icon-woman:before {
  content: "\e9a6";
}

.icon-export:before {
  content: "\e7a7";
}

.icon-Dollar-circle-fill:before {
  content: "\e8a7";
}

.icon-plus:before {
  content: "\e9a7";
}

.icon-save:before {
  content: "\e7a8";
}

.icon-poweroff-circle-fill:before {
  content: "\e8a8";
}

.icon-eyeclose-fill:before {
  content: "\e9a8";
}

.icon-Import:before {
  content: "\e7a9";
}

.icon-meh-fill:before {
  content: "\e8a9";
}

.icon-eye-close:before {
  content: "\e9a9";
}

.icon-appstore:before {
  content: "\e7aa";
}

.icon-play-circle-fill:before {
  content: "\e8aa";
}

.icon-clear:before {
  content: "\e9aa";
}

.icon-close-square:before {
  content: "\e7ab";
}

.icon-Pound-circle-fill:before {
  content: "\e8ab";
}

.icon-collapse:before {
  content: "\e9ab";
}

.icon-down-square:before {
  content: "\e7ac";
}

.icon-smile-fill:before {
  content: "\e8ac";
}

.icon-expand:before {
  content: "\e9ac";
}

.icon-layout:before {
  content: "\e7ad";
}

.icon-stop-fill:before {
  content: "\e8ad";
}

.icon-deletecolumn:before {
  content: "\e9ad";
}

.icon-left-square:before {
  content: "\e7ae";
}

.icon-warning-circle-fill:before {
  content: "\e8ae";
}

.icon-merge-cells:before {
  content: "\e9ae";
}

.icon-play-square:before {
  content: "\e7af";
}

.icon-time-circle-fill:before {
  content: "\e8af";
}

.icon-subnode:before {
  content: "\e9af";
}

.icon-control:before {
  content: "\e7b0";
}

.icon-trademark-circle-fil:before {
  content: "\e8b0";
}

.icon-rotate-left:before {
  content: "\e9b0";
}

.icon-codelibrary:before {
  content: "\e7b1";
}

.icon-YUAN-circle-fill:before {
  content: "\e8b1";
}

.icon-rotate-right:before {
  content: "\e9b1";
}

.icon-minus-square:before {
  content: "\e7b2";
}

.icon-heart-fill:before {
  content: "\e8b2";
}

.icon-insertrowbelow:before {
  content: "\e9b2";
}

.icon-plus-square:before {
  content: "\e7b3";
}

.icon-piechart-circle-fil:before {
  content: "\e8b3";
}

.icon-insertrowabove:before {
  content: "\e9b3";
}

.icon-right-square:before {
  content: "\e7b4";
}

.icon-dashboard-fill:before {
  content: "\e8b4";
}

.icon-table1:before {
  content: "\e9b4";
}

.icon-project:before {
  content: "\e7b5";
}

.icon-message-fill:before {
  content: "\e8b5";
}

.icon-solit-cells:before {
  content: "\e9b5";
}

.icon-wallet:before {
  content: "\e7b6";
}

.icon-check-square-fill:before {
  content: "\e8b6";
}

.icon-formatpainter:before {
  content: "\e9b6";
}

.icon-up-square:before {
  content: "\e7b7";
}

.icon-down-square-fill:before {
  content: "\e8b7";
}

.icon-insertrowright:before {
  content: "\e9b7";
}

.icon-calculator:before {
  content: "\e7b8";
}

.icon-minus-square-fill:before {
  content: "\e8b8";
}

.icon-formatpainter-fill:before {
  content: "\e9b8";
}

.icon-interation:before {
  content: "\e7b9";
}

.icon-close-square-fill:before {
  content: "\e8b9";
}

.icon-insertrowleft:before {
  content: "\e9b9";
}

.icon-check-square:before {
  content: "\e7ba";
}

.icon-codelibrary-fill:before {
  content: "\e8ba";
}

.icon-translate:before {
  content: "\e9ba";
}

.icon-border:before {
  content: "\e7bb";
}

.icon-left-square-fill:before {
  content: "\e8bb";
}

.icon-deleterow:before {
  content: "\e9bb";
}

.icon-border-outer:before {
  content: "\e7bc";
}

.icon-play-square-fill:before {
  content: "\e8bc";
}

.icon-sisternode:before {
  content: "\e9bc";
}

.icon-border-top:before {
  content: "\e7bd";
}

.icon-up-square-fill:before {
  content: "\e8bd";
}

.icon-Field-number:before {
  content: "\e9bd";
}

.icon-border-bottom:before {
  content: "\e7be";
}

.icon-right-square-fill:before {
  content: "\e8be";
}

.icon-Field-String:before {
  content: "\e9be";
}

.icon-border-left:before {
  content: "\e7bf";
}

.icon-plus-square-fill:before {
  content: "\e8bf";
}

.icon-Function:before {
  content: "\e9bf";
}

.icon-border-right:before {
  content: "\e7c0";
}

.icon-accountbook-fill:before {
  content: "\e8c0";
}

.icon-Field-time:before {
  content: "\e9c0";
}

.icon-border-inner:before {
  content: "\e7c1";
}

.icon-carryout-fill:before {
  content: "\e8c1";
}

.icon-GIF:before {
  content: "\e9c1";
}

.icon-border-verticle:before {
  content: "\e7c2";
}

.icon-calendar-fill:before {
  content: "\e8c2";
}

.icon-Partition:before {
  content: "\e9c2";
}

.icon-border-horizontal:before {
  content: "\e7c3";
}

.icon-calculator-fill:before {
  content: "\e8c3";
}

.icon-index:before {
  content: "\e9c3";
}

.icon-radius-bottomleft:before {
  content: "\e7c4";
}

.icon-interation-fill:before {
  content: "\e8c4";
}

.icon-Storedprocedure:before {
  content: "\e9c4";
}

.icon-radius-bottomright:before {
  content: "\e7c5";
}

.icon-project-fill:before {
  content: "\e8c5";
}

.icon-Field-Binary:before {
  content: "\e9c5";
}

.icon-radius-upleft:before {
  content: "\e7c6";
}

.icon-detail-fill:before {
  content: "\e8c6";
}

.icon-Console-SQL:before {
  content: "\e9c6";
}

.icon-radius-upright:before {
  content: "\e7c7";
}

.icon-save-fill:before {
  content: "\e8c7";
}

.icon-icon-test:before {
  content: "\e9c7";
}

.icon-radius-setting:before {
  content: "\e7c8";
}

.icon-wallet-fill:before {
  content: "\e8c8";
}

.icon-aim:before {
  content: "\e9c8";
}

.icon-adduser:before {
  content: "\e7c9";
}

.icon-control-fill:before {
  content: "\e8c9";
}

.icon-compress:before {
  content: "\e9c9";
}

.icon-deleteteam:before {
  content: "\e7ca";
}

.icon-layout-fill:before {
  content: "\e8ca";
}

.icon-expend:before {
  content: "\e9ca";
}

.icon-deleteuser:before {
  content: "\e7cb";
}

.icon-appstore-fill:before {
  content: "\e8cb";
}

.icon-folder-view:before {
  content: "\e9cb";
}

.icon-addteam:before {
  content: "\e7cc";
}

.icon-mobile-fill:before {
  content: "\e8cc";
}

.icon-file-GIF:before {
  content: "\e9cc";
}

.icon-user:before {
  content: "\e7cd";
}

.icon-tablet-fill:before {
  content: "\e8cd";
}

.icon-group:before {
  content: "\e9cd";
}

.icon-team:before {
  content: "\e7ce";
}

.icon-book-fill:before {
  content: "\e8ce";
}

.icon-send:before {
  content: "\e9ce";
}

.icon-areachart:before {
  content: "\e7cf";
}

.icon-redenvelope-fill:before {
  content: "\e8cf";
}

.icon-Report:before {
  content: "\e9cf";
}

.icon-linechart:before {
  content: "\e7d0";
}

.icon-safetycertificate-f:before {
  content: "\e8d0";
}

.icon-View:before {
  content: "\e9d0";
}

.icon-barchart:before {
  content: "\e7d1";
}

.icon-propertysafety-fill:before {
  content: "\e8d1";
}

.icon-shortcut:before {
  content: "\e9d1";
}

.icon-pointmap:before {
  content: "\e7d2";
}

.icon-insurance-fill:before {
  content: "\e8d2";
}

.icon-ungroup:before {
  content: "\e9d2";
}

.icon-container:before {
  content: "\e7d3";
}

.icon-securityscan-fill:before {
  content: "\e8d3";
}

.icon-database:before {
  content: "\e7d4";
}

.icon-file-exclamation-fil:before {
  content: "\e8d4";
}

.icon-sever:before {
  content: "\e7d5";
}

.icon-file-add-fill:before {
  content: "\e8d5";
}

.icon-mobile:before {
  content: "\e7d6";
}

.icon-file-fill:before {
  content: "\e8d6";
}

.icon-tablet:before {
  content: "\e7d7";
}

.icon-file-excel-fill:before {
  content: "\e8d7";
}

.icon-redenvelope:before {
  content: "\e7d8";
}

.icon-file-markdown-fill:before {
  content: "\e8d8";
}

.icon-book:before {
  content: "\e7d9";
}

.icon-file-text-fill:before {
  content: "\e8d9";
}

.icon-filedone:before {
  content: "\e7da";
}

.icon-file-ppt-fill:before {
  content: "\e8da";
}

.icon-reconciliation:before {
  content: "\e7db";
}

.icon-file-unknown-fill:before {
  content: "\e8db";
}

.icon-file-exception:before {
  content: "\e7dc";
}

.icon-file-word-fill:before {
  content: "\e8dc";
}

.icon-filesync:before {
  content: "\e7dd";
}

.icon-file-zip-fill:before {
  content: "\e8dd";
}

.icon-filesearch:before {
  content: "\e7de";
}

.icon-file-pdf-fill:before {
  content: "\e8de";
}

.icon-solution:before {
  content: "\e7df";
}

.icon-file-image-fill:before {
  content: "\e8df";
}

.icon-fileprotect:before {
  content: "\e7e0";
}

.icon-diff-fill:before {
  content: "\e8e0";
}

.icon-file-add:before {
  content: "\e7e1";
}

.icon-file-copy-fill:before {
  content: "\e8e1";
}

.icon-file-excel:before {
  content: "\e7e2";
}

.icon-snippets-fill:before {
  content: "\e8e2";
}

.icon-file-exclamation:before {
  content: "\e7e3";
}

.icon-batchfolding-fill:before {
  content: "\e8e3";
}

.icon-file-pdf:before {
  content: "\e7e4";
}

.icon-reconciliation-fill:before {
  content: "\e8e4";
}

.icon-file-image:before {
  content: "\e7e5";
}

.icon-folder-add-fill:before {
  content: "\e8e5";
}

.icon-file-markdown:before {
  content: "\e7e6";
}

.icon-folder-fill:before {
  content: "\e8e6";
}

.icon-file-unknown:before {
  content: "\e7e7";
}

.icon-folder-open-fill:before {
  content: "\e8e7";
}

