import{c as jt}from"./@intlify-D8kAWvSi.js";var pe={};(function(Kt){(function(){typeof Math.sgn>"u"&&(Math.sgn=function(d){return d==0?0:d>0?1:-1});var J={subtract:function(d,E){return{x:d.x-E.x,y:d.y-E.y}},dotProduct:function(d,E){return d.x*E.x+d.y*E.y},square:function(d){return Math.sqrt(d.x*d.x+d.y*d.y)},scale:function(d,E){return{x:d.x*E,y:d.y*E}}},c=64,V=Math.pow(2,-65),it=function(d,E){for(var N=[],B=et(d,E),Y=E.length-1,n=2*Y-1,o=h(B,n,N,0),t=J.subtract(d,E[0]),p=J.square(t),v=0,I=0;I<o;I++){t=J.subtract(d,y(E,Y,N[I],null,null));var F=J.square(t);F<p&&(p=F,v=N[I])}return t=J.subtract(d,E[Y]),F=J.square(t),F<p&&(p=F,v=1),{location:v,distance:p}},K=function(d,E){var N=it(d,E);return{point:y(E,E.length-1,N.location,null,null),location:N.location}},et=function(d,E){for(var N=E.length-1,B=2*N-1,Y=[],n=[],o=[],t=[],p=[[1,.6,.3,.1],[.4,.6,.6,.4],[.1,.3,.6,1]],v=0;v<=N;v++)Y[v]=J.subtract(E[v],d);for(var v=0;v<=N-1;v++)n[v]=J.subtract(E[v+1],E[v]),n[v]=J.scale(n[v],3);for(var I=0;I<=N-1;I++)for(var F=0;F<=N;F++)o[I]||(o[I]=[]),o[I][F]=J.dotProduct(n[I],Y[F]);for(v=0;v<=B;v++)t[v]||(t[v]=[]),t[v].y=0,t[v].x=parseFloat(v)/B;for(var X=N,R=N-1,W=0;W<=X+R;W++){var at=Math.max(0,W-R),st=Math.min(W,X);for(v=at;v<=st;v++){var nt=W-v;t[v+nt].y+=o[nt][v]*p[nt][v]}}return t},h=function(d,E,N,B){var Y=[],n=[],o,t,p=[],v=[];switch(T(d,E)){case 0:return 0;case 1:{if(B>=c)return N[0]=(d[0].x+d[E].x)/2,1;if(a(d,E))return N[0]=i(d,E),1;break}}y(d,E,.5,Y,n),o=h(Y,E,p,B+1),t=h(n,E,v,B+1);for(var I=0;I<o;I++)N[I]=p[I];for(var I=0;I<t;I++)N[I+o]=v[I];return o+t},T=function(d,E){var N=0,B,Y;B=Y=Math.sgn(d[0].y);for(var n=1;n<=E;n++)B=Math.sgn(d[n].y),B!=Y&&N++,Y=B;return N},a=function(d,E){var N,B,Y,n,o,t,p,v,I,F,X,R,W,at,st,nt;t=d[0].y-d[E].y,p=d[E].x-d[0].x,v=d[0].x*d[E].y-d[E].x*d[0].y;var rt,gt;rt=gt=0;for(var dt=1;dt<E;dt++){var lt=t*d[dt].x+p*d[dt].y+v;lt>rt?rt=lt:lt<gt&&(gt=lt)}return X=0,R=1,W=0,at=t,st=p,nt=v-rt,I=X*st-at*R,F=1/I,B=(R*nt-st*W)*F,at=t,st=p,nt=v-gt,I=X*st-at*R,F=1/I,Y=(R*nt-st*W)*F,n=Math.min(B,Y),o=Math.max(B,Y),N=o-n,N<V?1:0},i=function(d,E){var N=1,B=0,Y=d[E].x-d[0].x,n=d[E].y-d[0].y,o=d[0].x-0,t=d[0].y-0,p=Y*B-n*N,v=1/p,I=(Y*t-n*o)*v;return 0+N*I},y=function(d,E,N,B,Y){for(var n=[[]],o=0;o<=E;o++)n[0][o]=d[o];for(var t=1;t<=E;t++)for(var o=0;o<=E-t;o++)n[t]||(n[t]=[]),n[t][o]||(n[t][o]={}),n[t][o].x=(1-N)*n[t-1][o].x+N*n[t-1][o+1].x,n[t][o].y=(1-N)*n[t-1][o].y+N*n[t-1][o+1].y;if(B!=null)for(o=0;o<=E;o++)B[o]=n[o][0];if(Y!=null)for(o=0;o<=E;o++)Y[o]=n[E-o][o];return n[E][0]},D={},M=function(d){var E=D[d];if(!E){E=[];var N=function(){return function(F){return Math.pow(F,d)}},B=function(){return function(F){return Math.pow(1-F,d)}},Y=function(F){return function(X){return F}},n=function(){return function(F){return F}},o=function(){return function(F){return 1-F}},t=function(F){return function(X){for(var R=1,W=0;W<F.length;W++)R=R*F[W](X);return R}};E.push(new N);for(var p=1;p<d;p++){for(var v=[new Y(d)],I=0;I<d-p;I++)v.push(new n);for(var I=0;I<p;I++)v.push(new o);E.push(new t(v))}E.push(new B),D[d]=E}return E},C=function(d,E){for(var N=M(d.length-1),B=0,Y=0,n=0;n<d.length;n++)B=B+d[n].x*N[n](E),Y=Y+d[n].y*N[n](E);return{x:B,y:Y}},g=function(d,E){return Math.sqrt(Math.pow(d.x-E.x,2)+Math.pow(d.y-E.y,2))},e=function(d){return d[0].x===d[1].x&&d[0].y===d[1].y},k=function(d,E,N){if(e(d))return{point:d[0],location:E};for(var B=C(d,E),Y=0,n=E,o=N>0?1:-1,t=null;Y<Math.abs(N);)n+=.005*o,t=C(d,n),Y+=g(t,B),B=t;return{point:t,location:n}},L=function(d){var E=new Date().getTime();if(e(d))return 0;for(var N=C(d,0),B=0,Y=0,n=1,o=null;Y<1;)Y+=.005*n,o=C(d,Y),B+=g(o,N),N=o;return console.log("length",new Date().getTime()-E),B},U=function(d,E,N){return k(d,E,N).point},G=function(d,E,N){return k(d,E,N).location},P=function(d,E){var N=C(d,E),B=C(d.slice(0,d.length-1),E),Y=B.y-N.y,n=B.x-N.x;return Y===0?1/0:Math.atan(Y/n)},w=function(d,E,N){var B=k(d,E,N);return B.location>1&&(B.location=1),B.location<0&&(B.location=0),P(d,B.location)},r=function(d,E,N,B){B=B??0;var Y=k(d,E,B),n=P(d,Y.location),o=Math.atan(-1/n),t=N/2*Math.sin(o),p=N/2*Math.cos(o);return[{x:Y.point.x+p,y:Y.point.y+t},{x:Y.point.x-p,y:Y.point.y-t}]},f=function(d,E,N,B,Y){var n=B-E,o=d-N,t=d*(E-B)+E*(N-d),p=u(Y),v=[n*p[0][0]+o*p[1][0],n*p[0][1]+o*p[1][1],n*p[0][2]+o*p[1][2],n*p[0][3]+o*p[1][3]+t],I=b.apply(null,v),F=[];if(I!=null)for(var X=0;X<3;X++){var R=I[X],W=Math.pow(R,2),at=Math.pow(R,3),st=[p[0][0]*at+p[0][1]*W+p[0][2]*R+p[0][3],p[1][0]*at+p[1][1]*W+p[1][2]*R+p[1][3]],nt;N-d!==0?nt=(st[0]-d)/(N-d):nt=(st[1]-E)/(B-E),R>=0&&R<=1&&nt>=0&&nt<=1&&F.push(st)}return F},l=function(d,E,N,B,Y){var n=[];return n.push.apply(n,f(d,E,d+N,E,Y)),n.push.apply(n,f(d+N,E,d+N,E+B,Y)),n.push.apply(n,f(d+N,E+B,d,E+B,Y)),n.push.apply(n,f(d,E+B,d,E,Y)),n},_=function(d,E){var N=[];return N.push.apply(N,f(d.x,d.y,d.x+d.w,d.y,E)),N.push.apply(N,f(d.x+d.w,d.y,d.x+d.w,d.y+d.h,E)),N.push.apply(N,f(d.x+d.w,d.y+d.h,d.x,d.y+d.h,E)),N.push.apply(N,f(d.x,d.y+d.h,d.x,d.y,E)),N};function x(d,E){return[-d[0][E]+3*d[1][E]+-3*d[2][E]+d[3][E],3*d[0][E]-6*d[1][E]+3*d[2][E],-3*d[0][E]+3*d[1][E],d[0][E]]}function u(d){return[x(d,"x"),x(d,"y")]}function j(d){return d<0?-1:d>0?1:0}function b(d,E,N,B){var Y=E/d,n=N/d,o=B/d,t=(3*n-Math.pow(Y,2))/9,p=(9*Y*n-27*o-2*Math.pow(Y,3))/54,v=Math.pow(t,3)+Math.pow(p,2),I,F,X=[];if(v>=0)I=j(p+Math.sqrt(v))*Math.pow(Math.abs(p+Math.sqrt(v)),1/3),F=j(p-Math.sqrt(v))*Math.pow(Math.abs(p-Math.sqrt(v)),1/3),X[0]=-Y/3+(I+F),X[1]=-Y/3-(I+F)/2,X[2]=-Y/3-(I+F)/2,Math.abs(Math.sqrt(3)*(I-F)/2)!==0&&(X[1]=-1,X[2]=-1);else{var R=Math.acos(p/Math.sqrt(-Math.pow(t,3)));X[0]=2*Math.sqrt(-t)*Math.cos(R/3)-Y/3,X[1]=2*Math.sqrt(-t)*Math.cos((R+2*Math.PI)/3)-Y/3,X[2]=2*Math.sqrt(-t)*Math.cos((R+4*Math.PI)/3)-Y/3}for(var W=0;W<3;W++)(X[W]<0||X[W]>1)&&(X[W]=-1);return X}var A=this.jsBezier={distanceFromCurve:it,gradientAtPoint:P,gradientAtPointAlongCurveFrom:w,nearestPointOnCurve:K,pointOnCurve:C,pointAlongCurveFrom:U,perpendicularToCurveAt:r,locationAlongCurveFrom:G,getLength:L,lineIntersection:f,boxIntersection:l,boundingBoxIntersection:_,version:"0.9.0"};Kt.jsBezier=A}).call(typeof window<"u"?window:jt),(function(){var J=this,c=J.Biltong={version:"0.4.0"};Kt.Biltong=c;var V=function(a){return Object.prototype.toString.call(a)==="[object Array]"},it=function(a,i,y){return a=V(a)?a:[a.x,a.y],i=V(i)?i:[i.x,i.y],y(a,i)},K=c.gradient=function(a,i){return it(a,i,function(y,D){return D[0]==y[0]?D[1]>y[1]?1/0:-1/0:D[1]==y[1]?D[0]>y[0]?0:-0:(D[1]-y[1])/(D[0]-y[0])})};c.normal=function(a,i){return-1/K(a,i)},c.lineLength=function(a,i){return it(a,i,function(y,D){return Math.sqrt(Math.pow(D[1]-y[1],2)+Math.pow(D[0]-y[0],2))})};var et=c.quadrant=function(a,i){return it(a,i,function(y,D){return D[0]>y[0]||D[0]==y[0]?D[1]>y[1]?2:1:D[1]>y[1]?3:4})};c.theta=function(a,i){return it(a,i,function(y,D){var M=K(y,D),C=Math.atan(M),g=et(y,D);return(g==4||g==3)&&(C+=Math.PI),C<0&&(C+=2*Math.PI),C})},c.intersects=function(a,i){var y=a.x,D=a.x+a.w,M=a.y,C=a.y+a.h,g=i.x,e=i.x+i.w,k=i.y,L=i.y+i.h;return y<=g&&g<=D&&M<=k&&k<=C||y<=e&&e<=D&&M<=k&&k<=C||y<=g&&g<=D&&M<=L&&L<=C||y<=e&&g<=D&&M<=L&&L<=C||g<=y&&y<=e&&k<=M&&M<=L||g<=D&&D<=e&&k<=M&&M<=L||g<=y&&y<=e&&k<=C&&C<=L||g<=D&&y<=e&&k<=C&&C<=L},c.encloses=function(a,i,y){var D=a.x,M=a.x+a.w,C=a.y,g=a.y+a.h,e=i.x,k=i.x+i.w,L=i.y,U=i.y+i.h,G=function(P,w,r,f){return y?P<=w&&r>=f:P<w&&r>f};return G(D,e,M,k)&&G(C,L,g,U)};var h=[null,[1,-1],[1,1],[-1,1],[-1,-1]],T=[null,[-1,-1],[-1,1],[1,1],[1,-1]];c.pointOnLine=function(a,i,y){var D=K(a,i),M=et(a,i),C=y>0?h[M]:T[M],g=Math.atan(D),e=Math.abs(y*Math.sin(g))*C[1],k=Math.abs(y*Math.cos(g))*C[0];return{x:a.x+k,y:a.y+e}},c.perpendicularLineTo=function(a,i,y){var D=K(a,i),M=Math.atan(-1/D),C=y/2*Math.sin(M),g=y/2*Math.cos(M);return[{x:i.x+g,y:i.y+C},{x:i.x-g,y:i.y-C}]}}).call(typeof window<"u"?window:jt),(function(){function J(n,o,t,p,v,I,F,X){return new Touch({target:o,identifier:Y(),pageX:t,pageY:p,screenX:v,screenY:I,clientX:F||v,clientY:X||I})}function c(){var n=[];return Array.prototype.push.apply(n,arguments),n.item=function(o){return this[o]},n}function V(n,o,t,p,v,I,F,X){return c(J.apply(null,arguments))}var it=this,K=function(n,o,t){t=t||n.parentNode;for(var p=t.querySelectorAll(o),v=0;v<p.length;v++)if(p[v]===n)return!0;return!1},et=function(n){return typeof n=="string"||n.constructor===String?document.getElementById(n):n},h=function(n){return n.srcElement||n.target},T=function(n,o,t,p){if(p){if(typeof n.path<"u"&&n.path.indexOf)return{path:n.path,end:n.path.indexOf(t)};var v={path:[],end:-1},I=function(F){v.path.push(F),F===t?v.end=v.path.length-1:F.parentNode!=null&&I(F.parentNode)};return I(o),v}else return{path:[o],end:1}},a=function(n,o){for(var t=0,p=n.length;t<p&&n[t]!=o;t++);t<n.length&&n.splice(t,1)},i=1,y=function(n,o,t){var p=i++;return n.__ta=n.__ta||{},n.__ta[o]=n.__ta[o]||{},n.__ta[o][p]=t,t.__tauid=p,p},D=function(n,o,t){if(n.__ta&&n.__ta[o]&&delete n.__ta[o][t.__tauid],t.__taExtra){for(var p=0;p<t.__taExtra.length;p++)N(n,t.__taExtra[p][0],t.__taExtra[p][1]);t.__taExtra.length=0}t.__taUnstore&&t.__taUnstore()},M=function(n,o,t,p){if(n==null)return t;var v=n.split(","),I=function(F){I.__tauid=t.__tauid;var X=h(F),R=X,W=T(F,X,o,n!=null);if(W.end!=-1)for(var at=0;at<W.end;at++){R=W.path[at];for(var st=0;st<v.length;st++)K(R,v[st],o)&&t.apply(R,arguments)}};return C(t,p,I),I},C=function(n,o,t){n.__taExtra=n.__taExtra||[],n.__taExtra.push([o,t])},g=function(n,o,t,p){if(P&&r[o]){var v=M(p,n,t,r[o]);E(n,r[o],v,t)}o==="focus"&&n.getAttribute("tabindex")==null&&n.setAttribute("tabindex","1"),E(n,o,M(p,n,t,o),t)},e=function(n,o,t,p){if(n.__taSmartClicks==null){var v=function(X){n.__tad=x(X)},I=function(X){n.__tau=x(X)},F=function(X){if(n.__tad&&n.__tau&&n.__tad[0]===n.__tau[0]&&n.__tad[1]===n.__tau[1])for(var R=0;R<n.__taSmartClicks.length;R++)n.__taSmartClicks[R].apply(h(X),[X])};g(n,"mousedown",v,p),g(n,"mouseup",I,p),g(n,"click",F,p),n.__taSmartClicks=[]}n.__taSmartClicks.push(t),t.__taUnstore=function(){a(n.__taSmartClicks,t)}},k={tap:{touches:1,taps:1},dbltap:{touches:1,taps:2},contextmenu:{touches:2,taps:1}},L=function(n,o){return function(t,p,v,I){if(p=="contextmenu"&&w)g(t,p,v,I);else{if(t.__taTapHandler==null){var F=t.__taTapHandler={tap:[],dbltap:[],contextmenu:[],down:!1,taps:0,downSelectors:[]},X=function(st){for(var nt=h(st),rt=T(st,nt,t,I!=null),gt=!1,dt=0;dt<rt.end;dt++){if(gt)return;nt=rt.path[dt];for(var lt=0;lt<F.downSelectors.length;lt++)if(F.downSelectors[lt]==null||K(nt,F.downSelectors[lt],t)){F.down=!0,setTimeout(W,n),setTimeout(at,o),gt=!0;break}}},R=function(st){if(F.down){var nt=h(st),rt,gt;F.taps++;var dt=d(st);for(var lt in k)if(k.hasOwnProperty(lt)){var yt=k[lt];if(yt.touches===dt&&(yt.taps===1||yt.taps===F.taps))for(var pt=0;pt<F[lt].length;pt++){gt=T(st,nt,t,F[lt][pt][1]!=null);for(var Z=0;Z<gt.end;Z++)if(rt=gt.path[Z],F[lt][pt][1]==null||K(rt,F[lt][pt][1],t)){F[lt][pt][0].apply(rt,[st]);break}}}}},W=function(){F.down=!1},at=function(){F.taps=0};g(t,"mousedown",X),g(t,"mouseup",R)}t.__taTapHandler.downSelectors.push(I),t.__taTapHandler[p].push([v,I]),v.__taUnstore=function(){a(t.__taTapHandler[p],v)}}}},U=function(n,o,t,p){for(var v in t.__tamee[n])t.__tamee[n].hasOwnProperty(v)&&t.__tamee[n][v].apply(p,[o])},G=function(){var n=[];return function(o,t,p,v){if(!o.__tamee){o.__tamee={over:!1,mouseenter:[],mouseexit:[]};var I=function(X){var R=h(X);(v==null&&R==o&&!o.__tamee.over||K(R,v,o)&&(R.__tamee==null||!R.__tamee.over))&&(U("mouseenter",X,o,R),R.__tamee=R.__tamee||{},R.__tamee.over=!0,n.push(R))},F=function(X){for(var R=h(X),W=0;W<n.length;W++)R==n[W]&&!K(X.relatedTarget||X.toElement,"*",R)&&(R.__tamee.over=!1,n.splice(W,1),U("mouseexit",X,o,R))};E(o,"mouseover",M(v,o,I,"mouseover"),I),E(o,"mouseout",M(v,o,F,"mouseout"),F)}p.__taUnstore=function(){delete o.__tamee[t][p.__tauid]},y(o,t,p),o.__tamee[t][p.__tauid]=p}},P="ontouchstart"in document.documentElement||navigator.maxTouchPoints,w="onmousedown"in document.documentElement,r={mousedown:"touchstart",mouseup:"touchend",mousemove:"touchmove"},f=function(){var n=-1;if(navigator.appName=="Microsoft Internet Explorer"){var o=navigator.userAgent,t=new RegExp("MSIE ([0-9]{1,}[.0-9]{0,})");t.exec(o)!=null&&(n=parseFloat(RegExp.$1))}return n}(),l=f>-1&&f<9,_=function(n,o){if(n==null)return[0,0];var t=A(n),p=b(t,0);return[p[o+"X"],p[o+"Y"]]},x=function(n){return n==null?[0,0]:l?[n.clientX+document.documentElement.scrollLeft,n.clientY+document.documentElement.scrollTop]:_(n,"page")},u=function(n){return _(n,"screen")},j=function(n){return _(n,"client")},b=function(n,o){return n.item?n.item(o):n[o]},A=function(n){return n.touches&&n.touches.length>0?n.touches:n.changedTouches&&n.changedTouches.length>0?n.changedTouches:n.targetTouches&&n.targetTouches.length>0?n.targetTouches:[n]},d=function(n){return A(n).length},E=function(n,o,t,p){if(y(n,o,t),p.__tauid=t.__tauid,n.addEventListener)n.addEventListener(o,t,!1);else if(n.attachEvent){var v=o+t.__tauid;n["e"+v]=t,n[v]=function(){n["e"+v]&&n["e"+v](window.event)},n.attachEvent("on"+o,n[v])}},N=function(n,o,t){t!=null&&B(n,function(){var p=et(this);if(D(p,o,t),t.__tauid!=null){if(p.removeEventListener)p.removeEventListener(o,t,!1),P&&r[o]&&p.removeEventListener(r[o],t,!1);else if(this.detachEvent){var v=o+t.__tauid;p[v]&&p.detachEvent("on"+o,p[v]),p[v]=null,p["e"+v]=null}}t.__taTouchProxy&&N(n,t.__taTouchProxy[1],t.__taTouchProxy[0])})},B=function(n,o){if(n!=null){n=typeof Window<"u"&&typeof n.top!="unknown"&&n==n.top?[n]:typeof n!="string"&&n.tagName==null&&n.length!=null?n:typeof n=="string"?document.querySelectorAll(n):[n];for(var t=0;t<n.length;t++)o.apply(n[t])}},Y=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(n){var o=Math.random()*16|0,t=n=="x"?o:o&3|8;return t.toString(16)})};it.Mottle=function(n){n=n||{};var o=n.clickThreshold||250,t=n.dblClickThreshold||450,p=new G,v=new L(o,t),I=n.smartClicks,F=function(X,R,W,at){W!=null&&B(X,function(){var st=et(this);I&&R==="click"?e(st,R,W,at):R==="tap"||R==="dbltap"||R==="contextmenu"?v(st,R,W,at):R==="mouseenter"||R=="mouseexit"?p(st,R,W,at):g(st,R,W,at)})};this.remove=function(X){return B(X,function(){var R=et(this);if(R.__ta){for(var W in R.__ta)if(R.__ta.hasOwnProperty(W))for(var at in R.__ta[W])R.__ta[W].hasOwnProperty(at)&&N(R,W,R.__ta[W][at])}R.parentNode&&R.parentNode.removeChild(R)}),this},this.on=function(X,R,W,at){var st=arguments[0],nt=arguments.length==4?arguments[2]:null,rt=arguments[1],gt=arguments[arguments.length-1];return F(st,rt,gt,nt),this},this.off=function(X,R,W){return N(X,R,W),this},this.trigger=function(X,R,W,at){var st=w&&(typeof MouseEvent>"u"||W==null||W.constructor===MouseEvent),nt=P&&!w&&r[R]?r[R]:R,rt=!(P&&!w&&r[R]),gt=x(W),dt=u(W),lt=j(W);return B(X,function(){var yt=et(this),pt;W=W||{screenX:dt[0],screenY:dt[1],clientX:lt[0],clientY:lt[1]};var Z=function(vt){at&&(vt.payload=at)},q={TouchEvent:function(vt){var Dt=V(window,yt,0,gt[0],gt[1],dt[0],dt[1],lt[0],lt[1]),Ot=vt.initTouchEvent||vt.initEvent;Ot(nt,!0,!0,window,null,dt[0],dt[1],lt[0],lt[1],!1,!1,!1,!1,Dt,Dt,Dt,1,0)},MouseEvents:function(vt){vt.initMouseEvent(nt,!0,!0,window,0,dt[0],dt[1],lt[0],lt[1],!1,!1,!1,!1,1,yt)}};if(document.createEvent){var $=!rt&&!st&&P&&r[R],ut=$?"TouchEvent":"MouseEvents";pt=document.createEvent(ut),q[ut](pt),Z(pt),yt.dispatchEvent(pt)}else document.createEventObject&&(pt=document.createEventObject(),pt.eventType=pt.eventName=nt,pt.screenX=dt[0],pt.screenY=dt[1],pt.clientX=lt[0],pt.clientY=lt[1],Z(pt),yt.fireEvent("on"+nt,pt))}),this}},it.Mottle.consume=function(n,o){n.stopPropagation?n.stopPropagation():n.returnValue=!1,!o&&n.preventDefault&&n.preventDefault()},it.Mottle.pageLocation=x,it.Mottle.setForceTouchEvents=function(n){P=n},it.Mottle.setForceMouseEvents=function(n){w=n},it.Mottle.version="0.8.0",Kt.Mottle=it.Mottle}).call(typeof window>"u"?jt:window),(function(){var J=this,c=function(n,o,t){return n.indexOf(o)===-1?(n.push(o),!0):!1},V=function(n,o){var t=n.indexOf(o);t!==-1&&n.splice(t,1)},it=function(n,o){for(var t=[],p=0;p<n.length;p++)o.indexOf(n[p])===-1&&t.push(n[p]);return t},K=function(n){return n==null?!1:typeof n=="string"||n.constructor===String},et=function(n){var o=n.getBoundingClientRect(),t=document.body,p=document.documentElement,v=window.pageYOffset||p.scrollTop||t.scrollTop,I=window.pageXOffset||p.scrollLeft||t.scrollLeft,F=p.clientTop||t.clientTop||0,X=p.clientLeft||t.clientLeft||0,R=o.top+v-F,W=o.left+I-X;return{top:Math.round(R),left:Math.round(W)}},h=function(n,o,t){t=t||n.parentNode;for(var p=t.querySelectorAll(o),v=0;v<p.length;v++)if(p[v]===n)return!0;return!1},T=function(n,o,t){if(h(o,t,n))return o;for(var p=o.parentNode;p!=null&&p!==n;){if(h(p,t,n))return p;p=p.parentNode}},a=function(n,o,t){for(var p=null,v=o.getAttribute("katavorio-draggable"),I=v!=null?"[katavorio-draggable='"+v+"'] ":"",F=0;F<n.length;F++)if(p=T(o,t,I+n[F].selector),p!=null){if(n[F].filter){var X=h(t,n[F].filter,p),R=n[F].filterExclude===!0;if(R&&!X||X)return null}return[n[F],p]}return null},i=function(){var n=-1;if(navigator.appName==="Microsoft Internet Explorer"){var o=navigator.userAgent,t=new RegExp("MSIE ([0-9]{1,}[.0-9]{0,})");t.exec(o)!=null&&(n=parseFloat(RegExp.$1))}return n}(),y=10,D=10,M=i>-1&&i<9,C=i===9,g=function(n){if(M)return[n.clientX+document.documentElement.scrollLeft,n.clientY+document.documentElement.scrollTop];var o=k(n),t=e(o,0);return C?[t.pageX||t.clientX,t.pageY||t.clientY]:[t.pageX,t.pageY]},e=function(n,o){return n.item?n.item(o):n[o]},k=function(n){return n.touches&&n.touches.length>0?n.touches:n.changedTouches&&n.changedTouches.length>0?n.changedTouches:n.targetTouches&&n.targetTouches.length>0?n.targetTouches:[n]},L={delegatedDraggable:"katavorio-delegated-draggable",draggable:"katavorio-draggable",droppable:"katavorio-droppable",drag:"katavorio-drag",selected:"katavorio-drag-selected",active:"katavorio-drag-active",hover:"katavorio-drag-hover",noSelect:"katavorio-drag-no-select",ghostProxy:"katavorio-ghost-proxy",clonedDrag:"katavorio-clone-drag"},U="katavorio-drag-scope",G=["stop","start","drag","drop","over","out","beforeStart"],P=function(){},w=function(){return!0},r=function(n,o,t){for(var p=0;p<n.length;p++)n[p]!=t&&o(n[p])},f=function(n,o,t,p){r(n,function(v){v.setActive(o),o&&v.updatePosition(),t&&v.setHover(p,o)})},l=function(n,o){if(n!=null){n=!K(n)&&n.tagName==null&&n.length!=null?n:[n];for(var t=0;t<n.length;t++)o.apply(n[t],[n[t]])}},_=function(n){n.stopPropagation?(n.stopPropagation(),n.preventDefault()):n.returnValue=!1},x="input,textarea,select,button,option",u=function(n,o,t){var p=n.srcElement||n.target;return!h(p,t.getInputFilterSelector(),o)},j=function(n,o,t,p){this.params=o||{},this.el=n,this.params.addClass(this.el,this._class),this.uuid=N();var v=!0;return this.setEnabled=function(I){v=I},this.isEnabled=function(){return v},this.toggleEnabled=function(){v=!v},this.setScope=function(I){this.scopes=I?I.split(/\s+/):[p]},this.addScope=function(I){var F={};l(this.scopes,function(R){F[R]=!0}),l(I?I.split(/\s+/):[],function(R){F[R]=!0}),this.scopes=[];for(var X in F)this.scopes.push(X)},this.removeScope=function(I){var F={};l(this.scopes,function(R){F[R]=!0}),l(I?I.split(/\s+/):[],function(R){delete F[R]}),this.scopes=[];for(var X in F)this.scopes.push(X)},this.toggleScope=function(I){var F={};l(this.scopes,function(R){F[R]=!0}),l(I?I.split(/\s+/):[],function(R){F[R]?delete F[R]:F[R]=!0}),this.scopes=[];for(var X in F)this.scopes.push(X)},this.setScope(o.scope),this.k=o.katavorio,o.katavorio},b=function(){return!0},A=function(){return!1},d=function(n,o,t,p){this._class=t.draggable;var v=j.apply(this,arguments);this.rightButtonCanDrag=this.params.rightButtonCanDrag;var I=[0,0],F=null,X=null,R=[0,0],W=!1,at=[0,0],st=this.params.consumeStartEvent!==!1,nt=this.el,rt=this.params.clone;this.params.scroll;var gt=o.multipleDrop!==!1,dt=!1,lt,yt,pt=null,Z=[],q=null,$=o.ghostProxyParent,ut,vt,Dt,Ot;if(o.ghostProxy===!0?lt=b:o.ghostProxy&&typeof o.ghostProxy=="function"?lt=o.ghostProxy:lt=function(Q,_t){return q&&q.useGhostProxy?q.useGhostProxy(Q,_t):!1},o.makeGhostProxy?yt=o.makeGhostProxy:yt=function(Q){return q&&q.makeGhostProxy?q.makeGhostProxy(Q):Q.cloneNode(!0)},o.selector){var Ft=n.getAttribute("katavorio-draggable");Ft==null&&(Ft=""+new Date().getTime(),n.setAttribute("katavorio-draggable",Ft)),Z.push(o)}var Rt=o.snapThreshold,Gt=function(Q,_t,Pt,bt,Et){var xt=Math.floor(Q[0]/_t),s=_t*xt,S=s+_t,m=Math.abs(Q[0]-s)<=bt?s:Math.abs(S-Q[0])<=bt?S:Q[0],O=Math.floor(Q[1]/Pt),H=Pt*O,z=H+Pt,tt=Math.abs(Q[1]-H)<=Et?H:Math.abs(z-Q[1])<=Et?z:Q[1];return[m,tt]};this.posses=[],this.posseRoles={},this.toGrid=function(Q){if(this.params.grid==null)return Q;var _t=this.params.grid?this.params.grid[0]/2:Rt||y/2,Pt=this.params.grid?this.params.grid[1]/2:Rt||D/2;return Gt(Q,this.params.grid[0],this.params.grid[1],_t,Pt)},this.snap=function(Q,_t){if(nt!=null){Q=Q||(this.params.grid?this.params.grid[0]:y),_t=_t||(this.params.grid?this.params.grid[1]:D);var Pt=this.params.getPosition(nt),bt=this.params.grid?this.params.grid[0]/2:Rt,Et=this.params.grid?this.params.grid[1]/2:Rt,xt=Gt(Pt,Q,_t,bt,Et);return this.params.setPosition(nt,xt),xt}},this.setUseGhostProxy=function(Q){lt=Q?b:A};var Nt,ne=function(Q){return o.allowNegative===!1?[Math.max(0,Q[0]),Math.max(0,Q[1])]:Q},ie=(function(Q){Nt=typeof Q=="function"?Q:Q?(function(_t,Pt,bt,Et){return ne([Math.max(0,Math.min(bt.w-Et[0],_t[0])),Math.max(0,Math.min(bt.h-Et[1],_t[1]))])}).bind(this):function(_t){return ne(_t)}}).bind(this);ie(typeof this.params.constrain=="function"?this.params.constrain:this.params.constrain||this.params.containment),this.setConstrain=function(Q){ie(Q)};var se=function(Q,_t,Pt,bt){return q!=null&&q.constrain&&typeof q.constrain=="function"?q.constrain(Q,_t,Pt,bt):Nt(Q,_t,Pt,bt)},Ut;this.setRevert=function(Q){Ut=Q},this.params.revert&&(Ut=this.params.revert);var Zt=function(Q){return typeof Q=="function"?(Q._katavorioId=N(),Q._katavorioId):Q},Ht={},Jt=function(Q){for(var _t in Ht){var Pt=Ht[_t],bt=Pt[0](Q);if(Pt[1]&&(bt=!bt),!bt)return!1}return!0},$t=this.setFilter=function(Q,_t){if(Q){var Pt=Zt(Q);Ht[Pt]=[function(bt){var Et=bt.srcElement||bt.target,xt;return K(Q)?xt=h(Et,Q,n):typeof Q=="function"&&(xt=Q(bt,n)),xt},_t!==!1]}};this.addFilter=$t,this.removeFilter=function(Q){var _t=typeof Q=="function"?Q._katavorioId:Q;delete Ht[_t]},this.clearAllFilters=function(){Ht={}},this.canDrag=this.params.canDrag||w;var Qt,wt=[],Bt=[];this.addSelector=function(Q){Q.selector&&Z.push(Q)},this.downListener=(function(Q){if(!Q.defaultPrevented){var _t=this.rightButtonCanDrag||Q.which!==3&&Q.button!==2;if(_t&&this.isEnabled()&&this.canDrag()){var Pt=Jt(Q)&&u(Q,this.el,this.k);if(Pt){if(q=null,pt=null,Z.length>0){var bt=a(Z,this.el,Q.target||Q.srcElement);if(bt!=null&&(q=bt[0],pt=bt[1]),pt==null)return}else pt=this.el;if(rt)if(nt=pt.cloneNode(!0),this.params.addClass(nt,L.clonedDrag),nt.setAttribute("id",null),nt.style.position="absolute",this.params.parent!=null){var Et=this.params.getPosition(this.el);nt.style.left=Et[0]+"px",nt.style.top=Et[1]+"px",this.params.parent.appendChild(nt)}else{var xt=et(pt);nt.style.left=xt.left+"px",nt.style.top=xt.top+"px",document.body.appendChild(nt)}else nt=pt;st&&_(Q),I=g(Q),nt&&nt.parentNode&&(at=[nt.parentNode.scrollLeft,nt.parentNode.scrollTop]),this.params.bind(document,"mousemove",this.moveListener),this.params.bind(document,"mouseup",this.upListener),v.markSelection(this),v.markPosses(this),this.params.addClass(document.body,t.noSelect),Yt("beforeStart",{el:this.el,pos:F,e:Q,drag:this})}else this.params.consumeFilteredEvents&&_(Q)}}}).bind(this),this.moveListener=(function(Q){if(I){if(!W){var _t=Yt("start",{el:this.el,pos:F,e:Q,drag:this});if(_t!==!1){if(!I)return;this.mark(!0),W=!0}else this.abort()}if(I){Bt.length=0;var Pt=g(Q),bt=Pt[0]-I[0],Et=Pt[1]-I[1],xt=this.params.ignoreZoom?1:v.getZoom();nt&&nt.parentNode&&(bt+=nt.parentNode.scrollLeft-at[0],Et+=nt.parentNode.scrollTop-at[1]),bt/=xt,Et/=xt,this.moveBy(bt,Et,Q),v.updateSelection(bt,Et,this),v.updatePosses(bt,Et,this)}}}).bind(this),this.upListener=(function(Q){I&&(I=null,this.params.unbind(document,"mousemove",this.moveListener),this.params.unbind(document,"mouseup",this.upListener),this.params.removeClass(document.body,t.noSelect),this.unmark(Q),v.unmarkSelection(this,Q),v.unmarkPosses(this,Q),this.stop(Q),v.notifyPosseDragStop(this,Q),W=!1,Bt.length=0,rt?(nt&&nt.parentNode&&nt.parentNode.removeChild(nt),nt=null):Ut&&Ut(nt,this.params.getPosition(nt))===!0&&(this.params.setPosition(nt,F),Yt("revert",nt)))}).bind(this),this.getFilters=function(){return Ht},this.abort=function(){I!=null&&this.upListener()},this.getDragElement=function(Q){return Q?pt||this.el:nt||this.el};var Mt={start:[],drag:[],stop:[],over:[],out:[],beforeStart:[],revert:[]};o.events.start&&Mt.start.push(o.events.start),o.events.beforeStart&&Mt.beforeStart.push(o.events.beforeStart),o.events.stop&&Mt.stop.push(o.events.stop),o.events.drag&&Mt.drag.push(o.events.drag),o.events.revert&&Mt.revert.push(o.events.revert),this.on=function(Q,_t){Mt[Q]&&Mt[Q].push(_t)},this.off=function(Q,_t){if(Mt[Q]){for(var Pt=[],bt=0;bt<Mt[Q].length;bt++)Mt[Q][bt]!==_t&&Pt.push(Mt[Q][bt]);Mt[Q]=Pt}};var Yt=function(Q,_t){var Pt=null;if(q&&q[Q])Pt=q[Q](_t);else if(Mt[Q])for(var bt=0;bt<Mt[Q].length;bt++)try{var Et=Mt[Q][bt](_t);Et!=null&&(Pt=Et)}catch{}return Pt};this.notifyStart=function(Q){Yt("start",{el:this.el,pos:this.params.getPosition(nt),e:Q,drag:this})},this.stop=function(Q,_t){if(_t||W){var Pt=[],bt=v.getSelection(),Et=this.params.getPosition(nt);if(bt.length>0)for(var xt=0;xt<bt.length;xt++){var s=this.params.getPosition(bt[xt].el);Pt.push([bt[xt].el,{left:s[0],top:s[1]},bt[xt]])}else Pt.push([nt,{left:Et[0],top:Et[1]},this]);Yt("stop",{el:nt,pos:Wt||Et,finalPos:Et,e:Q,drag:this,selection:Pt})}},this.mark=function(Q){F=this.params.getPosition(nt),X=this.params.getPosition(nt,!0),R=[X[0]-F[0],X[1]-F[1]],this.size=this.params.getSize(nt),wt=v.getMatchingDroppables(this),f(wt,!0,!1,this),this.params.addClass(nt,this.params.dragClass||t.drag);var _t;this.params.getConstrainingRectangle?_t=this.params.getConstrainingRectangle(nt):_t=this.params.getSize(nt.parentNode),Qt={w:_t[0],h:_t[1]},Dt=0,Ot=0,Q&&v.notifySelectionDragStart(this)};var Wt;this.unmark=function(Q,_t){if(f(wt,!1,!0,this),dt&&lt(pt,nt)?(Wt=[nt.offsetLeft-Dt,nt.offsetTop-Ot],nt.parentNode.removeChild(nt),nt=pt):Wt=null,this.params.removeClass(nt,this.params.dragClass||t.drag),wt.length=0,dt=!1,!_t){Bt.length>0&&Wt&&o.setPosition(pt,Wt),Bt.sort(B);for(var Pt=0;Pt<Bt.length;Pt++){var bt=Bt[Pt].drop(this,Q);if(bt===!0)break}}},this.moveBy=function(Q,_t,Pt){Bt.length=0;var bt=this.toGrid([F[0]+Q,F[1]+_t]),Et=se(bt,nt,Qt,this.size);if(lt(this.el,nt))if(bt[0]!==Et[0]||bt[1]!==Et[1]){if(!dt){var xt=yt(pt);o.addClass(xt,L.ghostProxy),$?($.appendChild(xt),ut=o.getPosition(pt.parentNode,!0),vt=o.getPosition(o.ghostProxyParent,!0),Dt=ut[0]-vt[0],Ot=ut[1]-vt[1]):pt.parentNode.appendChild(xt),nt=xt,dt=!0}Et=bt}else dt&&(nt.parentNode.removeChild(nt),nt=pt,dt=!1,ut=null,vt=null,Dt=0,Ot=0);var s={x:Et[0],y:Et[1],w:this.size[0],h:this.size[1]},S={x:s.x+R[0],y:s.y+R[1],w:s.w,h:s.h},m=null;this.params.setPosition(nt,[Et[0]+Dt,Et[1]+Ot]);for(var O=0;O<wt.length;O++){var H={x:wt[O].pagePosition[0],y:wt[O].pagePosition[1],w:wt[O].size[0],h:wt[O].size[1]};this.params.intersects(S,H)&&(gt||m==null||m===wt[O].el)&&wt[O].canDrop(this)?(m||(m=wt[O].el),Bt.push(wt[O]),wt[O].setHover(this,!0,Pt)):wt[O].isHover()&&wt[O].setHover(this,!1,Pt)}Yt("drag",{el:this.el,pos:Et,e:Pt,drag:this})},this.destroy=function(){this.params.unbind(this.el,"mousedown",this.downListener),this.params.unbind(document,"mousemove",this.moveListener),this.params.unbind(document,"mouseup",this.upListener),this.downListener=null,this.upListener=null,this.moveListener=null},this.params.bind(this.el,"mousedown",this.downListener),this.params.handle?$t(this.params.handle,!1):$t(this.params.filter,this.params.filterExclude)},E=function(n,o,t,p){this._class=t.droppable,this.params=o||{},this.rank=o.rank||0,this._activeClass=this.params.activeClass||t.active,this._hoverClass=this.params.hoverClass||t.hover,j.apply(this,arguments);var v=!1;this.allowLoopback=this.params.allowLoopback!==!1,this.setActive=function(I){this.params[I?"addClass":"removeClass"](this.el,this._activeClass)},this.updatePosition=function(){this.position=this.params.getPosition(this.el),this.pagePosition=this.params.getPosition(this.el,!0),this.size=this.params.getSize(this.el)},this.canDrop=this.params.canDrop||function(I){return!0},this.isHover=function(){return v},this.setHover=function(I,F,X){(F||this.el._katavorioDragHover==null||this.el._katavorioDragHover===I.el._katavorio)&&(this.params[F?"addClass":"removeClass"](this.el,this._hoverClass),this.el._katavorioDragHover=F?I.el._katavorio:null,v!==F&&this.params.events[F?"over":"out"]({el:this.el,e:X,drag:I,drop:this}),v=F)},this.drop=function(I,F){return this.params.events.drop({drag:I,e:F,drop:this})},this.destroy=function(){this._class=null,this._activeClass=null,this._hoverClass=null,v=null}},N=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(n){var o=Math.random()*16|0,t=n==="x"?o:o&3|8;return t.toString(16)})},B=function(n,o){return n.rank<o.rank?1:n.rank>o.rank?-1:0},Y=function(n){return n==null||(n=typeof n=="string"||n.constructor===String?document.getElementById(n):n,n==null)?null:(n._katavorio=n._katavorio||N(),n)};J.Katavorio=function(n){var o=[],t={};this._dragsByScope={},this._dropsByScope={};var p=1,v=function(Z,q){l(Z,function($){for(var ut=0;ut<$.scopes.length;ut++)q[$.scopes[ut]]=q[$.scopes[ut]]||[],q[$.scopes[ut]].push($)})},I=function(Z,q){var $=0;return l(Z,function(ut){for(var vt=0;vt<ut.scopes.length;vt++)if(q[ut.scopes[vt]]){var Dt=n.indexOf(q[ut.scopes[vt]],ut);Dt!==-1&&(q[ut.scopes[vt]].splice(Dt,1),$++)}}),$>0};this.getMatchingDroppables=function(Z){for(var q=[],$={},ut=0;ut<Z.scopes.length;ut++){var vt=this._dropsByScope[Z.scopes[ut]];if(vt)for(var Dt=0;Dt<vt.length;Dt++)vt[Dt].canDrop(Z)&&!$[vt[Dt].uuid]&&(vt[Dt].allowLoopback||vt[Dt].el!==Z.el)&&($[vt[Dt].uuid]=!0,q.push(vt[Dt]))}return q.sort(B),q};var F=(function(Z){Z=Z||{};var q={events:{}},$;for($ in n)q[$]=n[$];for($ in Z)q[$]=Z[$];for($=0;$<G.length;$++)q.events[G[$]]=Z[G[$]]||P;return q.katavorio=this,q}).bind(this),X=(function(Z,q){for(var $=0;$<G.length;$++)q[G[$]]&&Z.on(G[$],q[G[$]])}).bind(this),R={},W=n.css||{},at=n.scope||U;for(var st in L)R[st]=L[st];for(var st in W)R[st]=W[st];var nt=n.inputFilterSelector||x;this.getInputFilterSelector=function(){return nt},this.setInputFilterSelector=function(Z){return nt=Z,this},this.draggable=function(Z,q){var $=[];return l(Z,(function(ut){if(ut=Y(ut),ut!=null)if(ut._katavorioDrag==null){var vt=F(q);ut._katavorioDrag=new d(ut,vt,R,at),v(ut._katavorioDrag,this._dragsByScope),$.push(ut._katavorioDrag),n.addClass(ut,vt.selector?R.delegatedDraggable:R.draggable)}else X(ut._katavorioDrag,q)}).bind(this)),$},this.droppable=function(Z,q){var $=[];return l(Z,(function(ut){if(ut=Y(ut),ut!=null){var vt=new E(ut,F(q),R,at);ut._katavorioDrop=ut._katavorioDrop||[],ut._katavorioDrop.push(vt),v(vt,this._dropsByScope),$.push(vt),n.addClass(ut,R.droppable)}}).bind(this)),$},this.select=function(Z){return l(Z,function(){var q=Y(this);q&&q._katavorioDrag&&(t[q._katavorio]||(o.push(q._katavorioDrag),t[q._katavorio]=[q,o.length-1],n.addClass(q,R.selected)))}),this},this.deselect=function(Z){return l(Z,function(){var q=Y(this);if(q&&q._katavorio){var $=t[q._katavorio];if($){for(var ut=[],vt=0;vt<o.length;vt++)o[vt].el!==q&&ut.push(o[vt]);o=ut,delete t[q._katavorio],n.removeClass(q,R.selected)}}}),this},this.deselectAll=function(){for(var Z in t){var q=t[Z];n.removeClass(q[0],R.selected)}o.length=0,t={}},this.markSelection=function(Z){r(o,function(q){q.mark()},Z)},this.markPosses=function(Z){Z.posses&&l(Z.posses,function(q){Z.posseRoles[q]&&yt[q]&&r(yt[q].members,function($){$.mark()},Z)})},this.unmarkSelection=function(Z,q){r(o,function($){$.unmark(q)},Z)},this.unmarkPosses=function(Z,q){Z.posses&&l(Z.posses,function($){Z.posseRoles[$]&&yt[$]&&r(yt[$].members,function(ut){ut.unmark(q,!0)},Z)})},this.getSelection=function(){return o.slice(0)},this.updateSelection=function(Z,q,$){r(o,function(ut){ut.moveBy(Z,q)},$)};var rt=function(Z,q){q.posses&&l(q.posses,function($){q.posseRoles[$]&&yt[$]&&r(yt[$].members,function(ut){Z(ut)},q)})};this.updatePosses=function(Z,q,$){rt(function(ut){ut.moveBy(Z,q)},$)},this.notifyPosseDragStop=function(Z,q){rt(function($){$.stop(q,!0)},Z)},this.notifySelectionDragStop=function(Z,q){r(o,function($){$.stop(q,!0)},Z)},this.notifySelectionDragStart=function(Z,q){r(o,function($){$.notifyStart(q)},Z)},this.setZoom=function(Z){p=Z},this.getZoom=function(){return p};var gt=function(Z,q,$,ut){l(Z,function(vt){I(vt,$),vt[ut](q),v(vt,$)})};l(["set","add","remove","toggle"],(function(Z){this[Z+"Scope"]=(function(q,$){gt(q._katavorioDrag,$,this._dragsByScope,Z+"Scope"),gt(q._katavorioDrop,$,this._dropsByScope,Z+"Scope")}).bind(this),this[Z+"DragScope"]=(function(q,$){gt(q.constructor===d?q:q._katavorioDrag,$,this._dragsByScope,Z+"Scope")}).bind(this),this[Z+"DropScope"]=(function(q,$){gt(q.constructor===E?q:q._katavorioDrop,$,this._dropsByScope,Z+"Scope")}).bind(this)}).bind(this)),this.snapToGrid=function(Z,q){for(var $ in this._dragsByScope)r(this._dragsByScope[$],function(ut){ut.snap(Z,q)})},this.getDragsForScope=function(Z){return this._dragsByScope[Z]},this.getDropsForScope=function(Z){return this._dropsByScope[Z]};var dt=function(Z,q,$){if(Z=Y(Z),Z[q]){var ut=o.indexOf(Z[q]);ut>=0&&o.splice(ut,1),I(Z[q],$)&&l(Z[q],function(vt){vt.destroy()}),delete Z[q]}},lt=function(Z,q,$,ut){Z=Y(Z),Z[q]&&Z[q].off($,ut)};this.elementRemoved=function(Z){Z._katavorioDrag&&this.destroyDraggable(Z),Z._katavorioDrop&&this.destroyDroppable(Z)},this.destroyDraggable=function(Z,q,$){arguments.length===1?dt(Z,"_katavorioDrag",this._dragsByScope):lt(Z,"_katavorioDrag",q,$)},this.destroyDroppable=function(Z,q,$){arguments.length===1?dt(Z,"_katavorioDrop",this._dropsByScope):lt(Z,"_katavorioDrop",q,$)},this.reset=function(){this._dragsByScope={},this._dropsByScope={},o=[],t={},yt={}};var yt={},pt=function(Z,q,$){var ut=K(q)?q:q.id,vt=K(q)?!0:q.active!==!1,Dt=yt[ut]||function(){var Ot={name:ut,members:[]};return yt[ut]=Ot,Ot}();return l(Z,function(Ot){if(Ot._katavorioDrag){if($&&Ot._katavorioDrag.posseRoles[Dt.name]!=null)return;c(Dt.members,Ot._katavorioDrag),c(Ot._katavorioDrag.posses,Dt.name),Ot._katavorioDrag.posseRoles[Dt.name]=vt}}),Dt};this.addToPosse=function(Z,q){for(var $=[],ut=1;ut<arguments.length;ut++)$.push(pt(Z,arguments[ut]));return $.length===1?$[0]:$},this.setPosse=function(Z,q){for(var $=[],ut=1;ut<arguments.length;ut++)$.push(pt(Z,arguments[ut],!0).name);return l(Z,(function(vt){if(vt._katavorioDrag){var Dt=it(vt._katavorioDrag.posses,$),Ot=[];Array.prototype.push.apply(Ot,vt._katavorioDrag.posses);for(var Ft=0;Ft<Dt.length;Ft++)this.removeFromPosse(vt,Dt[Ft])}}).bind(this)),$.length===1?$[0]:$},this.removeFromPosse=function(Z,q){if(arguments.length<2)throw new TypeError("No posse id provided for remove operation");for(var $=1;$<arguments.length;$++)q=arguments[$],l(Z,function(ut){if(ut._katavorioDrag&&ut._katavorioDrag.posses){var vt=ut._katavorioDrag;l(q,function(Dt){V(yt[Dt].members,vt),V(vt.posses,Dt),delete vt.posseRoles[Dt]})}})},this.removeFromAllPosses=function(Z){l(Z,function(q){if(q._katavorioDrag&&q._katavorioDrag.posses){var $=q._katavorioDrag;l($.posses,function(ut){V(yt[ut].members,$)}),$.posses.length=0,$.posseRoles={}}})},this.setPosseState=function(Z,q,$){var ut=yt[q];ut&&l(Z,function(vt){vt._katavorioDrag&&vt._katavorioDrag.posses&&(vt._katavorioDrag.posseRoles[ut.name]=$)})}},J.Katavorio.version="1.0.0",Kt.Katavorio=J.Katavorio}).call(typeof window<"u"?window:jt),(function(){var J=this;J.jsPlumbUtil=J.jsPlumbUtil||{};var c=J.jsPlumbUtil;Kt.jsPlumbUtil=c;function V(n){return Object.prototype.toString.call(n)==="[object Array]"}c.isArray=V;function it(n){return Object.prototype.toString.call(n)==="[object Number]"}c.isNumber=it;function K(n){return typeof n=="string"}c.isString=K;function et(n){return typeof n=="boolean"}c.isBoolean=et;function h(n){return n==null}c.isNull=h;function T(n){return n==null?!1:Object.prototype.toString.call(n)==="[object Object]"}c.isObject=T;function a(n){return Object.prototype.toString.call(n)==="[object Date]"}c.isDate=a;function i(n){return Object.prototype.toString.call(n)==="[object Function]"}c.isFunction=i;function y(n){return i(n)&&n.name!=null&&n.name.length>0}c.isNamedFunction=y;function D(n){for(var o in n)if(n.hasOwnProperty(o))return!1;return!0}c.isEmpty=D;function M(n){if(K(n))return""+n;if(et(n))return!!n;if(a(n))return new Date(n.getTime());if(i(n))return n;if(V(n)){for(var o=[],t=0;t<n.length;t++)o.push(M(n[t]));return o}else if(T(n)){var p={};for(var v in n)p[v]=M(n[v]);return p}else return n}c.clone=M;function C(n,o,t,p){var v={},I,F,X={};for(t=t||[],p=p||[],F=0;F<t.length;F++)v[t[F]]=!0;for(F=0;F<p.length;F++)X[p[F]]=!0;var R=M(n);for(F in o)if(R[F]==null||X[F])R[F]=o[F];else if(K(o[F])||et(o[F]))v[F]?(I=[],I.push.apply(I,V(R[F])?R[F]:[R[F]]),I.push.apply(I,et(o[F])?o[F]:[o[F]]),R[F]=I):R[F]=o[F];else if(V(o[F]))I=[],V(R[F])&&I.push.apply(I,R[F]),I.push.apply(I,o[F]),R[F]=I;else if(T(o[F])){T(R[F])||(R[F]={});for(var W in o[F])R[F][W]=o[F][W]}return R}c.merge=C;function g(n,o,t){if(n!=null){var p=n,v=p;return o.replace(/([^\.])+/g,function(I,F,X,R){var W=I.match(/([^\[0-9]+){1}(\[)([0-9+])/),at=X+I.length>=R.length,st=function(){return v[W[1]]||function(){return v[W[1]]=[],v[W[1]]}()};if(at)W?st()[W[3]]=t:v[I]=t;else if(W){var nt=st();v=nt[W[3]]||function(){return nt[W[3]]={},nt[W[3]]}()}else v=v[I]||function(){return v[I]={},v[I]}();return""}),n}}c.replace=g;function e(n,o,t){for(var p=0;p<t.length;p++){var v=t[p][0][t[p][1]].apply(t[p][0],t[p][2]);if(v===o)return v}return n}c.functionChain=e;function k(n,o,t,p){var v=function(F){var X=F.match(/(\${.*?})/g);if(X!=null)for(var R=0;R<X.length;R++){var W=o[X[R].substring(2,X[R].length-1)]||"";W!=null&&(F=F.replace(X[R],W))}return F},I=function(F){if(F!=null){if(K(F))return v(F);if(i(F)&&!p&&(t==null||(F.name||"").indexOf(t)===0))return F(o);if(V(F)){for(var X=[],R=0;R<F.length;R++)X.push(I(F[R]));return X}else if(T(F)){var W={};for(var at in F)W[at]=I(F[at]);return W}else return F}};return I(n)}c.populate=k;function L(n,o){if(n){for(var t=0;t<n.length;t++)if(o(n[t]))return t}return-1}c.findWithFunction=L;function U(n,o){var t=L(n,o);return t>-1&&n.splice(t,1),t!==-1}c.removeWithFunction=U;function G(n,o){var t=n.indexOf(o);return t>-1&&n.splice(t,1),t!==-1}c.remove=G;function P(n,o,t){L(n,t)===-1&&n.push(o)}c.addWithFunction=P;function w(n,o,t,p){var v=n[o];return v==null&&(v=[],n[o]=v),v[p?"unshift":"push"](t),v}c.addToList=w;function r(n,o,t){return n.indexOf(o)===-1?(t?n.unshift(o):n.push(o),!0):!1}c.suggest=r;function f(n,o,t){var p;o=V(o)?o:[o];var v=function(R){for(var W=R.__proto__;W!=null;)if(W.prototype!=null){for(var at in W.prototype)W.prototype.hasOwnProperty(at)&&!n.prototype.hasOwnProperty(at)&&(n.prototype[at]=W.prototype[at]);W=W.prototype.__proto__}else W=null};for(p=0;p<o.length;p++){for(var I in o[p].prototype)o[p].prototype.hasOwnProperty(I)&&!n.prototype.hasOwnProperty(I)&&(n.prototype[I]=o[p].prototype[I]);v(o[p])}var F=function(R,W){return function(){for(p=0;p<o.length;p++)o[p].prototype[R]&&o[p].prototype[R].apply(this,arguments);return W.apply(this,arguments)}},X=function(R){for(var W in R)n.prototype[W]=F(W,R[W])};if(arguments.length>2)for(p=2;p<arguments.length;p++)X(arguments[p]);return n}c.extend=f;for(var l=[],_=0;_<256;_++)l[_]=(_<16?"0":"")+_.toString(16);function x(){var n=Math.random()*4294967295|0,o=Math.random()*4294967295|0,t=Math.random()*4294967295|0,p=Math.random()*4294967295|0;return l[n&255]+l[n>>8&255]+l[n>>16&255]+l[n>>24&255]+"-"+l[o&255]+l[o>>8&255]+"-"+l[o>>16&15|64]+l[o>>24&255]+"-"+l[t&63|128]+l[t>>8&255]+"-"+l[t>>16&255]+l[t>>24&255]+l[p&255]+l[p>>8&255]+l[p>>16&255]+l[p>>24&255]}c.uuid=x;function u(n){if(n==null)return null;for(var o=n.replace(/^\s\s*/,""),t=/\s/,p=o.length;t.test(o.charAt(--p)););return o.slice(0,p+1)}c.fastTrim=u;function j(n,o){n=n.length==null||typeof n=="string"?[n]:n;for(var t=0;t<n.length;t++)o(n[t])}c.each=j;function b(n,o){for(var t=[],p=0;p<n.length;p++)t.push(o(n[p]));return t}c.map=b;function A(n,o,t){t=t||"parent";var p=function(R){return R?o[R]:null},v=function(R){return R?p(R[t]):null},I=function(R,W){if(R==null)return W;var at=["anchor","anchors","cssClass","connector","paintStyle","hoverPaintStyle","endpoint","endpoints"];W.mergeStrategy==="override"&&Array.prototype.push.apply(at,["events","overlays"]);var st=C(R,W,[],at);return I(v(R),st)},F=function(R){if(R==null)return{};if(typeof R=="string")return p(R);if(R.length){for(var W=!1,at=0,st=void 0;!W&&at<R.length;)st=F(R[at]),st?W=!0:at++;return st}},X=F(n);return X?I(v(X),X):{}}c.mergeWithParents=A,c.logEnabled=!0;function d(){if(c.logEnabled&&typeof console<"u")try{var n=arguments[arguments.length-1];console.log(n)}catch{}}c.log=d;function E(n,o,t){return function(){var p=null;try{o!=null&&(p=o.apply(this,arguments))}catch(v){d("jsPlumb function failed : "+v)}if(n!=null&&(t==null||p!==t))try{p=n.apply(this,arguments)}catch(v){d("wrapped function failed : "+v)}return p}}c.wrap=E;var N=function(){function n(){var o=this;this._listeners={},this.eventsSuspended=!1,this.tick=!1,this.eventsToDieOn={ready:!0},this.queue=[],this.bind=function(t,p,v){var I=function(X){w(o._listeners,X,p,v),p.__jsPlumb=p.__jsPlumb||{},p.__jsPlumb[x()]=X};if(typeof t=="string")I(t);else if(t.length!=null)for(var F=0;F<t.length;F++)I(t[F]);return o},this.fire=function(t,p,v){if(this.tick)this.queue.unshift(arguments);else{if(this.tick=!0,!this.eventsSuspended&&this._listeners[t]){var I=this._listeners[t].length,F=0,X=!1,R=null;if(!this.shouldFireEvent||this.shouldFireEvent(t,p,v))for(;!X&&F<I&&R!==!1;){if(this.eventsToDieOn[t])this._listeners[t][F].apply(this,[p,v]);else try{R=this._listeners[t][F].apply(this,[p,v])}catch(W){d("jsPlumb: fire failed for event "+t+" : "+W)}F++,(this._listeners==null||this._listeners[t]==null)&&(X=!0)}}this.tick=!1,this._drain()}return this},this._drain=function(){var t=o.queue.pop();t&&o.fire.apply(o,t)},this.unbind=function(t,p){if(arguments.length===0)this._listeners={};else if(arguments.length===1){if(typeof t=="string")delete this._listeners[t];else if(t.__jsPlumb){var v=void 0;for(var I in t.__jsPlumb)v=t.__jsPlumb[I],G(this._listeners[v]||[],t)}}else arguments.length===2&&G(this._listeners[t]||[],p);return this},this.getListener=function(t){return o._listeners[t]},this.setSuspendEvents=function(t){o.eventsSuspended=t},this.isSuspendEvents=function(){return o.eventsSuspended},this.silently=function(t){o.setSuspendEvents(!0);try{t()}catch(p){d("Cannot execute silent function "+p)}o.setSuspendEvents(!1)},this.cleanupListeners=function(){for(var t in o._listeners)o._listeners[t]=null}}return n}();c.EventGenerator=N;function B(n,o,t){var p=[n[0]-o[0],n[1]-o[1]],v=Math.cos(t/360*Math.PI*2),I=Math.sin(t/360*Math.PI*2);return[p[0]*v-p[1]*I+o[0],p[1]*v+p[0]*I+o[1],v,I]}c.rotatePoint=B;function Y(n,o){var t=B(n,[0,0],o);return[Math.round(t[0]),Math.round(t[1])]}c.rotateAnchorOrientation=Y}).call(typeof window<"u"?window:jt),(function(){var J=this;J.jsPlumbUtil.matchesSelector=function(c,V,it){it=it||c.parentNode;for(var K=it.querySelectorAll(V),et=0;et<K.length;et++)if(K[et]===c)return!0;return!1},J.jsPlumbUtil.consume=function(c,V){c.stopPropagation?c.stopPropagation():c.returnValue=!1,!V&&c.preventDefault&&c.preventDefault()},J.jsPlumbUtil.sizeElement=function(c,V,it,K,et){c&&(c.style.height=et+"px",c.height=et,c.style.width=K+"px",c.width=K,c.style.left=V+"px",c.style.top=it+"px")}}).call(typeof window<"u"?window:jt),(function(){var J={deriveAnchor:function(K,et,h,T){return{top:["TopRight","TopLeft"],bottom:["BottomRight","BottomLeft"]}[K][et]}},c=this,V=function(K,et){this.count=0,this.instance=K,this.lists={},this.options=et||{},this.instance.addList=function(h,T){return this.listManager.addList(h,T)},this.instance.removeList=function(h){this.listManager.removeList(h)},this.instance.bind("manageElement",(function(h){for(var T=this.instance.getSelector(h.el,"[jtk-scrollable-list]"),a=0;a<T.length;a++)this.addList(T[a])}).bind(this)),this.instance.bind("unmanageElement",function(h){this.removeList(h.el)}),this.instance.bind("connection",(function(h,T){T==null&&(this._maybeUpdateParentList(h.source),this._maybeUpdateParentList(h.target))}).bind(this))};c.jsPlumbListManager=V,V.prototype={addList:function(K,et){var h=this.instance.extend({},J);this.instance.extend(h,this.options),et=this.instance.extend(h,et||{});var T=[this.instance.getInstanceIndex(),this.count++].join("_");this.lists[T]=new it(this.instance,K,et,T)},removeList:function(K){var et=this.lists[K._jsPlumbList];et&&(et.destroy(),delete this.lists[K._jsPlumbList])},_maybeUpdateParentList:function(K){for(var et=K.parentNode,h=this.instance.getContainer();et!=null&&et!==h;){if(et._jsPlumbList!=null&&this.lists[et._jsPlumbList]!=null){et._jsPlumbScrollHandler();return}et=et.parentNode}}};var it=function(K,et,h,T){et._jsPlumbList=T;function a(M,C,g,e){return h.anchor?h.anchor:h.deriveAnchor(M,C,g,e)}function i(M,C,g,e){return h.deriveEndpoint?h.deriveEndpoint(M,C,g,e):h.endpoint?h.endpoint:g.type}function y(M){for(var C=M.parentNode,g=K.getContainer();C!=null&&C!==g;){if(K.hasClass(C,"jtk-managed")){K.recalculateOffsets(C);return}C=C.parentNode}}var D=function(M){for(var C=K.getSelector(et,".jtk-managed"),g=K.getId(et),e=0;e<C.length;e++){if(C[e].offsetTop<et.scrollTop)C[e]._jsPlumbProxies||(C[e]._jsPlumbProxies=C[e]._jsPlumbProxies||[],K.select({source:C[e]}).each(function(L){K.proxyConnection(L,0,et,g,function(){return i("top",0,L.endpoints[0],L)},function(){return a("top",0,L.endpoints[0],L)}),C[e]._jsPlumbProxies.push([L,0])}),K.select({target:C[e]}).each(function(L){K.proxyConnection(L,1,et,g,function(){return i("top",1,L.endpoints[1],L)},function(){return a("top",1,L.endpoints[1],L)}),C[e]._jsPlumbProxies.push([L,1])}));else if(C[e].offsetTop+C[e].offsetHeight>et.scrollTop+et.offsetHeight)C[e]._jsPlumbProxies||(C[e]._jsPlumbProxies=C[e]._jsPlumbProxies||[],K.select({source:C[e]}).each(function(L){K.proxyConnection(L,0,et,g,function(){return i("bottom",0,L.endpoints[0],L)},function(){return a("bottom",0,L.endpoints[0],L)}),C[e]._jsPlumbProxies.push([L,0])}),K.select({target:C[e]}).each(function(L){K.proxyConnection(L,1,et,g,function(){return i("bottom",1,L.endpoints[1],L)},function(){return a("bottom",1,L.endpoints[1],L)}),C[e]._jsPlumbProxies.push([L,1])}));else if(C[e]._jsPlumbProxies){for(var k=0;k<C[e]._jsPlumbProxies.length;k++)K.unproxyConnection(C[e]._jsPlumbProxies[k][0],C[e]._jsPlumbProxies[k][1],g);delete C[e]._jsPlumbProxies}K.revalidate(C[e])}y(et)};K.setAttribute(et,"jtk-scrollable-list","true"),et._jsPlumbScrollHandler=D,K.on(et,"scroll",D),D(),this.destroy=function(){K.off(et,"scroll",D),delete et._jsPlumbScrollHandler;for(var M=K.getSelector(et,".jtk-managed"),C=K.getId(et),g=0;g<M.length;g++)if(M[g]._jsPlumbProxies){for(var e=0;e<M[g]._jsPlumbProxies.length;e++)K.unproxyConnection(M[g]._jsPlumbProxies[e][0],M[g]._jsPlumbProxies[e][1],C);delete M[g]._jsPlumbProxies}}}}).call(typeof window<"u"?window:jt),(function(){var J=this,c=J.jsPlumbUtil,V=function(g){if(g._jsPlumb.paintStyle&&g._jsPlumb.hoverPaintStyle){var e={};C.extend(e,g._jsPlumb.paintStyle),C.extend(e,g._jsPlumb.hoverPaintStyle),delete g._jsPlumb.hoverPaintStyle,e.gradient&&g._jsPlumb.paintStyle.fill&&delete e.gradient,g._jsPlumb.hoverPaintStyle=e}},it=["tap","dbltap","click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","contextmenu"],K=function(g,e,k,L){var U=g.getAttachedElements();if(U)for(var G=0,P=U.length;G<P;G++)(!L||L!==U[G])&&U[G].setHover(e,!0,k)},et=function(g){return g==null?null:g.split(" ")},h=function(g,e,k){for(var L in e)g[L]=k},T=function(g,e,k){if(g.getDefaultType){var L=g.getTypeDescriptor(),U={},G=g.getDefaultType(),P=c.merge({},G);h(U,G,"__default");for(var w=0,r=g._jsPlumb.types.length;w<r;w++){var f=g._jsPlumb.types[w];if(f!=="__default"){var l=g._jsPlumb.instance.getType(f,L);if(l!=null){var _=["anchor","anchors","connector","paintStyle","hoverPaintStyle","endpoint","endpoints","connectorOverlays","connectorStyle","connectorHoverStyle","endpointStyle","endpointHoverStyle"],x=[];l.mergeStrategy==="override"?Array.prototype.push.apply(_,["events","overlays","cssClass"]):x.push("cssClass"),P=c.merge(P,l,x,_),h(U,l,f)}}}e&&(P=c.populate(P,e,"_")),g.applyType(P,k,U),k||g.repaint()}},a=J.jsPlumbUIComponent=function(g){c.EventGenerator.apply(this,arguments);var e=this,k=arguments,L=e.idPrefix,U=L+new Date().getTime();this._jsPlumb={instance:g._jsPlumb,parameters:g.parameters||{},paintStyle:null,hoverPaintStyle:null,paintStyleInUse:null,hover:!1,beforeDetach:g.beforeDetach,beforeDrop:g.beforeDrop,overlayPlacements:[],hoverClass:g.hoverClass||g._jsPlumb.Defaults.HoverClass,types:[],typeCache:{}},this.cacheTypeItem=function(x,u,j){this._jsPlumb.typeCache[j]=this._jsPlumb.typeCache[j]||{},this._jsPlumb.typeCache[j][x]=u},this.getCachedTypeItem=function(x,u){return this._jsPlumb.typeCache[u]?this._jsPlumb.typeCache[u][x]:null},this.getId=function(){return U};var G=g.overlays||[],P={};if(this.defaultOverlayKeys){for(var w=0;w<this.defaultOverlayKeys.length;w++)Array.prototype.push.apply(G,this._jsPlumb.instance.Defaults[this.defaultOverlayKeys[w]]||[]);for(w=0;w<G.length;w++){var r=C.convertToFullOverlaySpec(G[w]);P[r[1].id]=r}}var f={overlays:P,parameters:g.parameters||{},scope:g.scope||this._jsPlumb.instance.getDefaultScope()};if(this.getDefaultType=function(){return f},this.appendToDefaultType=function(x){for(var u in x)f[u]=x[u]},g.events)for(var l in g.events)e.bind(l,g.events[l]);this.clone=(function(){var x=Object.create(this.constructor.prototype);return this.constructor.apply(x,k),x}).bind(this),this.isDetachAllowed=function(x){var u=!0;if(this._jsPlumb.beforeDetach)try{u=this._jsPlumb.beforeDetach(x)}catch(j){c.log("jsPlumb: beforeDetach callback failed",j)}return u},this.isDropAllowed=function(x,u,j,b,A,d,E){var N=this._jsPlumb.instance.checkCondition("beforeDrop",{sourceId:x,targetId:u,scope:j,connection:b,dropEndpoint:A,source:d,target:E});if(this._jsPlumb.beforeDrop)try{N=this._jsPlumb.beforeDrop({sourceId:x,targetId:u,scope:j,connection:b,dropEndpoint:A,source:d,target:E})}catch(B){c.log("jsPlumb: beforeDrop callback failed",B)}return N};var _=[];this.setListenerComponent=function(x){for(var u=0;u<_.length;u++)_[u][3]=x}},i=function(g,e){var k=g._jsPlumb.types[e],L=g._jsPlumb.instance.getType(k,g.getTypeDescriptor());L!=null&&L.cssClass&&g.canvas&&g._jsPlumb.instance.removeClass(g.canvas,L.cssClass)};c.extend(J.jsPlumbUIComponent,c.EventGenerator,{getParameter:function(g){return this._jsPlumb.parameters[g]},setParameter:function(g,e){this._jsPlumb.parameters[g]=e},getParameters:function(){return this._jsPlumb.parameters},setParameters:function(g){this._jsPlumb.parameters=g},getClass:function(){return C.getClass(this.canvas)},hasClass:function(g){return C.hasClass(this.canvas,g)},addClass:function(g){C.addClass(this.canvas,g)},removeClass:function(g){C.removeClass(this.canvas,g)},updateClasses:function(g,e){C.updateClasses(this.canvas,g,e)},setType:function(g,e,k){this.clearTypes(),this._jsPlumb.types=et(g)||[],T(this,e,k)},getType:function(){return this._jsPlumb.types},reapplyTypes:function(g,e){T(this,g,e)},hasType:function(g){return this._jsPlumb.types.indexOf(g)!==-1},addType:function(g,e,k){var L=et(g),U=!1;if(L!=null){for(var G=0,P=L.length;G<P;G++)this.hasType(L[G])||(this._jsPlumb.types.push(L[G]),U=!0);U&&T(this,e,k)}},removeType:function(g,e,k){var L=et(g),U=!1,G=(function(r){var f=this._jsPlumb.types.indexOf(r);return f!==-1?(i(this,f),this._jsPlumb.types.splice(f,1),!0):!1}).bind(this);if(L!=null){for(var P=0,w=L.length;P<w;P++)U=G(L[P])||U;U&&T(this,e,k)}},clearTypes:function(g,e){for(var k=this._jsPlumb.types.length,L=0;L<k;L++)i(this,0),this._jsPlumb.types.splice(0,1);T(this,g,e)},toggleType:function(g,e,k){var L=et(g);if(L!=null){for(var U=0,G=L.length;U<G;U++){var P=this._jsPlumb.types.indexOf(L[U]);P!==-1?(i(this,P),this._jsPlumb.types.splice(P,1)):this._jsPlumb.types.push(L[U])}T(this,e,k)}},applyType:function(g,e){if(this.setPaintStyle(g.paintStyle,e),this.setHoverPaintStyle(g.hoverPaintStyle,e),g.parameters)for(var k in g.parameters)this.setParameter(k,g.parameters[k]);this._jsPlumb.paintStyleInUse=this.getPaintStyle()},setPaintStyle:function(g,e){this._jsPlumb.paintStyle=g,this._jsPlumb.paintStyleInUse=this._jsPlumb.paintStyle,V(this),e||this.repaint()},getPaintStyle:function(){return this._jsPlumb.paintStyle},setHoverPaintStyle:function(g,e){this._jsPlumb.hoverPaintStyle=g,V(this),e||this.repaint()},getHoverPaintStyle:function(){return this._jsPlumb.hoverPaintStyle},destroy:function(g){(g||this.typeId==null)&&(this.cleanupListeners(),this.clone=null,this._jsPlumb=null)},isHover:function(){return this._jsPlumb.hover},setHover:function(g,e,k){if(this._jsPlumb&&!this._jsPlumb.instance.currentlyDragging&&!this._jsPlumb.instance.isHoverSuspended()){this._jsPlumb.hover=g;var L=g?"addClass":"removeClass";this.canvas!=null&&(this._jsPlumb.instance.hoverClass!=null&&this._jsPlumb.instance[L](this.canvas,this._jsPlumb.instance.hoverClass),this._jsPlumb.hoverClass!=null&&this._jsPlumb.instance[L](this.canvas,this._jsPlumb.hoverClass)),this._jsPlumb.hoverPaintStyle!=null&&(this._jsPlumb.paintStyleInUse=g?this._jsPlumb.hoverPaintStyle:this._jsPlumb.paintStyle,this._jsPlumb.instance.isSuspendDrawing()||(k=k||jsPlumbUtil.uuid(),this.repaint({timestamp:k,recalc:!1}))),this.getAttachedElements&&!e&&K(this,g,jsPlumbUtil.uuid(),this)}}});var y=0,D=function(){var g=y+1;return y++,g},M=J.jsPlumbInstance=function(g){this.version="2.15.6",this.Defaults={Anchor:"Bottom",Anchors:[null,null],ConnectionsDetachable:!0,ConnectionOverlays:[],Connector:"Bezier",Container:null,DoNotThrowErrors:!1,DragOptions:{},DropOptions:{},Endpoint:"Dot",EndpointOverlays:[],Endpoints:[null,null],EndpointStyle:{fill:"#456"},EndpointStyles:[null,null],EndpointHoverStyle:null,EndpointHoverStyles:[null,null],HoverPaintStyle:null,LabelStyle:{color:"black"},ListStyle:{},LogEnabled:!1,Overlays:[],MaxConnections:1,PaintStyle:{"stroke-width":4,stroke:"#456"},ReattachConnections:!1,RenderMode:"svg",Scope:"jsPlumb_DefaultScope"},g&&C.extend(this.Defaults,g),this.logEnabled=this.Defaults.LogEnabled,this._connectionTypes={},this._endpointTypes={},c.EventGenerator.apply(this);var e=this,k=D(),L=e.bind,U={},G=1,P=function(s){if(s==null)return null;if(s.nodeType===3||s.nodeType===8)return{el:s,text:!0};var S=e.getElement(s);return{el:S,id:c.isString(s)&&S==null?s:lt(S)}};this.getInstanceIndex=function(){return k},this.setZoom=function(s,S){return G=s,e.fire("zoom",G),S&&e.repaintEverything(),!0},this.getZoom=function(){return G};for(var w in this.Defaults)U[w]=this.Defaults[w];var r,f=[];this.unbindContainer=function(){if(r!=null&&f.length>0)for(var s=0;s<f.length;s++)e.off(r,f[s][0],f[s][1])},this.setContainer=function(s){this.unbindContainer(),s=this.getElement(s),this.select().each(function(ct){ct.moveParent(s)}),this.selectEndpoints().each(function(ct){ct.moveParent(s)});var S=r;r=s,f.length=0;for(var m={endpointclick:"endpointClick",endpointdblclick:"endpointDblClick"},O=function(ct,ht,Ct){var mt=ht.srcElement||ht.target,St=(mt&&mt.parentNode?mt.parentNode._jsPlumb:null)||(mt?mt._jsPlumb:null)||(mt&&mt.parentNode&&mt.parentNode.parentNode?mt.parentNode.parentNode._jsPlumb:null);if(St){St.fire(ct,St,ht);var At=Ct&&m[Ct+ct]||ct;e.fire(At,St.component||St,ht)}},H=function(ct,ht,Ct){f.push([ct,Ct]),e.on(r,ct,ht,Ct)},z=function(ct){H(ct,".jtk-connector",function(ht){O(ct,ht)}),H(ct,".jtk-endpoint",function(ht){O(ct,ht,"endpoint")}),H(ct,".jtk-overlay",function(ht){O(ct,ht)})},tt=0;tt<it.length;tt++)z(it[tt]);for(var ft in b){var ot=b[ft].el;ot.parentNode===S&&(S.removeChild(ot),r.appendChild(ot))}},this.getContainer=function(){return r},this.bind=function(s,S){s==="ready"&&_?S():L.apply(e,[s,S])},e.importDefaults=function(s){for(var S in s)e.Defaults[S]=s[S];return s.Container&&e.setContainer(s.Container),e},e.restoreDefaults=function(){return e.Defaults=C.extend({},U),e};var l=null,_=!1,x=[],u={},j={},b={},A={},d={},E=!1,N=[],B=!1,Y=null,n=this.Defaults.Scope,o=1,t=function(){return""+o++},p=(function(s,S){r?r.appendChild(s):S?this.getElement(S).appendChild(s):this.appendToRoot(s)}).bind(this),v=function(s,S,m,O){var H={c:[],e:[]};if(!B&&(s=e.getElement(s),s!=null)){var z=lt(s),tt=s.querySelectorAll(".jtk-managed");m==null&&(m=jsPlumbUtil.uuid()),wt({elId:z,offset:S,recalc:!1,timestamp:m});for(var ft=0;ft<tt.length;ft++)wt({elId:tt[ft].getAttribute("id"),recalc:!0,timestamp:m});var ot=e.router.redraw(z,S,m,null,O);if(Array.prototype.push.apply(H.c,ot.c),Array.prototype.push.apply(H.e,ot.e),tt)for(var ct=0;ct<tt.length;ct++)ot=e.router.redraw(tt[ct].getAttribute("id"),null,m,null,O,!0),Array.prototype.push.apply(H.c,ot.c),Array.prototype.push.apply(H.e,ot.e)}return H},I=function(s){return j[s]},F=function(s,S){for(var m=s.scope.split(/\s/),O=S.scope.split(/\s/),H=0;H<m.length;H++)for(var z=0;z<O.length;z++)if(O[z]===m[H])return!0;return!1},X=function(s,S){var m=C.extend({},s);for(var O in S)S[O]&&(m[O]=S[O]);return m},R=(function(s,S){var m=C.extend({},s);if(S&&C.extend(m,S),m.source&&(m.source.endpoint?m.sourceEndpoint=m.source:m.source=e.getElement(m.source)),m.target&&(m.target.endpoint?m.targetEndpoint=m.target:m.target=e.getElement(m.target)),s.uuids&&(m.sourceEndpoint=I(s.uuids[0]),m.targetEndpoint=I(s.uuids[1])),m.sourceEndpoint&&m.sourceEndpoint.isFull()){c.log(e,"could not add connection; source endpoint is full");return}if(m.targetEndpoint&&m.targetEndpoint.isFull()){c.log(e,"could not add connection; target endpoint is full");return}if(!m.type&&m.sourceEndpoint&&(m.type=m.sourceEndpoint.connectionType),m.sourceEndpoint&&m.sourceEndpoint.connectorOverlays){m.overlays=m.overlays||[];for(var O=0,H=m.sourceEndpoint.connectorOverlays.length;O<H;O++)m.overlays.push(m.sourceEndpoint.connectorOverlays[O])}m.sourceEndpoint&&m.sourceEndpoint.scope&&(m.scope=m.sourceEndpoint.scope),!m["pointer-events"]&&m.sourceEndpoint&&m.sourceEndpoint.connectorPointerEvents&&(m["pointer-events"]=m.sourceEndpoint.connectorPointerEvents);var z=function(ft,ot,ct){var ht=X(ot,{anchor:m.anchors?m.anchors[ct]:m.anchor,endpoint:m.endpoints?m.endpoints[ct]:m.endpoint,paintStyle:m.endpointStyles?m.endpointStyles[ct]:m.endpointStyle,hoverPaintStyle:m.endpointHoverStyles?m.endpointHoverStyles[ct]:m.endpointHoverStyle});return e.addEndpoint(ft,ht)},tt=function(ft,ot,ct,ht){if(m[ft]&&!m[ft].endpoint&&!m[ft+"Endpoint"]&&!m.newConnection){var Ct=lt(m[ft]),mt=ct[Ct];if(mt=mt?mt[ht]:null,mt){if(!mt.enabled)return!1;var St=C.extend({},mt.def);delete St.label;var At=mt.endpoint!=null&&mt.endpoint._jsPlumb?mt.endpoint:z(m[ft],St,ot);if(At.isFull())return!1;m[ft+"Endpoint"]=At,!m.scope&&St.scope&&(m.scope=St.scope),mt.uniqueEndpoint?mt.endpoint?At.finalEndpoint=mt.endpoint:(mt.endpoint=At,At.setDeleteOnEmpty(!1)):At.setDeleteOnEmpty(!0),ot===0&&mt.def.connectorOverlays&&(m.overlays=m.overlays||[],Array.prototype.push.apply(m.overlays,mt.def.connectorOverlays))}}};if(tt("source",0,this.sourceEndpointDefinitions,m.type||"default")!==!1&&tt("target",1,this.targetEndpointDefinitions,m.type||"default")!==!1)return m.sourceEndpoint&&m.targetEndpoint&&(F(m.sourceEndpoint,m.targetEndpoint)||(m=null)),m}).bind(e),W=function(s){var S=e.Defaults.ConnectionType||e.getDefaultConnectionType();s._jsPlumb=e,s.newConnection=W,s.newEndpoint=st,s.endpointsByUUID=j,s.endpointsByElement=u,s.finaliseConnection=at,s.id="con_"+t();var m=new S(s);return m.isDetachable()&&(m.endpoints[0].initDraggable("_jsPlumbSource"),m.endpoints[1].initDraggable("_jsPlumbTarget")),m},at=e.finaliseConnection=function(s,S,m,O){if(S=S||{},s.suspendedEndpoint||x.push(s),s.pending=null,s.endpoints[0].isTemporarySource=!1,O!==!1&&e.router.newConnection(s),v(s.source),!S.doNotFireConnectionEvent&&S.fireEvent!==!1){var H={connection:s,source:s.source,target:s.target,sourceId:s.sourceId,targetId:s.targetId,sourceEndpoint:s.endpoints[0],targetEndpoint:s.endpoints[1]};e.fire("connection",H,m)}},st=function(s,S){var m=e.Defaults.EndpointType||C.Endpoint,O=C.extend({},s);O._jsPlumb=e,O.newConnection=W,O.newEndpoint=st,O.endpointsByUUID=j,O.endpointsByElement=u,O.fireDetachEvent=Z,O.elementId=S||lt(O.source);var H=new m(O);return H.id="ep_"+t(),Qt(O.elementId,O.source),C.headless||e.getDragManager().endpointAdded(O.source,S),H},nt=function(s,S,m){var O=u[s];if(O&&O.length)for(var H=0,z=O.length;H<z;H++){for(var tt=0,ft=O[H].connections.length;tt<ft;tt++){var ot=S(O[H].connections[tt]);if(ot)return}m&&m(O[H])}},rt=function(s,S,m){S=S==="block";var O=null;m&&(O=function(z){z.setVisible(S,!0,!0)});var H=P(s);nt(H.id,function(z){if(S&&m){var tt=z.sourceId===H.id?1:0;z.endpoints[tt].isVisible()&&z.setVisible(!0)}else z.setVisible(S)},O)},gt=function(s,S){var m=null;S&&(m=function(O){var H=O.isVisible();O.setVisible(!H)}),nt(s,function(O){var H=O.isVisible();O.setVisible(!H)},m)},dt=function(s){var S=A[s];return S?{o:S,s:N[s]}:wt({elId:s})},lt=function(s,S,m){if(c.isString(s))return s;if(s==null)return null;var O=e.getAttribute(s,"id");return(!O||O==="undefined")&&(arguments.length===2&&arguments[1]!==void 0?O=S:(arguments.length===1||arguments.length===3&&!arguments[2])&&(O="jsPlumb_"+k+"_"+t()),m||e.setAttribute(s,"id",O)),O};this.setConnectionBeingDragged=function(s){E=s},this.isConnectionBeingDragged=function(){return E},this.getManagedElements=function(){return b},this.connectorClass="jtk-connector",this.connectorOutlineClass="jtk-connector-outline",this.connectedClass="jtk-connected",this.hoverClass="jtk-hover",this.endpointClass="jtk-endpoint",this.endpointConnectedClass="jtk-endpoint-connected",this.endpointFullClass="jtk-endpoint-full",this.endpointDropAllowedClass="jtk-endpoint-drop-allowed",this.endpointDropForbiddenClass="jtk-endpoint-drop-forbidden",this.overlayClass="jtk-overlay",this.draggingClass="jtk-dragging",this.elementDraggingClass="jtk-element-dragging",this.sourceElementDraggingClass="jtk-source-element-dragging",this.targetElementDraggingClass="jtk-target-element-dragging",this.endpointAnchorClassPrefix="jtk-endpoint-anchor",this.hoverSourceClass="jtk-source-hover",this.hoverTargetClass="jtk-target-hover",this.dragSelectClass="jtk-drag-select",this.Anchors={},this.Connectors={svg:{}},this.Endpoints={svg:{}},this.Overlays={svg:{}},this.ConnectorRenderers={},this.SVG="svg",this.addEndpoint=function(s,S,m){m=m||{};var O=C.extend({},m);C.extend(O,S),O.endpoint=O.endpoint||e.Defaults.Endpoint,O.paintStyle=O.paintStyle||e.Defaults.EndpointStyle;for(var H=[],z=c.isArray(s)||s.length!=null&&!c.isString(s)?s:[s],tt=0,ft=z.length;tt<ft;tt++){O.source=e.getElement(z[tt]),Jt(O.source);var ot=lt(O.source),ct=st(O,ot),ht=Qt(ot,O.source,null,!B).info.o;c.addToList(u,ot,ct),B||ct.paint({anchorLoc:ct.anchor.compute({xy:[ht.left,ht.top],wh:N[ot],element:ct,timestamp:Y,rotation:this.getRotation(ot)}),timestamp:Y}),H.push(ct)}return H.length===1?H[0]:H},this.addEndpoints=function(s,S,m){for(var O=[],H=0,z=S.length;H<z;H++){var tt=e.addEndpoint(s,S[H],m);c.isArray(tt)?Array.prototype.push.apply(O,tt):O.push(tt)}return O},this.animate=function(s,S,m){if(!this.animationSupported)return!1;m=m||{};var O=e.getElement(s),H=lt(O),z=C.animEvents.step,tt=C.animEvents.complete;m[z]=c.wrap(m[z],function(){e.revalidate(H)}),m[tt]=c.wrap(m[tt],function(){e.revalidate(H)}),e.doAnimate(O,S,m)},this.checkCondition=function(s,S){var m=e.getListener(s),O=!0;if(m&&m.length>0){var H=Array.prototype.slice.call(arguments,1);try{for(var z=0,tt=m.length;z<tt;z++)O=O&&m[z].apply(m[z],H)}catch(ft){c.log(e,"cannot check condition ["+s+"]"+ft)}}return O},this.connect=function(s,S){var m=R(s,S),O;if(m){if(m.source==null&&m.sourceEndpoint==null){c.log("Cannot establish connection - source does not exist");return}if(m.target==null&&m.targetEndpoint==null){c.log("Cannot establish connection - target does not exist");return}Jt(m.source),O=W(m),at(O,m)}return O};var yt=[{el:"source",elId:"sourceId",epDefs:"sourceEndpointDefinitions"},{el:"target",elId:"targetId",epDefs:"targetEndpointDefinitions"}],pt=(function(s,S,m,O){var H,z=yt[m],tt=s[z.elId];s[z.el];var ft,ot,ct=s.endpoints[m],ht={index:m,originalSourceId:m===0?tt:s.sourceId,newSourceId:s.sourceId,originalTargetId:m===1?tt:s.targetId,newTargetId:s.targetId,connection:s};if(S.constructor===C.Endpoint)H=S,H.addConnection(s),S=H.element;else if(ft=lt(S),ot=this[z.epDefs][ft],ft===s[z.elId])H=null;else if(ot)for(var Ct in ot){if(!ot[Ct].enabled)return;H=ot[Ct].endpoint!=null&&ot[Ct].endpoint._jsPlumb?ot[Ct].endpoint:this.addEndpoint(S,ot[Ct].def),ot[Ct].uniqueEndpoint&&(ot[Ct].endpoint=H),H.addConnection(s)}else H=s.makeEndpoint(m===0,S,ft);return H!=null&&(ct.detachFromConnection(s),s.endpoints[m]=H,s[z.el]=H.element,s[z.elId]=H.elementId,ht[m===0?"newSourceId":"newTargetId"]=H.elementId,q(ht),O||s.repaint()),ht.element=S,ht}).bind(this);this.setSource=function(s,S,m){var O=pt(s,S,0,m);this.router.sourceOrTargetChanged(O.originalSourceId,O.newSourceId,s,O.el,0)},this.setTarget=function(s,S,m){var O=pt(s,S,1,m);this.router.sourceOrTargetChanged(O.originalTargetId,O.newTargetId,s,O.el,1)},this.deleteEndpoint=function(s,S,m){var O=typeof s=="string"?j[s]:s;return O&&e.deleteObject({endpoint:O,dontUpdateHover:S,deleteAttachedObjects:m}),e},this.deleteEveryEndpoint=function(){var s=e.setSuspendDrawing(!0);for(var S in u){var m=u[S];if(m&&m.length)for(var O=0,H=m.length;O<H;O++)e.deleteEndpoint(m[O],!0)}u={},b={},j={},A={},d={},e.router.reset();var z=e.getDragManager();return z&&z.reset(),s||e.setSuspendDrawing(!1),e};var Z=function(s,S,m){var O=e.Defaults.ConnectionType||e.getDefaultConnectionType(),H=s.constructor===O,z=H?{connection:s,source:s.source,target:s.target,sourceId:s.sourceId,targetId:s.targetId,sourceEndpoint:s.endpoints[0],targetEndpoint:s.endpoints[1]}:s;S&&e.fire("connectionDetached",z,m),e.fire("internal.connectionDetached",z,m),e.router.connectionDetached(z)},q=e.fireMoveEvent=function(s,S){e.fire("connectionMoved",s,S)};this.unregisterEndpoint=function(s){s._jsPlumb.uuid&&(j[s._jsPlumb.uuid]=null),e.router.deleteEndpoint(s);for(var S in u){var m=u[S];if(m){for(var O=[],H=0,z=m.length;H<z;H++)m[H]!==s&&O.push(m[H]);u[S]=O}u[S].length<1&&delete u[S]}};var $="isDetachAllowed",ut="beforeDetach",vt="checkCondition";this.deleteConnection=function(s,S){return s!=null&&(S=S||{},S.force||c.functionChain(!0,!1,[[s.endpoints[0],$,[s]],[s.endpoints[1],$,[s]],[s,$,[s]],[e,vt,[ut,s]]]))?(s.setHover(!1),Z(s,!s.pending&&S.fireEvent!==!1,S.originalEvent),s.endpoints[0].detachFromConnection(s),s.endpoints[1].detachFromConnection(s),c.removeWithFunction(x,function(m){return s.id===m.id}),s.cleanup(),s.destroy(),!0):!1},this.deleteEveryConnection=function(s){s=s||{};var S=x.length,m=0;return e.batch(function(){for(var O=0;O<S;O++)m+=e.deleteConnection(x[0],s)?1:0}),m},this.deleteConnectionsForElement=function(s,S){S=S||{},s=e.getElement(s);var m=lt(s),O=u[m];if(O&&O.length)for(var H=0,z=O.length;H<z;H++)O[H].deleteEveryConnection(S);return e},this.deleteObject=function(s){var S={endpoints:{},connections:{},endpointCount:0,connectionCount:0},m=s.deleteAttachedObjects!==!1,O=function(ht){ht!=null&&S.connections[ht.id]==null&&(!s.dontUpdateHover&&ht._jsPlumb!=null&&ht.setHover(!1),S.connections[ht.id]=ht,S.connectionCount++)},H=function(ht){if(ht!=null&&S.endpoints[ht.id]==null&&(!s.dontUpdateHover&&ht._jsPlumb!=null&&ht.setHover(!1),S.endpoints[ht.id]=ht,S.endpointCount++,m))for(var Ct=0;Ct<ht.connections.length;Ct++){var mt=ht.connections[Ct];O(mt)}};s.connection?O(s.connection):H(s.endpoint);for(var z in S.connections){var tt=S.connections[z];if(tt._jsPlumb){c.removeWithFunction(x,function(ht){return tt.id===ht.id}),Z(tt,s.fireEvent===!1?!1:!tt.pending,s.originalEvent);var ft=s.deleteAttachedObjects==null?null:!s.deleteAttachedObjects;tt.endpoints[0].detachFromConnection(tt,null,ft),tt.endpoints[1].detachFromConnection(tt,null,ft),tt.cleanup(!0),tt.destroy(!0)}}for(var ot in S.endpoints){var ct=S.endpoints[ot];ct._jsPlumb&&(e.unregisterEndpoint(ct),ct.cleanup(!0),ct.destroy(!0))}return S};var Dt=function(s,S,m,O){for(var H=0,z=s.length;H<z;H++)s[H][S].apply(s[H],m);return O(s)},Ot=function(s,S,m){for(var O=[],H=0,z=s.length;H<z;H++)O.push([s[H][S].apply(s[H],m),s[H]]);return O},Ft=function(s,S,m){return function(){return Dt(s,S,arguments,m)}},Rt=function(s,S){return function(){return Ot(s,S,arguments)}},Gt=function(s,S){var m=[];if(s)if(typeof s=="string"){if(s==="*")return s;m.push(s)}else if(S)m=s;else if(s.length)for(var O=0,H=s.length;O<H;O++)m.push(P(s[O]).id);else m.push(P(s).id);return m},Nt=function(s,S,m){return s==="*"?!0:s.length>0?s.indexOf(S)!==-1:!m};this.getConnections=function(s,S){s?s.constructor===String&&(s={scope:s}):s={};for(var m=s.scope||e.getDefaultScope(),O=Gt(m,!0),H=Gt(s.source),z=Gt(s.target),tt=!S&&O.length>1?{}:[],ft=function(St,At){if(!S&&O.length>1){var kt=tt[St];kt==null&&(kt=tt[St]=[]),kt.push(At)}else tt.push(At)},ot=0,ct=x.length;ot<ct;ot++){var ht=x[ot],Ct=ht.proxies&&ht.proxies[0]?ht.proxies[0].originalEp.elementId:ht.sourceId,mt=ht.proxies&&ht.proxies[1]?ht.proxies[1].originalEp.elementId:ht.targetId;Nt(O,ht.scope)&&Nt(H,Ct)&&Nt(z,mt)&&ft(ht.scope,ht)}return tt};var ne=function(s,S){return function(m){for(var O=0,H=s.length;O<H;O++)m(s[O]);return S(s)}},ie=function(s){return function(S){return s[S]}},se=function(s,S){var m={length:s.length,each:ne(s,S),get:ie(s)},O=["setHover","removeAllOverlays","setLabel","addClass","addOverlay","removeOverlay","removeOverlays","showOverlay","hideOverlay","showOverlays","hideOverlays","setPaintStyle","setHoverPaintStyle","setSuspendEvents","setParameter","setParameters","setVisible","repaint","addType","toggleType","removeType","removeClass","setType","bind","unbind"],H=["getLabel","getOverlay","isHover","getParameter","getParameters","getPaintStyle","getHoverPaintStyle","isVisible","hasType","getType","isSuspendEvents"],z,tt;for(z=0,tt=O.length;z<tt;z++)m[O[z]]=Ft(s,O[z],S);for(z=0,tt=H.length;z<tt;z++)m[H[z]]=Rt(s,H[z]);return m},Ut=function(s){var S=se(s,Ut);return C.extend(S,{setDetachable:Ft(s,"setDetachable",Ut),setReattach:Ft(s,"setReattach",Ut),setConnector:Ft(s,"setConnector",Ut),delete:function(){for(var m=0,O=s.length;m<O;m++)e.deleteConnection(s[m])},isDetachable:Rt(s,"isDetachable"),isReattach:Rt(s,"isReattach")})},Zt=function(s){var S=se(s,Zt);return C.extend(S,{setEnabled:Ft(s,"setEnabled",Zt),setAnchor:Ft(s,"setAnchor",Zt),isEnabled:Rt(s,"isEnabled"),deleteEveryConnection:function(){for(var m=0,O=s.length;m<O;m++)s[m].deleteEveryConnection()},delete:function(){for(var m=0,O=s.length;m<O;m++)e.deleteEndpoint(s[m])}})};this.select=function(s){return s=s||{},s.scope=s.scope||"*",Ut(s.connections||e.getConnections(s,!0))},this.selectEndpoints=function(s){s=s||{},s.scope=s.scope||"*";var S=!s.element&&!s.source&&!s.target,m=S?"*":Gt(s.element),O=S?"*":Gt(s.source),H=S?"*":Gt(s.target),z=Gt(s.scope,!0),tt=[];for(var ft in u){var ot=Nt(m,ft,!0),ct=Nt(O,ft,!0),ht=O!=="*",Ct=Nt(H,ft,!0),mt=H!=="*";if(ot||ct||Ct)t:for(var St=0,At=u[ft].length;St<At;St++){var kt=u[ft][St];if(Nt(z,kt.scope,!0)){var re=ht&&O.length>0&&!kt.isSource,oe=mt&&H.length>0&&!kt.isTarget;if(re||oe)continue t;tt.push(kt)}}}return Zt(tt)},this.getAllConnections=function(){return x},this.getDefaultScope=function(){return n},this.getEndpoint=I,this.getEndpoints=function(s){return u[P(s).id]||[]},this.getDefaultEndpointType=function(){return C.Endpoint},this.getDefaultConnectionType=function(){return C.Connection},this.getId=lt,this.draw=v,this.info=P,this.appendElement=p;var Ht=!1;this.isHoverSuspended=function(){return Ht},this.setHoverSuspended=function(s){Ht=s},this.hide=function(s,S){return rt(s,"none",S),e},this.idstamp=t;var Jt=function(s){if(!r&&s){var S=e.getElement(s);S.offsetParent&&e.setContainer(S.offsetParent)}},$t=function(){e.Defaults.Container&&e.setContainer(e.Defaults.Container)},Qt=e.manage=function(s,S,m,O){return b[s]?O&&(b[s].info=wt({elId:s,timestamp:Y,recalc:!0})):(b[s]={el:S,endpoints:[],connections:[],rotation:0},b[s].info=wt({elId:s,timestamp:Y}),e.addClass(S,"jtk-managed"),m||e.fire("manageElement",{id:s,info:b[s].info,el:S})),b[s]};this.unmanage=function(s){if(b[s]){var S=b[s].el;e.removeClass(S,"jtk-managed"),delete b[s],e.fire("unmanageElement",{id:s,el:S})}},this.rotate=function(s,S,m){return b[s]&&(b[s].rotation=S,b[s].el.style.transform="rotate("+S+"deg)",b[s].el.style.transformOrigin="center center",m!==!0)?this.revalidate(s):{c:[],e:[]}},this.getRotation=function(s){return b[s]&&b[s].rotation||0};var wt=function(s){var S=s.timestamp,m=s.recalc,O=s.offset,H=s.elId,z;return B&&!S&&(S=Y),!m&&S&&S===d[H]?{o:s.offset||A[H],s:N[H]}:(m||!O&&A[H]==null?(z=b[H]?b[H].el:null,z!=null&&(N[H]=e.getSize(z),A[H]=e.getOffset(z),d[H]=S)):(A[H]=O||A[H],N[H]==null&&(z=b[H].el,z!=null&&(N[H]=e.getSize(z))),d[H]=S),A[H]&&!A[H].right&&(A[H].right=A[H].left+N[H][0],A[H].bottom=A[H].top+N[H][1],A[H].width=N[H][0],A[H].height=N[H][1],A[H].centerx=A[H].left+A[H].width/2,A[H].centery=A[H].top+A[H].height/2),{o:A[H],s:N[H]})};this.updateOffset=wt,this.init=(function(){_||($t(),e.router=new J.jsPlumb.DefaultRouter(e),e.anchorManager=e.router.anchorManager,_=!0,e.fire("ready",e))}).bind(this),this.log=l,this.jsPlumbUIComponent=a,this.makeAnchor=function(){var s,S=function(tt,ft){if(J.jsPlumb.Anchors[tt])return new J.jsPlumb.Anchors[tt](ft);if(!e.Defaults.DoNotThrowErrors)throw{msg:"jsPlumb: unknown anchor type '"+tt+"'"}};if(arguments.length===0)return null;var m=arguments[0],O=arguments[1],H=null;if(m.compute&&m.getOrientation)return m;if(typeof m=="string")H=S(arguments[0],{elementId:O,jsPlumbInstance:e});else if(c.isArray(m))if(c.isArray(m[0])||c.isString(m[0]))m.length===2&&c.isObject(m[1])?c.isString(m[0])?(s=J.jsPlumb.extend({elementId:O,jsPlumbInstance:e},m[1]),H=S(m[0],s)):(s=J.jsPlumb.extend({elementId:O,jsPlumbInstance:e,anchors:m[0]},m[1]),H=new J.jsPlumb.DynamicAnchor(s)):H=new C.DynamicAnchor({anchors:m,selector:null,elementId:O,jsPlumbInstance:e});else{var z={x:m[0],y:m[1],orientation:m.length>=4?[m[2],m[3]]:[0,0],offsets:m.length>=6?[m[4],m[5]]:[0,0],elementId:O,jsPlumbInstance:e,cssClass:m.length===7?m[6]:null};H=new J.jsPlumb.Anchor(z),H.clone=function(){return new J.jsPlumb.Anchor(z)}}return H.id||(H.id="anchor_"+t()),H},this.makeAnchors=function(s,S,m){for(var O=[],H=0,z=s.length;H<z;H++)typeof s[H]=="string"?O.push(J.jsPlumb.Anchors[s[H]]({elementId:S,jsPlumbInstance:m})):c.isArray(s[H])&&O.push(e.makeAnchor(s[H],S,m));return O},this.makeDynamicAnchor=function(s,S){return new J.jsPlumb.DynamicAnchor({anchors:s,selector:S,elementId:null,jsPlumbInstance:e})},this.targetEndpointDefinitions={},this.sourceEndpointDefinitions={};var Bt=function(s,S,m,O,H){for(var z=s.target||s.srcElement,tt=!1,ft=O.getSelector(S,m),ot=0;ot<ft.length;ot++)if(ft[ot]===z){tt=!0;break}return H?!tt:tt},Mt=function(s,S,m,O,H){var z=new a(S),tt=S._jsPlumb.EndpointDropHandler({jsPlumb:e,enabled:function(){return s.def.enabled},isFull:function(){var ot=e.select({target:s.id}).length;return s.def.maxConnections>0&&ot>=s.def.maxConnections},element:s.el,elementId:s.id,isSource:O,isTarget:H,addClass:function(ot){e.addClass(s.el,ot)},removeClass:function(ot){e.removeClass(s.el,ot)},onDrop:function(ot){var ct=ot.endpoints[0];ct.anchor.locked=!1},isDropAllowed:function(){return z.isDropAllowed.apply(z,arguments)},isRedrop:function(ot){return ot.suspendedElement!=null&&ot.suspendedEndpoint!=null&&ot.suspendedEndpoint.element===s.el},getEndpoint:function(ot){var ct=s.def.endpoint;if(ct==null||ct._jsPlumb==null){var ht=e.deriveEndpointAndAnchorSpec(ot.getType().join(" "),!0),Ct=ht.endpoints?J.jsPlumb.extend(S,{endpoint:s.def.def.endpoint||ht.endpoints[1]}):S;ht.anchors&&(Ct=J.jsPlumb.extend(Ct,{anchor:s.def.def.anchor||ht.anchors[1]})),ct=e.addEndpoint(s.el,Ct),ct._mtNew=!0}if(S.uniqueEndpoint&&(s.def.endpoint=ct),ct.setDeleteOnEmpty(!0),ot.isDetachable()&&ct.initDraggable(),ct.anchor.positionFinder!=null){var mt=e.getUIPosition(arguments,e.getZoom()),St=e.getOffset(s.el),At=e.getSize(s.el),kt=mt==null?[0,0]:ct.anchor.positionFinder(mt,St,At,ct.anchor.constructorParams);ct.anchor.x=kt[0],ct.anchor.y=kt[1]}return ct},maybeCleanup:function(ot){ot._mtNew&&ot.connections.length===0?e.deleteObject({endpoint:ot}):delete ot._mtNew}}),ft=J.jsPlumb.dragEvents.drop;return m.scope=m.scope||S.scope||e.Defaults.Scope,m[ft]=c.wrap(m[ft],tt,!0),m.rank=S.rank||0,H&&(m[J.jsPlumb.dragEvents.over]=function(){return!0}),S.allowLoopback===!1&&(m.canDrop=function(ot){var ct=ot.getDragElement()._jsPlumbRelatedElement;return ct!==s.el}),e.initDroppable(s.el,m,"internal"),tt};this.makeTarget=function(s,S,m){var O=J.jsPlumb.extend({_jsPlumb:this},m);J.jsPlumb.extend(O,S);for(var H=O.maxConnections||-1,z=(function(ct){var ht=P(ct),Ct=ht.id,mt=J.jsPlumb.extend({},O.dropOptions||{}),St=O.connectionType||"default";this.targetEndpointDefinitions[Ct]=this.targetEndpointDefinitions[Ct]||{},Jt(Ct),ht.el._isJsPlumbGroup&&mt.rank==null&&(mt.rank=-1);var At={def:J.jsPlumb.extend({},O),uniqueEndpoint:O.uniqueEndpoint,maxConnections:H,enabled:!0};O.createEndpoint&&(At.uniqueEndpoint=!0,At.endpoint=e.addEndpoint(ct,At.def),At.endpoint.setDeleteOnEmpty(!1)),ht.def=At,this.targetEndpointDefinitions[Ct][St]=At,Mt(ht,O,mt,O.isSource===!0,!0),ht.el._katavorioDrop[ht.el._katavorioDrop.length-1].targetDef=At}).bind(this),tt=s.length&&s.constructor!==String?s:[s],ft=0,ot=tt.length;ft<ot;ft++)z(tt[ft]);return this},this.unmakeTarget=function(s,S){var m=P(s);return e.destroyDroppable(m.el,"internal"),S||delete this.targetEndpointDefinitions[m.id],this},this.makeSource=function(s,S,m){var O=J.jsPlumb.extend({_jsPlumb:this},m);J.jsPlumb.extend(O,S);var H=O.connectionType||"default",z=e.deriveEndpointAndAnchorSpec(H);O.endpoint=O.endpoint||z.endpoints[0],O.anchor=O.anchor||z.anchors[0];for(var tt=O.maxConnections||-1,ft=O.onMaxConnections,ot=(function(mt){var St=mt.id,At=this.getElement(mt.el);this.sourceEndpointDefinitions[St]=this.sourceEndpointDefinitions[St]||{},Jt(St);var kt={def:J.jsPlumb.extend({},O),uniqueEndpoint:O.uniqueEndpoint,maxConnections:tt,enabled:!0};O.createEndpoint&&(kt.uniqueEndpoint=!0,kt.endpoint=e.addEndpoint(s,kt.def),kt.endpoint.setDeleteOnEmpty(!1)),this.sourceEndpointDefinitions[St][H]=kt,mt.def=kt;var re=J.jsPlumb.dragEvents.stop,oe=J.jsPlumb.dragEvents.drag,Vt=J.jsPlumb.extend({},O.dragOptions||{}),he=Vt.drag,ce=Vt.stop,Tt=null,ae=!1;Vt.scope=Vt.scope||O.scope,Vt[oe]=c.wrap(Vt[oe],function(){he&&he.apply(this,arguments),ae=!1}),Vt[re]=c.wrap(Vt[re],(function(){if(ce&&ce.apply(this,arguments),this.currentlyDragging=!1,Tt._jsPlumb!=null){var It=O.anchor||this.Defaults.Anchor,Lt=Tt.anchor,le=Tt.connections[0],zt=this.makeAnchor(It,St,this),te=Tt.element;if(zt.positionFinder!=null){var Xt=e.getOffset(te),qt=this.getSize(te),ue={left:Xt.left+Lt.x*qt[0],top:Xt.top+Lt.y*qt[1]},ee=zt.positionFinder(ue,Xt,qt,zt.constructorParams);zt.x=ee[0],zt.y=ee[1]}Tt.setAnchor(zt,!0),Tt.repaint(),this.repaint(Tt.elementId),le!=null&&this.repaint(le.targetId)}}).bind(this));var fe=(function(It){if(!(It.which===3||It.button===2)){St=this.getId(this.getElement(mt.el));var Lt=this.sourceEndpointDefinitions[St][H];if(Lt.enabled){if(O.filter){var le=c.isString(O.filter)?Bt(It,mt.el,O.filter,this,O.filterExclude):O.filter(It,mt.el);if(le===!1)return}var zt=this.select({source:St}).length;if(Lt.maxConnections>=0&&zt>=Lt.maxConnections)return ft&&ft({element:mt.el,maxConnections:tt},It),!1;var te=J.jsPlumb.getPositionOnElement(It,At,G),Xt={};J.jsPlumb.extend(Xt,Lt.def),Xt.isTemporarySource=!0,Xt.anchor=[te[0],te[1],0,0],Xt.dragOptions=Vt,Lt.def.scope&&(Xt.scope=Lt.def.scope),Tt=this.addEndpoint(St,Xt),ae=!0,Tt.setDeleteOnEmpty(!0),Lt.uniqueEndpoint&&(Lt.endpoint?Tt.finalEndpoint=Lt.endpoint:(Lt.endpoint=Tt,Tt.setDeleteOnEmpty(!1)));var qt=function(){e.off(Tt.canvas,"mouseup",qt),e.off(mt.el,"mouseup",qt),ae&&(ae=!1,e.deleteEndpoint(Tt))};e.on(Tt.canvas,"mouseup",qt),e.on(mt.el,"mouseup",qt);var ue={};if(Lt.def.extract)for(var ee in Lt.def.extract){var de=(It.srcElement||It.target).getAttribute(ee);de&&(ue[Lt.def.extract[ee]]=de)}e.trigger(Tt.canvas,"mousedown",It,ue),c.consume(It)}}}).bind(this);this.on(mt.el,"mousedown",fe),kt.trigger=fe,O.filter&&(c.isString(O.filter)||c.isFunction(O.filter))&&e.setDragFilter(mt.el,O.filter);var ge=J.jsPlumb.extend({},O.dropOptions||{});Mt(mt,O,ge,!0,O.isTarget===!0)}).bind(this),ct=s.length&&s.constructor!==String?s:[s],ht=0,Ct=ct.length;ht<Ct;ht++)ot(P(ct[ht]));return this},this.unmakeSource=function(s,S,m){var O=P(s);e.destroyDroppable(O.el,"internal");var H=this.sourceEndpointDefinitions[O.id];if(H){for(var z in H)if(S==null||S===z){var tt=H[z].trigger;tt&&e.off(O.el,"mousedown",tt),m||delete this.sourceEndpointDefinitions[O.id][z]}}return this},this.unmakeEverySource=function(){for(var s in this.sourceEndpointDefinitions)e.unmakeSource(s,null,!0);return this.sourceEndpointDefinitions={},this};var Yt=(function(s,S,m){S=c.isArray(S)?S:[S];var O=lt(s);m=m||"default";for(var H=0;H<S.length;H++){var z=this[S[H]][O];if(z&&z[m])return z[m].def.scope||this.Defaults.Scope}}).bind(this),Wt=(function(s,S,m,O){m=c.isArray(m)?m:[m];var H=lt(s);O=O||"default";for(var z=0;z<m.length;z++){var tt=this[m[z]][H];tt&&tt[O]&&(tt[O].def.scope=S)}}).bind(this);this.getScope=function(s,S){return Yt(s,["sourceEndpointDefinitions","targetEndpointDefinitions"])},this.getSourceScope=function(s){return Yt(s,"sourceEndpointDefinitions")},this.getTargetScope=function(s){return Yt(s,"targetEndpointDefinitions")},this.setScope=function(s,S,m){this.setSourceScope(s,S,m),this.setTargetScope(s,S,m)},this.setSourceScope=function(s,S,m){Wt(s,S,"sourceEndpointDefinitions",m),this.setDragScope(s,S)},this.setTargetScope=function(s,S,m){Wt(s,S,"targetEndpointDefinitions",m),this.setDropScope(s,S)},this.unmakeEveryTarget=function(){for(var s in this.targetEndpointDefinitions)e.unmakeTarget(s,!0);return this.targetEndpointDefinitions={},this};var Q=(function(s,S,m,O,H){var z=s==="source"?this.sourceEndpointDefinitions:this.targetEndpointDefinitions,tt,ft,ot;if(H=H||"default",S.length&&!c.isString(S)){tt=[];for(var ct=0,ht=S.length;ct<ht;ct++)ft=P(S[ct]),z[ft.id]&&z[ft.id][H]&&(tt[ct]=z[ft.id][H].enabled,ot=O?!tt[ct]:m,z[ft.id][H].enabled=ot,e[ot?"removeClass":"addClass"](ft.el,"jtk-"+s+"-disabled"))}else{ft=P(S);var Ct=ft.id;z[Ct]&&z[Ct][H]&&(tt=z[Ct][H].enabled,ot=O?!tt:m,z[Ct][H].enabled=ot,e[ot?"removeClass":"addClass"](ft.el,"jtk-"+s+"-disabled"))}return tt}).bind(this),_t=(function(s,S){if(s!=null){if(c.isString(s)||!s.length)return S.apply(this,[s]);if(s.length)return S.apply(this,[s[0]])}}).bind(this);this.toggleSourceEnabled=function(s,S){return Q("source",s,null,!0,S),this.isSourceEnabled(s,S)},this.setSourceEnabled=function(s,S,m){return Q("source",s,S,null,m)},this.isSource=function(s,S){return S=S||"default",_t(s,(function(m){var O=this.sourceEndpointDefinitions[P(m).id];return O!=null&&O[S]!=null}).bind(this))},this.isSourceEnabled=function(s,S){return S=S||"default",_t(s,(function(m){var O=this.sourceEndpointDefinitions[P(m).id];return O&&O[S]&&O[S].enabled===!0}).bind(this))},this.toggleTargetEnabled=function(s,S){return Q("target",s,null,!0,S),this.isTargetEnabled(s,S)},this.isTarget=function(s,S){return S=S||"default",_t(s,(function(m){var O=this.targetEndpointDefinitions[P(m).id];return O!=null&&O[S]!=null}).bind(this))},this.isTargetEnabled=function(s,S){return S=S||"default",_t(s,(function(m){var O=this.targetEndpointDefinitions[P(m).id];return O&&O[S]&&O[S].enabled===!0}).bind(this))},this.setTargetEnabled=function(s,S,m){return Q("target",s,S,null,m)},this.ready=function(s){e.bind("ready",s)};var Pt=function(s,S){if(typeof s=="object"&&s.length)for(var m=0,O=s.length;m<O;m++)S(s[m]);else S(s);return e};this.repaint=function(s,S,m){return Pt(s,function(O){v(O,S,m)})},this.revalidate=function(s,S,m){var O=m?s:e.getId(s);e.updateOffset({elId:O,recalc:!0,timestamp:S});var H=e.getDragManager();return H&&H.updateOffsets(O),v(s,null,S)},this.repaintEverything=function(){var s=jsPlumbUtil.uuid(),S;for(S in u)e.updateOffset({elId:S,recalc:!0,timestamp:s});for(S in u)v(S,null,s);return this},this.removeAllEndpoints=function(s,S,m){m=m||[];var O=function(H){var z=P(H),tt=u[z.id],ft,ot;if(tt)for(m.push(z),ft=0,ot=tt.length;ft<ot;ft++)e.deleteEndpoint(tt[ft],!1);if(delete u[z.id],S&&z.el&&z.el.nodeType!==3&&z.el.nodeType!==8)for(ft=0,ot=z.el.childNodes.length;ft<ot;ft++)O(z.el.childNodes[ft])};return O(s),this};var bt=function(s,S){e.removeAllEndpoints(s.id,!0,S);for(var m=e.getDragManager(),O=function(z){m&&m.elementRemoved(z.id),e.router.elementRemoved(z.id),e.isSource(z.el)&&e.unmakeSource(z.el),e.isTarget(z.el)&&e.unmakeTarget(z.el),e.destroyDraggable(z.el),e.destroyDroppable(z.el),delete e.floatingConnections[z.id],delete b[z.id],delete A[z.id],z.el&&(e.removeElement(z.el),z.el._jsPlumb=null)},H=1;H<S.length;H++)O(S[H]);O(s)};this.remove=function(s,S){var m=P(s),O=[];return m.text&&m.el.parentNode?m.el.parentNode.removeChild(m.el):m.id&&e.batch(function(){bt(m,O)},S===!0),e},this.empty=function(s,S){var m=[],O=function(H,z){var tt=P(H);if(tt.text)tt.el.parentNode.removeChild(tt.el);else if(tt.el){for(;tt.el.childNodes.length>0;)O(tt.el.childNodes[0]);z||bt(tt,m)}};return e.batch(function(){O(s,!0)},S===!1),e},this.reset=function(s){e.silently((function(){Ht=!1,e.removeAllGroups(),e.removeGroupManager(),e.deleteEveryEndpoint(),s||e.unbind(),this.targetEndpointDefinitions={},this.sourceEndpointDefinitions={},x.length=0,this.doReset&&this.doReset()}).bind(this))},this.destroy=function(){this.reset(),r=null,f=null};var Et=function(s){s.canvas&&s.canvas.parentNode&&s.canvas.parentNode.removeChild(s.canvas),s.cleanup(),s.destroy()};this.clear=function(){e.select().each(Et),e.selectEndpoints().each(Et),u={},j={}},this.setDefaultScope=function(s){return n=s,e},this.deriveEndpointAndAnchorSpec=function(s,S){for(var m=((S?"":"default ")+s).split(/[\s]/),O=null,H=null,z=null,tt=null,ft=0;ft<m.length;ft++){var ot=e.getType(m[ft],"connection");ot&&(ot.endpoints&&(O=ot.endpoints),ot.endpoint&&(H=ot.endpoint),ot.anchors&&(tt=ot.anchors),ot.anchor&&(z=ot.anchor))}return{endpoints:O||[H,H],anchors:tt||[z,z]}},this.setId=function(s,S,m){var O;c.isString(s)?O=s:(s=this.getElement(s),O=this.getId(s));var H=this.getConnections({source:O,scope:"*"},!0),z=this.getConnections({target:O,scope:"*"},!0);S=""+S,m?s=this.getElement(S):(s=this.getElement(O),this.setAttribute(s,"id",S)),u[S]=u[O]||[];for(var tt=0,ft=u[S].length;tt<ft;tt++)u[S][tt].setElementId(S),u[S][tt].setReferenceElement(s);delete u[O],this.sourceEndpointDefinitions[S]=this.sourceEndpointDefinitions[O],delete this.sourceEndpointDefinitions[O],this.targetEndpointDefinitions[S]=this.targetEndpointDefinitions[O],delete this.targetEndpointDefinitions[O],this.router.changeId(O,S);var ot=this.getDragManager();ot&&ot.changeId(O,S),b[S]=b[O],delete b[O];var ct=function(ht,Ct,mt){for(var St=0,At=ht.length;St<At;St++)ht[St].endpoints[Ct].setElementId(S),ht[St].endpoints[Ct].setReferenceElement(s),ht[St][mt+"Id"]=S,ht[St][mt]=s};ct(H,0,"source"),ct(z,1,"target"),this.repaint(S)},this.setDebugLog=function(s){l=s},this.setSuspendDrawing=function(s,S){var m=B;return B=s,s?Y=new Date().getTime():Y=null,S&&this.repaintEverything(),m},this.isSuspendDrawing=function(){return B},this.getSuspendedAt=function(){return Y},this.batch=function(s,S){var m=this.isSuspendDrawing();m||this.setSuspendDrawing(!0);try{s()}catch(O){c.log("Function run while suspended failed",O)}m||this.setSuspendDrawing(!1,!S)},this.doWhileSuspended=this.batch,this.getCachedData=dt,this.show=function(s,S){return rt(s,"block",S),e},this.toggleVisible=gt,this.addListener=this.bind;var xt=[];this.registerFloatingConnection=function(s,S,m){xt[s.id]=S,c.addToList(u,s.id,m)},this.getFloatingConnectionFor=function(s){return xt[s]},this.listManager=new J.jsPlumbListManager(this,this.Defaults.ListStyle)};c.extend(J.jsPlumbInstance,c.EventGenerator,{setAttribute:function(g,e,k){this.setAttribute(g,e,k)},getAttribute:function(g,e){return this.getAttribute(J.jsPlumb.getElement(g),e)},convertToFullOverlaySpec:function(g){return c.isString(g)&&(g=[g,{}]),g[1].id=g[1].id||c.uuid(),g},registerConnectionType:function(g,e){if(this._connectionTypes[g]=J.jsPlumb.extend({},e),e.overlays){for(var k={},L=0;L<e.overlays.length;L++){var U=this.convertToFullOverlaySpec(e.overlays[L]);k[U[1].id]=U}this._connectionTypes[g].overlays=k}},registerConnectionTypes:function(g){for(var e in g)this.registerConnectionType(e,g[e])},registerEndpointType:function(g,e){if(this._endpointTypes[g]=J.jsPlumb.extend({},e),e.overlays){for(var k={},L=0;L<e.overlays.length;L++){var U=this.convertToFullOverlaySpec(e.overlays[L]);k[U[1].id]=U}this._endpointTypes[g].overlays=k}},registerEndpointTypes:function(g){for(var e in g)this.registerEndpointType(e,g[e])},getType:function(g,e){return e==="connection"?this._connectionTypes[g]:this._endpointTypes[g]},setIdChanged:function(g,e){this.setId(g,e,!0)},setParent:function(g,e){var k=this.getElement(g),L=this.getId(k),U=this.getElement(e),G=this.getId(U),P=this.getDragManager();k.parentNode.removeChild(k),U.appendChild(k),P&&P.setParent(k,L,U,G)},extend:function(g,e,k){var L;if(k)for(L=0;L<k.length;L++)g[k[L]]=e[k[L]];else for(L in e)g[L]=e[L];return g},floatingConnections:{},getFloatingAnchorIndex:function(g){return g.endpoints[0].isFloating()?0:g.endpoints[1].isFloating()?1:-1},proxyConnection:function(g,e,k,L,U,G){var P,w=g.endpoints[e].elementId,r=g.endpoints[e];g.proxies=g.proxies||[],g.proxies[e]?P=g.proxies[e].ep:P=this.addEndpoint(k,{endpoint:U(g,e),anchor:G(g,e),parameters:{isProxyEndpoint:!0}}),P.setDeleteOnEmpty(!0),g.proxies[e]={ep:P,originalEp:r},e===0?this.router.sourceOrTargetChanged(w,L,g,k,0):this.router.sourceOrTargetChanged(w,L,g,k,1),r.detachFromConnection(g,null,!0),P.connections=[g],g.endpoints[e]=P,r.setVisible(!1),g.setVisible(!0),this.revalidate(k)},unproxyConnection:function(g,e,k){if(!(g._jsPlumb==null||g.proxies==null||g.proxies[e]==null)){var L=g.proxies[e].originalEp.element,U=g.proxies[e].originalEp.elementId;g.endpoints[e]=g.proxies[e].originalEp,e===0?this.router.sourceOrTargetChanged(k,U,g,L,0):this.router.sourceOrTargetChanged(k,U,g,L,1),g.proxies[e].ep.detachFromConnection(g,null),g.proxies[e].originalEp.addConnection(g),g.isVisible()&&g.proxies[e].originalEp.setVisible(!0),delete g.proxies[e]}}});var C=new M;J.jsPlumb=C,C.getInstance=function(g,e){var k=new M(g);if(e)for(var L in e)k[L]=e[L];return k.init(),k},C.each=function(g,e){if(g!=null)if(typeof g=="string")e(C.getElement(g));else if(g.length!=null)for(var k=0;k<g.length;k++)e(C.getElement(g[k]));else e(g)},Kt.jsPlumb=C}).call(typeof window<"u"?window:jt),(function(){var J=this,c=J.jsPlumb,V=J.jsPlumbUtil,it="__label",K=function(h,T){var a={cssClass:T.cssClass,labelStyle:h.labelStyle,id:it,component:h,_jsPlumb:h._jsPlumb.instance},i=c.extend(a,T);return new c.Overlays[h._jsPlumb.instance.getRenderMode()].Label(i)},et=function(h,T){var a=null;if(V.isArray(T)){var i=T[0],y=c.extend({component:h,_jsPlumb:h._jsPlumb.instance},T[1]);T.length===3&&c.extend(y,T[2]),a=new c.Overlays[h._jsPlumb.instance.getRenderMode()][i](y)}else T.constructor===String?a=new c.Overlays[h._jsPlumb.instance.getRenderMode()][T]({component:h,_jsPlumb:h._jsPlumb.instance}):a=T;return a.id=a.id||V.uuid(),h.cacheTypeItem("overlay",a,a.id),h._jsPlumb.overlays[a.id]=a,a};c.OverlayCapableJsPlumbUIComponent=function(h){J.jsPlumbUIComponent.apply(this,arguments),this._jsPlumb.overlays={},this._jsPlumb.overlayPositions={},h.label&&(this.getDefaultType().overlays[it]=["Label",{label:h.label,location:h.labelLocation||this.defaultLabelLocation||.5,labelStyle:h.labelStyle||this._jsPlumb.instance.Defaults.LabelStyle,id:it}]),this.setListenerComponent=function(T){if(this._jsPlumb)for(var a in this._jsPlumb.overlays)this._jsPlumb.overlays[a].setListenerComponent(T)}},c.OverlayCapableJsPlumbUIComponent.applyType=function(h,T){if(T.overlays){var a={},i;for(i in T.overlays){var y=h._jsPlumb.overlays[T.overlays[i][1].id];if(y)y.updateFrom(T.overlays[i][1]),a[T.overlays[i][1].id]=!0,y.reattach(h._jsPlumb.instance,h);else{var D=h.getCachedTypeItem("overlay",T.overlays[i][1].id);D!=null?(D.reattach(h._jsPlumb.instance,h),D.setVisible(!0),D.updateFrom(T.overlays[i][1]),h._jsPlumb.overlays[D.id]=D):D=h.addOverlay(T.overlays[i],!0),a[D.id]=!0}}for(i in h._jsPlumb.overlays)a[h._jsPlumb.overlays[i].id]==null&&h.removeOverlay(h._jsPlumb.overlays[i].id,!0)}},V.extend(c.OverlayCapableJsPlumbUIComponent,J.jsPlumbUIComponent,{setHover:function(h,T){if(this._jsPlumb&&!this._jsPlumb.instance.isConnectionBeingDragged())for(var a in this._jsPlumb.overlays)this._jsPlumb.overlays[a][h?"addClass":"removeClass"](this._jsPlumb.instance.hoverClass)},addOverlay:function(h,T){var a=et(this,h);if(this.getData&&a.type==="Label"&&V.isArray(h)){var i=this.getData(),y=h[1];if(i){var D=y.labelLocationAttribute||"labelLocation",M=i?i[D]:null;M&&(a.loc=M)}}return T||this.repaint(),a},getOverlay:function(h){return this._jsPlumb.overlays[h]},getOverlays:function(){return this._jsPlumb.overlays},hideOverlay:function(h){var T=this.getOverlay(h);T&&T.hide()},hideOverlays:function(){for(var h in this._jsPlumb.overlays)this._jsPlumb.overlays[h].hide()},showOverlay:function(h){var T=this.getOverlay(h);T&&T.show()},showOverlays:function(){for(var h in this._jsPlumb.overlays)this._jsPlumb.overlays[h].show()},removeAllOverlays:function(h){for(var T in this._jsPlumb.overlays)this._jsPlumb.overlays[T].cleanup&&this._jsPlumb.overlays[T].cleanup();this._jsPlumb.overlays={},this._jsPlumb.overlayPositions=null,this._jsPlumb.overlayPlacements={},h||this.repaint()},removeOverlay:function(h,T){var a=this._jsPlumb.overlays[h];a&&(a.setVisible(!1),!T&&a.cleanup&&a.cleanup(),delete this._jsPlumb.overlays[h],this._jsPlumb.overlayPositions&&delete this._jsPlumb.overlayPositions[h],this._jsPlumb.overlayPlacements&&delete this._jsPlumb.overlayPlacements[h])},removeOverlays:function(){for(var h=0,T=arguments.length;h<T;h++)this.removeOverlay(arguments[h])},moveParent:function(h){if(this.bgCanvas&&(this.bgCanvas.parentNode.removeChild(this.bgCanvas),h.appendChild(this.bgCanvas)),this.canvas&&this.canvas.parentNode){this.canvas.parentNode.removeChild(this.canvas),h.appendChild(this.canvas);for(var T in this._jsPlumb.overlays)if(this._jsPlumb.overlays[T].isAppendedAtTopLevel){var a=this._jsPlumb.overlays[T].getElement();a.parentNode.removeChild(a),h.appendChild(a)}}},getLabel:function(){var h=this.getOverlay(it);return h!=null?h.getLabel():null},getLabelOverlay:function(){return this.getOverlay(it)},setLabel:function(h){var T=this.getOverlay(it);if(T)h.constructor===String||h.constructor===Function?T.setLabel(h):(h.label&&T.setLabel(h.label),h.location&&T.setLocation(h.location));else{var a=h.constructor===String||h.constructor===Function?{label:h}:h;T=K(this,a),this._jsPlumb.overlays[it]=T}this._jsPlumb.instance.isSuspendDrawing()||this.repaint()},cleanup:function(h){for(var T in this._jsPlumb.overlays)this._jsPlumb.overlays[T].cleanup(h),this._jsPlumb.overlays[T].destroy(h);h&&(this._jsPlumb.overlays={},this._jsPlumb.overlayPositions=null)},setVisible:function(h){this[h?"showOverlays":"hideOverlays"]()},setAbsoluteOverlayPosition:function(h,T){this._jsPlumb.overlayPositions[h.id]=T},getAbsoluteOverlayPosition:function(h){return this._jsPlumb.overlayPositions?this._jsPlumb.overlayPositions[h.id]:null},_clazzManip:function(h,T,a){if(!a)for(var i in this._jsPlumb.overlays)this._jsPlumb.overlays[i][h+"Class"](T)},addClass:function(h,T){this._clazzManip("add",h,T)},removeClass:function(h,T){this._clazzManip("remove",h,T)}})}).call(typeof window<"u"?window:jt),(function(){var J=this,c=J.jsPlumb,V=J.jsPlumbUtil,it=function(a,i,y){var D=!1;return{drag:function(){if(D)return D=!1,!0;if(i.element){var M=y.getUIPosition(arguments,y.getZoom());M!=null&&y.setPosition(i.element,M),y.repaint(i.element,M),a.paint({anchorPoint:a.anchor.getCurrentLocation({element:a})})}},stopDrag:function(){D=!0}}},K=function(a,i,y,D){var M=i.createElement("div",{position:"absolute"});i.appendElement(M);var C=i.getId(M);i.setPosition(M,y),M.style.width=D[0]+"px",M.style.height=D[1]+"px",i.manage(C,M,!0),a.id=C,a.element=M},et=function(a,i,y,D,M,C,g,e){var k=new c.FloatingAnchor({reference:i,referenceCanvas:D,jsPlumbInstance:C});return g({paintStyle:a,endpoint:y,anchor:k,source:M,scope:e})},h=["connectorStyle","connectorHoverStyle","connectorOverlays","connector","connectionType","connectorClass","connectorHoverClass"],T=function(a,i){var y=0;if(i!=null){for(var D=0;D<a.connections.length;D++)if(a.connections[D].sourceId===i||a.connections[D].targetId===i){y=D;break}}return a.connections[y]};c.Endpoint=function(a){var i=a._jsPlumb,y=a.newConnection,D=a.newEndpoint;this.idPrefix="_jsplumb_e_",this.defaultLabelLocation=[.5,.5],this.defaultOverlayKeys=["Overlays","EndpointOverlays"],c.OverlayCapableJsPlumbUIComponent.apply(this,arguments),this.appendToDefaultType({connectionType:a.connectionType,maxConnections:a.maxConnections==null?this._jsPlumb.instance.Defaults.MaxConnections:a.maxConnections,paintStyle:a.endpointStyle||a.paintStyle||a.style||this._jsPlumb.instance.Defaults.EndpointStyle||c.Defaults.EndpointStyle,hoverPaintStyle:a.endpointHoverStyle||a.hoverPaintStyle||this._jsPlumb.instance.Defaults.EndpointHoverStyle||c.Defaults.EndpointHoverStyle,connectorStyle:a.connectorStyle,connectorHoverStyle:a.connectorHoverStyle,connectorClass:a.connectorClass,connectorHoverClass:a.connectorHoverClass,connectorOverlays:a.connectorOverlays,connector:a.connector,connectorTooltip:a.connectorTooltip}),this._jsPlumb.enabled=a.enabled!==!1,this._jsPlumb.visible=!0,this.element=c.getElement(a.source),this._jsPlumb.uuid=a.uuid,this._jsPlumb.floatingEndpoint=null,this._jsPlumb.uuid&&(a.endpointsByUUID[this._jsPlumb.uuid]=this),this.elementId=a.elementId,this.dragProxy=a.dragProxy,this._jsPlumb.connectionCost=a.connectionCost,this._jsPlumb.connectionsDirected=a.connectionsDirected,this._jsPlumb.currentAnchorClass="",this._jsPlumb.events={};var M=a.deleteOnEmpty===!0;this.setDeleteOnEmpty=function(P){M=P};var C=(function(){var P=i.endpointAnchorClassPrefix+"-"+this._jsPlumb.currentAnchorClass;this._jsPlumb.currentAnchorClass=this.anchor.getCssClass();var w=i.endpointAnchorClassPrefix+(this._jsPlumb.currentAnchorClass?"-"+this._jsPlumb.currentAnchorClass:"");this.removeClass(P),this.addClass(w),c.updateClasses(this.element,w,P)}).bind(this);this.prepareAnchor=function(P){var w=this._jsPlumb.instance.makeAnchor(P,this.elementId,i);return w.bind("anchorChanged",(function(r){this.fire("anchorChanged",{endpoint:this,anchor:r}),C()}).bind(this)),w},this.setPreparedAnchor=function(P,w){return this._jsPlumb.instance.continuousAnchorFactory.clear(this.elementId),this.anchor=P,C(),w||this._jsPlumb.instance.repaint(this.elementId),this},this.setAnchor=function(P,w){var r=this.prepareAnchor(P);return this.setPreparedAnchor(r,w),this};var g=(function(P){if(this.connections.length>0)for(var w=0;w<this.connections.length;w++)this.connections[w].setHover(P,!1);else this.setHover(P)}).bind(this);this.bind("mouseover",function(){g(!0)}),this.bind("mouseout",function(){g(!1)}),a._transient||this._jsPlumb.instance.router.addEndpoint(this,this.elementId),this.prepareEndpoint=function(P,w){var r=function(_,x){var u=i.getRenderMode();if(c.Endpoints[u][_])return new c.Endpoints[u][_](x);if(!i.Defaults.DoNotThrowErrors)throw{msg:"jsPlumb: unknown endpoint type '"+_+"'"}},f={_jsPlumb:this._jsPlumb.instance,cssClass:a.cssClass,container:a.container,tooltip:a.tooltip,connectorTooltip:a.connectorTooltip,endpoint:this},l;return V.isString(P)?l=r(P,f):V.isArray(P)?(f=V.merge(P[1],f),l=r(P[0],f)):l=P.clone(),l.clone=(function(){if(V.isString(P))return r(P,f);if(V.isArray(P))return f=V.merge(P[1],f),r(P[0],f)}).bind(this),l.typeId=w,l},this.setEndpoint=function(P,w){var r=this.prepareEndpoint(P);this.setPreparedEndpoint(r,!0)},this.setPreparedEndpoint=function(P,w){this.endpoint!=null&&(this.endpoint.cleanup(),this.endpoint.destroy()),this.endpoint=P,this.type=this.endpoint.type,this.canvas=this.endpoint.canvas},c.extend(this,a,h),this.isSource=a.isSource||!1,this.isTemporarySource=a.isTemporarySource||!1,this.isTarget=a.isTarget||!1,this.connections=a.connections||[],this.connectorPointerEvents=a["connector-pointer-events"],this.scope=a.scope||i.getDefaultScope(),this.timestamp=null,this.reattachConnections=a.reattach||i.Defaults.ReattachConnections,this.connectionsDetachable=i.Defaults.ConnectionsDetachable,(a.connectionsDetachable===!1||a.detachable===!1)&&(this.connectionsDetachable=!1),this.dragAllowedWhenFull=a.dragAllowedWhenFull!==!1,a.onMaxConnections&&this.bind("maxConnections",a.onMaxConnections),this.addConnection=function(P){this.connections.push(P),this[(this.connections.length>0?"add":"remove")+"Class"](i.endpointConnectedClass),this[(this.isFull()?"add":"remove")+"Class"](i.endpointFullClass)},this.detachFromConnection=function(P,w,r){w=w??this.connections.indexOf(P),w>=0&&(this.connections.splice(w,1),this[(this.connections.length>0?"add":"remove")+"Class"](i.endpointConnectedClass),this[(this.isFull()?"add":"remove")+"Class"](i.endpointFullClass)),!r&&M&&this.connections.length===0&&i.deleteObject({endpoint:this,fireEvent:!1,deleteAttachedObjects:r!==!0})},this.deleteEveryConnection=function(P){for(var w=this.connections.length,r=0;r<w;r++)i.deleteConnection(this.connections[0],P)},this.detachFrom=function(P,w,r){for(var f=[],l=0;l<this.connections.length;l++)(this.connections[l].endpoints[1]===P||this.connections[l].endpoints[0]===P)&&f.push(this.connections[l]);for(var _=0,x=f.length;_<x;_++)i.deleteConnection(f[0]);return this},this.getElement=function(){return this.element},this.setElement=function(P){var w=this._jsPlumb.instance.getId(P),r=this.elementId;return V.removeWithFunction(a.endpointsByElement[this.elementId],(function(f){return f.id===this.id}).bind(this)),this.element=c.getElement(P),this.elementId=i.getId(this.element),i.router.rehomeEndpoint(this,r,this.element),i.dragManager.endpointAdded(this.element),V.addToList(a.endpointsByElement,w,this),this},this.makeInPlaceCopy=function(){var P=this.anchor.getCurrentLocation({element:this}),w=this.anchor.getOrientation(this),r=this.anchor.getCssClass(),f={bind:function(){},compute:function(){return[P[0],P[1]]},getCurrentLocation:function(){return[P[0],P[1]]},getOrientation:function(){return w},getCssClass:function(){return r}};return D({dropOptions:a.dropOptions,anchor:f,source:this.element,paintStyle:this.getPaintStyle(),endpoint:a.hideOnDrag?"Blank":this.endpoint,_transient:!0,scope:this.scope,reference:this})},this.connectorSelector=function(){return this.connections[0]},this.setStyle=this.setPaintStyle,this.paint=function(P){P=P||{};var w=P.timestamp,r=P.recalc!==!1;if(!w||this.timestamp!==w){var f=i.updateOffset({elId:this.elementId,timestamp:w}),l=P.offset?P.offset.o:f.o;if(l!=null){var _=P.anchorPoint,x=P.connectorPaintStyle;if(_==null){var u=P.dimensions||f.s,j={xy:[l.left,l.top],wh:u,element:this,timestamp:w};if(r&&this.anchor.isDynamic&&this.connections.length>0){var b=T(this,P.elementWithPrecedence),A=b.endpoints[0]===this?1:0,d=A===0?b.sourceId:b.targetId,E=i.getCachedData(d),N=E.o,B=E.s;j.index=A===0?1:0,j.connection=b,j.txy=[N.left,N.top],j.twh=B,j.tElement=b.endpoints[A],j.tRotation=i.getRotation(d)}else this.connections.length>0&&(j.connection=this.connections[0]);j.rotation=i.getRotation(this.elementId),_=this.anchor.compute(j)}this.endpoint.compute(_,this.anchor.getOrientation(this),this._jsPlumb.paintStyleInUse,x||this.paintStyleInUse),this.endpoint.paint(this._jsPlumb.paintStyleInUse,this.anchor),this.timestamp=w;for(var Y in this._jsPlumb.overlays)if(this._jsPlumb.overlays.hasOwnProperty(Y)){var n=this._jsPlumb.overlays[Y];n.isVisible()&&(this._jsPlumb.overlayPlacements[Y]=n.draw(this.endpoint,this._jsPlumb.paintStyleInUse),n.paint(this._jsPlumb.overlayPlacements[Y]))}}}},this.getTypeDescriptor=function(){return"endpoint"},this.isVisible=function(){return this._jsPlumb.visible},this.repaint=this.paint;var e=!1;this.initDraggable=function(){if(!e&&c.isDragSupported(this.element)){var P={id:null,element:null},w=null,r=!1,f=null,l=it(this,P,i),_=a.dragOptions||{},x={},u=c.dragEvents.start,j=c.dragEvents.stop,b=c.dragEvents.drag,A=c.dragEvents.beforeStart,d,E=function(Y){d=Y.e.payload||{}},N=(function(Y){w=this.connectorSelector();var n=!0;this.isEnabled()||(n=!1),w==null&&!this.isSource&&!this.isTemporarySource&&(n=!1),this.isSource&&this.isFull()&&!(w!=null&&this.dragAllowedWhenFull)&&(n=!1),w!=null&&!w.isDetachable(this)&&(this.isFull()?n=!1:w=null);var o=i.checkCondition(w==null?"beforeDrag":"beforeStartDetach",{endpoint:this,source:this.element,sourceId:this.elementId,connection:w});if(o===!1?n=!1:typeof o=="object"?c.extend(o,d||{}):o=d||{},n===!1)return i.stopDrag&&i.stopDrag(this.canvas),l.stopDrag(),!1;for(var t=0;t<this.connections.length;t++)this.connections[t].setHover(!1);this.addClass("endpointDrag"),i.setConnectionBeingDragged(!0),w&&!this.isFull()&&this.isSource&&(w=null),i.updateOffset({elId:this.elementId});var p=this._jsPlumb.instance.getOffset(this.canvas),v=this.canvas,I=this._jsPlumb.instance.getSize(this.canvas);K(P,i,p,I),i.setAttributes(this.canvas,{dragId:P.id,elId:this.elementId});var F=this.dragProxy||this.endpoint;if(this.dragProxy==null&&this.connectionType!=null){var X=this._jsPlumb.instance.deriveEndpointAndAnchorSpec(this.connectionType);X.endpoints[1]&&(F=X.endpoints[1])}var R=this._jsPlumb.instance.makeAnchor("Center");R.isFloating=!0,this._jsPlumb.floatingEndpoint=et(this.getPaintStyle(),R,F,this.canvas,P.element,i,D,this.scope);var W=this._jsPlumb.floatingEndpoint.anchor;if(w==null)this.setHover(!1,!1),w=y({sourceEndpoint:this,targetEndpoint:this._jsPlumb.floatingEndpoint,source:this.element,target:P.element,anchors:[this.anchor,this._jsPlumb.floatingEndpoint.anchor],paintStyle:a.connectorStyle,hoverPaintStyle:a.connectorHoverStyle,connector:a.connector,overlays:a.connectorOverlays,type:this.connectionType,cssClass:this.connectorClass,hoverClass:this.connectorHoverClass,scope:a.scope,data:o}),w.pending=!0,w.addClass(i.draggingClass),this._jsPlumb.floatingEndpoint.addClass(i.draggingClass),this._jsPlumb.floatingEndpoint.anchor=W,i.fire("connectionDrag",w),i.router.newConnection(w);else{r=!0,w.setHover(!1);var at=w.endpoints[0].id===this.id?0:1;this.detachFromConnection(w,null,!0);var st=i.getDragScope(v);i.setAttribute(this.canvas,"originalScope",st),i.fire("connectionDrag",w),at===0?(f=[w.source,w.sourceId,v,st],i.router.sourceOrTargetChanged(w.endpoints[at].elementId,P.id,w,P.element,0)):(f=[w.target,w.targetId,v,st],i.router.sourceOrTargetChanged(w.endpoints[at].elementId,P.id,w,P.element,1)),w.suspendedEndpoint=w.endpoints[at],w.suspendedElement=w.endpoints[at].getElement(),w.suspendedElementId=w.endpoints[at].elementId,w.suspendedElementType=at===0?"source":"target",w.suspendedEndpoint.setHover(!1),this._jsPlumb.floatingEndpoint.referenceEndpoint=w.suspendedEndpoint,w.endpoints[at]=this._jsPlumb.floatingEndpoint,w.addClass(i.draggingClass),this._jsPlumb.floatingEndpoint.addClass(i.draggingClass)}i.registerFloatingConnection(P,w,this._jsPlumb.floatingEndpoint),i.currentlyDragging=!0}).bind(this),B=(function(){if(i.setConnectionBeingDragged(!1),w&&w.endpoints!=null){var Y=i.getDropEvent(arguments),n=i.getFloatingAnchorIndex(w);if(w.endpoints[n===0?1:0].anchor.locked=!1,w.removeClass(i.draggingClass),this._jsPlumb&&(w.deleteConnectionNow||w.endpoints[n]===this._jsPlumb.floatingEndpoint)&&r&&w.suspendedEndpoint){n===0?(w.floatingElement=w.source,w.floatingId=w.sourceId,w.floatingEndpoint=w.endpoints[0],w.floatingIndex=0,w.source=f[0],w.sourceId=f[1]):(w.floatingElement=w.target,w.floatingId=w.targetId,w.floatingEndpoint=w.endpoints[1],w.floatingIndex=1,w.target=f[0],w.targetId=f[1]);var o=this._jsPlumb.floatingEndpoint;i.setDragScope(f[2],f[3]),w.endpoints[n]=w.suspendedEndpoint,w.isReattach()||w._forceReattach||w._forceDetach||!i.deleteConnection(w,{originalEvent:Y})?(w.setHover(!1),w._forceDetach=null,w._forceReattach=null,this._jsPlumb.floatingEndpoint.detachFromConnection(w),w.suspendedEndpoint.addConnection(w),n===1?i.router.sourceOrTargetChanged(w.floatingId,w.targetId,w,w.target,n):i.router.sourceOrTargetChanged(w.floatingId,w.sourceId,w,w.source,n),i.repaint(f[1])):i.deleteObject({endpoint:o})}this.deleteAfterDragStop?i.deleteObject({endpoint:this}):this._jsPlumb&&this.paint({recalc:!1}),i.fire("connectionDragStop",w,Y),w.pending&&i.fire("connectionAborted",w,Y),i.currentlyDragging=!1,w.suspendedElement=null,w.suspendedEndpoint=null,w=null}P&&P.element&&i.remove(P.element,!1,!1),this._jsPlumb&&(this.canvas.style.visibility="visible",this.anchor.locked=!1,this._jsPlumb.floatingEndpoint=null)}).bind(this);_=c.extend(x,_),_.scope=this.scope||_.scope,_[A]=V.wrap(_[A],E,!1),_[u]=V.wrap(_[u],N,!1),_[b]=V.wrap(_[b],l.drag),_[j]=V.wrap(_[j],B),_.multipleDrop=!1,_.canDrag=(function(){return this.isSource||this.isTemporarySource||this.connections.length>0&&this.connectionsDetachable!==!1}).bind(this),i.initDraggable(this.canvas,_,"internal"),this.canvas._jsPlumbRelatedElement=this.element,e=!0}};var k=a.endpoint||this._jsPlumb.instance.Defaults.Endpoint||c.Defaults.Endpoint;this.setEndpoint(k,!0);var L=a.anchor?a.anchor:a.anchors?a.anchors:i.Defaults.Anchor||"Top";this.setAnchor(L,!0);var U=["default",a.type||""].join(" ");this.addType(U,a.data,!0),this.canvas=this.endpoint.canvas,this.canvas._jsPlumb=this,this.initDraggable();var G=(function(P,w,r,f){if(c.isDropSupported(this.element)){var l=a.dropOptions||i.Defaults.DropOptions||c.Defaults.DropOptions;l=c.extend({},l),l.scope=l.scope||this.scope;var _=c.dragEvents.drop,x=c.dragEvents.over,u=c.dragEvents.out,j=this,b=i.EndpointDropHandler({getEndpoint:function(){return j},jsPlumb:i,enabled:function(){return r!=null?r.isEnabled():!0},isFull:function(){return r.isFull()},element:this.element,elementId:this.elementId,isSource:this.isSource,isTarget:this.isTarget,addClass:function(A){j.addClass(A)},removeClass:function(A){j.removeClass(A)},isDropAllowed:function(){return j.isDropAllowed.apply(j,arguments)},reference:f,isRedrop:function(A,d){return A.suspendedEndpoint&&d.reference&&A.suspendedEndpoint.id===d.reference.id}});l[_]=V.wrap(l[_],b,!0),l[x]=V.wrap(l[x],(function(){var A=c.getDragObject(arguments),d=i.getAttribute(c.getElement(A),"dragId"),E=i.getFloatingConnectionFor(d);if(E!=null){var N=i.getFloatingAnchorIndex(E),B=this.isTarget&&N!==0||E.suspendedEndpoint&&this.referenceEndpoint&&this.referenceEndpoint.id===E.suspendedEndpoint.id;if(B){var Y=i.checkCondition("checkDropAllowed",{sourceEndpoint:E.endpoints[N],targetEndpoint:this,connection:E});this[(Y?"add":"remove")+"Class"](i.endpointDropAllowedClass),this[(Y?"remove":"add")+"Class"](i.endpointDropForbiddenClass),E.endpoints[N].anchor.over(this.anchor,this)}}}).bind(this)),l[u]=V.wrap(l[u],(function(){var A=c.getDragObject(arguments),d=A==null?null:i.getAttribute(c.getElement(A),"dragId"),E=d?i.getFloatingConnectionFor(d):null;if(E!=null){var N=i.getFloatingAnchorIndex(E),B=this.isTarget&&N!==0||E.suspendedEndpoint&&this.referenceEndpoint&&this.referenceEndpoint.id===E.suspendedEndpoint.id;B&&(this.removeClass(i.endpointDropAllowedClass),this.removeClass(i.endpointDropForbiddenClass),E.endpoints[N].anchor.out())}}).bind(this)),i.initDroppable(P,l,"internal",w)}}).bind(this);return this.anchor.isFloating||G(this.canvas,!(a._transient||this.anchor.isFloating),this,a.reference),this},V.extend(c.Endpoint,c.OverlayCapableJsPlumbUIComponent,{setVisible:function(a,i,y){if(this._jsPlumb.visible=a,this.canvas&&(this.canvas.style.display=a?"block":"none"),this[a?"showOverlays":"hideOverlays"](),!i){for(var D=0;D<this.connections.length;D++)if(this.connections[D].setVisible(a),!y){var M=this===this.connections[D].endpoints[0]?1:0;this.connections[D].endpoints[M].connections.length===1&&this.connections[D].endpoints[M].setVisible(a,!0,!0)}}},getAttachedElements:function(){return this.connections},applyType:function(a,i){this.setPaintStyle(a.endpointStyle||a.paintStyle,i),this.setHoverPaintStyle(a.endpointHoverStyle||a.hoverPaintStyle,i),a.maxConnections!=null&&(this._jsPlumb.maxConnections=a.maxConnections),a.scope&&(this.scope=a.scope),c.extend(this,a,h),a.cssClass!=null&&this.canvas&&this._jsPlumb.instance.addClass(this.canvas,a.cssClass),c.OverlayCapableJsPlumbUIComponent.applyType(this,a)},isEnabled:function(){return this._jsPlumb.enabled},setEnabled:function(a){this._jsPlumb.enabled=a},cleanup:function(){var a=this._jsPlumb.instance.endpointAnchorClassPrefix+(this._jsPlumb.currentAnchorClass?"-"+this._jsPlumb.currentAnchorClass:"");c.removeClass(this.element,a),this.anchor=null,this.endpoint.cleanup(!0),this.endpoint.destroy(),this.endpoint=null,this._jsPlumb.instance.destroyDraggable(this.canvas,"internal"),this._jsPlumb.instance.destroyDroppable(this.canvas,"internal")},setHover:function(a){this.endpoint&&this._jsPlumb&&!this._jsPlumb.instance.isConnectionBeingDragged()&&this.endpoint.setHover(a)},isFull:function(){return this._jsPlumb.maxConnections===0?!0:!(this.isFloating()||this._jsPlumb.maxConnections<0||this.connections.length<this._jsPlumb.maxConnections)},isFloating:function(){return this.anchor!=null&&this.anchor.isFloating},isConnectedTo:function(a){var i=!1;if(a){for(var y=0;y<this.connections.length;y++)if(this.connections[y].endpoints[1]===a||this.connections[y].endpoints[0]===a){i=!0;break}}return i},getConnectionCost:function(){return this._jsPlumb.connectionCost},setConnectionCost:function(a){this._jsPlumb.connectionCost=a},areConnectionsDirected:function(){return this._jsPlumb.connectionsDirected},setConnectionsDirected:function(a){this._jsPlumb.connectionsDirected=a},setElementId:function(a){this.elementId=a,this.anchor.elementId=a},setReferenceElement:function(a){this.element=c.getElement(a)},setDragAllowedWhenFull:function(a){this.dragAllowedWhenFull=a},equals:function(a){return this.anchor.equals(a.anchor)},getUuid:function(){return this._jsPlumb.uuid},computeAnchor:function(a){return this.anchor.compute(a)}}),J.jsPlumbInstance.prototype.EndpointDropHandler=function(a){return function(i){var y=a.jsPlumb;a.removeClass(y.endpointDropAllowedClass),a.removeClass(y.endpointDropForbiddenClass);var D=y.getDropEvent(arguments),M=y.getDragObject(arguments),C=y.getAttribute(M,"dragId");y.getAttribute(M,"elId");var g=y.getAttribute(M,"originalScope"),e=y.getFloatingConnectionFor(C);if(e!=null){var k=e.suspendedEndpoint!=null;if(!(k&&e.suspendedEndpoint._jsPlumb==null)){var L=a.getEndpoint(e);if(L!=null){if(a.isRedrop(e,a)){e._forceReattach=!0,e.setHover(!1),a.maybeCleanup&&a.maybeCleanup(L);return}var U=y.getFloatingAnchorIndex(e);if(U===0&&!a.isSource||U===1&&!a.isTarget){a.maybeCleanup&&a.maybeCleanup(L);return}a.onDrop&&a.onDrop(e),g&&y.setDragScope(M,g);var G=a.isFull(i);if(G&&L.fire("maxConnections",{endpoint:this,connection:e,maxConnections:L._jsPlumb.maxConnections},D),!G&&a.enabled()){var P=!0;U===0?(e.floatingElement=e.source,e.floatingId=e.sourceId,e.floatingEndpoint=e.endpoints[0],e.floatingIndex=0,e.source=a.element,e.sourceId=y.getId(a.element)):(e.floatingElement=e.target,e.floatingId=e.targetId,e.floatingEndpoint=e.endpoints[1],e.floatingIndex=1,e.target=a.element,e.targetId=y.getId(a.element)),k&&e.suspendedEndpoint.id!==L.id&&(!e.isDetachAllowed(e)||!e.endpoints[U].isDetachAllowed(e)||!e.suspendedEndpoint.isDetachAllowed(e)||!y.checkCondition("beforeDetach",e))&&(P=!1);var w=(function(f){e.endpoints[U].detachFromConnection(e),e.suspendedEndpoint&&e.suspendedEndpoint.detachFromConnection(e),e.endpoints[U]=L,L.addConnection(e);var l=L.getParameters();for(var _ in l)e.setParameter(_,l[_]);if(!k)l.draggable&&y.initDraggable(this.element,a.dragOptions,"internal",y);else{var x=e.suspendedEndpoint.elementId;y.fireMoveEvent({index:U,originalSourceId:U===0?x:e.sourceId,newSourceId:U===0?L.elementId:e.sourceId,originalTargetId:U===1?x:e.targetId,newTargetId:U===1?L.elementId:e.targetId,originalSourceEndpoint:U===0?e.suspendedEndpoint:e.endpoints[0],newSourceEndpoint:U===0?L:e.endpoints[0],originalTargetEndpoint:U===1?e.suspendedEndpoint:e.endpoints[1],newTargetEndpoint:U===1?L:e.endpoints[1],connection:e},D)}if(U===1?y.router.sourceOrTargetChanged(e.floatingId,e.targetId,e,e.target,1):y.router.sourceOrTargetChanged(e.floatingId,e.sourceId,e,e.source,0),e.endpoints[0].finalEndpoint){var u=e.endpoints[0];u.detachFromConnection(e),e.endpoints[0]=e.endpoints[0].finalEndpoint,e.endpoints[0].addConnection(e)}V.isObject(f)&&e.mergeData(f),y.finaliseConnection(e,null,D,!1),e.setHover(!1),y.revalidate(e.endpoints[0].element)}).bind(this),r=function(){e.suspendedEndpoint&&(e.endpoints[U]=e.suspendedEndpoint,e.setHover(!1),e._forceDetach=!0,U===0?(e.source=e.suspendedEndpoint.element,e.sourceId=e.suspendedEndpoint.elementId):(e.target=e.suspendedEndpoint.element,e.targetId=e.suspendedEndpoint.elementId),e.suspendedEndpoint.addConnection(e),U===1?y.router.sourceOrTargetChanged(e.floatingId,e.targetId,e,e.target,1):y.router.sourceOrTargetChanged(e.floatingId,e.sourceId,e,e.source,0),y.repaint(e.sourceId),e._forceDetach=!1)};if(P=P&&a.isDropAllowed(e.sourceId,e.targetId,e.scope,e,L),P)return w(P),!0;r()}a.maybeCleanup&&a.maybeCleanup(L),y.currentlyDragging=!1}}}}}}).call(typeof window<"u"?window:jt),(function(){var J=this,c=J.jsPlumb,V=J.jsPlumbUtil,it=function(h,T,a,i,y){if(c.Connectors[T]=c.Connectors[T]||{},c.Connectors[T][a]==null){if(c.Connectors[a]==null){if(h.Defaults.DoNotThrowErrors)return null;throw new TypeError("jsPlumb: unknown connector type '"+a+"'")}c.Connectors[T][a]=function(){c.Connectors[a].apply(this,arguments),c.ConnectorRenderers[T].apply(this,arguments)},V.extend(c.Connectors[T][a],[c.Connectors[a],c.ConnectorRenderers[T]])}return new c.Connectors[T][a](i,y)},K=function(h,T,a){return h?a.makeAnchor(h,T,a):null},et=function(h,T,a,i){T!=null&&(T._jsPlumbConnections=T._jsPlumbConnections||{},i?delete T._jsPlumbConnections[h.id]:T._jsPlumbConnections[h.id]=!0,V.isEmpty(T._jsPlumbConnections)?a.removeClass(T,a.connectedClass):a.addClass(T,a.connectedClass))};c.Connection=function(h){var T=h.newEndpoint;this.id=h.id,this.connector=null,this.idPrefix="_jsplumb_c_",this.defaultLabelLocation=.5,this.defaultOverlayKeys=["Overlays","ConnectionOverlays"],this.previousConnection=h.previousConnection,this.source=c.getElement(h.source),this.target=c.getElement(h.target),c.OverlayCapableJsPlumbUIComponent.apply(this,arguments),h.sourceEndpoint?(this.source=h.sourceEndpoint.getElement(),this.sourceId=h.sourceEndpoint.elementId):this.sourceId=this._jsPlumb.instance.getId(this.source),h.targetEndpoint?(this.target=h.targetEndpoint.getElement(),this.targetId=h.targetEndpoint.elementId):this.targetId=this._jsPlumb.instance.getId(this.target),this.scope=h.scope,this.endpoints=[],this.endpointStyles=[];var a=this._jsPlumb.instance;a.manage(this.sourceId,this.source),a.manage(this.targetId,this.target),this._jsPlumb.visible=!0,this._jsPlumb.params={cssClass:h.cssClass,container:h.container,"pointer-events":h["pointer-events"],editorParams:h.editorParams,overlays:h.overlays},this._jsPlumb.lastPaintedAt=null,this.bind("mouseover",(function(){this.setHover(!0)}).bind(this)),this.bind("mouseout",(function(){this.setHover(!1)}).bind(this)),this.makeEndpoint=function(_,x,u,j,b){return u=u||this._jsPlumb.instance.getId(x),this.prepareEndpoint(a,T,this,j,_?0:1,h,x,u,b)},h.type&&(h.endpoints=h.endpoints||this._jsPlumb.instance.deriveEndpointAndAnchorSpec(h.type).endpoints);var i=this.makeEndpoint(!0,this.source,this.sourceId,h.sourceEndpoint),y=this.makeEndpoint(!1,this.target,this.targetId,h.targetEndpoint);i&&V.addToList(h.endpointsByElement,this.sourceId,i),y&&V.addToList(h.endpointsByElement,this.targetId,y),this.scope||(this.scope=this.endpoints[0].scope),h.deleteEndpointsOnEmpty!=null&&(this.endpoints[0].setDeleteOnEmpty(h.deleteEndpointsOnEmpty),this.endpoints[1].setDeleteOnEmpty(h.deleteEndpointsOnEmpty));var D=a.Defaults.ConnectionsDetachable;h.detachable===!1&&(D=!1),this.endpoints[0].connectionsDetachable===!1&&(D=!1),this.endpoints[1].connectionsDetachable===!1&&(D=!1);var M=h.reattach||this.endpoints[0].reattachConnections||this.endpoints[1].reattachConnections||a.Defaults.ReattachConnections;this.appendToDefaultType({detachable:D,reattach:M,paintStyle:this.endpoints[0].connectorStyle||this.endpoints[1].connectorStyle||h.paintStyle||a.Defaults.PaintStyle||c.Defaults.PaintStyle,hoverPaintStyle:this.endpoints[0].connectorHoverStyle||this.endpoints[1].connectorHoverStyle||h.hoverPaintStyle||a.Defaults.HoverPaintStyle||c.Defaults.HoverPaintStyle});var C=a.getSuspendedAt();if(!a.isSuspendDrawing()){var g=a.getCachedData(this.sourceId),e=g.o,k=g.s,L=a.getCachedData(this.targetId),U=L.o,G=L.s,P=C||jsPlumbUtil.uuid(),w=this.endpoints[0].anchor.compute({xy:[e.left,e.top],wh:k,element:this.endpoints[0],elementId:this.endpoints[0].elementId,txy:[U.left,U.top],twh:G,tElement:this.endpoints[1],timestamp:P,rotation:a.getRotation(this.endpoints[0].elementId)});this.endpoints[0].paint({anchorLoc:w,timestamp:P}),w=this.endpoints[1].anchor.compute({xy:[U.left,U.top],wh:G,element:this.endpoints[1],elementId:this.endpoints[1].elementId,txy:[e.left,e.top],twh:k,tElement:this.endpoints[0],timestamp:P,rotation:a.getRotation(this.endpoints[1].elementId)}),this.endpoints[1].paint({anchorLoc:w,timestamp:P})}this.getTypeDescriptor=function(){return"connection"},this.getAttachedElements=function(){return this.endpoints},this.isDetachable=function(_){return this._jsPlumb.detachable===!1?!1:_!=null?_.connectionsDetachable===!0:this._jsPlumb.detachable===!0},this.setDetachable=function(_){this._jsPlumb.detachable=_===!0},this.isReattach=function(){return this._jsPlumb.reattach===!0||this.endpoints[0].reattachConnections===!0||this.endpoints[1].reattachConnections===!0},this.setReattach=function(_){this._jsPlumb.reattach=_===!0},this._jsPlumb.cost=h.cost||this.endpoints[0].getConnectionCost(),this._jsPlumb.directed=h.directed,h.directed==null&&(this._jsPlumb.directed=this.endpoints[0].areConnectionsDirected());var r=c.extend({},this.endpoints[1].getParameters());c.extend(r,this.endpoints[0].getParameters()),c.extend(r,this.getParameters()),this.setParameters(r),this.setConnector(this.endpoints[0].connector||this.endpoints[1].connector||h.connector||a.Defaults.Connector||c.Defaults.Connector,!0);var f=h.data==null||!V.isObject(h.data)?{}:h.data;this.getData=function(){return f},this.setData=function(_){f=_||{}},this.mergeData=function(_){f=c.extend(f,_)};var l=["default",this.endpoints[0].connectionType,this.endpoints[1].connectionType,h.type].join(" ");/[^\s]/.test(l)&&this.addType(l,h.data,!0),this.updateConnectedClass()},V.extend(c.Connection,c.OverlayCapableJsPlumbUIComponent,{applyType:function(h,T,a){var i=null;h.connector!=null&&(i=this.getCachedTypeItem("connector",a.connector),i==null&&(i=this.prepareConnector(h.connector,a.connector),this.cacheTypeItem("connector",i,a.connector)),this.setPreparedConnector(i)),h.detachable!=null&&this.setDetachable(h.detachable),h.reattach!=null&&this.setReattach(h.reattach),h.scope&&(this.scope=h.scope),h.cssClass!=null&&this.canvas&&this._jsPlumb.instance.addClass(this.canvas,h.cssClass);var y=null;h.anchor?(y=this.getCachedTypeItem("anchors",a.anchor),y==null&&(y=[this._jsPlumb.instance.makeAnchor(h.anchor),this._jsPlumb.instance.makeAnchor(h.anchor)],this.cacheTypeItem("anchors",y,a.anchor))):h.anchors&&(y=this.getCachedTypeItem("anchors",a.anchors),y==null&&(y=[this._jsPlumb.instance.makeAnchor(h.anchors[0]),this._jsPlumb.instance.makeAnchor(h.anchors[1])],this.cacheTypeItem("anchors",y,a.anchors))),y!=null&&(this.endpoints[0].anchor=y[0],this.endpoints[1].anchor=y[1],this.endpoints[1].anchor.isDynamic&&this._jsPlumb.instance.repaint(this.endpoints[1].elementId)),c.OverlayCapableJsPlumbUIComponent.applyType(this,h)},addClass:function(h,T){T&&(this.endpoints[0].addClass(h),this.endpoints[1].addClass(h),this.suspendedEndpoint&&this.suspendedEndpoint.addClass(h)),this.connector&&this.connector.addClass(h)},removeClass:function(h,T){T&&(this.endpoints[0].removeClass(h),this.endpoints[1].removeClass(h),this.suspendedEndpoint&&this.suspendedEndpoint.removeClass(h)),this.connector&&this.connector.removeClass(h)},isVisible:function(){return this._jsPlumb.visible},setVisible:function(h){this._jsPlumb.visible=h,this.connector&&this.connector.setVisible(h),this.repaint()},cleanup:function(){this.updateConnectedClass(!0),this.endpoints=null,this.source=null,this.target=null,this.connector!=null&&(this.connector.cleanup(!0),this.connector.destroy(!0)),this.connector=null},updateConnectedClass:function(h){this._jsPlumb&&(et(this,this.source,this._jsPlumb.instance,h),et(this,this.target,this._jsPlumb.instance,h))},setHover:function(h){this.connector&&this._jsPlumb&&!this._jsPlumb.instance.isConnectionBeingDragged()&&(this.connector.setHover(h),J.jsPlumb[h?"addClass":"removeClass"](this.source,this._jsPlumb.instance.hoverSourceClass),J.jsPlumb[h?"addClass":"removeClass"](this.target,this._jsPlumb.instance.hoverTargetClass))},getUuids:function(){return[this.endpoints[0].getUuid(),this.endpoints[1].getUuid()]},getCost:function(){return this._jsPlumb?this._jsPlumb.cost:-1/0},setCost:function(h){this._jsPlumb.cost=h},isDirected:function(){return this._jsPlumb.directed},getConnector:function(){return this.connector},prepareConnector:function(h,T){var a={_jsPlumb:this._jsPlumb.instance,cssClass:this._jsPlumb.params.cssClass,container:this._jsPlumb.params.container,"pointer-events":this._jsPlumb.params["pointer-events"]},i=this._jsPlumb.instance.getRenderMode(),y;return V.isString(h)?y=it(this._jsPlumb.instance,i,h,a,this):V.isArray(h)&&(h.length===1?y=it(this._jsPlumb.instance,i,h[0],a,this):y=it(this._jsPlumb.instance,i,h[0],V.merge(h[1],a),this)),T!=null&&(y.typeId=T),y},setPreparedConnector:function(h,T,a,i){if(this.connector!==h){var y,D="";if(this.connector!=null&&(y=this.connector,D=y.getClass(),this.connector.cleanup(),this.connector.destroy()),this.connector=h,i&&this.cacheTypeItem("connector",h,i),this.canvas=this.connector.canvas,this.bgCanvas=this.connector.bgCanvas,this.connector.reattach(this._jsPlumb.instance),this.addClass(D),this.canvas&&(this.canvas._jsPlumb=this),this.bgCanvas&&(this.bgCanvas._jsPlumb=this),y!=null)for(var M=this.getOverlays(),C=0;C<M.length;C++)M[C].transfer&&M[C].transfer(this.connector);a||this.setListenerComponent(this.connector),T||this.repaint()}},setConnector:function(h,T,a,i){var y=this.prepareConnector(h,i);this.setPreparedConnector(y,T,a,i)},paint:function(h){if(!this._jsPlumb.instance.isSuspendDrawing()&&this._jsPlumb.visible){h=h||{};var T=h.timestamp,a=!1,i=a?this.sourceId:this.targetId,y=a?this.targetId:this.sourceId,D=a?0:1,M=a?1:0;if(T==null||T!==this._jsPlumb.lastPaintedAt){var C=this._jsPlumb.instance.updateOffset({elId:y}).o,g=this._jsPlumb.instance.updateOffset({elId:i}).o,e=this.endpoints[M],k=this.endpoints[D],L=e.anchor.getCurrentLocation({xy:[C.left,C.top],wh:[C.width,C.height],element:e,timestamp:T,rotation:this._jsPlumb.instance.getRotation(this.sourceId)}),U=k.anchor.getCurrentLocation({xy:[g.left,g.top],wh:[g.width,g.height],element:k,timestamp:T,rotation:this._jsPlumb.instance.getRotation(this.targetId)});this.connector.resetBounds(),this.connector.compute({sourcePos:L,targetPos:U,sourceOrientation:e.anchor.getOrientation(e),targetOrientation:k.anchor.getOrientation(k),sourceEndpoint:this.endpoints[M],targetEndpoint:this.endpoints[D],"stroke-width":this._jsPlumb.paintStyleInUse.strokeWidth,sourceInfo:C,targetInfo:g});var G={minX:1/0,minY:1/0,maxX:-1/0,maxY:-1/0};for(var P in this._jsPlumb.overlays)if(this._jsPlumb.overlays.hasOwnProperty(P)){var w=this._jsPlumb.overlays[P];w.isVisible()&&(this._jsPlumb.overlayPlacements[P]=w.draw(this.connector,this._jsPlumb.paintStyleInUse,this.getAbsoluteOverlayPosition(w)),G.minX=Math.min(G.minX,this._jsPlumb.overlayPlacements[P].minX),G.maxX=Math.max(G.maxX,this._jsPlumb.overlayPlacements[P].maxX),G.minY=Math.min(G.minY,this._jsPlumb.overlayPlacements[P].minY),G.maxY=Math.max(G.maxY,this._jsPlumb.overlayPlacements[P].maxY))}var r=parseFloat(this._jsPlumb.paintStyleInUse.strokeWidth||1)/2,f=parseFloat(this._jsPlumb.paintStyleInUse.strokeWidth||0),l={xmin:Math.min(this.connector.bounds.minX-(r+f),G.minX),ymin:Math.min(this.connector.bounds.minY-(r+f),G.minY),xmax:Math.max(this.connector.bounds.maxX+(r+f),G.maxX),ymax:Math.max(this.connector.bounds.maxY+(r+f),G.maxY)};this.connector.paintExtents=l,this.connector.paint(this._jsPlumb.paintStyleInUse,null,l);for(var _ in this._jsPlumb.overlays)if(this._jsPlumb.overlays.hasOwnProperty(_)){var x=this._jsPlumb.overlays[_];x.isVisible()&&x.paint(this._jsPlumb.overlayPlacements[_],l)}}this._jsPlumb.lastPaintedAt=T}},repaint:function(h){var T=jsPlumb.extend(h||{},{});T.elId=this.sourceId,this.paint(T)},prepareEndpoint:function(h,T,a,i,y,D,M,C,g){var e;if(i)a.endpoints[y]=i,i.addConnection(a);else{D.endpoints||(D.endpoints=[null,null]);var k=g||D.endpoints[y]||D.endpoint||h.Defaults.Endpoints[y]||c.Defaults.Endpoints[y]||h.Defaults.Endpoint||c.Defaults.Endpoint;D.endpointStyles||(D.endpointStyles=[null,null]),D.endpointHoverStyles||(D.endpointHoverStyles=[null,null]);var L=D.endpointStyles[y]||D.endpointStyle||h.Defaults.EndpointStyles[y]||c.Defaults.EndpointStyles[y]||h.Defaults.EndpointStyle||c.Defaults.EndpointStyle;L.fill==null&&D.paintStyle!=null&&(L.fill=D.paintStyle.stroke),L.outlineStroke==null&&D.paintStyle!=null&&(L.outlineStroke=D.paintStyle.outlineStroke),L.outlineWidth==null&&D.paintStyle!=null&&(L.outlineWidth=D.paintStyle.outlineWidth);var U=D.endpointHoverStyles[y]||D.endpointHoverStyle||h.Defaults.EndpointHoverStyles[y]||c.Defaults.EndpointHoverStyles[y]||h.Defaults.EndpointHoverStyle||c.Defaults.EndpointHoverStyle;D.hoverPaintStyle!=null&&(U==null&&(U={}),U.fill==null&&(U.fill=D.hoverPaintStyle.stroke));var G=D.anchors?D.anchors[y]:D.anchor?D.anchor:K(h.Defaults.Anchors[y],C,h)||K(c.Defaults.Anchors[y],C,h)||K(h.Defaults.Anchor,C,h)||K(c.Defaults.Anchor,C,h),P=D.uuids?D.uuids[y]:null;e=T({paintStyle:L,hoverPaintStyle:U,endpoint:k,connections:[a],uuid:P,anchor:G,source:M,scope:D.scope,reattach:D.reattach||h.Defaults.ReattachConnections,detachable:D.detachable||h.Defaults.ConnectionsDetachable}),i==null&&e.setDeleteOnEmpty(!0),a.endpoints[y]=e,D.drawEndpoints===!1&&e.setVisible(!1,!0,!0)}return e},replaceEndpoint:function(h,T){var a=this.endpoints[h],i=a.elementId,y=this._jsPlumb.instance.getEndpoints(i),D=y.indexOf(a),M=this.makeEndpoint(h===0,a.element,i,null,T);this.endpoints[h]=M,y.splice(D,1,M),this._jsPlumb.instance.deleteObject({endpoint:a,deleteAttachedObjects:!1}),this._jsPlumb.instance.fire("endpointReplaced",{previous:a,current:M}),this._jsPlumb.instance.router.sourceOrTargetChanged(this.endpoints[1].elementId,this.endpoints[1].elementId,this,this.endpoints[1].element,1)}})}).call(typeof window<"u"?window:jt),(function(){var J=this,c=J.jsPlumbUtil,V=J.jsPlumb;V.AnchorManager=function(h){var T={},a={},i={},y={},D=this,M={},C=h.jsPlumbInstance,g={},e=function(l,_,x,u,j,b,A,d){for(var E=[],N=_[j?0:1]/(u.length+1),B=0;B<u.length;B++){var Y=(B+1)*N,n=b*_[j?1:0];A&&(Y=_[j?0:1]-Y);var o=j?Y:n,t=x.left+o,p=o/_[0],v=j?n:Y,I=x.top+v,F=v/_[1];if(d!==0){var X=jsPlumbUtil.rotatePoint([t,I],[x.centerx,x.centery],d);t=X[0],I=X[1]}E.push([t,I,p,F,u[B][1],u[B][2]])}return E},k=function(l,_){return _[0][0]-l[0][0]},L=function(l,_){var x=l[0][0]<0?-Math.PI-l[0][0]:Math.PI-l[0][0],u=_[0][0]<0?-Math.PI-_[0][0]:Math.PI-_[0][0];return x-u},U={top:L,right:k,bottom:k,left:L},G=function(l,_){return l.sort(_)},P=function(l,_){var x=C.getCachedData(l),u=x.s,j=x.o,b=function(A,d,E,N,B,Y,n){if(N.length>0)for(var o=G(N,U[A]),t=A==="right"||A==="top",p=C.getRotation(l),v=e(A,d,E,o,B,Y,t,p),I=function(at,st){a[at.id]=[st[0],st[1],st[2],st[3]],i[at.id]=n},F=0;F<v.length;F++){var X=v[F][4],R=X.endpoints[0].elementId===l,W=X.endpoints[1].elementId===l;R&&I(X.endpoints[0],v[F]),W&&I(X.endpoints[1],v[F])}};b("bottom",u,j,_.bottom,!0,1,[0,1]),b("top",u,j,_.top,!0,0,[0,-1]),b("left",u,j,_.left,!1,0,[-1,0]),b("right",u,j,_.right,!1,1,[1,0])};this.reset=function(){T={},y={},M={}},this.addFloatingConnection=function(l,_){g[l]=_},this.newConnection=function(l){var _=l.sourceId,x=l.targetId,u=l.endpoints,j=!0,b=function(A,d,E,N,B){_===x&&E.isContinuous&&(l._jsPlumb.instance.removeElement(u[1].canvas),j=!1),c.addToList(y,N,[B,d,E.constructor===V.DynamicAnchor])};b(0,u[0],u[0].anchor,x,l),j&&b(1,u[1],u[1].anchor,_,l)};var w=function(l){(function(_,x){if(_){var u=function(j){return j[4]===x};c.removeWithFunction(_.top,u),c.removeWithFunction(_.left,u),c.removeWithFunction(_.bottom,u),c.removeWithFunction(_.right,u)}})(M[l.elementId],l.id)};this.connectionDetached=function(l,_){var x=l.connection||l,u=l.sourceId,j=l.targetId,b=x.endpoints,A=function(d,E,N,B,Y){c.removeWithFunction(y[B],function(n){return n[0].id===Y.id})};A(1,b[1],b[1].anchor,u,x),A(0,b[0],b[0].anchor,j,x),x.floatingId&&(A(x.floatingIndex,x.floatingEndpoint,x.floatingEndpoint.anchor,x.floatingId,x),w(x.floatingEndpoint)),w(x.endpoints[0]),w(x.endpoints[1]),_||(D.redraw(x.sourceId),x.targetId!==x.sourceId&&D.redraw(x.targetId))},this.addEndpoint=function(l,_){c.addToList(T,_,l)},this.changeId=function(l,_){y[_]=y[l],T[_]=T[l],delete y[l],delete T[l]},this.getConnectionsFor=function(l){return y[l]||[]},this.getEndpointsFor=function(l){return T[l]||[]},this.deleteEndpoint=function(l){c.removeWithFunction(T[l.elementId],function(_){return _.id===l.id}),w(l)},this.elementRemoved=function(l){delete g[l],delete T[l],T[l]=[]};var r=function(l,_,x,u,j,b,A,d,E,N,B,Y){var n=-1,o=u.endpoints[A],t=o.id,p=[1,0][A],v=[[_,x],u,j,b,t],I=l[E],F=o._continuousAnchorEdge?l[o._continuousAnchorEdge]:null,X,R;if(F){var W=c.findWithFunction(F,function(st){return st[4]===t});if(W!==-1)for(F.splice(W,1),X=0;X<F.length;X++)R=F[X][1],c.addWithFunction(B,R,function(st){return st.id===R.id}),c.addWithFunction(Y,F[X][1].endpoints[A],function(st){return st.id===R.endpoints[A].id}),c.addWithFunction(Y,F[X][1].endpoints[p],function(st){return st.id===R.endpoints[p].id})}for(X=0;X<I.length;X++)R=I[X][1],h.idx===1&&I[X][3]===b&&n===-1&&(n=X),c.addWithFunction(B,R,function(st){return st.id===R.id}),c.addWithFunction(Y,I[X][1].endpoints[A],function(st){return st.id===R.endpoints[A].id}),c.addWithFunction(Y,I[X][1].endpoints[p],function(st){return st.id===R.endpoints[p].id});{var at=d?n!==-1?n:0:I.length;I.splice(at,0,v)}o._continuousAnchorEdge=E};this.sourceOrTargetChanged=function(l,_,x,u,j){if(j===0){if(l!==_){x.sourceId=_,x.source=u,c.removeWithFunction(y[l],function(N){return N[0].id===x.id});var b=c.findWithFunction(y[x.targetId],function(N){return N[0].id===x.id});b>-1&&(y[x.targetId][b][0]=x,y[x.targetId][b][1]=x.endpoints[0],y[x.targetId][b][2]=x.endpoints[0].anchor.constructor===V.DynamicAnchor),c.addToList(y,_,[x,x.endpoints[1],x.endpoints[1].anchor.constructor===V.DynamicAnchor]),x.endpoints[1].anchor.isContinuous&&(x.source===x.target?x._jsPlumb.instance.removeElement(x.endpoints[1].canvas):x.endpoints[1].canvas.parentNode==null&&x._jsPlumb.instance.appendElement(x.endpoints[1].canvas)),x.updateConnectedClass()}}else if(j===1){var A=x.endpoints[0].elementId;x.target=u,x.targetId=_;var d=c.findWithFunction(y[A],function(N){return N[0].id===x.id}),E=c.findWithFunction(y[l],function(N){return N[0].id===x.id});d!==-1&&(y[A][d][0]=x,y[A][d][1]=x.endpoints[1],y[A][d][2]=x.endpoints[1].anchor.constructor===V.DynamicAnchor),E>-1&&(y[l].splice(E,1),c.addToList(y,_,[x,x.endpoints[0],x.endpoints[0].anchor.constructor===V.DynamicAnchor])),x.updateConnectedClass()}},this.rehomeEndpoint=function(l,_,x){var u=T[_]||[],j=C.getId(x);if(j!==_){var b=u.indexOf(l);if(b>-1){var A=u.splice(b,1)[0];D.add(A,j)}}for(var d=0;d<l.connections.length;d++)l.connections[d].sourceId===_?D.sourceOrTargetChanged(_,l.elementId,l.connections[d],l.element,0):l.connections[d].targetId===_&&D.sourceOrTargetChanged(_,l.elementId,l.connections[d],l.element,1)},this.redraw=function(l,_,x,u,j,b){var A=[],d=[],E=[];if(!C.isSuspendDrawing()){var N=T[l]||[],B=y[l]||[];x=x||jsPlumbUtil.uuid(),u=u||{left:0,top:0},_&&(_={left:_.left+u.left,top:_.top+u.top});for(var Y=C.updateOffset({elId:l,offset:_,recalc:!1,timestamp:x}),n={},o=0;o<B.length;o++){var t=B[o][0],p=t.sourceId,v=t.targetId,I=t.endpoints[0].anchor.isContinuous,F=t.endpoints[1].anchor.isContinuous;if(I||F){var X=p+"_"+v,R=n[X],W=t.sourceId===l?1:0,at=C.getRotation(v),st=C.getRotation(p);I&&!M[p]&&(M[p]={top:[],right:[],bottom:[],left:[]}),F&&!M[v]&&(M[v]={top:[],right:[],bottom:[],left:[]}),l!==v&&C.updateOffset({elId:v,timestamp:x}),l!==p&&C.updateOffset({elId:p,timestamp:x});var nt=C.getCachedData(v),rt=C.getCachedData(p);v===p&&(I||F)?(r(M[p],-Math.PI/2,0,t,!1,v,0,!1,"top",p,A,d),r(M[v],-Math.PI/2,0,t,!1,p,1,!1,"top",v,A,d)):(R||(R=this.calculateOrientation(p,v,rt.o,nt.o,t.endpoints[0].anchor,t.endpoints[1].anchor,t,st,at),n[X]=R),I&&r(M[p],R.theta,0,t,!1,v,0,!1,R.a[0],p,A,d),F&&r(M[v],R.theta2,-1,t,!0,p,1,!0,R.a[1],v,A,d)),I&&c.addWithFunction(E,p,function(pt){return pt===p}),F&&c.addWithFunction(E,v,function(pt){return pt===v}),c.addWithFunction(A,t,function(pt){return pt.id===t.id}),(I&&W===0||F&&W===1)&&c.addWithFunction(d,t.endpoints[W],function(pt){return pt.id===t.endpoints[W].id})}}for(o=0;o<N.length;o++)N[o].connections.length===0&&N[o].anchor.isContinuous&&(M[l]||(M[l]={top:[],right:[],bottom:[],left:[]}),r(M[l],-Math.PI/2,0,{endpoints:[N[o],N[o]],paint:function(){}},!1,l,0,!1,N[o].anchor.getDefaultFace(),l,A,d),c.addWithFunction(E,l,function(pt){return pt===l}));for(o=0;o<E.length;o++)P(E[o],M[E[o]]);for(o=0;o<N.length;o++)N[o].paint({timestamp:x,offset:Y,dimensions:Y.s,recalc:b!==!0});for(o=0;o<d.length;o++){var gt=C.getCachedData(d[o].elementId);d[o].paint({timestamp:null,offset:gt,dimensions:gt.s})}for(o=0;o<B.length;o++){var dt=B[o][1];if(dt.anchor.constructor===V.DynamicAnchor){dt.paint({elementWithPrecedence:l,timestamp:x}),c.addWithFunction(A,B[o][0],function(pt){return pt.id===B[o][0].id});for(var lt=0;lt<dt.connections.length;lt++)dt.connections[lt]!==B[o][0]&&c.addWithFunction(A,dt.connections[lt],function(pt){return pt.id===dt.connections[lt].id})}else c.addWithFunction(A,B[o][0],function(pt){return pt.id===B[o][0].id})}var yt=g[l];for(yt&&yt.paint({timestamp:x,recalc:!1,elId:l}),o=0;o<A.length;o++)A[o].paint({elId:l,timestamp:null,recalc:!1,clearEdits:j})}return{c:A,e:d}};var f=function(l){c.EventGenerator.apply(this),this.type="Continuous",this.isDynamic=!0,this.isContinuous=!0;for(var _=l.faces||["top","right","bottom","left"],x=l.clockwise!==!1,u={},j={top:"bottom",right:"left",left:"right",bottom:"top"},b={top:"right",right:"bottom",left:"top",bottom:"left"},A={top:"left",right:"top",left:"bottom",bottom:"right"},d=x?b:A,E=x?A:b,N=l.cssClass||"",B=null,Y=null,n=["left","right"],o=["top","bottom"],t=null,p=0;p<_.length;p++)u[_[p]]=!0;this.getDefaultFace=function(){return _.length===0?"top":_[0]},this.isRelocatable=function(){return!0},this.isSnapOnRelocate=function(){return!0},this.verifyEdge=function(v){return u[v]?v:u[j[v]]?j[v]:u[d[v]]?d[v]:u[E[v]]?E[v]:v},this.isEdgeSupported=function(v){return t==null?Y==null?u[v]===!0:Y===v:t.indexOf(v)!==-1},this.setCurrentFace=function(v,I){B=v,I&&Y!=null&&(Y=B)},this.getCurrentFace=function(){return B},this.getSupportedFaces=function(){var v=[];for(var I in u)u[I]&&v.push(I);return v},this.lock=function(){Y=B},this.unlock=function(){Y=null},this.isLocked=function(){return Y!=null},this.lockCurrentAxis=function(){B!=null&&(t=B==="left"||B==="right"?n:o)},this.unlockCurrentAxis=function(){t=null},this.compute=function(v){return a[v.element.id]||[0,0]},this.getCurrentLocation=function(v){return a[v.element.id]||[0,0]},this.getOrientation=function(v){return i[v.id]||[0,0]},this.getCssClass=function(){return N}};C.continuousAnchorFactory={get:function(l){return new f(l)},clear:function(l){delete a[l]}}},V.AnchorManager.prototype.calculateOrientation=function(h,T,a,i,y,D,M,C,g){var e={IDENTITY:"identity"},k=["left","top","right","bottom"];if(h===T)return{orientation:e.IDENTITY,a:["top","top"]};var L=Math.atan2(i.centery-a.centery,i.centerx-a.centerx),U=Math.atan2(a.centery-i.centery,a.centerx-i.centerx),G=[],P={};(function(x,u){for(var j=0;j<x.length;j++)if(P[x[j]]={left:[u[j][0].left,u[j][0].centery],right:[u[j][0].right,u[j][0].centery],top:[u[j][0].centerx,u[j][0].top],bottom:[u[j][0].centerx,u[j][0].bottom]},u[j][1]!==0)for(var b in P[x[j]])P[x[j]][b]=jsPlumbUtil.rotatePoint(P[x[j]][b],[u[j][0].centerx,u[j][0].centery],u[j][1])})(["source","target"],[[a,C],[i,g]]);for(var w=0;w<k.length;w++)for(var r=0;r<k.length;r++)G.push({source:k[w],target:k[r],dist:Biltong.lineLength(P.source[k[w]],P.target[k[r]])});G.sort(function(x,u){return x.dist<u.dist?-1:x.dist>u.dist?1:0});for(var f=G[0].source,l=G[0].target,_=0;_<G.length&&(y.isContinuous&&y.locked?f=y.getCurrentFace():!y.isContinuous||y.isEdgeSupported(G[_].source)?f=G[_].source:f=null,D.isContinuous&&D.locked?l=D.getCurrentFace():!D.isContinuous||D.isEdgeSupported(G[_].target)?l=G[_].target:l=null,!(f!=null&&l!=null));_++);return y.isContinuous&&y.setCurrentFace(f),D.isContinuous&&D.setCurrentFace(l),{a:[f,l],theta:L,theta2:U}},V.Anchor=function(h){this.x=h.x||0,this.y=h.y||0,this.elementId=h.elementId,this.cssClass=h.cssClass||"",this.orientation=h.orientation||[0,0],this.lastReturnValue=null,this.offsets=h.offsets||[0,0],this.timestamp=null,this._unrotatedOrientation=[this.orientation[0],this.orientation[1]],this.relocatable=h.relocatable!==!1,this.snapOnRelocate=h.snapOnRelocate!==!1,this.locked=!1,c.EventGenerator.apply(this),this.compute=function(T){var a=T.xy,i=T.wh,y=T.timestamp;if(y&&y===this.timestamp)return this.lastReturnValue;var D=[a[0]+this.x*i[0]+this.offsets[0],a[1]+this.y*i[1]+this.offsets[1],this.x,this.y],M=T.rotation;if(M!=null&&M!==0){var C=jsPlumbUtil.rotatePoint(D,[a[0]+i[0]/2,a[1]+i[1]/2],M);this.orientation[0]=Math.round(this._unrotatedOrientation[0]*C[2]-this._unrotatedOrientation[1]*C[3]),this.orientation[1]=Math.round(this._unrotatedOrientation[1]*C[2]+this._unrotatedOrientation[0]*C[3]),this.lastReturnValue=[C[0],C[1],this.x,this.y]}else this.orientation[0]=this._unrotatedOrientation[0],this.orientation[1]=this._unrotatedOrientation[1],this.lastReturnValue=D;return this.timestamp=y,this.lastReturnValue},this.getCurrentLocation=function(T){return T=T||{},this.lastReturnValue==null||T.timestamp!=null&&this.timestamp!==T.timestamp?this.compute(T):this.lastReturnValue},this.setPosition=function(T,a,i,y,D){(!this.locked||D)&&(this.x=T,this.y=a,this.orientation=[i,y],this.lastReturnValue=null)}},c.extend(V.Anchor,c.EventGenerator,{equals:function(h){if(!h)return!1;var T=h.getOrientation(),a=this.getOrientation();return this.x===h.x&&this.y===h.y&&this.offsets[0]===h.offsets[0]&&this.offsets[1]===h.offsets[1]&&a[0]===T[0]&&a[1]===T[1]},getOrientation:function(){return this.orientation},getCssClass:function(){return this.cssClass}}),V.FloatingAnchor=function(h){V.Anchor.apply(this,arguments);var T=h.reference,a=h.referenceCanvas,i=V.getSize(a),y=0,D=0,M=null,C=null;this.orientation=null,this.x=0,this.y=0,this.isFloating=!0,this.compute=function(g){var e=g.xy,k=[e[0]+i[0]/2,e[1]+i[1]/2];return C=k,k},this.getOrientation=function(g){if(M)return M;var e=T.getOrientation(g);return[Math.abs(e[0])*y*-1,Math.abs(e[1])*D*-1]},this.over=function(g,e){M=g.getOrientation(e)},this.out=function(){M=null},this.getCurrentLocation=function(g){return C??this.compute(g)}},c.extend(V.FloatingAnchor,V.Anchor);var it=function(h,T,a){return h.constructor===V.Anchor?h:T.makeAnchor(h,a,T)};V.DynamicAnchor=function(h){V.Anchor.apply(this,arguments),this.isDynamic=!0,this.anchors=[],this.elementId=h.elementId,this.jsPlumbInstance=h.jsPlumbInstance;for(var T=0;T<h.anchors.length;T++)this.anchors[T]=it(h.anchors[T],this.jsPlumbInstance,this.elementId);this.getAnchors=function(){return this.anchors};var a=this.anchors.length>0?this.anchors[0]:null,i=a,y=function(M,C,g,e,k,L,U){var G=e[0]+M.x*k[0],P=e[1]+M.y*k[1],w=e[0]+k[0]/2,r=e[1]+k[1]/2;if(L!=null&&L!==0){var f=jsPlumbUtil.rotatePoint([G,P],[w,r],L);G=f[0],P=f[1]}return Math.sqrt(Math.pow(C-G,2)+Math.pow(g-P,2))+Math.sqrt(Math.pow(w-G,2)+Math.pow(r-P,2))},D=h.selector||function(M,C,g,e,k,L,U){for(var G=g[0]+e[0]/2,P=g[1]+e[1]/2,w=-1,r=1/0,f=0;f<U.length;f++){var l=y(U[f],G,P,M,C,k);l<r&&(w=f+0,r=l)}return U[w]};this.compute=function(M){var C=M.xy,g=M.wh,e=M.txy,k=M.twh,L=M.rotation,U=M.tRotation;return this.timestamp=M.timestamp,this.locked||e==null||k==null?(this.lastReturnValue=a.compute(M),this.lastReturnValue):(M.timestamp=null,a=D(C,g,e,k,L,U,this.anchors),this.x=a.x,this.y=a.y,a!==i&&this.fire("anchorChanged",a),i=a,this.lastReturnValue=a.compute(M),this.lastReturnValue)},this.getCurrentLocation=function(M){return a!=null?a.getCurrentLocation(M):null},this.getOrientation=function(M){return a!=null?a.getOrientation(M):[0,0]},this.over=function(M,C){a!=null&&a.over(M,C)},this.out=function(){a!=null&&a.out()},this.setAnchor=function(M){a=M},this.getCssClass=function(){return a&&a.getCssClass()||""},this.setAnchorCoordinates=function(M){var C=jsPlumbUtil.findWithFunction(this.anchors,function(g){return g.x===M[0]&&g.y===M[1]});return C!==-1?(this.setAnchor(this.anchors[C]),!0):!1}},c.extend(V.DynamicAnchor,V.Anchor);var K=function(h,T,a,i,y,D){V.Anchors[y]=function(M){var C=M.jsPlumbInstance.makeAnchor([h,T,a,i,0,0],M.elementId,M.jsPlumbInstance);return C.type=y,D&&D(C,M),C}};K(.5,0,0,-1,"TopCenter"),K(.5,1,0,1,"BottomCenter"),K(0,.5,-1,0,"LeftMiddle"),K(1,.5,1,0,"RightMiddle"),K(.5,0,0,-1,"Top"),K(.5,1,0,1,"Bottom"),K(0,.5,-1,0,"Left"),K(1,.5,1,0,"Right"),K(.5,.5,0,0,"Center"),K(1,0,0,-1,"TopRight"),K(1,1,0,1,"BottomRight"),K(0,0,0,-1,"TopLeft"),K(0,1,0,1,"BottomLeft"),V.Defaults.DynamicAnchors=function(h){return h.jsPlumbInstance.makeAnchors(["TopCenter","RightMiddle","BottomCenter","LeftMiddle"],h.elementId,h.jsPlumbInstance)},V.Anchors.AutoDefault=function(h){var T=h.jsPlumbInstance.makeDynamicAnchor(V.Defaults.DynamicAnchors(h));return T.type="AutoDefault",T};var et=function(h,T){V.Anchors[h]=function(a){var i=a.jsPlumbInstance.makeAnchor(["Continuous",{faces:T}],a.elementId,a.jsPlumbInstance);return i.type=h,i}};V.Anchors.Continuous=function(h){return h.jsPlumbInstance.continuousAnchorFactory.get(h)},et("ContinuousLeft",["left"]),et("ContinuousTop",["top"]),et("ContinuousBottom",["bottom"]),et("ContinuousRight",["right"]),K(0,0,0,0,"Assign",function(h,T){var a=T.position||"Fixed";h.positionFinder=a.constructor===String?T.jsPlumbInstance.AnchorPositionFinders[a]:a,h.constructorParams=T}),J.jsPlumbInstance.prototype.AnchorPositionFinders={Fixed:function(h,T,a){return[(h.left-T.left)/a[0],(h.top-T.top)/a[1]]},Grid:function(h,T,a,i){var y=h.left-T.left,D=h.top-T.top,M=a[0]/i.grid[0],C=a[1]/i.grid[1],g=Math.floor(y/M),e=Math.floor(D/C);return[(g*M+M/2)/a[0],(e*C+C/2)/a[1]]}},V.Anchors.Perimeter=function(h){h=h||{};var T=h.anchorCount||60,a=h.shape;if(!a)throw new Error("no shape supplied to Perimeter Anchor type");var i=function(){for(var L=.5,U=Math.PI*2/T,G=0,P=[],w=0;w<T;w++){var r=L+L*Math.sin(G),f=L+L*Math.cos(G);P.push([r,f,0,0]),G+=U}return P},y=function(L){for(var U=T/L.length,G=[],P=function(r,f,l,_,x,u,j){U=T*x;for(var b=(l-r)/U,A=(_-f)/U,d=0;d<U;d++)G.push([r+b*d,f+A*d,u??0,j??0])},w=0;w<L.length;w++)P.apply(null,L[w]);return G},D=function(L){for(var U=[],G=0;G<L.length;G++)U.push([L[G][0],L[G][1],L[G][2],L[G][3],1/L.length,L[G][4],L[G][5]]);return y(U)},M=function(){return D([[0,0,1,0,0,-1],[1,0,1,1,1,0],[1,1,0,1,0,1],[0,1,0,0,-1,0]])},C={Circle:i,Ellipse:i,Diamond:function(){return D([[.5,0,1,.5],[1,.5,.5,1],[.5,1,0,.5],[0,.5,.5,0]])},Rectangle:M,Square:M,Triangle:function(){return D([[.5,0,1,1],[1,1,0,1],[0,1,.5,0]])},Path:function(L){for(var U=L.points,G=[],P=0,w=0;w<U.length-1;w++){var r=Math.sqrt(Math.pow(U[w][2]-U[w][0])+Math.pow(U[w][3]-U[w][1]));P+=r,G.push([U[w][0],U[w][1],U[w+1][0],U[w+1][1],r])}for(var f=0;f<G.length;f++)G[f][4]=G[f][4]/P;return y(G)}},g=function(L,U){for(var G=[],P=U/180*Math.PI,w=0;w<L.length;w++){var r=L[w][0]-.5,f=L[w][1]-.5;G.push([.5+(r*Math.cos(P)-f*Math.sin(P)),.5+(r*Math.sin(P)+f*Math.cos(P)),L[w][2],L[w][3]])}return G};if(!C[a])throw new Error("Shape ["+a+"] is unknown by Perimeter Anchor type");var e=C[a](h);h.rotation&&(e=g(e,h.rotation));var k=h.jsPlumbInstance.makeDynamicAnchor(e);return k.type="Perimeter",k}}).call(typeof window<"u"?window:jt),(function(){var J=this;J.jsPlumbUtil;var c=J.jsPlumb;c.DefaultRouter=function(V){this.jsPlumbInstance=V,this.anchorManager=new c.AnchorManager({jsPlumbInstance:V}),this.sourceOrTargetChanged=function(it,K,et,h,T){this.anchorManager.sourceOrTargetChanged(it,K,et,h,T)},this.reset=function(){this.anchorManager.reset()},this.changeId=function(it,K){this.anchorManager.changeId(it,K)},this.elementRemoved=function(it){this.anchorManager.elementRemoved(it)},this.newConnection=function(it){this.anchorManager.newConnection(it)},this.connectionDetached=function(it,K){this.anchorManager.connectionDetached(it,K)},this.redraw=function(it,K,et,h,T,a){return this.anchorManager.redraw(it,K,et,h,T,a)},this.deleteEndpoint=function(it){this.anchorManager.deleteEndpoint(it)},this.rehomeEndpoint=function(it,K,et){this.anchorManager.rehomeEndpoint(it,K,et)},this.addEndpoint=function(it,K){this.anchorManager.addEndpoint(it,K)}}}).call(typeof window<"u"?window:jt),(function(){var J=this,c=J.jsPlumb,V=J.jsPlumbUtil,it=J.Biltong;c.Segments={AbstractSegment:function(i){this.params=i,this.findClosestPointOnPath=function(y,D){return{d:1/0,x:null,y:null,l:null}},this.getBounds=function(){return{minX:Math.min(i.x1,i.x2),minY:Math.min(i.y1,i.y2),maxX:Math.max(i.x1,i.x2),maxY:Math.max(i.y1,i.y2)}},this.lineIntersection=function(y,D,M,C){return[]},this.boxIntersection=function(y,D,M,C){var g=[];return g.push.apply(g,this.lineIntersection(y,D,y+M,D)),g.push.apply(g,this.lineIntersection(y+M,D,y+M,D+C)),g.push.apply(g,this.lineIntersection(y+M,D+C,y,D+C)),g.push.apply(g,this.lineIntersection(y,D+C,y,D)),g},this.boundingBoxIntersection=function(y){return this.boxIntersection(y.x,y.y,y.w,y.y)}},Straight:function(i){c.Segments.AbstractSegment.apply(this,arguments);var y,D,M,C,g,e,k,L=function(){y=Math.sqrt(Math.pow(g-C,2)+Math.pow(k-e,2)),D=it.gradient({x:C,y:e},{x:g,y:k}),M=-1/D};this.type="Straight",this.getLength=function(){return y},this.getGradient=function(){return D},this.getCoordinates=function(){return{x1:C,y1:e,x2:g,y2:k}},this.setCoordinates=function(r){C=r.x1,e=r.y1,g=r.x2,k=r.y2,L()},this.setCoordinates({x1:i.x1,y1:i.y1,x2:i.x2,y2:i.y2}),this.getBounds=function(){return{minX:Math.min(C,g),minY:Math.min(e,k),maxX:Math.max(C,g),maxY:Math.max(e,k)}},this.pointOnPath=function(r,f){if(r===0&&!f)return{x:C,y:e};if(r===1&&!f)return{x:g,y:k};var l=f?r>0?r:y+r:r*y;return it.pointOnLine({x:C,y:e},{x:g,y:k},l)},this.gradientAtPoint=function(r){return D},this.pointAlongPathFrom=function(r,f,l){var _=this.pointOnPath(r,l),x=f<=0?{x:C,y:e}:{x:g,y:k};return f<=0&&Math.abs(f)>1&&(f*=-1),it.pointOnLine(_,x,f)};var U=function(r,f,l){return l>=Math.min(r,f)&&l<=Math.max(r,f)},G=function(r,f,l){return Math.abs(l-r)<Math.abs(l-f)?r:f};this.findClosestPointOnPath=function(r,f){var l={d:1/0,x:null,y:null,l:null,x1:C,x2:g,y1:e,y2:k};if(D===0)l.y=e,l.x=U(C,g,r)?r:G(C,g,r);else if(D===1/0||D===-1/0)l.x=C,l.y=U(e,k,f)?f:G(e,k,f);else{var _=e-D*C,x=f-M*r,u=(x-_)/(D-M),j=D*u+_;l.x=U(C,g,u)?u:G(C,g,u),l.y=U(e,k,j)?j:G(e,k,j)}var b=it.lineLength([l.x,l.y],[C,e]);return l.d=it.lineLength([r,f],[l.x,l.y]),l.l=b/y,l};var P=function(r,f,l){return l>f?f<=r&&r<=l:f>=r&&r>=l},w=P;this.lineIntersection=function(r,f,l,_){var x=Math.abs(it.gradient({x:r,y:f},{x:l,y:_})),u=Math.abs(D),j=u===1/0?C:e-u*C,b=[],A=x===1/0?r:f-x*r;if(x!==u)if(x===1/0&&u===0)w(r,C,g)&&w(e,f,_)&&(b=[r,e]);else if(x===0&&u===1/0)w(f,e,k)&&w(C,r,l)&&(b=[C,f]);else{var d,E;x===1/0?(d=r,w(d,C,g)&&(E=u*r+j,w(E,f,_)&&(b=[d,E]))):x===0?(E=f,w(E,e,k)&&(d=(f-j)/u,w(d,r,l)&&(b=[d,E]))):(d=(A-j)/(u-x),E=u*d+j,w(d,C,g)&&w(E,e,k)&&(b=[d,E]))}return b},this.boxIntersection=function(r,f,l,_){var x=[];return x.push.apply(x,this.lineIntersection(r,f,r+l,f)),x.push.apply(x,this.lineIntersection(r+l,f,r+l,f+_)),x.push.apply(x,this.lineIntersection(r+l,f+_,r,f+_)),x.push.apply(x,this.lineIntersection(r,f+_,r,f)),x},this.boundingBoxIntersection=function(r){return this.boxIntersection(r.x,r.y,r.w,r.h)}},Arc:function(i){c.Segments.AbstractSegment.apply(this,arguments);var y=function(G,P){return it.theta([i.cx,i.cy],[G,P])},D=function(G,P){if(G.anticlockwise){var w=G.startAngle<G.endAngle?G.startAngle+M:G.startAngle,r=Math.abs(w-G.endAngle);return w-r*P}else{var f=G.endAngle<G.startAngle?G.endAngle+M:G.endAngle,l=Math.abs(f-G.startAngle);return G.startAngle+l*P}},M=2*Math.PI;this.radius=i.r,this.anticlockwise=i.ac,this.type="Arc",i.startAngle&&i.endAngle?(this.startAngle=i.startAngle,this.endAngle=i.endAngle,this.x1=i.cx+this.radius*Math.cos(i.startAngle),this.y1=i.cy+this.radius*Math.sin(i.startAngle),this.x2=i.cx+this.radius*Math.cos(i.endAngle),this.y2=i.cy+this.radius*Math.sin(i.endAngle)):(this.startAngle=y(i.x1,i.y1),this.endAngle=y(i.x2,i.y2),this.x1=i.x1,this.y1=i.y1,this.x2=i.x2,this.y2=i.y2),this.endAngle<0&&(this.endAngle+=M),this.startAngle<0&&(this.startAngle+=M);var C=this.endAngle<this.startAngle?this.endAngle+M:this.endAngle;this.sweep=Math.abs(C-this.startAngle),this.anticlockwise&&(this.sweep=M-this.sweep);var g=2*Math.PI*this.radius,e=this.sweep/M,k=g*e;this.getLength=function(){return k},this.getBounds=function(){return{minX:i.cx-i.r,maxX:i.cx+i.r,minY:i.cy-i.r,maxY:i.cy+i.r}};var L=1e-10,U=function(G){var P=Math.floor(G),w=Math.ceil(G);return G-P<L?P:w-G<L?w:G};this.pointOnPath=function(G,P){if(G===0)return{x:this.x1,y:this.y1,theta:this.startAngle};if(G===1)return{x:this.x2,y:this.y2,theta:this.endAngle};P&&(G=G/k);var w=D(this,G),r=i.cx+i.r*Math.cos(w),f=i.cy+i.r*Math.sin(w);return{x:U(r),y:U(f),theta:w}},this.gradientAtPoint=function(G,P){var w=this.pointOnPath(G,P),r=it.normal([i.cx,i.cy],[w.x,w.y]);return!this.anticlockwise&&(r===1/0||r===-1/0)&&(r*=-1),r},this.pointAlongPathFrom=function(G,P,w){var r=this.pointOnPath(G,w),f=P/g*2*Math.PI,l=this.anticlockwise?-1:1,_=r.theta+l*f,x=i.cx+this.radius*Math.cos(_),u=i.cy+this.radius*Math.sin(_);return{x,y:u}}},Bezier:function(i){this.curve=[{x:i.x1,y:i.y1},{x:i.cp1x,y:i.cp1y},{x:i.cp2x,y:i.cp2y},{x:i.x2,y:i.y2}];var y=function(k){return k[0].x===k[1].x&&k[0].y===k[1].y},D=function(k,L){return Math.sqrt(Math.pow(k.x-L.x,2)+Math.pow(k.y-L.y,2))},M=(function(k){var L={x:0,y:0};if(k===0)return this.curve[0];var U=this.curve.length-1;if(k===1)return this.curve[U];var G=this.curve,P=1-k;if(U===0)return this.curve[0];if(U===1)return{x:P*G[0].x+k*G[1].x,y:P*G[0].y+k*G[1].y};if(U<4){var w=P*P,r=k*k,f=0,l,_,x;return U===2?(G=[G[0],G[1],G[2],L],l=w,_=2*(P*k),x=r):U===3&&(l=w*P,_=3*(w*k),x=3*(P*r),f=k*r),{x:l*G[0].x+_*G[1].x+x*G[2].x+f*G[3].x,y:l*G[0].y+_*G[1].y+x*G[2].y+f*G[3].y}}else return L}).bind(this),C=function(k){var L=[];k--;for(var U=0;U<=k;U++)L.push(M(U/k));return L},g=(function(){y(this.curve)&&(this.length=0);var k=16,L=C(k);this.length=0;for(var U=0;U<k-1;U++){var G=L[U],P=L[U+1];this.length+=D(G,P)}}).bind(this);c.Segments.AbstractSegment.apply(this,arguments),this.bounds={minX:Math.min(i.x1,i.x2,i.cp1x,i.cp2x),minY:Math.min(i.y1,i.y2,i.cp1y,i.cp2y),maxX:Math.max(i.x1,i.x2,i.cp1x,i.cp2x),maxY:Math.max(i.y1,i.y2,i.cp1y,i.cp2y)},this.type="Bezier",g();var e=function(k,L,U){return U&&(L=J.jsBezier.locationAlongCurveFrom(k,L>0?0:1,L)),L};this.pointOnPath=function(k,L){return k=e(this.curve,k,L),J.jsBezier.pointOnCurve(this.curve,k)},this.gradientAtPoint=function(k,L){return k=e(this.curve,k,L),J.jsBezier.gradientAtPoint(this.curve,k)},this.pointAlongPathFrom=function(k,L,U){return k=e(this.curve,k,U),J.jsBezier.pointAlongCurveFrom(this.curve,k,L)},this.getLength=function(){return this.length},this.getBounds=function(){return this.bounds},this.findClosestPointOnPath=function(k,L){var U=J.jsBezier.nearestPointOnCurve({x:k,y:L},this.curve);return{d:Math.sqrt(Math.pow(U.point.x-k,2)+Math.pow(U.point.y-L,2)),x:U.point.x,y:U.point.y,l:1-U.location,s:this}},this.lineIntersection=function(k,L,U,G){return J.jsBezier.lineIntersection(k,L,U,G,this.curve)}}},c.SegmentRenderer={getPath:function(i,y){return{Straight:function(D){var M=i.getCoordinates();return(D?"M "+M.x1+" "+M.y1+" ":"")+"L "+M.x2+" "+M.y2},Bezier:function(D){var M=i.params;return(D?"M "+M.x2+" "+M.y2+" ":"")+"C "+M.cp2x+" "+M.cp2y+" "+M.cp1x+" "+M.cp1y+" "+M.x1+" "+M.y1},Arc:function(D){var M=i.params,C=i.sweep>Math.PI?1:0,g=i.anticlockwise?0:1;return(D?"M"+i.x1+" "+i.y1+" ":"")+"A "+i.radius+" "+M.r+" 0 "+C+","+g+" "+i.x2+" "+i.y2}}[i.type](y)}};var K=function(){this.resetBounds=function(){this.bounds={minX:1/0,minY:1/0,maxX:-1/0,maxY:-1/0}},this.resetBounds()};c.Connectors.AbstractConnector=function(i){K.apply(this,arguments);var y=[],D=0,M=[],C=[],g=i.stub||0,e=V.isArray(g)?g[0]:g,k=V.isArray(g)?g[1]:g,L=i.gap||0,U=V.isArray(L)?L[0]:L,G=V.isArray(L)?L[1]:L,P=null,w=null;this.getPathData=function(){for(var u="",j=0;j<y.length;j++)u+=c.SegmentRenderer.getPath(y[j],j===0),u+=" ";return u},this.findSegmentForPoint=function(u,j){for(var b={d:1/0,s:null,x:null,y:null,l:null},A=0;A<y.length;A++){var d=y[A].findClosestPointOnPath(u,j);d.d<b.d&&(b.d=d.d,b.l=d.l,b.x=d.x,b.y=d.y,b.s=y[A],b.x1=d.x1,b.x2=d.x2,b.y1=d.y1,b.y2=d.y2,b.index=A,b.connectorLocation=M[A][0]+d.l*(M[A][1]-M[A][0]))}return b},this.lineIntersection=function(u,j,b,A){for(var d=[],E=0;E<y.length;E++)d.push.apply(d,y[E].lineIntersection(u,j,b,A));return d},this.boxIntersection=function(u,j,b,A){for(var d=[],E=0;E<y.length;E++)d.push.apply(d,y[E].boxIntersection(u,j,b,A));return d},this.boundingBoxIntersection=function(u){for(var j=[],b=0;b<y.length;b++)j.push.apply(j,y[b].boundingBoxIntersection(u));return j};var r=function(){for(var u=0,j=0;j<y.length;j++){var b=y[j].getLength();C[j]=b/D,M[j]=[u,u+=b/D]}},f=function(u,j){var b,A,d;if(j&&(u=u>0?u/D:(D+u)/D),u===1)b=y.length-1,d=1;else if(u===0)d=0,b=0;else if(u>=.5){for(b=0,d=0,A=M.length-1;A>-1;A--)if(M[A][1]>=u&&M[A][0]<=u){b=A,d=(u-M[A][0])/C[A];break}}else for(b=M.length-1,d=1,A=0;A<M.length;A++)if(M[A][1]>=u){b=A,d=(u-M[A][0])/C[A];break}return{segment:y[b],proportion:d,index:b}},l=function(u,j,b){if(!(b.x1===b.x2&&b.y1===b.y2)){var A=new c.Segments[j](b);y.push(A),D+=A.getLength(),u.updateBounds(A)}},_=function(){D=y.length=M.length=C.length=0};this.setSegments=function(u){P=[],D=0;for(var j=0;j<u.length;j++)P.push(u[j]),D+=u[j].getLength()},this.getLength=function(){return D};var x=function(u){this.strokeWidth=u.strokeWidth;var j=it.quadrant(u.sourcePos,u.targetPos),b=u.targetPos[0]<u.sourcePos[0],A=u.targetPos[1]<u.sourcePos[1],d=u.strokeWidth||1,E=u.sourceEndpoint.anchor.getOrientation(u.sourceEndpoint),N=u.targetEndpoint.anchor.getOrientation(u.targetEndpoint),B=b?u.targetPos[0]:u.sourcePos[0],Y=A?u.targetPos[1]:u.sourcePos[1],n=Math.abs(u.targetPos[0]-u.sourcePos[0]),o=Math.abs(u.targetPos[1]-u.sourcePos[1]);if(E[0]===0&&E[1]===0||N[0]===0&&N[1]===0){var t=n>o?0:1,p=[1,0][t];E=[],N=[],E[t]=u.sourcePos[t]>u.targetPos[t]?-1:1,N[t]=u.sourcePos[t]>u.targetPos[t]?1:-1,E[p]=0,N[p]=0}var v=b?n+U*E[0]:U*E[0],I=A?o+U*E[1]:U*E[1],F=b?G*N[0]:n+G*N[0],X=A?G*N[1]:o+G*N[1],R=E[0]*N[0]+E[1]*N[1],W={sx:v,sy:I,tx:F,ty:X,lw:d,xSpan:Math.abs(F-v),ySpan:Math.abs(X-I),mx:(v+F)/2,my:(I+X)/2,so:E,to:N,x:B,y:Y,w:n,h:o,segment:j,startStubX:v+E[0]*e,startStubY:I+E[1]*e,endStubX:F+N[0]*k,endStubY:X+N[1]*k,isXGreaterThanStubTimes2:Math.abs(v-F)>e+k,isYGreaterThanStubTimes2:Math.abs(I-X)>e+k,opposite:R===-1,perpendicular:R===0,orthogonal:R===1,sourceAxis:E[0]===0?"y":"x",points:[B,Y,n,o,v,I,F,X],stubs:[e,k]};return W.anchorOrientation=W.opposite?"opposite":W.orthogonal?"orthogonal":"perpendicular",W};return this.getSegments=function(){return y},this.updateBounds=function(u){var j=u.getBounds();this.bounds.minX=Math.min(this.bounds.minX,j.minX),this.bounds.maxX=Math.max(this.bounds.maxX,j.maxX),this.bounds.minY=Math.min(this.bounds.minY,j.minY),this.bounds.maxY=Math.max(this.bounds.maxY,j.maxY)},this.pointOnPath=function(u,j){var b=f(u,j);return b.segment&&b.segment.pointOnPath(b.proportion,!1)||[0,0]},this.gradientAtPoint=function(u,j){var b=f(u,j);return b.segment&&b.segment.gradientAtPoint(b.proportion,!1)||0},this.pointAlongPathFrom=function(u,j,b){var A=f(u,b);return A.segment&&A.segment.pointAlongPathFrom(A.proportion,j,!1)||[0,0]},this.compute=function(u){w=x.call(this,u),_(),this._compute(w,u),this.x=w.points[0],this.y=w.points[1],this.w=w.points[2],this.h=w.points[3],this.segment=w.segment,r()},{addSegment:l,prepareCompute:x,sourceStub:e,targetStub:k,maxStub:Math.max(e,k),sourceGap:U,targetGap:G,maxGap:Math.max(U,G)}},V.extend(c.Connectors.AbstractConnector,K),c.Endpoints.AbstractEndpoint=function(i){K.apply(this,arguments);var y=this.compute=function(D,M,C,g){var e=this._compute.apply(this,arguments);return this.x=e[0],this.y=e[1],this.w=e[2],this.h=e[3],this.bounds.minX=this.x,this.bounds.minY=this.y,this.bounds.maxX=this.x+this.w,this.bounds.maxY=this.y+this.h,e};return{compute:y,cssClass:i.cssClass}},V.extend(c.Endpoints.AbstractEndpoint,K),c.Endpoints.Dot=function(i){this.type="Dot",c.Endpoints.AbstractEndpoint.apply(this,arguments),i=i||{},this.radius=i.radius||10,this.defaultOffset=.5*this.radius,this.defaultInnerRadius=this.radius/3,this._compute=function(y,D,M,C){this.radius=M.radius||this.radius;var g=y[0]-this.radius,e=y[1]-this.radius,k=this.radius*2,L=this.radius*2;if(M.stroke){var U=M.strokeWidth||1;g-=U,e-=U,k+=U*2,L+=U*2}return[g,e,k,L,this.radius]}},V.extend(c.Endpoints.Dot,c.Endpoints.AbstractEndpoint),c.Endpoints.Rectangle=function(i){this.type="Rectangle",c.Endpoints.AbstractEndpoint.apply(this,arguments),i=i||{},this.width=i.width||20,this.height=i.height||20,this._compute=function(y,D,M,C){var g=M.width||this.width,e=M.height||this.height,k=y[0]-g/2,L=y[1]-e/2;return[k,L,g,e]}},V.extend(c.Endpoints.Rectangle,c.Endpoints.AbstractEndpoint);var et=function(i){c.jsPlumbUIComponent.apply(this,arguments),this._jsPlumb.displayElements=[]};V.extend(et,c.jsPlumbUIComponent,{getDisplayElements:function(){return this._jsPlumb.displayElements},appendDisplayElement:function(i){this._jsPlumb.displayElements.push(i)}}),c.Endpoints.Image=function(i){this.type="Image",et.apply(this,arguments),c.Endpoints.AbstractEndpoint.apply(this,arguments);var y=i.onload,D=i.src||i.url,M=i.cssClass?" "+i.cssClass:"";this._jsPlumb.img=new Image,this._jsPlumb.ready=!1,this._jsPlumb.initialized=!1,this._jsPlumb.deleted=!1,this._jsPlumb.widthToUse=i.width,this._jsPlumb.heightToUse=i.height,this._jsPlumb.endpoint=i.endpoint,this._jsPlumb.img.onload=(function(){this._jsPlumb!=null&&(this._jsPlumb.ready=!0,this._jsPlumb.widthToUse=this._jsPlumb.widthToUse||this._jsPlumb.img.width,this._jsPlumb.heightToUse=this._jsPlumb.heightToUse||this._jsPlumb.img.height,y&&y(this))}).bind(this),this._jsPlumb.endpoint.setImage=(function(C,g){var e=C.constructor===String?C:C.src;y=g,this._jsPlumb.img.src=e,this.canvas!=null&&this.canvas.setAttribute("src",this._jsPlumb.img.src)}).bind(this),this._jsPlumb.endpoint.setImage(D,y),this._compute=function(C,g,e,k){return this.anchorPoint=C,this._jsPlumb.ready?[C[0]-this._jsPlumb.widthToUse/2,C[1]-this._jsPlumb.heightToUse/2,this._jsPlumb.widthToUse,this._jsPlumb.heightToUse]:[0,0,0,0]},this.canvas=c.createElement("img",{position:"absolute",margin:0,padding:0,outline:0},this._jsPlumb.instance.endpointClass+M),this._jsPlumb.widthToUse&&this.canvas.setAttribute("width",this._jsPlumb.widthToUse),this._jsPlumb.heightToUse&&this.canvas.setAttribute("height",this._jsPlumb.heightToUse),this._jsPlumb.instance.appendElement(this.canvas),this.actuallyPaint=function(C,g,e){if(!this._jsPlumb.deleted){this._jsPlumb.initialized||(this.canvas.setAttribute("src",this._jsPlumb.img.src),this.appendDisplayElement(this.canvas),this._jsPlumb.initialized=!0);var k=this.anchorPoint[0]-this._jsPlumb.widthToUse/2,L=this.anchorPoint[1]-this._jsPlumb.heightToUse/2;V.sizeElement(this.canvas,k,L,this._jsPlumb.widthToUse,this._jsPlumb.heightToUse)}},this.paint=function(C,g){this._jsPlumb!=null&&(this._jsPlumb.ready?this.actuallyPaint(C,g):J.setTimeout((function(){this.paint(C,g)}).bind(this),200))}},V.extend(c.Endpoints.Image,[et,c.Endpoints.AbstractEndpoint],{cleanup:function(i){i&&(this._jsPlumb.deleted=!0,this.canvas&&this.canvas.parentNode.removeChild(this.canvas),this.canvas=null)}}),c.Endpoints.Blank=function(i){c.Endpoints.AbstractEndpoint.apply(this,arguments),this.type="Blank",et.apply(this,arguments),this._compute=function(D,M,C,g){return[D[0],D[1],10,0]};var y=i.cssClass?" "+i.cssClass:"";this.canvas=c.createElement("div",{display:"block",width:"1px",height:"1px",background:"transparent",position:"absolute"},this._jsPlumb.instance.endpointClass+y),this._jsPlumb.instance.appendElement(this.canvas),this.paint=function(D,M){V.sizeElement(this.canvas,this.x,this.y,this.w,this.h)}},V.extend(c.Endpoints.Blank,[c.Endpoints.AbstractEndpoint,et],{cleanup:function(){this.canvas&&this.canvas.parentNode&&this.canvas.parentNode.removeChild(this.canvas)}}),c.Endpoints.Triangle=function(i){this.type="Triangle",c.Endpoints.AbstractEndpoint.apply(this,arguments);var y=this;i=i||{},i.width=i.width||55,i.height=i.height||55,this.width=i.width,this.height=i.height,this._compute=function(D,M,C,g){var e=C.width||y.width,k=C.height||y.height,L=D[0]-e/2,U=D[1]-k/2;return[L,U,e,k]}};var h=c.Overlays.AbstractOverlay=function(i){this.visible=!0,this.isAppendedAtTopLevel=!0,this.component=i.component,this.loc=i.location==null?.5:i.location,this.endpointLoc=i.endpointLocation==null?[.5,.5]:i.endpointLocation,this.visible=i.visible!==!1};h.prototype={cleanup:function(i){i&&(this.component=null,this.canvas=null,this.endpointLoc=null)},reattach:function(i,y){},setVisible:function(i){this.visible=i,this.component.repaint()},isVisible:function(){return this.visible},hide:function(){this.setVisible(!1)},show:function(){this.setVisible(!0)},incrementLocation:function(i){this.loc+=i,this.component.repaint()},setLocation:function(i){this.loc=i,this.component.repaint()},getLocation:function(){return this.loc},updateFrom:function(){}},c.Overlays.Arrow=function(i){this.type="Arrow",h.apply(this,arguments),this.isAppendedAtTopLevel=!1,i=i||{};var y=this;this.length=i.length||20,this.width=i.width||20,this.id=i.id,this.direction=(i.direction||1)<0?-1:1;var D=i.paintStyle||{},M=i.foldback||.623;this.computeMaxSize=function(){return y.width*1.5},this.elementCreated=function(C,g){if(this.path=C,i.events)for(var e in i.events)c.on(C,e,i.events[e])},this.draw=function(C,g){var e,k,L,U,G;if(C.pointAlongPathFrom){if(V.isString(this.loc)||this.loc>1||this.loc<0){var P=parseInt(this.loc,10),w=this.loc<0?1:0;e=C.pointAlongPathFrom(w,P,!1),k=C.pointAlongPathFrom(w,P-this.direction*this.length/2,!1),L=it.pointOnLine(e,k,this.length)}else if(this.loc===1){if(e=C.pointOnPath(this.loc),k=C.pointAlongPathFrom(this.loc,-this.length),L=it.pointOnLine(e,k,this.length),this.direction===-1){var r=L;L=e,e=r}}else if(this.loc===0){if(L=C.pointOnPath(this.loc),k=C.pointAlongPathFrom(this.loc,this.length),e=it.pointOnLine(L,k,this.length),this.direction===-1){var f=L;L=e,e=f}}else e=C.pointAlongPathFrom(this.loc,this.direction*this.length/2),k=C.pointOnPath(this.loc),L=it.pointOnLine(e,k,this.length);U=it.perpendicularLineTo(e,L,this.width),G=it.pointOnLine(e,L,M*this.length);var l={hxy:e,tail:U,cxy:G},_=D.stroke||g.stroke,x=D.fill||g.stroke,u=D.strokeWidth||g.strokeWidth;return{component:C,d:l,"stroke-width":u,stroke:_,fill:x,minX:Math.min(e.x,U[0].x,U[1].x),maxX:Math.max(e.x,U[0].x,U[1].x),minY:Math.min(e.y,U[0].y,U[1].y),maxY:Math.max(e.y,U[0].y,U[1].y)}}else return{component:C,minX:0,maxX:0,minY:0,maxY:0}}},V.extend(c.Overlays.Arrow,h,{updateFrom:function(i){this.length=i.length||this.length,this.width=i.width||this.width,this.direction=i.direction!=null?i.direction:this.direction,this.foldback=i.foldback||this.foldback},cleanup:function(){this.path&&this.path.parentNode&&this.path.parentNode.removeChild(this.path)}}),c.Overlays.PlainArrow=function(i){i=i||{};var y=c.extend(i,{foldback:1});c.Overlays.Arrow.call(this,y),this.type="PlainArrow"},V.extend(c.Overlays.PlainArrow,c.Overlays.Arrow),c.Overlays.Diamond=function(i){i=i||{};var y=i.length||40,D=c.extend(i,{length:y/2,foldback:2});c.Overlays.Arrow.call(this,D),this.type="Diamond"},V.extend(c.Overlays.Diamond,c.Overlays.Arrow);var T=function(i,y){return(i._jsPlumb.cachedDimensions==null||y)&&(i._jsPlumb.cachedDimensions=i.getDimensions()),i._jsPlumb.cachedDimensions},a=function(i){c.jsPlumbUIComponent.apply(this,arguments),h.apply(this,arguments);var y=this.fire;this.fire=function(){y.apply(this,arguments),this.component&&this.component.fire.apply(this.component,arguments)},this.detached=!1,this.id=i.id,this._jsPlumb.div=null,this._jsPlumb.initialised=!1,this._jsPlumb.component=i.component,this._jsPlumb.cachedDimensions=null,this._jsPlumb.create=i.create,this._jsPlumb.initiallyInvisible=i.visible===!1,this.getElement=function(){if(this._jsPlumb.div==null){var D=this._jsPlumb.div=c.getElement(this._jsPlumb.create(this._jsPlumb.component));D.style.position="absolute",jsPlumb.addClass(D,this._jsPlumb.instance.overlayClass+" "+(this.cssClass?this.cssClass:i.cssClass?i.cssClass:"")),this._jsPlumb.instance.appendElement(D),this._jsPlumb.instance.getId(D),this.canvas=D;var M="translate(-50%, -50%)";D.style.webkitTransform=M,D.style.mozTransform=M,D.style.msTransform=M,D.style.oTransform=M,D.style.transform=M,D._jsPlumb=this,i.visible===!1&&(D.style.display="none")}return this._jsPlumb.div},this.draw=function(D,M,C){var g=T(this);if(g!=null&&g.length===2){var e={x:0,y:0};if(C)e={x:C[0],y:C[1]};else if(D.pointOnPath){var k=this.loc,L=!1;(V.isString(this.loc)||this.loc<0||this.loc>1)&&(k=parseInt(this.loc,10),L=!0),e=D.pointOnPath(k,L)}else{var U=this.loc.constructor===Array?this.loc:this.endpointLoc;e={x:U[0]*D.w,y:U[1]*D.h}}var G=e.x-g[0]/2,P=e.y-g[1]/2;return{component:D,d:{minx:G,miny:P,td:g,cxy:e},minX:G,maxX:G+g[0],minY:P,maxY:P+g[1]}}else return{minX:0,maxX:0,minY:0,maxY:0}}};V.extend(a,[c.jsPlumbUIComponent,h],{getDimensions:function(){return[1,1]},setVisible:function(i){this._jsPlumb.div&&(this._jsPlumb.div.style.display=i?"block":"none",i&&this._jsPlumb.initiallyInvisible&&(T(this,!0),this.component.repaint(),this._jsPlumb.initiallyInvisible=!1))},clearCachedDimensions:function(){this._jsPlumb.cachedDimensions=null},cleanup:function(i){i?this._jsPlumb.div!=null&&(this._jsPlumb.div._jsPlumb=null,this._jsPlumb.instance.removeElement(this._jsPlumb.div)):(this._jsPlumb&&this._jsPlumb.div&&this._jsPlumb.div.parentNode&&this._jsPlumb.div.parentNode.removeChild(this._jsPlumb.div),this.detached=!0)},reattach:function(i,y){this._jsPlumb.div!=null&&i.getContainer().appendChild(this._jsPlumb.div),this.detached=!1},computeMaxSize:function(){var i=T(this);return Math.max(i[0],i[1])},paint:function(i,y){this._jsPlumb.initialised||(this.getElement(),i.component.appendDisplayElement(this._jsPlumb.div),this._jsPlumb.initialised=!0,this.detached&&this._jsPlumb.div.parentNode.removeChild(this._jsPlumb.div)),this._jsPlumb.div.style.left=i.component.x+i.d.minx+"px",this._jsPlumb.div.style.top=i.component.y+i.d.miny+"px"}}),c.Overlays.Custom=function(i){this.type="Custom",a.apply(this,arguments)},V.extend(c.Overlays.Custom,a),c.Overlays.GuideLines=function(){var i=this;i.length=50,i.strokeWidth=5,this.type="GuideLines",h.apply(this,arguments),c.jsPlumbUIComponent.apply(this,arguments),this.draw=function(y,D){var M=y.pointAlongPathFrom(i.loc,i.length/2),C=y.pointOnPath(i.loc),g=it.pointOnLine(M,C,i.length),e=it.perpendicularLineTo(M,g,40),k=it.perpendicularLineTo(g,M,20);return{connector:y,head:M,tail:g,headLine:k,tailLine:e,minX:Math.min(M.x,g.x,k[0].x,k[1].x),minY:Math.min(M.y,g.y,k[0].y,k[1].y),maxX:Math.max(M.x,g.x,k[0].x,k[1].x),maxY:Math.max(M.y,g.y,k[0].y,k[1].y)}}},c.Overlays.Label=function(i){this.labelStyle=i.labelStyle,this.cssClass=this.labelStyle!=null?this.labelStyle.cssClass:null;var y=c.extend({create:function(){return c.createElement("div")}},i);if(c.Overlays.Custom.call(this,y),this.type="Label",this.label=i.label||"",this.labelText=null,this.labelStyle){var D=this.getElement();if(this.labelStyle.font=this.labelStyle.font||"12px sans-serif",D.style.font=this.labelStyle.font,D.style.color=this.labelStyle.color||"black",this.labelStyle.fill&&(D.style.background=this.labelStyle.fill),this.labelStyle.borderWidth>0){var M=this.labelStyle.borderStyle?this.labelStyle.borderStyle:"black";D.style.border=this.labelStyle.borderWidth+"px solid "+M}this.labelStyle.padding&&(D.style.padding=this.labelStyle.padding)}},V.extend(c.Overlays.Label,c.Overlays.Custom,{cleanup:function(i){i&&(this.div=null,this.label=null,this.labelText=null,this.cssClass=null,this.labelStyle=null)},getLabel:function(){return this.label},setLabel:function(i){this.label=i,this.labelText=null,this.clearCachedDimensions(),this.update(),this.component.repaint()},getDimensions:function(){return this.update(),a.prototype.getDimensions.apply(this,arguments)},update:function(){if(typeof this.label=="function"){var i=this.label(this);this.getElement().innerHTML=i.replace(/\r\n/g,"<br/>")}else this.labelText==null&&(this.labelText=this.label,this.getElement().innerHTML=this.labelText.replace(/\r\n/g,"<br/>"))},updateFrom:function(i){i.label!=null&&this.setLabel(i.label)}})}).call(typeof window<"u"?window:jt),(function(){var J=this,c=J.jsPlumbUtil,V=J.jsPlumbInstance,it="jtk-group-collapsed",K="jtk-group-expanded",et="[jtk-group-content]",h="elementDraggable",T="stop",a="revert",i="_groupManager",y="_jsPlumbGroup",D="_jsPlumbGroupDrag",M="group:addMember",C="group:removeMember",g="group:add",e="group:remove",k="group:expand",L="group:collapse",U="groupDragStop",G="connectionMoved",P="internal.connectionDetached",w="removeAll",r="orphanAll",f="show",l="hide",_=function(u){var j={},b={},A={},d=this;function E(t,p){for(var v=u.getContainer();;){if(t==null||t===v)return!1;if(t===p)return!0;t=t.parentNode}}u.bind("connection",function(t){var p=u.getGroupFor(t.source),v=u.getGroupFor(t.target);p!=null&&v!=null&&p===v?(b[t.connection.id]=p,A[t.connection.id]=p):(p!=null&&(c.suggest(p.connections.source,t.connection),b[t.connection.id]=p),v!=null&&(c.suggest(v.connections.target,t.connection),A[t.connection.id]=v))});function N(t){delete t.proxies;var p=b[t.id],v;p!=null&&(v=function(I){return I.id===t.id},c.removeWithFunction(p.connections.source,v),c.removeWithFunction(p.connections.target,v),delete b[t.id]),p=A[t.id],p!=null&&(v=function(I){return I.id===t.id},c.removeWithFunction(p.connections.source,v),c.removeWithFunction(p.connections.target,v),delete A[t.id])}u.bind(P,function(t){N(t.connection)}),u.bind(G,function(t){var p=t.index===0?b:A,v=p[t.connection.id];if(v){var I=v.connections[t.index===0?"source":"target"],F=I.indexOf(t.connection);F!==-1&&I.splice(F,1)}}),this.addGroup=function(t){u.addClass(t.getEl(),K),j[t.id]=t,t.manager=this,o(t),u.fire(g,{group:t})},this.addToGroup=function(t,p,v){if(t=this.getGroup(t),t){var I=t.getEl();if(p._isJsPlumbGroup)return;var F=p._jsPlumbGroup;if(F!==t){u.removeFromDragSelection(p);var X=u.getOffset(p,!0),R=t.collapsed?u.getOffset(I,!0):u.getOffset(t.getDragArea(),!0);F!=null&&(F.remove(p,!1,v,!1,t),d.updateConnectionsForGroup(F)),t.add(p,v);var W=function(rt,gt){var dt=gt===0?1:0;rt.each(function(lt){lt.setVisible(!1),lt.endpoints[dt].element._jsPlumbGroup===t?(lt.endpoints[dt].setVisible(!1),n(lt,dt,t)):(lt.endpoints[gt].setVisible(!1),Y(lt,gt,t))})};t.collapsed&&(W(u.select({source:p}),0),W(u.select({target:p}),1));var at=u.getId(p);u.dragManager.setParent(p,at,I,u.getId(I),X);var st={left:X.left-R.left,top:X.top-R.top};if(u.setPosition(p,st),u.dragManager.revalidateParent(p,at,X),d.updateConnectionsForGroup(t),u.revalidate(at),!v){var nt={group:t,el:p,pos:st};F&&(nt.sourceGroup=F),u.fire(M,nt)}}}},this.removeFromGroup=function(t,p,v){if(t=this.getGroup(t),t){if(t.collapsed){var I=function(F,X){for(var R=0;R<F.length;R++){var W=F[R];if(W.proxies){for(var at=0;at<W.proxies.length;at++)if(W.proxies[at]!=null){var st=W.proxies[at].originalEp.element;(st===p||E(st,p))&&n(W,X,t)}}}};I(t.connections.source.slice(),0),I(t.connections.target.slice(),1)}t.remove(p,null,v)}},this.getGroup=function(t){var p=t;if(c.isString(t)&&(p=j[t],p==null))throw new TypeError("No such group ["+t+"]");return p},this.getGroups=function(){var t=[];for(var p in j)t.push(j[p]);return t},this.removeGroup=function(t,p,v,I){t=this.getGroup(t),this.expandGroup(t,!0);var F=t[p?w:r](v,I);return u.remove(t.getEl()),delete j[t.id],delete u._groups[t.id],u.fire(e,{group:t}),F},this.removeAllGroups=function(t,p,v){for(var I in j)this.removeGroup(j[I],t,p,v)};function B(t,p){for(var v=t.getEl().querySelectorAll(".jtk-managed"),I=0;I<v.length;I++)u[p?f:l](v[I],!0)}var Y=function(t,p,v){var I=t.endpoints[p===0?1:0].element;if(!(I[y]&&!I[y].shouldProxy()&&I[y].collapsed)){var F=v.getEl(),X=u.getId(F);u.proxyConnection(t,p,F,X,function(R,W){return v.getEndpoint(R,W)},function(R,W){return v.getAnchor(R,W)})}};this.collapseGroup=function(t){if(t=this.getGroup(t),!(t==null||t.collapsed)){var p=t.getEl();if(B(t,!1),t.shouldProxy()){var v=function(I,F){for(var X=0;X<I.length;X++){var R=I[X];Y(R,F,t)}};v(t.connections.source,0),v(t.connections.target,1)}t.collapsed=!0,u.removeClass(p,K),u.addClass(p,it),u.revalidate(p),u.fire(L,{group:t})}};var n=function(t,p,v){u.unproxyConnection(t,p,u.getId(v.getEl()))};this.expandGroup=function(t,p){if(t=this.getGroup(t),!(t==null||!t.collapsed)){var v=t.getEl();if(B(t,!0),t.shouldProxy()){var I=function(F,X){for(var R=0;R<F.length;R++){var W=F[R];n(W,X,t)}};I(t.connections.source,0),I(t.connections.target,1)}t.collapsed=!1,u.addClass(v,K),u.removeClass(v,it),u.revalidate(v),this.repaintGroup(t),p||u.fire(k,{group:t})}},this.repaintGroup=function(t){t=this.getGroup(t);for(var p=t.getMembers(),v=0;v<p.length;v++)u.revalidate(p[v])};function o(t){for(var p=t.getMembers().slice(),v=[],I=0;I<p.length;I++)Array.prototype.push.apply(v,p[I].querySelectorAll(".jtk-managed"));Array.prototype.push.apply(p,v);var F=u.getConnections({source:p,scope:"*"},!0),X=u.getConnections({target:p,scope:"*"},!0),R={};t.connections.source.length=0,t.connections.target.length=0;var W=function(at){for(var st=0;st<at.length;st++)if(!R[at[st].id]){R[at[st].id]=!0;var nt=u.getGroupFor(at[st].source),rt=u.getGroupFor(at[st].target);nt===t?(rt!==t&&t.connections.source.push(at[st]),b[at[st].id]=t):rt===t&&(t.connections.target.push(at[st]),A[at[st].id]=t)}};W(F),W(X)}this.updateConnectionsForGroup=o,this.refreshAllGroups=function(){for(var t in j)o(j[t]),u.dragManager.updateOffsets(u.getId(j[t].getEl()))}},x=function(u,j){var b=this,A=j.el;this.getEl=function(){return A},this.id=j.id||c.uuid(),A._isJsPlumbGroup=!0;var d=this.getDragArea=function(){var rt=u.getSelector(A,et);return rt&&rt.length>0?rt[0]:A},E=j.ghost===!0,N=E||j.constrain===!0,B=j.revert!==!1,Y=j.orphan===!0,n=j.prune===!0,o=j.dropOverride===!0,t=j.proxied!==!1,p=[];if(this.connections={source:[],target:[],internal:[]},this.getAnchor=function(rt,gt){return j.anchor||"Continuous"},this.getEndpoint=function(rt,gt){return j.endpoint||["Dot",{radius:10}]},this.collapsed=!1,j.draggable!==!1){var v={drag:function(){for(var rt=0;rt<p.length;rt++)u.draw(p[rt])},stop:function(rt){u.fire(U,jsPlumb.extend(rt,{group:b}))},scope:D};j.dragOptions&&J.jsPlumb.extend(v,j.dragOptions),u.draggable(j.el,v)}j.droppable!==!1&&u.droppable(j.el,{drop:function(rt){var gt=rt.drag.el;if(!gt._isJsPlumbGroup){var dt=gt._jsPlumbGroup;if(dt!==b){if(dt!=null&&dt.overrideDrop(gt,b))return;u.getGroupManager().addToGroup(b,gt,!1)}}}});var I=function(rt,gt){for(var dt=rt.nodeType==null?rt:[rt],lt=0;lt<dt.length;lt++)gt(dt[lt])};this.overrideDrop=function(rt,gt){return o&&(B||n||Y)},this.add=function(rt,gt){var dt=d();I(rt,function(lt){if(lt._jsPlumbGroup!=null){if(lt._jsPlumbGroup===b)return;lt._jsPlumbGroup.remove(lt,!0,gt,!1)}lt._jsPlumbGroup=b,p.push(lt),u.isAlreadyDraggable(lt)&&nt(lt),lt.parentNode!==dt&&dt.appendChild(lt)}),u.getGroupManager().updateConnectionsForGroup(b)},this.remove=function(rt,gt,dt,lt,yt){I(rt,function(pt){if(pt._jsPlumbGroup===b){if(delete pt._jsPlumbGroup,c.removeWithFunction(p,function(q){return q===pt}),gt)try{b.getDragArea().removeChild(pt)}catch(q){jsPlumbUtil.log("Could not remove element from Group "+q)}if(st(pt),!dt){var Z={group:b,el:pt};yt&&(Z.targetGroup=yt),u.fire(C,Z)}}}),lt||u.getGroupManager().updateConnectionsForGroup(b)},this.removeAll=function(rt,gt){for(var dt=0,lt=p.length;dt<lt;dt++){var yt=p[0];b.remove(yt,rt,gt,!0),u.remove(yt,!0)}p.length=0,u.getGroupManager().updateConnectionsForGroup(b)},this.orphanAll=function(){for(var rt={},gt=0;gt<p.length;gt++){var dt=R(p[gt]);rt[dt[0]]=dt[1]}return p.length=0,rt},this.getMembers=function(){return p},A[y]=this,u.bind(h,(function(rt){rt.el._jsPlumbGroup===this&&nt(rt.el)}).bind(this));function F(rt){return rt.offsetParent}function X(rt,gt){var dt=F(rt),lt=u.getSize(dt),yt=u.getSize(rt),pt=gt[0],Z=pt+yt[0],q=gt[1],$=q+yt[1];return Z>0&&pt<lt[0]&&$>0&&q<lt[1]}function R(rt){var gt=u.getId(rt),dt=u.getOffset(rt);return rt.parentNode.removeChild(rt),u.getContainer().appendChild(rt),u.setPosition(rt,dt),st(rt),u.dragManager.clearParent(rt,gt),[gt,dt]}function W(rt){var gt=[];function dt(yt,pt,Z){var q=null;if(!X(yt,[pt,Z])){var $=yt._jsPlumbGroup;n?u.remove(yt):q=R(yt),$.remove(yt)}return q}for(var lt=0;lt<rt.selection.length;lt++)gt.push(dt(rt.selection[lt][0],rt.selection[lt][1].left,rt.selection[lt][1].top));return gt.length===1?gt[0]:gt}function at(rt){var gt=u.getId(rt);u.revalidate(rt),u.dragManager.revalidateParent(rt,gt)}function st(rt){rt._katavorioDrag&&((n||Y)&&rt._katavorioDrag.off(T,W),!n&&!Y&&B&&(rt._katavorioDrag.off(a,at),rt._katavorioDrag.setRevert(null)))}function nt(rt){rt._katavorioDrag&&((n||Y)&&rt._katavorioDrag.on(T,W),N&&rt._katavorioDrag.setConstrain(!0),E&&rt._katavorioDrag.setUseGhostProxy(!0),!n&&!Y&&B&&(rt._katavorioDrag.on(a,at),rt._katavorioDrag.setRevert(function(gt,dt){return!X(gt,dt)})))}this.shouldProxy=function(){return t},u.getGroupManager().addGroup(this)};V.prototype.addGroup=function(u){var j=this;if(j._groups=j._groups||{},j._groups[u.id]!=null)throw new TypeError("cannot create Group ["+u.id+"]; a Group with that ID exists");if(u.el[y]!=null)throw new TypeError("cannot create Group ["+u.id+"]; the given element is already a Group");var b=new x(j,u);return j._groups[b.id]=b,u.collapsed&&this.collapseGroup(b),b},V.prototype.addToGroup=function(u,j,b){var A=(function(E){var N=this.getId(E);this.manage(N,E),this.getGroupManager().addToGroup(u,E,b)}).bind(this);if(Array.isArray(j))for(var d=0;d<j.length;d++)A(j[d]);else A(j)},V.prototype.removeFromGroup=function(u,j,b){this.getGroupManager().removeFromGroup(u,j,b),this.getContainer().appendChild(j)},V.prototype.removeGroup=function(u,j,b,A){return this.getGroupManager().removeGroup(u,j,b,A)},V.prototype.removeAllGroups=function(u,j,b){this.getGroupManager().removeAllGroups(u,j,b)},V.prototype.getGroup=function(u){return this.getGroupManager().getGroup(u)},V.prototype.getGroups=function(){return this.getGroupManager().getGroups()},V.prototype.expandGroup=function(u){this.getGroupManager().expandGroup(u)},V.prototype.collapseGroup=function(u){this.getGroupManager().collapseGroup(u)},V.prototype.repaintGroup=function(u){this.getGroupManager().repaintGroup(u)},V.prototype.toggleGroup=function(u){u=this.getGroupManager().getGroup(u),u!=null&&this.getGroupManager()[u.collapsed?"expandGroup":"collapseGroup"](u)},V.prototype.getGroupManager=function(){var u=this[i];return u==null&&(u=this[i]=new _(this)),u},V.prototype.removeGroupManager=function(){delete this[i]},V.prototype.getGroupFor=function(u){if(u=this.getElement(u),u){for(var j=this.getContainer(),b=!1,A=null;!b;)u==null||u===j?b=!0:u[y]?(A=u[y],b=!0):u=u.parentNode;return A}}}).call(typeof window<"u"?window:jt),(function(){var J=this,c=J.jsPlumb,V=J.jsPlumbUtil,it="Straight",K="Arc",et=function(h){this.type="Flowchart",h=h||{},h.stub=h.stub==null?30:h.stub;var T,a=c.Connectors.AbstractConnector.apply(this,arguments),i=h.midpoint==null||isNaN(h.midpoint)?.5:h.midpoint,y=h.alwaysRespectStubs===!0,D=null,M=null,C=h.cornerRadius!=null?h.cornerRadius:0;h.loopbackRadius;var g=function(P){return P<0?-1:P===0?0:1},e=function(P){return[g(P[2]-P[0]),g(P[3]-P[1])]},k=function(P,w,r,f){if(!(D===w&&M===r)){var l=D??f.sx,_=M??f.sy,x=l===w?"v":"h";D=w,M=r,P.push([l,_,w,r,x])}},L=function(P){return Math.sqrt(Math.pow(P[0]-P[2],2)+Math.pow(P[1]-P[3],2))},U=function(P){var w=[];return w.push.apply(w,P),w},G=function(P,w,r){for(var f=null,l,_,x,u=0;u<w.length-1;u++){if(f=f||U(w[u]),l=U(w[u+1]),_=e(f),x=e(l),C>0&&f[4]!==l[4]){var j=Math.min(L(f),L(l)),b=Math.min(C,j/2);f[2]-=_[0]*b,f[3]-=_[1]*b,l[0]+=x[0]*b,l[1]+=x[1]*b;var A=_[1]===x[0]&&x[0]===1||_[1]===x[0]&&x[0]===0&&_[0]!==x[1]||_[1]===x[0]&&x[0]===-1,d=l[1]>f[3]?1:-1,E=l[0]>f[2]?1:-1,N=d===E,B=N&&A||!N&&!A?l[0]:f[2],Y=N&&A||!N&&!A?f[3]:l[1];a.addSegment(P,it,{x1:f[0],y1:f[1],x2:f[2],y2:f[3]}),a.addSegment(P,K,{r:b,x1:f[2],y1:f[3],x2:l[0],y2:l[1],cx:B,cy:Y,ac:A})}else{var n=f[2]===f[0]?0:f[2]>f[0]?r.lw/2:-(r.lw/2),o=f[3]===f[1]?0:f[3]>f[1]?r.lw/2:-(r.lw/2);a.addSegment(P,it,{x1:f[0]-n,y1:f[1]-o,x2:f[2]+n,y2:f[3]+o})}f=l}l!=null&&a.addSegment(P,it,{x1:l[0],y1:l[1],x2:l[2],y2:l[3]})};this.midpoint=i,this._compute=function(P,w){T=[],D=null,M=null;var r=function(){return[P.startStubX,P.startStubY,P.endStubX,P.endStubY]},f={perpendicular:r,orthogonal:r,opposite:function(o){var t=P,p=o==="x"?0:1,v={x:function(){return t.so[p]===1&&(t.startStubX>t.endStubX&&t.tx>t.startStubX||t.sx>t.endStubX&&t.tx>t.sx)||t.so[p]===-1&&(t.startStubX<t.endStubX&&t.tx<t.startStubX||t.sx<t.endStubX&&t.tx<t.sx)},y:function(){return t.so[p]===1&&(t.startStubY>t.endStubY&&t.ty>t.startStubY||t.sy>t.endStubY&&t.ty>t.sy)||t.so[p]===-1&&(t.startStubY<t.endStubY&&t.ty<t.startStubY||t.sy<t.endStubY&&t.ty<t.sy)}};return!y&&v[o]()?{x:[(P.sx+P.tx)/2,P.startStubY,(P.sx+P.tx)/2,P.endStubY],y:[P.startStubX,(P.sy+P.ty)/2,P.endStubX,(P.sy+P.ty)/2]}[o]:[P.startStubX,P.startStubY,P.endStubX,P.endStubY]}},l=f[P.anchorOrientation](P.sourceAxis),_=P.sourceAxis==="x"?0:1,x=P.sourceAxis==="x"?1:0,u=l[_],j=l[x],b=l[_+2],A=l[x+2];k(T,l[0],l[1],P);var d=P.startStubX+(P.endStubX-P.startStubX)*i,E=P.startStubY+(P.endStubY-P.startStubY)*i,N={x:[0,1],y:[1,0]},B={perpendicular:function(o){var t=P,p={x:[[[1,2,3,4],null,[2,1,4,3]],null,[[4,3,2,1],null,[3,4,1,2]]],y:[[[3,2,1,4],null,[2,3,4,1]],null,[[4,1,2,3],null,[1,4,3,2]]]},v={x:[[t.startStubX,t.endStubX],null,[t.endStubX,t.startStubX]],y:[[t.startStubY,t.endStubY],null,[t.endStubY,t.startStubY]]},I={x:[[d,t.startStubY],[d,t.endStubY]],y:[[t.startStubX,E],[t.endStubX,E]]},F={x:[[t.endStubX,t.startStubY]],y:[[t.startStubX,t.endStubY]]},X={x:[[t.startStubX,t.endStubY],[t.endStubX,t.endStubY]],y:[[t.endStubX,t.startStubY],[t.endStubX,t.endStubY]]},R={x:[[t.startStubX,E],[t.endStubX,E],[t.endStubX,t.endStubY]],y:[[d,t.startStubY],[d,t.endStubY],[t.endStubX,t.endStubY]]},W={x:[t.startStubY,t.endStubY],y:[t.startStubX,t.endStubX]},at=N[o][0],st=N[o][1],nt=t.so[at]+1,rt=t.to[st]+1,gt=t.to[st]===-1&&W[o][1]<W[o][0]||t.to[st]===1&&W[o][1]>W[o][0],dt=v[o][nt][0],lt=v[o][nt][1],yt=p[o][nt][rt];if(t.segment===yt[3]||t.segment===yt[2]&&gt)return I[o];if(t.segment===yt[2]&&lt<dt)return F[o];if(t.segment===yt[2]&&lt>=dt||t.segment===yt[1]&&!gt)return R[o];if(t.segment===yt[0]||t.segment===yt[1]&&gt)return X[o]},orthogonal:function(o,t,p,v,I){var F=P,X={x:F.so[0]===-1?Math.min(t,v):Math.max(t,v),y:F.so[1]===-1?Math.min(t,v):Math.max(t,v)}[o];return{x:[[X,p],[X,I],[v,I]],y:[[p,X],[I,X],[I,v]]}[o]},opposite:function(o,t,p,v){var I=P,F={x:"y",y:"x"}[o],X={x:"height",y:"width"}[o],R=I["is"+o.toUpperCase()+"GreaterThanStubTimes2"];if(w.sourceEndpoint.elementId===w.targetEndpoint.elementId){var W=p+(1-w.sourceEndpoint.anchor[F])*w.sourceInfo[X]+a.maxStub;return{x:[[t,W],[v,W]],y:[[W,t],[W,v]]}[o]}else{if(!R||I.so[_]===1&&t>v||I.so[_]===-1&&t<v)return{x:[[t,E],[v,E]],y:[[d,t],[d,v]]}[o];if(I.so[_]===1&&t<v||I.so[_]===-1&&t>v)return{x:[[d,I.sy],[d,I.ty]],y:[[I.sx,E],[I.tx,E]]}[o]}}},Y=B[P.anchorOrientation](P.sourceAxis,u,j,b,A);if(Y)for(var n=0;n<Y.length;n++)k(T,Y[n][0],Y[n][1],P);k(T,l[2],l[3],P),k(T,P.tx,P.ty,P),G(this,T,P)}};c.Connectors.Flowchart=et,V.extend(c.Connectors.Flowchart,c.Connectors.AbstractConnector)}).call(typeof window<"u"?window:jt),(function(){var J=this,c=J.jsPlumb,V=J.jsPlumbUtil;c.Connectors.AbstractBezierConnector=function(K){K=K||{};var et=K.showLoopback!==!1;K.curviness;var h=K.margin||5;K.proximityLimit;var T=K.orientation&&K.orientation==="clockwise",a=K.loopbackRadius||25,i;return this._compute=function(y,D){var M=D.sourcePos,C=D.targetPos,g=Math.abs(M[0]-C[0]),e=Math.abs(M[1]-C[1]);if(!et||D.sourceEndpoint.elementId!==D.targetEndpoint.elementId)this._computeBezier(y,D,M,C,g,e);else{var k=D.sourcePos[0],L=D.sourcePos[1]-h,U=k,G=L-a,P=U-a,w=G-a;g=2*a,e=2*a,y.points[0]=P,y.points[1]=w,y.points[2]=g,y.points[3]=e,i.addSegment(this,"Arc",{loopback:!0,x1:k-P+4,y1:L-w,startAngle:0,endAngle:2*Math.PI,r:a,ac:!T,x2:k-P-4,y2:L-w,cx:U-P,cy:G-w})}},i=c.Connectors.AbstractConnector.apply(this,arguments),i},V.extend(c.Connectors.AbstractBezierConnector,c.Connectors.AbstractConnector);var it=function(K){K=K||{},this.type="Bezier";var et=c.Connectors.AbstractBezierConnector.apply(this,arguments),h=K.curviness||150,T=10;this.getCurviness=function(){return h},this._findControlPoint=function(a,i,y,D,M,C,g){var e=C[0]!==g[0]||C[1]===g[1],k=[];return e?(g[0]===0?k.push(y[0]<i[0]?a[0]+T:a[0]-T):k.push(a[0]+h*g[0]),g[1]===0?k.push(y[1]<i[1]?a[1]+T:a[1]-T):k.push(a[1]+h*C[1])):(C[0]===0?k.push(i[0]<y[0]?a[0]+T:a[0]-T):k.push(a[0]-h*C[0]),C[1]===0?k.push(i[1]<y[1]?a[1]+T:a[1]-T):k.push(a[1]+h*g[1])),k},this._computeBezier=function(a,i,y,D,M,C){var g,e,k=y[0]<D[0]?M:0,L=y[1]<D[1]?C:0,U=y[0]<D[0]?0:M,G=y[1]<D[1]?0:C;g=this._findControlPoint([k,L],y,D,i.sourceEndpoint,i.targetEndpoint,a.so,a.to),e=this._findControlPoint([U,G],D,y,i.targetEndpoint,i.sourceEndpoint,a.to,a.so),et.addSegment(this,"Bezier",{x1:k,y1:L,x2:U,y2:G,cp1x:g[0],cp1y:g[1],cp2x:e[0],cp2y:e[1]})}};c.Connectors.Bezier=it,V.extend(it,c.Connectors.AbstractBezierConnector)}).call(typeof window<"u"?window:jt),(function(){var J=this,c=J.jsPlumb,V=J.jsPlumbUtil,it=function(h,T,a,i){return h<=a&&i<=T?1:h<=a&&T<=i?2:a<=h&&i>=T?3:4},K=function(h,T,a,i,y,D,M,C,g){if(C<=g)return[h,T];if(a===1)return i[3]<=0&&y[3]>=1?[h+(i[2]<.5?-1*D:D),T]:i[2]>=1&&y[2]<=0?[h,T+(i[3]<.5?-1*M:M)]:[h+-1*D,T+-1*M];if(a===2)return i[3]>=1&&y[3]<=0?[h+(i[2]<.5?-1*D:D),T]:i[2]>=1&&y[2]<=0?[h,T+(i[3]<.5?-1*M:M)]:[h+D,T+-1*M];if(a===3)return i[3]>=1&&y[3]<=0?[h+(i[2]<.5?-1*D:D),T]:i[2]<=0&&y[2]>=1?[h,T+(i[3]<.5?-1*M:M)]:[h+-1*D,T+-1*M];if(a===4)return i[3]<=0&&y[3]>=1?[h+(i[2]<.5?-1*D:D),T]:i[2]<=0&&y[2]>=1?[h,T+(i[3]<.5?-1*M:M)]:[h+D,T+-1*M]},et=function(h){h=h||{},this.type="StateMachine";var T=c.Connectors.AbstractBezierConnector.apply(this,arguments),a=h.curviness||10,i=h.margin||5,y=h.proximityLimit||80;h.orientation&&h.orientation;var D;this._computeBezier=function(M,C,g,e,k,L){var U=C.sourcePos[0]<C.targetPos[0]?0:k,G=C.sourcePos[1]<C.targetPos[1]?0:L,P=C.sourcePos[0]<C.targetPos[0]?k:0,w=C.sourcePos[1]<C.targetPos[1]?L:0;C.sourcePos[2]===0&&(U-=i),C.sourcePos[2]===1&&(U+=i),C.sourcePos[3]===0&&(G-=i),C.sourcePos[3]===1&&(G+=i),C.targetPos[2]===0&&(P-=i),C.targetPos[2]===1&&(P+=i),C.targetPos[3]===0&&(w-=i),C.targetPos[3]===1&&(w+=i);var r=(U+P)/2,f=(G+w)/2,l=it(U,G,P,w),_=Math.sqrt(Math.pow(P-U,2)+Math.pow(w-G,2)),x,u,j,b;D=K(r,f,l,C.sourcePos,C.targetPos,a,a,_,y),x=D[0],u=D[0],j=D[1],b=D[1],T.addSegment(this,"Bezier",{x1:P,y1:w,x2:U,y2:G,cp1x:x,cp1y:j,cp2x:u,cp2y:b})}};c.Connectors.StateMachine=et,V.extend(et,c.Connectors.AbstractBezierConnector)}).call(typeof window<"u"?window:jt),(function(){var J=this,c=J.jsPlumb,V=J.jsPlumbUtil,it="Straight",K=function(et){this.type=it;var h=c.Connectors.AbstractConnector.apply(this,arguments);this._compute=function(T,a){h.addSegment(this,it,{x1:T.sx,y1:T.sy,x2:T.startStubX,y2:T.startStubY}),h.addSegment(this,it,{x1:T.startStubX,y1:T.startStubY,x2:T.endStubX,y2:T.endStubY}),h.addSegment(this,it,{x1:T.endStubX,y1:T.endStubY,x2:T.tx,y2:T.ty})}};c.Connectors.Straight=K,V.extend(K,c.Connectors.AbstractConnector)}).call(typeof window<"u"?window:jt),(function(){var J=this,c=J.jsPlumb,V=J.jsPlumbUtil,it={"stroke-linejoin":"stroke-linejoin","stroke-dashoffset":"stroke-dashoffset","stroke-linecap":"stroke-linecap"},K="stroke-dasharray",et="dashstyle",h="linearGradient",T="radialGradient",a="defs",i="fill",y="stop",D="stroke",M="stroke-width",C="style",g="none",e="jsplumb_gradient_",k="strokeWidth",L={svg:"http://www.w3.org/2000/svg"},U=function(b,A){for(var d in A)b.setAttribute(d,""+A[d])},G=function(b,A){return A=A||{},A.version="1.1",A.xmlns=L.svg,c.createElementNS(L.svg,b,null,null,A)},P=function(b){return"position:absolute;left:"+b[0]+"px;top:"+b[1]+"px"},w=function(b){for(var A=b.querySelectorAll(" defs,linearGradient,radialGradient"),d=0;d<A.length;d++)A[d].parentNode.removeChild(A[d])},r=function(b,A,d,E,N){var B=e+N._jsPlumb.instance.idstamp();w(b);var Y;d.gradient.offset?Y=G(T,{id:B}):Y=G(h,{id:B,gradientUnits:"userSpaceOnUse"});var n=G(a);b.appendChild(n),n.appendChild(Y);for(var o=0;o<d.gradient.stops.length;o++){var t=N.segment===1||N.segment===2?o:d.gradient.stops.length-1-o,p=d.gradient.stops[t][1],v=G(y,{offset:Math.floor(d.gradient.stops[o][0]*100)+"%","stop-color":p});Y.appendChild(v)}var I=d.stroke?D:i;A.setAttribute(I,"url(#"+B+")")},f=function(b,A,d,E,N){if(A.setAttribute(i,d.fill?d.fill:g),A.setAttribute(D,d.stroke?d.stroke:g),d.gradient?r(b,A,d,E,N):(w(b),A.setAttribute(C,"")),d.strokeWidth&&A.setAttribute(M,d.strokeWidth),d[et]&&d[k]&&!d[K]){var B=d[et].indexOf(",")===-1?" ":",",Y=d[et].split(B),n="";Y.forEach(function(t){n+=Math.floor(t*d.strokeWidth)+B}),A.setAttribute(K,n)}else d[K]&&A.setAttribute(K,d[K]);for(var o in it)d[o]&&A.setAttribute(it[o],d[o])},l=function(b,A,d){b.childNodes.length>d?b.insertBefore(A,b.childNodes[d]):b.appendChild(A)};V.svg={node:G,attr:U,pos:P};var _=function(b){var A=b.pointerEventsSpec||"all",d={};c.jsPlumbUIComponent.apply(this,b.originalArgs),this.canvas=null,this.path=null,this.svg=null,this.bgCanvas=null;var E=b.cssClass+" "+(b.originalArgs[0].cssClass||""),N={style:"",width:0,height:0,"pointer-events":A,position:"absolute"};this.svg=G("svg",N),b.useDivWrapper?(this.canvas=c.createElement("div",{position:"absolute"}),V.sizeElement(this.canvas,0,0,1,1),this.canvas.className=E):(U(this.svg,{class:E}),this.canvas=this.svg),b._jsPlumb.appendElement(this.canvas,b.originalArgs[0].parent),b.useDivWrapper&&this.canvas.appendChild(this.svg);var B=[this.canvas];return this.getDisplayElements=function(){return B},this.appendDisplayElement=function(Y){B.push(Y)},this.paint=function(Y,n,o){if(Y!=null){var t=[this.x,this.y],p=[this.w,this.h],v;o!=null&&(o.xmin<0&&(t[0]+=o.xmin),o.ymin<0&&(t[1]+=o.ymin),p[0]=o.xmax+(o.xmin<0?-o.xmin:0),p[1]=o.ymax+(o.ymin<0?-o.ymin:0)),b.useDivWrapper?(V.sizeElement(this.canvas,t[0],t[1],p[0]>0?p[0]:1,p[1]>0?p[1]:1),t[0]=0,t[1]=0,v=P([0,0])):v=P([t[0],t[1]]),d.paint.apply(this,arguments),U(this.svg,{style:v,width:p[0]||1,height:p[1]||1})}},{renderer:d}};V.extend(_,c.jsPlumbUIComponent,{cleanup:function(b){b||this.typeId==null?(this.canvas&&(this.canvas._jsPlumb=null),this.svg&&(this.svg._jsPlumb=null),this.bgCanvas&&(this.bgCanvas._jsPlumb=null),this.canvas&&this.canvas.parentNode&&this.canvas.parentNode.removeChild(this.canvas),this.bgCanvas&&this.bgCanvas.parentNode&&this.canvas.parentNode.removeChild(this.canvas),this.svg=null,this.canvas=null,this.path=null,this.group=null,this._jsPlumb=null):(this.canvas&&this.canvas.parentNode&&this.canvas.parentNode.removeChild(this.canvas),this.bgCanvas&&this.bgCanvas.parentNode&&this.bgCanvas.parentNode.removeChild(this.bgCanvas))},reattach:function(b){var A=b.getContainer();this.canvas&&this.canvas.parentNode==null&&A.appendChild(this.canvas),this.bgCanvas&&this.bgCanvas.parentNode==null&&A.appendChild(this.bgCanvas)},setVisible:function(b){this.canvas&&(this.canvas.style.display=b?"block":"none")}}),c.ConnectorRenderers.svg=function(b){var A=this,d=_.apply(this,[{cssClass:b._jsPlumb.connectorClass,originalArgs:arguments,pointerEventsSpec:"none",_jsPlumb:b._jsPlumb}]);d.renderer.paint=function(E,N,B){var Y=A.getSegments(),n="",o=[0,0];if(B.xmin<0&&(o[0]=-B.xmin),B.ymin<0&&(o[1]=-B.ymin),Y.length>0){n=A.getPathData();var t={d:n,transform:"translate("+o[0]+","+o[1]+")","pointer-events":b["pointer-events"]||"visibleStroke"},p=null,v=[A.x,A.y,A.w,A.h];if(E.outlineStroke){var I=E.outlineWidth||1,F=E.strokeWidth+2*I;p=c.extend({},E),delete p.gradient,p.stroke=E.outlineStroke,p.strokeWidth=F,A.bgPath==null?(A.bgPath=G("path",t),c.addClass(A.bgPath,c.connectorOutlineClass),l(A.svg,A.bgPath,0)):U(A.bgPath,t),f(A.svg,A.bgPath,p,v,A)}A.path==null?(A.path=G("path",t),l(A.svg,A.path,E.outlineStroke?1:0)):U(A.path,t),f(A.svg,A.path,E,v,A)}}},V.extend(c.ConnectorRenderers.svg,_);var x=c.SvgEndpoint=function(b){var A=_.apply(this,[{cssClass:b._jsPlumb.endpointClass,originalArgs:arguments,pointerEventsSpec:"all",useDivWrapper:!0,_jsPlumb:b._jsPlumb}]);A.renderer.paint=(function(d){var E=c.extend({},d);E.outlineStroke&&(E.stroke=E.outlineStroke),this.node==null?(this.node=this.makeNode(E),this.svg.appendChild(this.node)):this.updateNode!=null&&this.updateNode(this.node),f(this.svg,this.node,E,[this.x,this.y,this.w,this.h],this),P(this.node,[this.x,this.y])}).bind(this)};V.extend(x,_),c.Endpoints.svg.Dot=function(){c.Endpoints.Dot.apply(this,arguments),x.apply(this,arguments),this.makeNode=function(b){return G("circle",{cx:this.w/2,cy:this.h/2,r:this.radius})},this.updateNode=function(b){U(b,{cx:this.w/2,cy:this.h/2,r:this.radius})}},V.extend(c.Endpoints.svg.Dot,[c.Endpoints.Dot,x]),c.Endpoints.svg.Rectangle=function(){c.Endpoints.Rectangle.apply(this,arguments),x.apply(this,arguments),this.makeNode=function(b){return G("rect",{width:this.w,height:this.h})},this.updateNode=function(b){U(b,{width:this.w,height:this.h})}},V.extend(c.Endpoints.svg.Rectangle,[c.Endpoints.Rectangle,x]),c.Endpoints.svg.Image=c.Endpoints.Image,c.Endpoints.svg.Blank=c.Endpoints.Blank,c.Overlays.svg.Label=c.Overlays.Label,c.Overlays.svg.Custom=c.Overlays.Custom;var u=function(b,A){b.apply(this,A),c.jsPlumbUIComponent.apply(this,A),this.isAppendedAtTopLevel=!1,this.path=null,this.paint=function(E,N){if(E.component.svg&&N){this.path==null&&(this.path=G("path",{"pointer-events":"all"}),E.component.svg.appendChild(this.path),this.elementCreated&&this.elementCreated(this.path,E.component),this.canvas=E.component.svg);var B=A&&A.length===1&&A[0].cssClass||"",Y=[0,0];N.xmin<0&&(Y[0]=-N.xmin),N.ymin<0&&(Y[1]=-N.ymin),U(this.path,{d:d(E.d),class:B,stroke:E.stroke?E.stroke:null,fill:E.fill?E.fill:null,transform:"translate("+Y[0]+","+Y[1]+")"})}};var d=function(E){return isNaN(E.cxy.x)||isNaN(E.cxy.y)?"":"M"+E.hxy.x+","+E.hxy.y+" L"+E.tail[0].x+","+E.tail[0].y+" L"+E.cxy.x+","+E.cxy.y+" L"+E.tail[1].x+","+E.tail[1].y+" L"+E.hxy.x+","+E.hxy.y};this.transfer=function(E){E.canvas&&this.path&&this.path.parentNode&&(this.path.parentNode.removeChild(this.path),E.canvas.appendChild(this.path))}},j={cleanup:function(b){this.path!=null&&(b?this._jsPlumb.instance.removeElement(this.path):this.path.parentNode&&this.path.parentNode.removeChild(this.path))},reattach:function(b,A){this.path&&A.canvas&&A.canvas.appendChild(this.path)},setVisible:function(b){this.path!=null&&(this.path.style.display=b?"block":"none")}};V.extend(u,[c.jsPlumbUIComponent,c.Overlays.AbstractOverlay]),c.Overlays.svg.Arrow=function(){u.apply(this,[c.Overlays.Arrow,arguments])},V.extend(c.Overlays.svg.Arrow,[c.Overlays.Arrow,u],j),c.Overlays.svg.PlainArrow=function(){u.apply(this,[c.Overlays.PlainArrow,arguments])},V.extend(c.Overlays.svg.PlainArrow,[c.Overlays.PlainArrow,u],j),c.Overlays.svg.Diamond=function(){u.apply(this,[c.Overlays.Diamond,arguments])},V.extend(c.Overlays.svg.Diamond,[c.Overlays.Diamond,u],j),c.Overlays.svg.GuideLines=function(){var b=null,A=this,d,E;c.Overlays.GuideLines.apply(this,arguments),this.paint=function(B,Y){b==null&&(b=G("path"),B.connector.svg.appendChild(b),A.attachListeners(b,B.connector),A.attachListeners(b,A),d=G("path"),B.connector.svg.appendChild(d),A.attachListeners(d,B.connector),A.attachListeners(d,A),E=G("path"),B.connector.svg.appendChild(E),A.attachListeners(E,B.connector),A.attachListeners(E,A));var n=[0,0];Y.xmin<0&&(n[0]=-Y.xmin),Y.ymin<0&&(n[1]=-Y.ymin),U(b,{d:N(B.head,B.tail),stroke:"red",fill:null,transform:"translate("+n[0]+","+n[1]+")"}),U(d,{d:N(B.tailLine[0],B.tailLine[1]),stroke:"blue",fill:null,transform:"translate("+n[0]+","+n[1]+")"}),U(E,{d:N(B.headLine[0],B.headLine[1]),stroke:"green",fill:null,transform:"translate("+n[0]+","+n[1]+")"})};var N=function(B,Y){return"M "+B.x+","+B.y+" L"+Y.x+","+Y.y}},V.extend(c.Overlays.svg.GuideLines,c.Overlays.GuideLines)}).call(typeof window<"u"?window:jt),(function(){var J=this,c=J.jsPlumb,V=J.jsPlumbUtil,it=J.Katavorio,K=J.Biltong,et=function(r){var f=r._mottle;return f||(f=r._mottle=new J.Mottle),f},h=function(r,f){f=f||"main";var l="_katavorio_"+f,_=r[l],x=r.getEventManager();return _||(_=new it({bind:x.on,unbind:x.off,getSize:c.getSize,getConstrainingRectangle:function(u){return[u.parentNode.scrollWidth,u.parentNode.scrollHeight]},getPosition:function(u,j){var b=r.getOffset(u,j,u._katavorioDrag?u.offsetParent:null);return[b.left,b.top]},setPosition:function(u,j){u.style.left=j[0]+"px",u.style.top=j[1]+"px"},addClass:c.addClass,removeClass:c.removeClass,intersects:K.intersects,indexOf:function(u,j){return u.indexOf(j)},scope:r.getDefaultScope(),css:{noSelect:r.dragSelectClass,droppable:"jtk-droppable",draggable:"jtk-draggable",drag:"jtk-drag",selected:"jtk-drag-selected",active:"jtk-drag-active",hover:"jtk-drag-hover",ghostProxy:"jtk-ghost-proxy"}}),_.setZoom(r.getZoom()),r[l]=_,r.bind("zoom",_.setZoom)),_},T=function(r){var f=r.el._jsPlumbDragOptions,l=!0;return f.canDrag&&(l=f.canDrag()),l&&(this.setHoverSuspended(!0),this.select({source:r.el}).addClass(this.elementDraggingClass+" "+this.sourceElementDraggingClass,!0),this.select({target:r.el}).addClass(this.elementDraggingClass+" "+this.targetElementDraggingClass,!0),this.setConnectionBeingDragged(!0)),l},a=function(r){var f=this.getUIPosition(arguments,this.getZoom());if(f!=null){var l=r.el._jsPlumbDragOptions;this.draw(r.el,f,null,!0),l._dragging&&this.addClass(r.el,"jtk-dragged"),l._dragging=!0}},i=function(r){for(var f=r.selection,l,_=(function(u){var j;u[1]!=null&&(l=this.getUIPosition([{el:u[2].el,pos:[u[1].left,u[1].top]}]),j=this.draw(u[2].el,l)),u[0]._jsPlumbDragOptions!=null&&delete u[0]._jsPlumbDragOptions._dragging,this.removeClass(u[0],"jtk-dragged"),this.select({source:u[2].el}).removeClass(this.elementDraggingClass+" "+this.sourceElementDraggingClass,!0),this.select({target:u[2].el}).removeClass(this.elementDraggingClass+" "+this.targetElementDraggingClass,!0),r.e._drawResult=r.e._drawResult||{c:[],e:[],a:[]},Array.prototype.push.apply(r.e._drawResult.c,j.c),Array.prototype.push.apply(r.e._drawResult.e,j.e),Array.prototype.push.apply(r.e._drawResult.a,j.a),this.getDragManager().dragEnded(u[2].el)}).bind(this),x=0;x<f.length;x++)_(f[x]);this.setHoverSuspended(!1),this.setConnectionBeingDragged(!1)},y=function(r,f){var l=function(_){if(f[_]!=null)if(V.isString(f[_])){var x=f[_].match(/-=/)?-1:1,u=f[_].substring(2);return r[_]+x*u}else return f[_];else return r[_]};return[l("left"),l("top")]},D=function(r,f){if(f==null)return[0,0];var l=k(f),_=e(l,0);return[_[r+"X"],_[r+"Y"]]},M=D.bind(this,"page"),C=D.bind(this,"screen"),g=D.bind(this,"client"),e=function(r,f){return r.item?r.item(f):r[f]},k=function(r){return r.touches&&r.touches.length>0?r.touches:r.changedTouches&&r.changedTouches.length>0?r.changedTouches:r.targetTouches&&r.targetTouches.length>0?r.targetTouches:[r]},L=function(r){var f={},l=[],_={},x={},u={};this.register=function(j){var b=r.getId(j),A;f[b]||(f[b]=j,l.push(j),_[b]={});var d=function(E){if(E){for(var N=0;N<E.childNodes.length;N++)if(E.childNodes[N].nodeType!==3&&E.childNodes[N].nodeType!==8){var B=jsPlumb.getElement(E.childNodes[N]),Y=r.getId(E.childNodes[N],null,!0);if(Y&&x[Y]&&x[Y]>0){A||(A=r.getOffset(j));var n=r.getOffset(B);_[b][Y]={id:Y,offset:{left:n.left-A.left,top:n.top-A.top}},u[Y]=b}d(E.childNodes[N])}}};d(j)},this.updateOffsets=function(j,b){if(j!=null){b=b||{};var A=jsPlumb.getElement(j),d=r.getId(A),E=_[d],N;if(E){for(var B in E)if(E.hasOwnProperty(B)){var Y=jsPlumb.getElement(B),n=b[B]||r.getOffset(Y);if(Y.offsetParent==null&&_[d][B]!=null)continue;N||(N=r.getOffset(A)),_[d][B]={id:B,offset:{left:n.left-N.left,top:n.top-N.top}},u[B]=d}}}},this.endpointAdded=function(j,b){b=b||r.getId(j);var A=document.body,d=j.parentNode;for(x[b]=x[b]?x[b]+1:1;d!=null&&d!==A;){var E=r.getId(d,null,!0);if(E&&f[E]){var N=r.getOffset(d);if(_[E][b]==null){var B=r.getOffset(j);_[E][b]={id:b,offset:{left:B.left-N.left,top:B.top-N.top}},u[b]=E}break}d=d.parentNode}},this.endpointDeleted=function(j){if(x[j.elementId]&&(x[j.elementId]--,x[j.elementId]<=0))for(var b in _)_.hasOwnProperty(b)&&_[b]&&(delete _[b][j.elementId],delete u[j.elementId])},this.changeId=function(j,b){_[b]=_[j],_[j]={},u[b]=u[j],u[j]=null},this.getElementsForDraggable=function(j){return _[j]},this.elementRemoved=function(j){var b=u[j];b&&(_[b]&&delete _[b][j],delete u[j])},this.reset=function(){f={},l=[],_={},x={}},this.dragEnded=function(j){if(j.offsetParent!=null){var b=r.getId(j),A=u[b];A&&this.updateOffsets(A)}},this.setParent=function(j,b,A,d,E){var N=u[b];_[d]||(_[d]={});var B=r.getOffset(A),Y=E||r.getOffset(j);N&&_[N]&&delete _[N][b],_[d][b]={id:b,offset:{left:Y.left-B.left,top:Y.top-B.top}},u[b]=d},this.clearParent=function(j,b){var A=u[b];A&&(delete _[A][b],delete u[b])},this.revalidateParent=function(j,b,A){var d=u[b];if(d){var E={};E[b]=A,this.updateOffsets(d,E),r.revalidate(d)}},this.getDragAncestor=function(j){var b=jsPlumb.getElement(j),A=r.getId(b),d=u[A];return d?jsPlumb.getElement(d):null}},U=function(r,f,l){f=V.fastTrim(f),typeof r.className.baseVal<"u"?r.className.baseVal=f:r.className=f;try{var _=r.classList;if(_!=null){for(;_.length>0;)_.remove(_.item(0));for(var x=0;x<l.length;x++)l[x]&&_.add(l[x])}}catch(u){V.log("JSPLUMB: cannot set class list",u)}},G=function(r){return typeof r.className.baseVal>"u"?r.className:r.className.baseVal},P=function(r,f,l){f=f==null?[]:V.isArray(f)?f:f.split(/\s+/),l=l==null?[]:V.isArray(l)?l:l.split(/\s+/);var _=G(r),x=_.split(/\s+/),u=function(j,b){for(var A=0;A<b.length;A++)if(j)x.indexOf(b[A])===-1&&x.push(b[A]);else{var d=x.indexOf(b[A]);d!==-1&&x.splice(d,1)}};u(!0,f),u(!1,l),U(r,x.join(" "),x)};J.jsPlumb.extend(J.jsPlumbInstance.prototype,{headless:!1,pageLocation:M,screenLocation:C,clientLocation:g,getDragManager:function(){return this.dragManager==null&&(this.dragManager=new L(this)),this.dragManager},recalculateOffsets:function(r){this.getDragManager().updateOffsets(r)},createElement:function(r,f,l,_){return this.createElementNS(null,r,f,l,_)},createElementNS:function(r,f,l,_,x){var u=r==null?document.createElement(f):document.createElementNS(r,f),j;l=l||{};for(j in l)u.style[j]=l[j];_&&(u.className=_),x=x||{};for(j in x)u.setAttribute(j,""+x[j]);return u},getAttribute:function(r,f){return r.getAttribute!=null?r.getAttribute(f):null},setAttribute:function(r,f,l){r.setAttribute!=null&&r.setAttribute(f,l)},setAttributes:function(r,f){for(var l in f)f.hasOwnProperty(l)&&r.setAttribute(l,f[l])},appendToRoot:function(r){document.body.appendChild(r)},getRenderModes:function(){return["svg"]},getClass:G,addClass:function(r,f){jsPlumb.each(r,function(l){P(l,f)})},hasClass:function(r,f){return r=jsPlumb.getElement(r),r.classList?r.classList.contains(f):G(r).indexOf(f)!==-1},removeClass:function(r,f){jsPlumb.each(r,function(l){P(l,null,f)})},toggleClass:function(r,f){jsPlumb.hasClass(r,f)?jsPlumb.removeClass(r,f):jsPlumb.addClass(r,f)},updateClasses:function(r,f,l){jsPlumb.each(r,function(_){P(_,f,l)})},setClass:function(r,f){f!=null&&jsPlumb.each(r,function(l){U(l,f,f.split(/\s+/))})},setPosition:function(r,f){r.style.left=f.left+"px",r.style.top=f.top+"px"},getPosition:function(r){var f=function(l){var _=r.style[l];return _?_.substring(0,_.length-2):0};return{left:f("left"),top:f("top")}},getStyle:function(r,f){return typeof window.getComputedStyle<"u"?getComputedStyle(r,null).getPropertyValue(f):r.currentStyle[f]},getSelector:function(r,f){var l=null;return arguments.length===1?l=r.nodeType!=null?r:document.querySelectorAll(r):l=r.querySelectorAll(f),l},getOffset:function(r,f,l){r=jsPlumb.getElement(r),l=l||this.getContainer();for(var _={left:r.offsetLeft,top:r.offsetTop},x=f||l!=null&&r!==l&&r.offsetParent!==l?r.offsetParent:null,u=(function(A){A!=null&&A!==document.body&&(A.scrollTop>0||A.scrollLeft>0)&&(_.left-=A.scrollLeft,_.top-=A.scrollTop)}).bind(this);x!=null;)_.left+=x.offsetLeft,_.top+=x.offsetTop,u(x),x=f?x.offsetParent:x.offsetParent===l?null:x.offsetParent;if(l!=null&&!f&&(l.scrollTop>0||l.scrollLeft>0)){var j=r.offsetParent!=null?this.getStyle(r.offsetParent,"position"):"static",b=this.getStyle(r,"position");b!=="absolute"&&b!=="fixed"&&j!=="absolute"&&j!=="fixed"&&(_.left-=l.scrollLeft,_.top-=l.scrollTop)}return _},getPositionOnElement:function(r,f,l){var _=typeof f.getBoundingClientRect<"u"?f.getBoundingClientRect():{left:0,top:0,width:0,height:0},x=document.body,u=document.documentElement,j=window.pageYOffset||u.scrollTop||x.scrollTop,b=window.pageXOffset||u.scrollLeft||x.scrollLeft,A=u.clientTop||x.clientTop||0,d=u.clientLeft||x.clientLeft||0,E=0,N=0,B=_.top+j-A+E*l,Y=_.left+b-d+N*l,n=jsPlumb.pageLocation(r),o=_.width||f.offsetWidth*l,t=_.height||f.offsetHeight*l,p=(n[0]-Y)/o,v=(n[1]-B)/t;return[p,v]},getAbsolutePosition:function(r){var f=function(l){var _=r.style[l];if(_)return parseFloat(_.substring(0,_.length-2))};return[f("left"),f("top")]},setAbsolutePosition:function(r,f,l,_){l?this.animate(r,{left:"+="+(f[0]-l[0]),top:"+="+(f[1]-l[1])},_):(r.style.left=f[0]+"px",r.style.top=f[1]+"px")},getSize:function(r){return[r.offsetWidth,r.offsetHeight]},getWidth:function(r){return r.offsetWidth},getHeight:function(r){return r.offsetHeight},getRenderMode:function(){return"svg"},draggable:function(r,f){var l;return r=V.isArray(r)||r.length!=null&&!V.isString(r)?r:[r],Array.prototype.slice.call(r).forEach((function(_){l=this.info(_),l.el&&this._initDraggableIfNecessary(l.el,!0,f,l.id,!0)}).bind(this)),this},snapToGrid:function(r,f,l){var _=[],x=(function(b){var A=this.info(b);if(A.el!=null&&A.el._katavorioDrag){var d=A.el._katavorioDrag.snap(f,l);this.revalidate(A.el),_.push([A.el,d])}}).bind(this);if(arguments.length===1||arguments.length===3)x(r,f,l);else{var u=this.getManagedElements();for(var j in u)x(j,arguments[0],arguments[1])}return _},initDraggable:function(r,f,l){h(this,l).draggable(r,f),r._jsPlumbDragOptions=f},destroyDraggable:function(r,f){h(this,f).destroyDraggable(r),r._jsPlumbDragOptions=null,r._jsPlumbRelatedElement=null},unbindDraggable:function(r,f,l,_){h(this,_).destroyDraggable(r,f,l)},setDraggable:function(r,f){return jsPlumb.each(r,(function(l){this.isDragSupported(l)&&(this._draggableStates[this.getAttribute(l,"id")]=f,this.setElementDraggable(l,f))}).bind(this))},_draggableStates:{},toggleDraggable:function(r){var f;return jsPlumb.each(r,(function(l){var _=this.getAttribute(l,"id");return f=this._draggableStates[_]==null?!1:this._draggableStates[_],f=!f,this._draggableStates[_]=f,this.setDraggable(l,f),f}).bind(this)),f},_initDraggableIfNecessary:function(r,f,l,_,x){if(!jsPlumb.headless){var u=f??!1;if(u&&jsPlumb.isDragSupported(r,this)){var j=l||this.Defaults.DragOptions;if(j=jsPlumb.extend({},j),jsPlumb.isAlreadyDraggable(r,this))l.force&&this.initDraggable(r,j);else{var b=jsPlumb.dragEvents.drag,A=jsPlumb.dragEvents.stop,d=jsPlumb.dragEvents.start;this.manage(_,r),j[d]=V.wrap(j[d],T.bind(this)),j[b]=V.wrap(j[b],a.bind(this)),j[A]=V.wrap(j[A],i.bind(this));var E=this.getId(r);this._draggableStates[E]=!0;var N=this._draggableStates[E];j.disabled=N==null?!1:!N,this.initDraggable(r,j),this.getDragManager().register(r),x&&this.fire("elementDraggable",{el:r,options:j})}}}},animationSupported:!0,getElement:function(r){return r==null?null:(r=typeof r=="string"?r:r.tagName==null&&r.length!=null&&r.enctype==null?r[0]:r,typeof r=="string"?document.getElementById(r):r)},removeElement:function(r){h(this).elementRemoved(r),this.getEventManager().remove(r)},doAnimate:function(r,f,l){l=l||{};var _=this.getOffset(r),x=y(_,f),u=x[0]-_.left,j=x[1]-_.top,b=l.duration||250,A=15,d=b/A,E=A/b*u,N=A/b*j,B=0,Y=setInterval(function(){c.setPosition(r,{left:_.left+E*(B+1),top:_.top+N*(B+1)}),l.step!=null&&l.step(B,Math.ceil(d)),B++,B>=d&&(window.clearInterval(Y),l.complete!=null&&l.complete())},A)},destroyDroppable:function(r,f){h(this,f).destroyDroppable(r)},unbindDroppable:function(r,f,l,_){h(this,_).destroyDroppable(r,f,l)},droppable:function(r,f){r=V.isArray(r)||r.length!=null&&!V.isString(r)?r:[r];var l;return f=f||{},f.allowLoopback=!1,Array.prototype.slice.call(r).forEach((function(_){l=this.info(_),l.el&&this.initDroppable(l.el,f)}).bind(this)),this},initDroppable:function(r,f,l){h(this,l).droppable(r,f)},isAlreadyDraggable:function(r){return r._katavorioDrag!=null},isDragSupported:function(r,f){return!0},isDropSupported:function(r,f){return!0},isElementDraggable:function(r){return r=c.getElement(r),r._katavorioDrag&&r._katavorioDrag.isEnabled()},getDragObject:function(r){return r[0].drag.getDragElement()},getDragScope:function(r){return r._katavorioDrag&&r._katavorioDrag.scopes.join(" ")||""},getDropEvent:function(r){return r[0].e},getUIPosition:function(r,f){var l=r[0].el;if(l.offsetParent==null)return null;var _=r[0].finalPos||r[0].pos,x={left:_[0],top:_[1]};if(l._katavorioDrag&&l.offsetParent!==this.getContainer()){var u=this.getOffset(l.offsetParent);x.left+=u.left,x.top+=u.top}return x},setDragFilter:function(r,f,l){r._katavorioDrag&&r._katavorioDrag.setFilter(f,l)},setElementDraggable:function(r,f){r=c.getElement(r),r._katavorioDrag&&r._katavorioDrag.setEnabled(f)},setDragScope:function(r,f){r._katavorioDrag&&r._katavorioDrag.k.setDragScope(r,f)},setDropScope:function(r,f){r._katavorioDrop&&r._katavorioDrop.length>0&&r._katavorioDrop[0].k.setDropScope(r,f)},addToPosse:function(r,f){var l=Array.prototype.slice.call(arguments,1),_=h(this);c.each(r,function(x){x=[c.getElement(x)],x.push.apply(x,l),_.addToPosse.apply(_,x)})},setPosse:function(r,f){var l=Array.prototype.slice.call(arguments,1),_=h(this);c.each(r,function(x){x=[c.getElement(x)],x.push.apply(x,l),_.setPosse.apply(_,x)})},removeFromPosse:function(r,f){var l=Array.prototype.slice.call(arguments,1),_=h(this);c.each(r,function(x){x=[c.getElement(x)],x.push.apply(x,l),_.removeFromPosse.apply(_,x)})},removeFromAllPosses:function(r){var f=h(this);c.each(r,function(l){f.removeFromAllPosses(c.getElement(l))})},setPosseState:function(r,f,l){var _=h(this);c.each(r,function(x){_.setPosseState(c.getElement(x),f,l)})},dragEvents:{start:"start",stop:"stop",drag:"drag",step:"step",over:"over",out:"out",drop:"drop",complete:"complete",beforeStart:"beforeStart"},animEvents:{step:"step",complete:"complete"},stopDrag:function(r){r._katavorioDrag&&r._katavorioDrag.abort()},addToDragSelection:function(r){var f=this.getElement(r);f!=null&&(f._isJsPlumbGroup||f._jsPlumbGroup==null)&&h(this).select(r)},removeFromDragSelection:function(r){h(this).deselect(r)},getDragSelection:function(){return h(this).getSelection()},clearDragSelection:function(){h(this).deselectAll()},trigger:function(r,f,l,_){this.getEventManager().trigger(r,f,l,_)},doReset:function(){for(var r in this)r.indexOf("_katavorio_")===0&&this[r].reset()},getEventManager:function(){return et(this)},on:function(r,f,l){return this.getEventManager().on.apply(this,arguments),this},off:function(r,f,l){return this.getEventManager().off.apply(this,arguments),this}});var w=function(r){var f=function(){/complete|loaded|interactive/.test(document.readyState)&&typeof document.body<"u"&&document.body!=null?r():setTimeout(f,9)};f()};w(c.init)}).call(typeof window<"u"?window:jt)})(pe);export{pe as j};
