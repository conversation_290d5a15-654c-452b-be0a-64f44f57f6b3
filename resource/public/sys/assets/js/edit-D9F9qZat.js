import{M as S}from"./index-Dxq8t3a8.js";import{g as P,a as L,u as O}from"./cmsAd-B2-W3wVY.js";import{u as N}from"./index-D8PZCYUo.js";import{a as I}from"./element-plus-CUmVNDWO.js";import{d as R,f as M,r as k,X as $,ad as q,u as J,ag as i,aq as h,c as y,o as m,P as r,H as l,O as E,a6 as B,G as p,L as v,M as U,J as j,a as G,I as H}from"./@vue-C21YZbHS.js";import{a as T}from"./index-BGmsw1a8.js";import"./vue-codemirror-DEL5ajhi.js";import"./codemirror-n3sawYF2.js";import"./@codemirror-D5lwEPkj.js";import"./@lezer-Dqox1lIz.js";import"./crelt-C8TCjufn.js";import"./@marijn-DXwl3gUT.js";import"./style-mod-Bc2inJdb.js";import"./w3c-keyname-Vcq4gwWv.js";import"./lodash-BPJNOONf.js";import"./@intlify-D8kAWvSi.js";import"./lodash-es-S0Y0Up6J.js";import"./@vueuse-D_5IXmcI.js";import"./@element-plus-_Cc-TEQX.js";import"./@popperjs-D_chPuIy.js";import"./@ctrl-r5W6hzzQ.js";import"./dayjs-CepoAVPu.js";import"./async-validator-9PlIezaS.js";import"./memoize-one-BdPwpGay.js";import"./normalize-wheel-es-BQoi3Ox2.js";import"./@floating-ui-DwceP2Gb.js";import"./pinia-DwJf82dV.js";import"./vue-router-BDT9dCQ7.js";import"./js-cookie-Cz0CWeBA.js";import"./nprogress-Scw6VIZr.js";import"./axios-B62VicFi.js";import"./qs-D41HS0uK.js";import"./side-channel-DytTmCZg.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-DbsE5JW_.js";import"./crypto-js-oM26lpET.js";import"./side-channel-list-C_7Uk4RP.js";import"./side-channel-map-BUokNT8A.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-bSkmVQJL.js";import"./vue-i18n-BkLk9Ti1.js";import"./vue-iNFchrNl.js";import"./vue-clipboard3-Dr8V5xR7.js";import"./clipboard-DzJOpNE6.js";import"./mitt-DJ65BbbF.js";import"./vue-grid-layout-CIPjP0XN.js";import"./vue-simple-uploader-j6sX1TKo.js";import"./vue-ueditor-wrap-CJ5gjsd3.js";const W=R({name:"apiV1CmsCmsAdEdit",components:{MyCodeMirror:S,uploadImg:N},props:{statusOptions:{type:Array,default:()=>[]},positionOptions:{type:Array,default:()=>[]}},setup(e,{emit:t}){const _="http://***************:8808/",{proxy:c}=M(),A=k(null),w=k(),f=k([]),a=$({loading:!1,isShowDialog:!1,formData:{id:void 0,name:void 0,type:"image",content:void 0,url:void 0,createdAt:void 0,updatedAt:void 0,sort:void 0,status:"1",position:void 0,linkedCmsAdCmsAdPosition:{id:void 0,name:void 0}},rules:{name:[{required:!0,message:"广告名称不能为空",trigger:"blur"}],position:[{required:!0,message:"广告位置不能为空",trigger:"blur"}]}}),{cms_ad_type:s}=c.useDict("cms_ad_type"),d=n=>{b(),f.value=[],n&&(D(),P(n.id).then(g=>{const o=g.data;o.type==="image"&&o.content!==""&&(f.value=JSON.parse(o.content)),o.status=""+o.status,a.formData=o})),a.isShowDialog=!0},u=()=>{a.isShowDialog=!1},V=()=>{u()},F=n=>{a.formData.content=n},C=()=>{const n=J(A);n&&(a.formData.type==="image"&&Array.isArray(a.formData.content)&&(a.formData.content=JSON.stringify(a.formData.content)),n.validate(g=>{g&&(a.loading=!0,!a.formData.id||a.formData.id===0?L(a.formData).then(()=>{I.success("添加成功"),u(),t("cmsAdList")}).finally(()=>{a.loading=!1}):O(a.formData).then(()=>{I.success("修改成功"),u(),t("cmsAdList")}).finally(()=>{a.loading=!1}))}))},b=()=>{a.formData={id:void 0,name:void 0,type:"image",content:"",url:void 0,createdAt:void 0,updatedAt:void 0,sort:void 0,status:"1",position:void 0,linkedCmsAdCmsAdPosition:{id:void 0,name:void 0}}},D=()=>{t("getCmsAdPositionItemsPosition")};return{proxy:c,baseURL:_,openDialog:d,closeDialog:u,onCancel:V,cms_ad_type:s,onSubmit:C,setUpImgList:F,menuRef:w,formRef:A,tempValue:f,getCmsAdPositionItemsPosition:D,...q(a)}}}),X={class:"cms-cmsAd-edit"},z={class:"dialog-footer"};function K(e,t,_,c,A,w){const f=i("el-option"),a=i("el-select"),s=i("el-form-item"),d=i("el-input"),u=i("el-radio"),V=i("el-radio-group"),F=i("upload-img"),C=i("my-code-mirror"),b=i("el-form"),D=i("el-button"),n=i("el-dialog"),g=h("drag");return m(),y("div",X,[r(n,{modelValue:e.isShowDialog,"onUpdate:modelValue":t[9]||(t[9]=o=>e.isShowDialog=o),"close-on-click-modal":!1,"destroy-on-close":!0,width:"769px"},{header:l(()=>[H((m(),y("div",null,[v(U((!e.formData.id||e.formData.id==0?"添加":"修改")+"广告"),1)])),[[g,[".cms-cmsAd-edit .el-dialog",".cms-cmsAd-edit .el-dialog__header"]]])]),footer:l(()=>[G("div",z,[r(D,{type:"primary",onClick:e.onSubmit},{default:l(()=>t[10]||(t[10]=[v("确 定")])),_:1},8,["onClick"]),r(D,{onClick:e.onCancel},{default:l(()=>t[11]||(t[11]=[v("取 消")])),_:1},8,["onClick"])])]),default:l(()=>[r(b,{ref:"formRef",model:e.formData,rules:e.rules,"label-width":"90px"},{default:l(()=>[r(s,{label:"广告位置",prop:"position"},{default:l(()=>[r(a,{modelValue:e.formData.position,"onUpdate:modelValue":t[0]||(t[0]=o=>e.formData.position=o),placeholder:"请选择广告位置",onClick:e.getCmsAdPositionItemsPosition},{default:l(()=>[(m(!0),y(E,null,B(e.positionOptions,o=>(m(),p(f,{key:o.key,label:o.value,value:parseInt(o.key)},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onClick"])]),_:1}),r(s,{label:"广告名称",prop:"name"},{default:l(()=>[r(d,{modelValue:e.formData.name,"onUpdate:modelValue":t[1]||(t[1]=o=>e.formData.name=o),placeholder:"请输入广告名称"},null,8,["modelValue"])]),_:1}),r(s,{label:"类型",prop:"type"},{default:l(()=>[r(V,{modelValue:e.formData.type,"onUpdate:modelValue":t[2]||(t[2]=o=>e.formData.type=o)},{default:l(()=>[(m(!0),y(E,null,B(e.cms_ad_type,o=>(m(),p(u,{key:o.value,label:o.value},{default:l(()=>[v(U(o.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),r(s,{label:"内容",prop:"content"},{default:l(()=>[e.formData.type==="text"?(m(),p(d,{key:0,modelValue:e.formData.content,"onUpdate:modelValue":t[3]||(t[3]=o=>e.formData.content=o),type:"textarea",rows:4,placeholder:"请输入广告内容"},null,8,["modelValue"])):e.formData.type==="image"?(m(),p(F,{key:1,modelValue:e.tempValue,"onUpdate:modelValue":t[4]||(t[4]=o=>e.tempValue=o),limit:10,action:e.baseURL+"api/v1/system/upload/singleImg",onUploadData:e.setUpImgList},null,8,["modelValue","action","onUploadData"])):e.formData.type==="js"?(m(),p(C,{key:2,height:250,modelValue:e.formData.content,"onUpdate:modelValue":t[5]||(t[5]=o=>e.formData.content=o),style:{width:"100%"}},null,8,["modelValue"])):j("",!0)]),_:1}),r(s,{label:"跳转链接",prop:"url"},{default:l(()=>[r(d,{modelValue:e.formData.url,"onUpdate:modelValue":t[6]||(t[6]=o=>e.formData.url=o),placeholder:"请输入跳转链接"},null,8,["modelValue"])]),_:1}),r(s,{label:"排序",prop:"sort"},{default:l(()=>[r(d,{modelValue:e.formData.sort,"onUpdate:modelValue":t[7]||(t[7]=o=>e.formData.sort=o),placeholder:"请输入排序"},null,8,["modelValue"])]),_:1}),r(s,{label:"状态",prop:"status"},{default:l(()=>[r(V,{modelValue:e.formData.status,"onUpdate:modelValue":t[8]||(t[8]=o=>e.formData.status=o)},{default:l(()=>[(m(!0),y(E,null,B(e.statusOptions,o=>(m(),p(u,{key:o.value,label:o.value},{default:l(()=>[v(U(o.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}const te=T(W,[["render",K]]);export{te as default};
