// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2023-03-09 16:48:47
// 生成路径: internal/app/ucenter/logic/ucenter_level.go
// 生成人：yxf
// desc:用户等级
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	apiAdmin "github.com/tiger1103/gfast/v3/api/v1/ucenter/admin"

	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/internal/app/ucenter/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ucenter/model"
	"github.com/tiger1103/gfast/v3/internal/app/ucenter/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ucenter/service"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterUCenterLevel(New())
}

func New() *sUCenterLevel {
	return &sUCenterLevel{}
}

type sUCenterLevel struct{}

func (s *sUCenterLevel) List(ctx context.Context, req *apiAdmin.UCenterLevelSearchReq) (listRes *apiAdmin.UCenterLevelSearchRes, err error) {
	listRes = new(apiAdmin.UCenterLevelSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.UCenterLevel.Ctx(ctx).WithAll()
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.UCenterLevelInfoRes
		err = m.Fields(apiAdmin.UCenterLevelSearchRes{}).Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.UCenterLevelListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.UCenterLevelListRes{
				Id:          v.Id,
				Name:        v.Name,
				Description: v.Description,
				ListOrder:   v.ListOrder,
			}
		}
	})
	return
}

func (s *sUCenterLevel) GetById(ctx context.Context, id uint) (res *model.UCenterLevelInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.UCenterLevel.Ctx(ctx).WithAll().Where(dao.UCenterLevel.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sUCenterLevel) Add(ctx context.Context, req *apiAdmin.UCenterLevelAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.UCenterLevel.Ctx(ctx).Insert(do.UCenterLevel{
			Name:        req.Name,
			Description: req.Description,
			ListOrder:   req.ListOrder,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sUCenterLevel) Edit(ctx context.Context, req *apiAdmin.UCenterLevelEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.UCenterLevel.Ctx(ctx).WherePri(req.Id).Update(do.UCenterLevel{
			Name:        req.Name,
			Description: req.Description,
			ListOrder:   req.ListOrder,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sUCenterLevel) Delete(ctx context.Context, ids []uint) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.UCenterLevel.Ctx(ctx).Delete(dao.UCenterLevel.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
