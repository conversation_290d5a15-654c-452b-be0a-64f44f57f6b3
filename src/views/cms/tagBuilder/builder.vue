<template>
  <div class="app-container">
    <el-tabs v-model="tabsValue">
      <el-tab-pane label="站点配置" name="cmsConfig">
        <el-row :gutter="24">
          <el-col :span="12" class="cms-config-btns">
            <el-alert title="基础配置" type="info"></el-alert>
            <el-button v-for="(item,index) in cmsConfig" :key="item.name" type="default" @click="builderCmsConfigTag(item.name)">
              {{ item.title }}
            </el-button>
          </el-col>
          <el-col :span="12">
            <el-alert title="复制到模板中" type="info"></el-alert>
            <div style="height: 5px"></div>
            <el-input v-model="cmsConfigTagContent" :rows="8" type="textarea"></el-input>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="文章列表" name="arcList">
        <el-row :gutter="24">
          <el-col :span="12" class="cms-config-btns">
            <el-alert title="文章标签，多个参数用 | 分隔" type="info"></el-alert>
            <el-form ref="form" inline label-width="110px" size="small" style="margin-top: 10px">
              <el-form-item label="分类ID">
                <el-input v-model="articleTagsParams.cateId" name="cateId" placeholder="示例：1,2,3" style="width: 150px" type="text"/>
              </el-form-item>
              <el-form-item label="最小发布日期">
                <el-date-picker
                    v-model="articleTagsParams.pubAtStart"
                    class="my-datetime-input"
                    placeholder="选择最小日期"
                    style="width: 150px"
                    type="datetime"
                    value-format="yyyy-MM-dd HH:mm:ss">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="最大发布日期">
                <el-date-picker
                    v-model="articleTagsParams.pubAtEnd"
                    class="my-datetime-input"
                    placeholder="选择最大日期"
                    style="width: 150px"
                    type="datetime"
                    value-format="yyyy-MM-dd HH:mm:ss">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="排序方式">
                <el-input v-model="articleTagsParams.orderBy" name="pubAtStart" placeholder="is_top desc,id desc" style="width: 150px" type="text"/>
              </el-form-item>
              <el-form-item label="关键字">
                <el-input v-model="articleTagsParams.keyWords" name="pubAtStart" placeholder="张三" style="width: 150px" type="text"/>
              </el-form-item>
              <el-form-item label="状态">
                <el-select v-model="articleTagsParams.status" placeholder="placeholder" style="width: 150px">
                  <el-option
                      v-for="item in articleStatusOptionsList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="页码">
                <el-input v-model="articleTagsParams.page" name="pubAtStart" placeholder="1" style="width: 150px" type="text"/>
              </el-form-item>
              <el-form-item label="每页条数">
                <el-input v-model="articleTagsParams.pageSize" name="pubAtStart" placeholder="10" style="width: 150px" type="text"/>
              </el-form-item>
              <el-form-item label="是否幻灯片">
                <el-select v-model="articleTagsParams.isSlide" placeholder="请选择" style="width: 150px">
                  <el-option
                      v-for="item in optionsList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="是否置顶">
                <el-select v-model="articleTagsParams.isTop" placeholder="请选择" style="width: 150px">
                  <el-option
                      v-for="item in optionsList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="是否是推荐">
                <el-select v-model="articleTagsParams.recommended" placeholder="请选择" style="width: 150px">
                  <el-option
                      v-for="item in optionsList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-form>
            <div>
              <el-button class="el-button el-button--primary" type="default" @click="builderArticleTags('GetArticleList')">获取文章列表</el-button>
              <el-button class="el-button el-button--primary" type="default" @click="builderArticleTags('GetArticle')">获取单条文章</el-button>
              <el-button class="el-button el-button--primary" type="default" @click="builderArticleTags('GetPrevAndNextArticle')">获取上一篇下一篇</el-button>
            </div>
            <el-collapse>
              <el-collapse-item name="1" title="文章列表返回值: []map[string]interface{}">
                <div>
                  <el-table :data="articleListReturnData" border size="small" style="width: 100%">
                    <el-table-column label="属性名" prop="name" width="180"></el-table-column>
                    <el-table-column label="类型" prop="type" width="180"></el-table-column>
                    <el-table-column label="说明" prop="desc"></el-table-column>
                  </el-table>
                </div>
              </el-collapse-item>
            </el-collapse>
            <el-collapse>
              <el-collapse-item name="1" title="单条文章返回值: map[string]interface{}">
                <div>
                  <el-table :data="articleReturnData" border size="small" style="width: 100%">
                    <el-table-column label="属性名" prop="name" width="180"></el-table-column>
                    <el-table-column label="类型" prop="type" width="180"></el-table-column>
                    <el-table-column label="说明" prop="desc"></el-table-column>
                  </el-table>
                </div>
              </el-collapse-item>
            </el-collapse>
          </el-col>
          <el-col :span="12">
            <el-alert title="复制到模板中" type="info"></el-alert>
            <div style="height: 5px"></div>
            <el-input v-model="articleTagsContent" :rows="14" class="my-textarea" type="textarea"></el-input>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="文章栏目" name="categoryList">
        <el-row :gutter="24">
          <el-col :span="12" class="cms-config-btns">
            <el-alert title="文章栏目列表标签，多个参数用 | 分隔" type="info"></el-alert>
            <el-form ref="form" inline label-width="110px" size="small" style="margin-top: 10px">
              <el-form-item label="父级类ID">
                <el-input v-model="menuTagsParams.parentId" name="parentId" placeholder="示例：0" style="width: 150px" type="text"/>
              </el-form-item>
              <el-form-item label="激活的栏目ID">
                <el-input v-model="menuTagsParams.activeId" name="activeId" placeholder="示例：1" style="width: 150px" type="text"/>
              </el-form-item>
            </el-form>
            <div>
              <el-button class="el-button el-button--primary" type="default" @click="builderMenuTags()">获取菜单列表</el-button>
            </div>
            <el-collapse>
              <el-collapse-item name="1" title="栏目列表返回值: []map[string]interface{}">
                <div>
                  <el-table :data="menuReturnData" border size="small" style="width: 100%">
                    <el-table-column label="属性名" prop="name" width="180"></el-table-column>
                    <el-table-column label="类型" prop="type" width="180"></el-table-column>
                    <el-table-column label="说明" prop="desc"></el-table-column>
                  </el-table>
                </div>
              </el-collapse-item>
            </el-collapse>
            <el-divider></el-divider>
            <el-alert title="文章栏目标签，参数为需要获取的栏目ID" type="info"></el-alert>
            <div>
              <el-button class="el-button el-button--primary" type="default" @click="builderCategoryTags()">获取单个文章分类信息</el-button>
            </div>
            <el-collapse>
              <el-collapse-item name="1" title="获取文章栏目返回值: map[string]interface{}">
                <div>
                  <el-table :data="categoryReturnData" border size="small" style="width: 100%">
                    <el-table-column label="属性名" prop="name" width="180"></el-table-column>
                    <el-table-column label="类型" prop="type" width="180"></el-table-column>
                    <el-table-column label="说明" prop="desc"></el-table-column>
                  </el-table>
                </div>
              </el-collapse-item>
            </el-collapse>
          </el-col>
          <el-col :span="12">
            <el-alert title="复制到模板中" type="info"></el-alert>
            <div style="height: 5px"></div>
            <el-input v-model="menuTagsContent" :rows="14" class="my-textarea" type="textarea"></el-input>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="广告列表" name="ad">
        <el-row :gutter="24">
          <el-col :span="12" class="cms-config-btns">
            <el-alert title="获取广告列表标签，多个参数用 | 分隔" type="info"></el-alert>
            <el-form ref="form" inline label-width="110px" size="small" style="margin-top: 10px">
              <el-form-item label="广告位ID">
                <el-input v-model="adTagsParams.position" name="parentId" placeholder="示例：22" style="width: 150px" type="text"/>
              </el-form-item>
              <el-form-item label="获取的数量">
                <el-input v-model="adTagsParams.pageSize" name="activeId" placeholder="示例：3" style="width: 150px" type="text"/>
              </el-form-item>
            </el-form>
            <div>
              <el-button class="el-button el-button--primary" type="default" @click="builderAdTags()">生成广告列表标签</el-button>
            </div>
            <el-collapse>
              <el-collapse-item name="1" title="广告列表返回值: []map[string]interface{}">
                <pre>{{ JSON.stringify(adReturnData, null, 2) }}</pre>
              </el-collapse-item>
            </el-collapse>
          </el-col>
          <el-col :span="12">
            <el-alert title="复制到模板中" type="info"></el-alert>
            <div style="height: 5px"></div>
            <el-input v-model="adTagsContent" :rows="14" class="my-textarea" type="textarea"></el-input>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="面包屑" name="Breadcrumb">
        <el-row :gutter="24">
          <el-col :span="12" class="cms-config-btns">
            <el-alert title="获取面包屑标签" type="info"></el-alert>
            <el-form ref="form" inline label-width="110px" size="small" style="margin-top: 10px">
              <el-form-item label="栏目ID">
                <el-input v-model="breadcrumbTagsParams.cateId" name="parentId" placeholder="示例：22" style="width: 150px" type="text"/>
              </el-form-item>
            </el-form>
            <div>
              <el-button class="el-button el-button--primary" type="default" @click="builderBreadcrumbTags()">生成获取面包屑标签</el-button>
            </div>
            <el-collapse>
              <el-collapse-item name="1" title="返回值: []map[string]interface{}">
                <el-table :data="breadcrumbTagsReturnData" border size="small" style="width: 100%">
                  <el-table-column label="属性名" prop="name" width="180"></el-table-column>
                  <el-table-column label="类型" prop="type" width="180"></el-table-column>
                  <el-table-column label="说明" prop="desc"></el-table-column>
                </el-table>
              </el-collapse-item>
            </el-collapse>
          </el-col>
          <el-col :span="12">
            <el-alert title="复制到模板中" type="info"></el-alert>
            <div style="height: 5px"></div>
            <el-input v-model="breadcrumbTagsContent" :rows="14" class="my-textarea" type="textarea"></el-input>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="query查询" name="query">
        <el-row :gutter="24">
          <el-col :span="12" class="cms-config-btns">
            <el-alert title="query标签 只允许select查询" type="info"></el-alert>
            <div>
              <el-button class="el-button el-button--primary" type="default" @click="builderQueryTags()">生成query标签</el-button>
            </div>
            <el-collapse>
              <el-collapse-item name="1" title="返回值: gdb.Result">
                返回对应数据库表查询内容
              </el-collapse-item>
            </el-collapse>
          </el-col>
          <el-col :span="12">
            <el-alert title="复制到模板中" type="info"></el-alert>
            <div style="height: 5px"></div>
            <el-input v-model="queryTagsContent" :rows="14" class="my-textarea" type="textarea"></el-input>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import {listCmsConfig} from "/@/api/cms/cmsConfig";

export default {
  name: 'TagBuilder',
  data() {
    return {
      tabsValue: 'cmsConfig',
      cmsConfig: [],
      breadcrumbTagsParams: {cateId: 17},
      breadcrumbTagsContent: '',
      breadcrumbTagsReturnData: [
        {name: "Name", type: "string", desc: "栏目名称"},
        {name: "Id", type: "uint64", desc: "栏目ID"},
        {name: "ParentId", type: "uint64", desc: "父ID"},
        {name: "Url", type: "string", desc: "跳转地址"},
      ],
      adTagsParams: {
        position: '22',
        pageSize: '3',
      },
      adReturnData: [
        {
          "AdId": 1,
          "AdName": "首页广告一",
          "AdAdtypeid": 2,  // 广告位
          "AdCheckid": 1,  // 类型1:图片，2:js代码
          "AdJs": "", // JS类型值
          "AdPic": "", // 图片类型地址
          "AdUrl": "", // 跳转URL
          "AdContent": "", // 内容
          "AdSort": 0,
          "AdOpen": 1,  // 是否打开
          "adtypeName": "CMS首页广告位一" // 广告位名称
        }
      ],
      adTagsContent: '',
      menuTagsParams: {
        activeId: '',
        parentId: '',
      },
      menuReturnData: [
        {name: "Name", type: "string", desc: "栏目名"},
        {name: "Id", type: "uint64", desc: "栏目ID"},
        {name: "ParentId", type: "uint64", desc: "父ID"},
        {name: "Alias", type: "string", desc: "别名"},
        {name: "ModelId", type: "uint", desc: "模型ID"},
        {name: "JumpUrl", type: "string", desc: "跳转地址"},
        {name: "CateType", type: "uint", desc: "栏目类型，1:频道,2:发布,3:跳转,4:单页"},
        {name: "ListOrder", type: "int", desc: "排序值"},
        {name: "Children", type: "map", desc: "子级栏目"},
        {name: "Thumb", type: "string", desc: "缩略图"},
        {name: "Active", type: "bool", desc: "是否激活"},
      ],
      categoryReturnData: [
        {name: "Name", type: "string", desc: "栏目名"},
        {name: "Id", type: "uint64", desc: "栏目ID"},
        {name: "ParentId", type: "uint64", desc: "父ID"},
        {name: "Alias", type: "string", desc: "别名"},
        {name: "ModelId", type: "uint", desc: "模型ID"},
        {name: "JumpUrl", type: "string", desc: "跳转地址"},
        {name: "CateType", type: "uint", desc: "栏目类型，1:频道,2:发布,3:跳转,4:单页"},
        {name: "ListOrder", type: "int", desc: "排序值"},
        {name: "Thumb", type: "string", desc: "缩略图"},
        {name: "SeoTitle", type: "string", desc: "Seo标题"},
        {name: "Keywords", type: "string", desc: "Seo关键词"},
        {name: "Description", type: "string", desc: "Seo描述"},
        {name: "Content", type: "string", desc: "单页内容"},
        {name: "CreatedAt", type: "string", desc: "创建日期"},
        {name: "UpdatedAt", type: "string", desc: "更新日期"},
        {name: "Banner", type: "string", desc: "栏目图片"},
      ],
      menuTagsContent: '',
      articleListReturnData: [
        {name: "Id", type: "uint64", desc: "文章ID"},
        {name: "Title", type: "string", desc: "文章标题"},
        {name: "SeoTitle", type: "string", desc: "Seo标题"},
        {name: "Description", type: "string", desc: "Seo描述"},
        {name: "AllowComment", type: "int", desc: "允许评论"},
        {name: "Keywords", type: "string", desc: "Seo关键字"},
        {name: "CategoryId", type: "uint", desc: "栏目ID"},
        {name: "ModuleId", type: "uint", desc: "动态模型ID"},
        {name: "Hits", type: "uint64", desc: "查看数"},
        {name: "Flag", type: "string", desc: "创建时间"},
        {name: "Attr", type: "[]string", desc: "推荐属性中文名称"},
        {name: "CommentCount", type: "int", desc: "评论数"},
        {name: "FavoriteCount", type: "int", desc: "收藏数"},
        {name: "LikeCount", type: "int", desc: "点赞数"},
        {name: "Thumb", type: "string", desc: "缩略图"},
        {name: "UserId", type: "uint64", desc: "发表者用户id"},
        {name: "OriginTitle", type: "string", desc: "来源标题"},
        {name: "OriginUrl", type: "string", desc: "来源链接"},
        {name: "Summary", type: "string", desc: "摘要"},
        {name: "Url", type: "string", desc: "文章地址"},
        {name: "IsJump", type: "uint", desc: "是否跳转地址"},
        {name: "JumpUrl", type: "string", desc: "跳转地址"},
        {name: "PublishedAt", type: "string", desc: "发布时间"},
        {name: "Category", type: "model.LinkedCmsCategoryCmsCategory", desc: "文章分类结构体，有可能为空"},
        {name: "Category.Id", type: "uint", desc: "文章分类ID"},
        {name: "Category.Name", type: "string", desc: "文章分类名称"},
        {name: "UserInfo", type: "model.SysUserSimpleRes",desc: "发布者信息结构体，有可能为空"},
        {name: "UserInfo.Id", type: "uint64", desc: "创建ID"},
        {name: "UserInfo.Avatar", type: "string", desc: "创建人头像"},
        {name: "UserInfo.Sex", type: "1", desc: "创建人性别"},
        {name: "UserInfo.UserNickname", type: "string", desc: "创建人昵称"},
      ],
      articleReturnData: [
        {name: "Id", type: "uint64", desc: "文章ID"},
        {name: "Title", type: "string", desc: "文章标题"},
        {name: "SeoTitle", type: "string", desc: "Seo标题"},
        {name: "Description", type: "string", desc: "Seo描述"},
        {name: "AllowComment", type: "int", desc: "允许评论"},
        {name: "Keywords", type: "string", desc: "Seo关键字"},
        {name: "CategoryId", type: "uint", desc: "栏目ID"},
        {name: "ModuleId", type: "uint", desc: "动态模型ID"},
        {name: "Hits", type: "uint64", desc: "查看数"},
        {name: "Flag", type: "string", desc: "创建时间"},
        {name: "Attr", type: "[]string", desc: "推荐属性中文名称"},
        {name: "CommentCount", type: "int", desc: "评论数"},
        {name: "FavoriteCount", type: "int", desc: "收藏数"},
        {name: "LikeCount", type: "int", desc: "点赞数"},
        {name: "Thumb", type: "string", desc: "缩略图"},
        {name: "UserId", type: "uint64", desc: "发表者用户id"},
        {name: "OriginTitle", type: "string", desc: "来源标题"},
        {name: "OriginUrl", type: "string", desc: "来源链接"},
        {name: "Summary", type: "string", desc: "摘要"},
        {name: "Url", type: "string", desc: "文章地址"},
        {name: "IsJump", type: "uint", desc: "是否跳转地址"},
        {name: "JumpUrl", type: "string", desc: "跳转地址"},
        {name: "PublishedAt", type: "string", desc: "发布时间"},
        {name: "Category", type: "model.LinkedCmsCategoryCmsCategory", desc: "文章分类结构体，有可能为空"},
        {name: "Category.Id", type: "uint", desc: "文章分类ID"},
        {name: "Category.Name", type: "string", desc: "文章分类名称"},
        {name: "UserInfo", type: "model.SysUserSimpleRes",desc: "发布者信息结构体，有可能为空"},
        {name: "UserInfo.Id", type: "uint64", desc: "创建ID"},
        {name: "UserInfo.Avatar", type: "string", desc: "创建人头像"},
        {name: "UserInfo.Sex", type: "1", desc: "创建人性别"},
        {name: "UserInfo.UserNickname", type: "string", desc: "创建人昵称"},
        {name: "ModulesData", type: "map[string]interface{}", desc: `动态模型数据map[string]interface{}{
    "Name"     ,
    "Type":    ,
    "Options": ,
    "Label":   ,
    "Sort":    ,
    "Value":   ,
}`},
      ],
      optionsList: [
        {label: '全部', value: ''},
        {label: '否', value: '0'},
        {label: '是', value: '1'},
      ],
      articleStatusOptionsList: [
        {label: '全部', value: ''},
        {label: '待审核', value: '0'},
        {label: '已审核', value: '1'},
        {label: '退回', value: '-1'},
      ],
      articleTagsParams: {
        cateId: '',
        pubAtStart: '',
        pubAtEnd: '',
        keyWords: '',
        isSlide: '',
        isTop: '',
        recommended: '',
        status: '',
        page: 1,
        pageSize: 10,
        orderBy: '',
      },
      articleTagsContent: '',
      listTags: [],
      cmsConfigTagContent: '',
      queryTagsContent: '',
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      listCmsConfig().then(res => {
        if (res.code === 0) {
          this.cmsConfig = res.data.list
        }
      })
    },
    builderCmsConfigTag(name) {
      this.cmsConfigTagContent = '${.cmsConfig.' + name + '}'
    },
    builderArticleTags(name) {
      if (name === 'GetArticle') {
        this.articleTagsContent = '${$article := GetArticle "id:123"}';
        return
      } else if (name === 'GetPrevAndNextArticle') {
        this.articleTagsContent = '参数为uint64,可直接使用文章变量id \r\n${$prevAndNext := GetPrevAndNextArticle 111}\r\n${$prevAndNext.prev.Title}\r\n${$prevAndNext.next.Title}';
        return
      }
      let params = []
      Object.keys(this.articleTagsParams).forEach((key) => {
        if (this.articleTagsParams[key] !== '') {
          params.push(key + ':' + this.articleTagsParams[key])
        }
      })
      if (params.length > 0) {
        params = '"' + params.join('|') + '"'
      }
      let tagStr = '<ul>\r\n${range $key,$article := GetArticleList ' + params + '}\r\n'
      tagStr += '    <li>${$article.ArticleTitle}</li>\r\n'
      tagStr += '${end}\r\n</ul>'
      tagStr += '\r\n\r\n注意：参数需要使用其它变量时应该用print转换:\r\n ${$articleList := GetArticleList (print "cateId:" $category.Id " | pageSize:5")}'
      this.articleTagsContent = tagStr
    },
    builderMenuTags() {
      let params = []
      Object.keys(this.menuTagsParams).forEach((key) => {
        if (this.menuTagsParams[key] !== '') {
          params.push(key + ':' + this.menuTagsParams[key])
        }
      })
      if (params.length > 0) {
        params = '"' + params.join('|') + '"'
      }
      let tagStr = '<ul>\r\n${range $key,$menu := GetMenu ' + params + '}\r\n'
      tagStr += '    <li><a href="${$menu.JumpUrl}">${$menu.Name}</a></li>\r\n'
      tagStr += '${end}\r\n</ul>'
      this.menuTagsContent = tagStr
    },
    builderCategoryTags(){
      let tagStr = '${$categoryInfo := GetCategory ' + "12" + '}\r\n'
      tagStr += '<div>栏目名称：${$categoryInfo.Name}</div>\r\n'
      tagStr += '<div>栏目ID：${$categoryInfo.Id}</div>\r\n'
      this.menuTagsContent = tagStr
    },
    builderAdTags() {
      let params = []
      Object.keys(this.adTagsParams).forEach((key) => {
        if (this.adTagsParams[key] !== '') {
          params.push(key + ':' + this.adTagsParams[key])
        }
      })
      if (params.length > 0) {
        params = '"' + params.join('|') + '"'
      }
      let tagStr = '<ul>\r\n${range $key,$ad := GetAdList ' + params + '}\r\n'
      tagStr += '    <li><a href="${$ad.AdUrl}">${$ad.AdName}</a></li>\r\n'
      tagStr += '${end}\r\n</ul>'
      this.adTagsContent = tagStr
    },
    builderBreadcrumbTags() {
      this.breadcrumbTagsContent = '<ul>\n' +
          '  <li>首页</li>\n' +
          '  ${range $b := GetBreadcrumb 17}\n' +
          '  <li>\n' +
          '    <span> > </span>\n' +
          '    <a href="${$b.Url}">${$b.Name}</a>\n' +
          '  </li>\n' +
          '  ${end}\n' +
          '</ul>'
    },
    builderQueryTags() {
      this.queryTagsContent = '${$rows := Query "select * from cms_article where id>1 order by id desc limit 0,10"}\n' +
          '<ul>${range $key, $row := $rows}\n' +
          '  <li>${$row.article_title}</li>\n' +
          '</ul>\n' +
          '${end}\n'
    }
  }
}
</script>

<style>
.cms-config-btns {

}

.cms-config-btns button {
  margin: 5px;
}

.my-datetime-input .el-input__prefix, .my-datetime-input .el-input__suffix {
  display: none;
}

.my-datetime-input .el-input__inner {
  padding-left: 16px;
  padding-right: 5px;
}

.my-textarea .el-textarea__inner {
  white-space: nowrap;
  overflow: auto;
}

</style>
