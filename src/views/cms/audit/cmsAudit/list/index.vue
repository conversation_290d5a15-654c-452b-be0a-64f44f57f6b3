<template>
  <div class="audit-cmsAudit-container">
    <el-card shadow="hover">
      <div class="container-wrapper">
        <!-- 左侧栏目树 -->
        <left-tree @node-click="handleNodeClick"></left-tree>

        <!-- 右侧列表 -->
        <right-list :current-node="currentNode"></right-list>
      </div>
    </el-card>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import LeftTree from './component/leftTree.vue';
import RightList from './component/rightList.vue';

defineOptions({ name: "apiV1CmsAuditCmsAuditList" });

// 当前选中的节点
const currentNode = ref(null);

// 处理节点点击事件
const handleNodeClick = (node) => {
  currentNode.value = node;
};
</script>
<style lang="scss" scoped>
.audit-cmsAudit-container {
  height: 100%;

  .el-card {
    height: 100%;
  }

  .container-wrapper {
    display: flex;
    height: 100%;
    overflow: hidden;
  }
}
</style>