// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2023-12-01 14:49:39
// 生成路径: internal/app/plugins/sms/dao/plugin_sms_config.go
// 生成人：gfast
// desc:短信配置
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/plugins/sms/dao/internal"
)

// pluginSmsConfigDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type pluginSmsConfigDao struct {
	*internal.PluginSmsConfigDao
}

var (
	// PluginSmsConfig is globally public accessible object for table tools_gen_table operations.
	PluginSmsConfig = pluginSmsConfigDao{
		internal.NewPluginSmsConfigDao(),
	}
)

// Fill with you ideas below.
