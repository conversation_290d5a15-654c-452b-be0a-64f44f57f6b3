import{g as b}from"./ucenterNotifications-Cj4LEkOJ.js";import{d as y,f as w,r as g,X as B,ad as h,ag as l,c as V,o as A,P as e,H as t,L as m,M as p,a as I}from"./@vue-C21YZbHS.js";import{a as C}from"./index-BGmsw1a8.js";import"./pinia-DwJf82dV.js";import"./vue-router-BDT9dCQ7.js";import"./js-cookie-Cz0CWeBA.js";import"./@element-plus-_Cc-TEQX.js";import"./nprogress-Scw6VIZr.js";import"./@intlify-D8kAWvSi.js";import"./axios-B62VicFi.js";import"./qs-D41HS0uK.js";import"./side-channel-DytTmCZg.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-DbsE5JW_.js";import"./crypto-js-oM26lpET.js";import"./side-channel-list-C_7Uk4RP.js";import"./side-channel-map-BUokNT8A.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-bSkmVQJL.js";import"./element-plus-CUmVNDWO.js";import"./lodash-es-S0Y0Up6J.js";import"./@vueuse-D_5IXmcI.js";import"./@popperjs-D_chPuIy.js";import"./@ctrl-r5W6hzzQ.js";import"./dayjs-CepoAVPu.js";import"./async-validator-9PlIezaS.js";import"./memoize-one-BdPwpGay.js";import"./normalize-wheel-es-BQoi3Ox2.js";import"./@floating-ui-DwceP2Gb.js";import"./vue-i18n-BkLk9Ti1.js";import"./vue-iNFchrNl.js";import"./vue-clipboard3-Dr8V5xR7.js";import"./clipboard-DzJOpNE6.js";import"./mitt-DJ65BbbF.js";import"./vue-grid-layout-CIPjP0XN.js";import"./vue-simple-uploader-j6sX1TKo.js";import"./vue-ueditor-wrap-CJ5gjsd3.js";const N=y({name:"apiV1UcenterUcenterNotificationsDetail",components:{},props:{typeOptions:{type:Array,default:()=>[]}},setup(o,{emit:s}){const{proxy:c}=w(),_=g(null),D=g(),i=B({loading:!1,isShowDialog:!1,formData:{id:void 0,type:void 0,content:void 0,status:!1,createdAt:void 0,memberId:void 0},rules:{id:[{required:!0,message:"ID不能为空",trigger:"blur"}],status:[{required:!0,message:"状态不能为空",trigger:"blur"}]}}),a=n=>{u(),n&&b(n.id).then(f=>{const v=f.data;i.formData=v}),i.isShowDialog=!0},r=()=>{i.isShowDialog=!1},d=()=>{r()},u=()=>{i.formData={id:void 0,type:void 0,content:void 0,status:!1,createdAt:void 0,memberId:void 0}};return{proxy:c,openDialog:a,closeDialog:r,onCancel:d,menuRef:D,formRef:_,...h(i)}}}),E={class:"ucenter-ucenterNotifications-detail"};function S(o,s,c,_,D,i){const a=l("el-form-item"),r=l("el-col"),d=l("el-row"),u=l("el-form"),n=l("el-drawer");return A(),V("div",E,[e(n,{modelValue:o.isShowDialog,"onUpdate:modelValue":s[0]||(s[0]=f=>o.isShowDialog=f),size:"80%",direction:"ltr"},{header:t(()=>s[1]||(s[1]=[I("h4",null,"用户通知详情",-1)])),default:t(()=>[e(u,{ref:"formRef",model:o.formData,"label-width":"100px"},{default:t(()=>[e(d,null,{default:t(()=>[e(r,{span:12},{default:t(()=>[e(a,{label:"ID"},{default:t(()=>[m(p(o.formData.id),1)]),_:1})]),_:1}),e(r,{span:12},{default:t(()=>[e(a,{label:"通知类型"},{default:t(()=>[m(p(o.proxy.getOptionValue(o.formData.type,o.typeOptions,"value","label")),1)]),_:1})]),_:1}),e(r,{span:12},{default:t(()=>[e(a,{label:"内容"},{default:t(()=>[m(p(o.formData.content),1)]),_:1})]),_:1}),e(r,{span:12},{default:t(()=>[e(a,{label:"状态"},{default:t(()=>[m(p(o.formData.status),1)]),_:1})]),_:1}),e(r,{span:12},{default:t(()=>[e(a,{label:"创建日期"},{default:t(()=>[m(p(o.proxy.parseTime(o.formData.createdAt,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1})]),_:1}),e(r,{span:12},{default:t(()=>[e(a,{label:"会员ID"},{default:t(()=>[m(p(o.formData.memberId),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}const At=C(N,[["render",S],["__scopeId","data-v-d1fd6bde"]]);export{At as default};
