import request from '/@/utils/request'
// 查询栏目审核设置列表
export function listCmsAuditCategory(query:object) {
  return request({
    url: '/api/v1/cms/audit/cmsAuditCategory/list',
    method: 'get',
    params: query
  })
}
// 查询栏目审核设置详细
export function getCmsAuditCategory(id:number) {
  return request({
    url: '/api/v1/cms/audit/cmsAuditCategory/get',
    method: 'get',
    params: {
      id: id.toString()
    }
  })
}
// 新增栏目审核设置
export function addCmsAuditCategory(data:object) {
  return request({
    url: '/api/v1/cms/audit/cmsAuditCategory/add',
    method: 'post',
    data: data
  })
}
// 修改栏目审核设置
export function updateCmsAuditCategory(data:object) {
  return request({
    url: '/api/v1/cms/audit/cmsAuditCategory/edit',
    method: 'put',
    data: data
  })
}
// 删除栏目审核设置
export function delCmsAuditCategory(ids:number[]) {
  return request({
    url: '/api/v1/cms/audit/cmsAuditCategory/delete',
    method: 'delete',
    data:{
      ids:ids
    }
  })
}
