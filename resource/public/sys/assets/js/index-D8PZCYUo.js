import{g as A,a as B}from"./index-BGmsw1a8.js";import{_ as C}from"./lodash-BPJNOONf.js";import{a as f}from"./element-plus-CUmVNDWO.js";import{d as F,r as c,f as z,X as _,k as E,ag as d,c as U,o as g,P as s,H as u,G as T}from"./@vue-C21YZbHS.js";const $=F({name:"uploadImg",props:{action:{type:String,default:""},name:{type:String,default:"file"},limit:{type:Number,default:1},method:{type:String,default:"post"},multiple:{type:Boolean,default:!0},showFileList:{type:Boolean,default:!0},drag:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},listType:{type:String,default:"picture-card"},uploadSize:{type:Number,default:10},modelValue:{type:Array,default:function(){return[]}}},emits:["update:modelValue"],setup(e,{emit:o}){const v=c(),b="http://***************:8808/",{proxy:h}=z(),y=c(""),n=c(!1),r=c(""),m=_({token:A()});let l=[];const p=E({get:()=>{let a=e.modelValue||[];return a.map(t=>(t.url&&(t.url=h.getUpFileUrl(t.url)),t)),l=C.cloneDeep(a),e.limit==1&&(l=[],r.value=a[0]?a[0].url:""),a},set:a=>{o("update:modelValue",a)}}),i=a=>a.type.substring(0,5)!=="image"?(f.error("请上传图片文件"),!1):a.size/1024/1024>e.uploadSize?(f.error("上传文件超过"+e.uploadSize+"M"),!1):!0,I=a=>{l.splice(l.findIndex(t=>t.name===a.name),1),w()},V=()=>{f.error("最多可上传"+e.limit+"个文件,已超出最大限制数。")},k=a=>{y.value=a.url,n.value=!0},S=(a,t)=>{e.limit==1&&(l=[],r.value=URL.createObjectURL(t.raw)),l=l.filter(R=>{var L,P;return((L=R.raw)==null?void 0:L.uid)!=((P=t.raw)==null?void 0:P.uid)}),a.code===0?l.push({name:a.data.name,url:a.data.path,fileType:a.data.type,size:a.data.size}):f.error(a.message),w()},w=()=>{p.value=l};return{upImageRef:v,dataFileList:p,imageUrl:r,baseURL:b,dialogVisible:n,dialogImageUrl:y,handleExceed:V,beforeAvatarUpload:i,handleRemove:I,handlePictureCardPreview:k,handleAvatarSuccess:S,stopUpImage:()=>{v.value.abort()},dataParam:m}}}),N={key:0,class:"up-img"},D={key:1,class:"up-img"},M=["src"];function j(e,o,v,b,h,y){const n=d("ele-Plus"),r=d("el-icon"),m=d("el-upload"),l=d("el-image"),p=d("el-dialog");return e.limit>1?(g(),U("div",N,[s(m,{"file-list":e.dataFileList,"onUpdate:fileList":o[0]||(o[0]=i=>e.dataFileList=i),limit:e.limit,action:e.action,multiple:e.multiple,"list-type":e.listType,"on-success":e.handleAvatarSuccess,"on-preview":e.handlePictureCardPreview,"on-remove":e.handleRemove,"on-exceed":e.handleExceed,"before-upload":e.beforeAvatarUpload,data:e.dataParam,ref:"upImageRef"},{default:u(()=>[s(r,null,{default:u(()=>[s(n)]),_:1})]),_:1},8,["file-list","limit","action","multiple","list-type","on-success","on-preview","on-remove","on-exceed","before-upload","data"]),s(p,{modelValue:e.dialogVisible,"onUpdate:modelValue":o[1]||(o[1]=i=>e.dialogVisible=i)},{default:u(()=>[s(l,{src:e.dialogImageUrl,fit:"contain"},null,8,["src"])]),_:1},8,["modelValue"])])):(g(),U("div",D,[s(m,{"file-list":e.dataFileList,"onUpdate:fileList":o[2]||(o[2]=i=>e.dataFileList=i),class:"avatar-uploader",action:e.action,"show-file-list":!1,"on-success":e.handleAvatarSuccess,"before-upload":e.beforeAvatarUpload,"on-preview":e.handlePictureCardPreview,"on-remove":e.handleRemove,data:e.dataParam},{default:u(()=>[e.imageUrl?(g(),U("img",{key:0,src:e.imageUrl,class:"avatar"},null,8,M)):(g(),T(r,{key:1,class:"avatar-uploader-icon"},{default:u(()=>[s(n)]),_:1}))]),_:1},8,["file-list","action","on-success","before-upload","on-preview","on-remove","data"])]))}const J=B($,[["render",j],["__scopeId","data-v-fd061693"]]);export{J as u};
