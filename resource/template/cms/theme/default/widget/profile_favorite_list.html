<div class="profile-favorite-list">
    <div id="favorite_list_container"></div>
    <div id="favorite_pagination">
    </div>
</div>
<script id="favorite-list" type="text/html">
    <%if(list){%>
    <ul class="comments-list">
        <% for(var i=0;i < list.length;i++){ %>
        <li class="comment byuser odd alt thread-odd thread-alt depth-1 parent">
            <div class="comment-body">
                <span class="comment-time" style="float:right;"><%=list[i].article.publishedAt%></span>
                <a href="${.cmsConfig.site_url}/show/<%=list[i].article.id%>" title="<%=list[i].article.title%>"><%=list[i].article.title%></a>
            </div>
        </li>
        <% } %>
    </ul>
    <%}else{%>
    <div class="empty-content-empty">没有数据</div>
    <%}%>
</script>
<script id="favorite-pagination-template" type="text/html">
    <ul class="pagination" >
        <li class="total">共<%=total%>条</li>
        <% if(page > 1){ %>
        <li class="prev">
            <a href="javascript:void(0)" onclick="getFavoriteList(1)" class="page-numbers"><i class="iconfont icon-left"></i></a>
        </li>
        <%}%>
        <% for(var i=1;i <= totalPage; i++) { %>
        <li class="<%=page===i?'active':''%>">
            <a href="javascript:void(0)" onclick="getFavoriteList(<%=i%>)" class="page-numbers"><%=i%></a>
        </li>
        <% } %>
        <% if(page < totalPage) { %>
        <li class="next">
            <a href="javascript:void(0)" onclick="getFavoriteList(<%=totalPage%>)" class="page-numbers"><i class="iconfont icon-right"></i></a>
        </li>
        <%}%>
    </ul>
</script>
<script>
    var totalFavoritePage = 0;
    var favoritePageSize = 10;
    var totalFavorite = 0
    function getFavoriteList(page){
        $.get(APIBASE+"home/favorite/list",{pageSize:favoritePageSize,pageNum:page,memberId:${.memberInfo.Id}},function (res) {
            if(res.code === 0){
                totalFavoritePage = Math.ceil(res.data.total/favoritePageSize)
                totalFavorite = res.data.total
                $("#favorite_list_container").html(template("favorite-list", res.data))
                if(res.data.total > favoritePageSize) {
                    $("#favorite_pagination").html(template("favorite-pagination-template",{page:res.data.currentPage,totalPage:totalFavoritePage,total:totalFavorite}))
                }
            }
        })
    }
    $(function (){
        getFavoriteList(1)
    })
</script>