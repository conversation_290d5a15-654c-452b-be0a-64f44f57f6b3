import{s as m,a as R}from"./index-BGmsw1a8.js";import{a as b}from"./element-plus-CUmVNDWO.js";import{d as B,r as y,X as M,ad as j,ag as i,aq as A,c as D,o as S,P as s,H as r,u as e,a as O,L as _,M as h,I as T,t as F}from"./@vue-C21YZbHS.js";function ee(n){return m({url:"/api/v1/system/post/list",method:"get",params:n})}function H(n){return m({url:"/api/v1/system/post/add",method:"post",data:n})}function W(n){return m({url:"/api/v1/system/post/edit",method:"put",data:n})}function te(n){return m({url:"/api/v1/system/post/delete",method:"delete",data:{ids:n}})}const X={class:"system-edit-post-container"},$={class:"dialog-footer"},G=B({name:"systemEditPost",__name:"editPost",emits:["getPostList"],setup(n,{expose:C,emit:P}){const g=P,v=y(null);y();const o=M({loading:!1,isShowDialog:!1,formData:{postId:0,postCode:"",postName:"",postSort:0,status:1,remark:""},rules:{postName:[{required:!0,message:"岗位名称不能为空",trigger:"blur"}],postCode:[{required:!0,message:"岗位编码不能为空",trigger:"blur"}],postSort:[{required:!0,message:"岗位顺序不能为空",trigger:"blur"}]},menuExpand:!1,menuNodeAll:!1,menuCheckStrictly:!1,menuProps:{children:"children",label:"title"}}),{isShowDialog:p,formData:l,loading:x,rules:k}=j(o);C({openDialog:d=>{E(),d&&(o.formData=d),o.isShowDialog=!0}});const f=()=>{o.isShowDialog=!1},N=()=>{f()},w=()=>{const d=e(v);d&&d.validate(t=>{t&&(o.loading=!0,o.formData.postId===0?H(o.formData).then(()=>{b.success("岗位添加成功"),f(),g("getPostList")}).finally(()=>{o.loading=!1}):W(o.formData).then(()=>{b.success("岗位修改成功"),f(),g("getPostList")}).finally(()=>{o.loading=!1}))})},E=()=>{o.menuCheckStrictly=!1,o.menuExpand=!1,o.menuNodeAll=!1,o.formData={postId:0,postCode:"",postName:"",postSort:0,status:1,remark:""}};return(d,t)=>{const c=i("el-input"),u=i("el-form-item"),I=i("el-input-number"),U=i("el-switch"),L=i("el-form"),V=i("el-button"),q=i("el-dialog"),z=A("drag");return S(),D("div",X,[s(q,{modelValue:e(p),"onUpdate:modelValue":t[5]||(t[5]=a=>F(p)?p.value=a:null),width:"769px"},{header:r(()=>[T((S(),D("div",null,[_(h((e(l).postId===0?"添加":"修改")+"岗位"),1)])),[[z,[".system-edit-post-container .el-dialog",".system-edit-post-container .el-dialog__header"]]])]),footer:r(()=>[O("span",$,[s(V,{onClick:N,size:"default"},{default:r(()=>t[6]||(t[6]=[_("取 消")])),_:1}),s(V,{type:"primary",onClick:w,size:"default",loading:e(x)},{default:r(()=>[_(h(e(l).postId===0?"新 增":"修 改"),1)]),_:1},8,["loading"])])]),default:r(()=>[s(L,{ref_key:"formRef",ref:v,model:e(l),rules:e(k),size:"default","label-width":"90px"},{default:r(()=>[s(u,{label:"岗位名称",prop:"postName"},{default:r(()=>[s(c,{modelValue:e(l).postName,"onUpdate:modelValue":t[0]||(t[0]=a=>e(l).postName=a),placeholder:"请输入岗位名称"},null,8,["modelValue"])]),_:1}),s(u,{label:"岗位编码",prop:"postCode"},{default:r(()=>[s(c,{modelValue:e(l).postCode,"onUpdate:modelValue":t[1]||(t[1]=a=>e(l).postCode=a),placeholder:"请输入编码名称"},null,8,["modelValue"])]),_:1}),s(u,{label:"岗位顺序",prop:"postSort"},{default:r(()=>[s(I,{modelValue:e(l).postSort,"onUpdate:modelValue":t[2]||(t[2]=a=>e(l).postSort=a),"controls-position":"right",min:0},null,8,["modelValue"])]),_:1}),s(u,{label:"岗位状态",prop:"status"},{default:r(()=>[s(U,{modelValue:e(l).status,"onUpdate:modelValue":t[3]||(t[3]=a=>e(l).status=a),"active-value":1,"inactive-value":0,"inline-prompt":"","active-text":"启","inactive-text":"禁"},null,8,["modelValue"])]),_:1}),s(u,{label:"备注",prop:"remark"},{default:r(()=>[s(c,{modelValue:e(l).remark,"onUpdate:modelValue":t[4]||(t[4]=a=>e(l).remark=a),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}}}),J=R(G,[["__scopeId","data-v-8c5c77b0"]]),oe=Object.freeze(Object.defineProperty({__proto__:null,default:J},Symbol.toStringTag,{value:"Module"}));export{J as E,te as d,oe as e,ee as g};
