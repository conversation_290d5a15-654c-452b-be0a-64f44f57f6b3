import{d as re,f as ue,r as E,X as ie,ad as pe,ag as m,c as I,o as N,P as l,H as t,u as o,I as b,a as H,J as fe,M as C,L as r,Q as R,t as c}from"./@vue-C21YZbHS.js";import{a as ge,b as _e,e as ce,c as ye}from"./index-BvZSjrda.js";import{p as xe,a as ke}from"./index-BGmsw1a8.js";import{a as Y}from"./element-plus-CUmVNDWO.js";import"./pinia-DwJf82dV.js";import"./vue-router-BDT9dCQ7.js";import"./js-cookie-Cz0CWeBA.js";import"./@element-plus-_Cc-TEQX.js";import"./nprogress-Scw6VIZr.js";import"./@intlify-D8kAWvSi.js";import"./axios-B62VicFi.js";import"./qs-D41HS0uK.js";import"./side-channel-DytTmCZg.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-DbsE5JW_.js";import"./crypto-js-oM26lpET.js";import"./side-channel-list-C_7Uk4RP.js";import"./side-channel-map-BUokNT8A.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-bSkmVQJL.js";import"./vue-i18n-BkLk9Ti1.js";import"./vue-iNFchrNl.js";import"./vue-clipboard3-Dr8V5xR7.js";import"./clipboard-DzJOpNE6.js";import"./mitt-DJ65BbbF.js";import"./vue-grid-layout-CIPjP0XN.js";import"./vue-simple-uploader-j6sX1TKo.js";import"./vue-ueditor-wrap-CJ5gjsd3.js";import"./lodash-es-S0Y0Up6J.js";import"./@vueuse-D_5IXmcI.js";import"./@popperjs-D_chPuIy.js";import"./@ctrl-r5W6hzzQ.js";import"./dayjs-CepoAVPu.js";import"./async-validator-9PlIezaS.js";import"./memoize-one-BdPwpGay.js";import"./normalize-wheel-es-BQoi3Ox2.js";import"./@floating-ui-DwceP2Gb.js";const Ve={class:"system-edit-role-container"},ve={key:0},De={class:"dialog-footer"},be=re({name:"systemEditRole",__name:"editRole",props:{roleData:{type:Array,default:()=>[]}},emits:["getRoleList"],setup(A,{expose:L,emit:K}){const w=K,{proxy:P}=ue(),S=E(null),g=E(),n=ie({loading:!1,isShowDialog:!1,formData:{id:0,pid:0,name:"",status:1,listOrder:0,remark:"",menuIds:[],effectiveType:0,weekDay:[1,2,3,4,5],dayRange:["2024-02-01 08:00:00","2024-02-01 19:00:00"],dateRange:[]},rules:{name:[{required:!0,message:"角色名称不能为空",trigger:"blur"}]},menuData:[],menuExpand:!1,menuNodeAll:!1,menuCheckStrictly:!1,menuProps:{children:"children",label:"title",disabled:"disabled"}}),B=s=>{F(),q(),s&&ge(s.id).then(e=>{e.data.role&&(n.formData=e.data.role,n.formData.weekDay||(n.formData.weekDay=[1,2,3,4,5]),n.formData.menuIds=e.data.menuIds??[])}),n.isShowDialog=!0},y=()=>{n.isShowDialog=!1},O=()=>{y()},z=()=>{const s=o(S);s&&s.validate(e=>{e&&(n.loading=!0,n.formData.menuIds=$(),n.formData.id===0?_e(n.formData).then(()=>{Y.success("角色添加成功"),y(),v(),w("getRoleList")}).finally(()=>{n.loading=!1}):ce(n.formData).then(()=>{Y.success("角色修改成功"),y(),v(),w("getRoleList")}).finally(()=>{n.loading=!1}))})},q=()=>{ye().then(s=>{const e=s.data.menu??[],f=s.data.accessMenus??[];e.map(u=>{u.disabled=!f.includes(u.id)}),n.menuData=P.handleTree(e,"id","pid")})},F=()=>{n.menuCheckStrictly=!1,n.menuExpand=!1,n.menuNodeAll=!1,n.formData={id:0,pid:0,name:"",status:1,listOrder:0,remark:"",menuIds:[],effectiveType:0,weekDay:[1,2,3,4,5],dayRange:["2024-02-01 08:00:00","2024-02-01 19:00:00"],dateRange:[]}},{formData:d,menuData:J,menuExpand:x,menuNodeAll:k,menuCheckStrictly:_,menuProps:Q,rules:W,loading:X,isShowDialog:V}=pe(n),j=s=>{let e=n.menuData;for(let f=0;f<e.length;f++)g.value.store.nodesMap[e[f].id].expanded=s},G=s=>{g.value.setCheckedNodes(s?n.menuData:[])},Z=s=>{n.menuCheckStrictly=!!s};function $(){let s=g.value.getCheckedKeys(),e=g.value.getHalfCheckedKeys();return s.unshift.apply(s,e),s}const v=()=>{xe()};return L({openDialog:B,resetMenuSession:v}),(s,e)=>{const f=m("el-cascader"),u=m("el-form-item"),i=m("el-col"),U=m("el-input"),ee=m("el-input-number"),le=m("el-switch"),D=m("el-radio"),te=m("el-radio-group"),p=m("el-checkbox"),oe=m("el-checkbox-group"),ae=m("el-time-picker"),de=m("el-date-picker"),ne=m("el-tree"),M=m("el-row"),se=m("el-form"),T=m("el-button"),me=m("el-dialog");return N(),I("div",Ve,[l(me,{title:(o(d).id===0?"添加":"修改")+"角色",modelValue:o(V),"onUpdate:modelValue":e[15]||(e[15]=a=>c(V)?V.value=a:null),width:"769px"},{footer:t(()=>[H("span",De,[l(T,{onClick:O,size:"default"},{default:t(()=>e[29]||(e[29]=[r("取 消")])),_:1}),l(T,{type:"primary",onClick:z,size:"default",loading:o(X)},{default:t(()=>[r(C(o(d).id===0?"新 增":"修 改"),1)]),_:1},8,["loading"])])]),default:t(()=>[l(se,{ref_key:"formRef",ref:S,model:o(d),rules:o(W),size:"default","label-width":"90px"},{default:t(()=>[l(M,{gutter:35},{default:t(()=>[l(i,{xs:24,sm:24,md:24,lg:24,xl:24},{default:t(()=>[l(u,{label:"上级角色"},{default:t(()=>[l(f,{options:A.roleData,props:{checkStrictly:!0,emitPath:!1,value:"id",label:"name"},placeholder:"请选择上级",clearable:"",class:"w100",modelValue:o(d).pid,"onUpdate:modelValue":e[0]||(e[0]=a=>o(d).pid=a)},{default:t(({node:a,data:h})=>[H("span",null,C(h.name),1),a.isLeaf?fe("",!0):(N(),I("span",ve," ("+C(h.children.length)+") ",1))]),_:1},8,["options","modelValue"])]),_:1})]),_:1}),l(i,{xs:24,sm:12,md:12,lg:12,xl:12},{default:t(()=>[l(u,{label:"角色名称",prop:"name"},{default:t(()=>[l(U,{modelValue:o(d).name,"onUpdate:modelValue":e[1]||(e[1]=a=>o(d).name=a),placeholder:"请输入角色名称",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),l(i,{xs:24,sm:12,md:12,lg:12,xl:12},{default:t(()=>[l(u,{label:"排序"},{default:t(()=>[l(ee,{modelValue:o(d).listOrder,"onUpdate:modelValue":e[2]||(e[2]=a=>o(d).listOrder=a),min:0,"controls-position":"right",placeholder:"请输入排序",class:"w100"},null,8,["modelValue"])]),_:1})]),_:1}),l(i,{xs:24,sm:12,md:12,lg:12,xl:12},{default:t(()=>[l(u,{label:"角色状态"},{default:t(()=>[l(le,{modelValue:o(d).status,"onUpdate:modelValue":e[3]||(e[3]=a=>o(d).status=a),"active-value":1,"inactive-value":0,"inline-prompt":"","active-text":"启","inactive-text":"禁"},null,8,["modelValue"])]),_:1})]),_:1}),l(i,{xs:24,sm:24,md:24,lg:24,xl:24},{default:t(()=>[l(u,{label:"角色描述"},{default:t(()=>[l(U,{modelValue:o(d).remark,"onUpdate:modelValue":e[4]||(e[4]=a=>o(d).remark=a),type:"textarea",placeholder:"请输入角色描述",maxlength:"150"},null,8,["modelValue"])]),_:1})]),_:1}),l(i,{xs:24,sm:24,md:24,lg:24,xl:24},{default:t(()=>[l(u,{label:"有效时间"},{default:t(()=>[l(te,{modelValue:o(d).effectiveType,"onUpdate:modelValue":e[5]||(e[5]=a=>o(d).effectiveType=a)},{default:t(()=>[l(D,{value:0},{default:t(()=>e[16]||(e[16]=[r("不设置")])),_:1}),l(D,{value:1},{default:t(()=>e[17]||(e[17]=[r("按起止日期")])),_:1}),l(D,{value:2},{default:t(()=>e[18]||(e[18]=[r("按时间段")])),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}),b(l(i,{xs:24,sm:24,md:24,lg:24,xl:24},{default:t(()=>[l(u,{label:"每周"},{default:t(()=>[l(oe,{modelValue:o(d).weekDay,"onUpdate:modelValue":e[6]||(e[6]=a=>o(d).weekDay=a)},{default:t(()=>[l(p,{value:1},{default:t(()=>e[19]||(e[19]=[r("周一")])),_:1}),l(p,{value:2},{default:t(()=>e[20]||(e[20]=[r("周二")])),_:1}),l(p,{value:3},{default:t(()=>e[21]||(e[21]=[r("周三")])),_:1}),l(p,{value:4},{default:t(()=>e[22]||(e[22]=[r("周四")])),_:1}),l(p,{value:5},{default:t(()=>e[23]||(e[23]=[r("周五")])),_:1}),l(p,{value:6},{default:t(()=>e[24]||(e[24]=[r("周六")])),_:1}),l(p,{value:0},{default:t(()=>e[25]||(e[25]=[r("周日")])),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},512),[[R,o(d).effectiveType===2]]),b(l(i,{xs:24,sm:24,md:24,lg:24,xl:24},{default:t(()=>[l(u,{label:"时间段",style:{width:"360px"}},{default:t(()=>[l(ae,{modelValue:o(d).dayRange,"onUpdate:modelValue":e[7]||(e[7]=a=>o(d).dayRange=a),"is-range":"",format:"HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"截止时间"},null,8,["modelValue"])]),_:1})]),_:1},512),[[R,o(d).effectiveType===2]]),b(l(i,{xs:24,sm:24,md:24,lg:24,xl:24},{default:t(()=>[l(u,{label:"起止日期",style:{width:"450px"}},{default:t(()=>[l(de,{modelValue:o(d).dateRange,"onUpdate:modelValue":e[8]||(e[8]=a=>o(d).dateRange=a),"value-format":"YYYY-MM-DD HH:mm:ss",type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},null,8,["modelValue"])]),_:1})]),_:1},512),[[R,o(d).effectiveType===1]]),l(i,{xs:24,sm:24,md:24,lg:24,xl:24},{default:t(()=>[l(u,{label:"菜单权限"},{default:t(()=>[l(M,{gutter:35},{default:t(()=>[l(i,{xs:24,sm:24,md:24,lg:24,xl:24},{default:t(()=>[l(p,{modelValue:o(x),"onUpdate:modelValue":e[9]||(e[9]=a=>c(x)?x.value=a:null),onChange:e[10]||(e[10]=a=>j(a))},{default:t(()=>e[26]||(e[26]=[r("展开/折叠")])),_:1},8,["modelValue"]),l(p,{modelValue:o(k),"onUpdate:modelValue":e[11]||(e[11]=a=>c(k)?k.value=a:null),onChange:e[12]||(e[12]=a=>G(a))},{default:t(()=>e[27]||(e[27]=[r("全选/全不选")])),_:1},8,["modelValue"]),l(p,{modelValue:o(_),"onUpdate:modelValue":e[13]||(e[13]=a=>c(_)?_.value=a:null),onChange:e[14]||(e[14]=a=>Z(a))},{default:t(()=>e[28]||(e[28]=[r("父子联动")])),_:1},8,["modelValue"])]),_:1}),l(i,{xs:24,sm:24,md:24,lg:24,xl:24},{default:t(()=>[l(ne,{data:o(J),ref_key:"menuRef",ref:g,props:o(Q),"default-checked-keys":o(d).menuIds,"node-key":"id","show-checkbox":"",class:"menu-data-tree tree-border","check-strictly":!o(_)},null,8,["data","props","default-checked-keys","check-strictly"])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}}),xl=ke(be,[["__scopeId","data-v-890dd55d"]]);export{xl as default};
