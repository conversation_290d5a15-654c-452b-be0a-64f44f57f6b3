import{s as Z}from"./index-xkwPUaHp.js";import{g as ee,a as te,u as ae}from"./cmsAuditCategory-BBJz2Kd5.js";import{l as oe}from"./cmsAuditStep-w5STeaQC.js";import{l as re}from"./cmsCategory-MP-V5q2B.js";import{a as S}from"./element-plus-CUmVNDWO.js";import{d as se,f as ie,r as s,X as le,ad as de,i as D,ag as p,aq as ne,c as h,o as c,P as m,H as i,u as r,G as w,O as me,a6 as ue,M as k,a as E,L as x,I as pe,t as ce}from"./@vue-C21YZbHS.js";import{a as fe}from"./index-BGmsw1a8.js";import"./@element-plus-_Cc-TEQX.js";import"./index-BrYF22oA.js";import"./lodash-es-S0Y0Up6J.js";import"./@vueuse-D_5IXmcI.js";import"./@popperjs-D_chPuIy.js";import"./@ctrl-r5W6hzzQ.js";import"./dayjs-CepoAVPu.js";import"./@intlify-D8kAWvSi.js";import"./async-validator-9PlIezaS.js";import"./memoize-one-BdPwpGay.js";import"./normalize-wheel-es-BQoi3Ox2.js";import"./@floating-ui-DwceP2Gb.js";import"./pinia-DwJf82dV.js";import"./vue-router-BDT9dCQ7.js";import"./js-cookie-Cz0CWeBA.js";import"./nprogress-Scw6VIZr.js";import"./axios-B62VicFi.js";import"./qs-D41HS0uK.js";import"./side-channel-DytTmCZg.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-DbsE5JW_.js";import"./crypto-js-oM26lpET.js";import"./side-channel-list-C_7Uk4RP.js";import"./side-channel-map-BUokNT8A.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-bSkmVQJL.js";import"./vue-i18n-BkLk9Ti1.js";import"./vue-iNFchrNl.js";import"./vue-clipboard3-Dr8V5xR7.js";import"./clipboard-DzJOpNE6.js";import"./mitt-DJ65BbbF.js";import"./vue-grid-layout-CIPjP0XN.js";import"./vue-simple-uploader-j6sX1TKo.js";import"./vue-ueditor-wrap-CJ5gjsd3.js";const ge={class:"audit-cmsAuditCategory-edit"},ve={key:1,class:"readonly-field"},_e={class:"readonly-field"},ye={class:"dialog-footer"},De=se({name:"ApiV1CmsAuditCmsAuditCategoryEdit",__name:"edit",emits:["cmsAuditCategoryList"],setup(he,{expose:$,emit:U}){const M=U,{proxy:q}=ie(),V=s(null);s();const u=s([]),g=s(!1),v=s(new Map),_=s([]),I=s(!1),y=s(new Map),o=le({loading:!1,isShowDialog:!1,formData:{id:void 0,userIds:"",stepId:void 0,step:void 0,categoryId:void 0},rules:{userIds:[{required:!0,message:"审核人员不能为空",trigger:"blur"}],stepId:[{required:!0,message:"审核步骤不能为空",trigger:"change"}],categoryId:[{required:!0,message:"栏目不能为空",trigger:"change"}]}}),{loading:B,isShowDialog:C,formData:d,rules:R}=de(o),f=s([]);D(()=>f.value,e=>{e&&e.length>0?o.formData.userIds=e.join(","):o.formData.userIds=""},{deep:!0}),D(()=>o.formData.userIds,e=>{e&&e.length>0?f.value=e.split(",").map(t=>parseInt(t)):f.value=[]},{immediate:!0}),D(()=>o.formData.stepId,e=>{if(e){const t=u.value.find(a=>a.value===e);t&&(o.formData.step=t.step)}else o.formData.step=void 0}),D(()=>_.value,e=>{if(e&&e.length>0&&o.formData.categoryId){const t=o.formData.categoryId;o.formData.categoryId=void 0,setTimeout(()=>{o.formData.categoryId=t},0)}},{deep:!0});const T=e=>{if(!e)return"";if(v.value.has(e))return v.value.get(e);const t=u.value.find(a=>a.value===e);return t?(v.value.set(e,t.label),t.label):`步骤ID: ${e}`},L=()=>{g.value=!0,oe({pageNum:1,pageSize:1e3}).then(e=>{e.code===0&&e.data&&e.data.list&&(u.value=e.data.list.map(t=>{const a=`${t.title} (第${t.step}审)`;return v.value.set(t.id,a),{value:t.id,label:a,step:t.step}})),g.value=!1}).catch(()=>{g.value=!1})},z=e=>{e&&u.value.length===0&&L()},b=s(new Map),F=e=>e?b.value.has(e)?b.value.get(e):y.value.has(e)?y.value.get(e):`分类ID: ${e}`:"",N=(e,t="")=>{e.forEach(a=>{y.value.set(a.id,a.name);const n=t?`${t} / ${a.name}`:a.name;b.value.set(a.id,n),a.children&&a.children.length>0&&N(a.children,n)})},P=()=>{I.value=!0,re({pageNum:1,pageSize:1e3,status:1}).then(e=>{if(e.code===0&&e.data&&e.data.list){const t=e.data.list||[];if(t.forEach(a=>{a.type!=="list"&&(a.disabled=!0),y.value.set(a.id,a.name)}),_.value=q.handleTree(t,"id","parentId"),N(_.value),o.formData.categoryId){const a=o.formData.categoryId;o.formData.categoryId=void 0,setTimeout(()=>{o.formData.categoryId=a},0)}}I.value=!1}).catch(()=>{I.value=!1})},j=()=>{_.value.length===0&&P()},G=(e,t)=>{X(),t&&(o.formData.categoryId=t),u.value.length===0&&L(),j(),e&&ee(e.id).then(a=>{const n=a.data;o.formData=n,n.userIds?o.formData.userIds=n.userIds:o.formData.userIds=""}),o.isShowDialog=!0},A=()=>{o.isShowDialog=!1};$({openDialog:G});const H=()=>{A()},W=()=>{const e=r(V);e&&e.validate(t=>{if(t){if(!o.formData.categoryId||o.formData.categoryId===0){S.error("分类ID不能为0，请选择有效的分类");return}o.loading=!0;const a={...o.formData};!o.formData.id||o.formData.id===0?te(a).then(()=>{S.success("添加成功"),A(),M("cmsAuditCategoryList")}).finally(()=>{o.loading=!1}):ae(a).then(()=>{S.success("修改成功"),A(),M("cmsAuditCategoryList")}).finally(()=>{o.loading=!1})}})},X=()=>{o.formData={id:void 0,userIds:"",stepId:void 0,step:void 0,categoryId:void 0}};return(e,t)=>{const a=p("el-form-item"),n=p("el-option"),J=p("el-select"),K=p("el-form"),O=p("el-button"),Q=p("el-dialog"),Y=ne("drag");return c(),h("div",ge,[m(Q,{modelValue:r(C),"onUpdate:modelValue":t[2]||(t[2]=l=>ce(C)?C.value=l:null),width:"800px","close-on-click-modal":!1,"destroy-on-close":!0},{header:i(()=>[pe((c(),h("div",null,[x(k((!r(d).id||r(d).id==0?"添加":"修改")+"栏目审核设置"),1)])),[[Y,[".audit-cmsAuditCategory-edit .el-dialog",".audit-cmsAuditCategory-edit .el-dialog__header"]]])]),footer:i(()=>[E("div",ye,[m(O,{type:"primary",onClick:W,disabled:r(B)},{default:i(()=>t[3]||(t[3]=[x("确 定")])),_:1},8,["disabled"]),m(O,{onClick:H},{default:i(()=>t[4]||(t[4]=[x("取 消")])),_:1})])]),default:i(()=>[m(K,{ref_key:"formRef",ref:V,model:r(d),rules:r(R),"label-width":"120px"},{default:i(()=>[m(a,{label:"审核人员",prop:"userIds"},{default:i(()=>[m(Z,{modelValue:f.value,"onUpdate:modelValue":t[0]||(t[0]=l=>f.value=l)},null,8,["modelValue"])]),_:1}),m(a,{label:"审核步骤",prop:"stepId"},{default:i(()=>[r(d).id?(c(),h("div",ve,k(T(r(d).stepId)),1)):(c(),w(J,{key:0,modelValue:r(d).stepId,"onUpdate:modelValue":t[1]||(t[1]=l=>r(d).stepId=l),placeholder:"请选择审核步骤",style:{width:"100%"},loading:g.value,onVisibleChange:z},{default:i(()=>[(c(!0),h(me,null,ue(u.value,l=>(c(),w(n,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"]))]),_:1}),m(a,{label:"分类",prop:"categoryId"},{default:i(()=>[E("div",_e,k(F(r(d).categoryId)),1)]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}}}),Dt=fe(De,[["__scopeId","data-v-9e7552c0"]]);export{Dt as default};
