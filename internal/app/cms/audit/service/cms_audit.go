// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-05-07 08:21:11
// 生成路径: internal/app/cms/audit/service/cms_audit.go
// 生成人：zhanghui
// desc:文章审核
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/cms/audit/model"
)

type ICmsAudit interface {
	List(ctx context.Context, req *model.CmsAuditSearchReq) (res *model.CmsAuditSearchRes, err error)
	GetById(ctx context.Context, Id uint) (res *model.CmsAuditInfoRes, err error)
	Audit(ctx context.Context, req *model.CmsAuditEditReq) (err error)
}

var localCmsAudit ICmsAudit

func CmsAudit() ICmsAudit {
	if localCmsAudit == nil {
		panic("implement not found for interface ICmsAudit, forgot register?")
	}
	return localCmsAudit
}

func RegisterCmsAudit(i ICmsAudit) {
	localCmsAudit = i
}
