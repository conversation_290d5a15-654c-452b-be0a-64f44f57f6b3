import { ro } from 'element-plus/es/locale'
import request from '/@/utils/request'
// 查询文章分类列表
export function listCmsCategory(query:object) {
  return request({
    url: '/api/v1/cms/cmsCategory/list',
    method: 'get',
    params: query
  })
}
// 查询文章分类详细
export function getCmsCategory(id:number) {
  // 检查 id 是否为 undefined 或 null
  if (id === undefined || id === null) {
    console.error('getCmsCategory called with undefined or null id');
    // 返回一个被拒绝的 Promise
    return Promise.reject(new Error('id is required'));
  }

  return request({
    url: '/api/v1/cms/cmsCategory/get',
    method: 'get',
    params: {
      id: id.toString()
    }
  })
}
// 新增文章分类
export function addCmsCategory(data:object) {
  return request({
    url: '/api/v1/cms/cmsCategory/add',
    method: 'post',
    data: data
  })
}
// 修改文章分类
export function updateCmsCategory(data:object) {
  return request({
    url: '/api/v1/cms/cmsCategory/edit',
    method: 'put',
    data: data
  })
}
// 删除文章分类
export function delCmsCategory(ids:number[]) {
  return request({
    url: '/api/v1/cms/cmsCategory/delete',
    method: 'delete',
    data:{
      ids:ids
    }
  })
}

// 关联modules_info表选项
export function listModulesInfo(query:object){
   return request({
     url: '/api/v1/system/modulesInfo/list',
     method: 'get',
     params: query
   })
}

// 获取栏目权限
export function listCmsCategoryPermissions(query:object) {
  return request({
    url: '/api/v1/cms/cmsCategory/listPermissions',
    method: 'get',
    params: query
  })
}

// 保存栏目权限
export function saveCmsCategoryPermissions(data:object) {
  return request({
    url : 'api/v1/cms/cmsCategory/savePermissions',
    method: 'put',
    data: data
  })
}

// 判断分类权限
export function checkCmsCategoryPermission(data:object) {
  return request({
    url : 'api/v1/cms/cmsCategory/checkPermission',
    params: data,
    method: 'get',
  })
}

// 查询具有文章分类相应权限的列表
export function listCmsPerCategorys(query:object) {
  return request({
    url: '/api/v1/cms/cmsCategory/getPerCatetorys',
    method: 'get',
    params: query
  })
}