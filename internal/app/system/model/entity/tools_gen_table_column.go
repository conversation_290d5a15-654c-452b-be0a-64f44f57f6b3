// =================================================================================
// Code generated by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// ToolsGenTableColumn is the golang structure for table tools_gen_table_column.
type ToolsGenTableColumn struct {
	ColumnId              int64  `json:"columnId"              description:"编号"`
	TableId               int64  `json:"tableId"               description:"归属表编号"`
	ColumnName            string `json:"columnName"            description:"列名称"`
	ColumnComment         string `json:"columnComment"         description:"列描述"`
	ColumnType            string `json:"columnType"            description:"列类型"`
	GoType                string `json:"goType"                description:"Go类型"`
	TsType                string `json:"tsType"                description:"TS类型"`
	GoField               string `json:"goField"               description:"Go字段名"`
	HtmlField             string `json:"htmlField"             description:"html字段名"`
	IsPk                  bool   `json:"isPk"                  description:"是否主键（1是）"`
	IsIncrement           bool   `json:"isIncrement"           description:"是否自增（1是）"`
	IsRequired            bool   `json:"isRequired"            description:"是否必填（1是）"`
	IsEdit                bool   `json:"isEdit"                description:"是否编辑字段（1是）"`
	IsList                bool   `json:"isList"                description:"是否列表字段（1是）"`
	IsDetail              bool   `json:"isDetail"              description:"是否详情字段"`
	IsQuery               bool   `json:"isQuery"               description:"是否查询字段（1是）"`
	SortOrderEdit         int    `json:"sortOrderEdit"         description:"插入编辑显示顺序"`
	SortOrderList         int    `json:"sortOrderList"         description:"列表显示顺序"`
	SortOrderDetail       int    `json:"sortOrderDetail"       description:"详情显示顺序"`
	SortOrderQuery        int    `json:"sortOrderQuery"        description:"查询显示顺序"`
	QueryType             string `json:"queryType"             description:"查询方式（等于、不等于、大于、小于、范围）"`
	HtmlType              string `json:"htmlType"              description:"显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）"`
	DictType              string `json:"dictType"              description:"字典类型"`
	LinkTableName         string `json:"linkTableName"         description:"关联表名"`
	LinkTableClass        string `json:"linkTableClass"        description:"关联表类名"`
	LinkTableModuleName   string `json:"linkTableModuleName"   description:"关联表模块名"`
	LinkTableBusinessName string `json:"linkTableBusinessName" description:"关联表业务名"`
	LinkTablePackage      string `json:"linkTablePackage"      description:"关联表包名"`
	LinkLabelId           string `json:"linkLabelId"           description:"关联表键名"`
	LinkLabelName         string `json:"linkLabelName"         description:"关联表字段值"`
	ColSpan               int    `json:"colSpan"               description:"详情页占列数"`
	RowSpan               int    `json:"rowSpan"               description:"详情页占行数"`
	IsRowStart            bool   `json:"isRowStart"            description:"详情页为行首"`
	MinWidth              int    `json:"minWidth"              description:"表格最小宽度"`
	IsFixed               bool   `json:"isFixed"               description:"是否表格列左固定"`
	IsOverflowTooltip     bool   `json:"isOverflowTooltip"     description:"是否过长自动隐藏"`
	IsCascade             bool   `json:"isCascade"             description:"是否级联查询"`
	ParentColumnName      string `json:"parentColumnName"      description:"上级字段名"`
	CascadeColumnName     string `json:"cascadeColumnName"     description:"级联查询字段"`
}
