<template>
  <div class="cms-visit-container">
    <el-card shadow="hover" class="mb20">
      <div class="statistics-cards">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card shadow="hover" class="statistics-card">
              <template #header>
                <div class="card-header">
                  <span>总访问量</span>
                </div>
              </template>
              <div class="card-body">
                <div class="card-value">{{ visitStats.total || 0 }}</div>
                <div class="card-icon">
                  <el-icon><ele-View /></el-icon>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="statistics-card">
              <template #header>
                <div class="card-header">
                  <span>首页访问量</span>
                </div>
              </template>
              <div class="card-body">
                <div class="card-value">{{ visitStats.home || 0 }}</div>
                <div class="card-icon">
                  <el-icon><ele-House /></el-icon>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="statistics-card">
              <template #header>
                <div class="card-header">
                  <span>分类访问量</span>
                </div>
              </template>
              <div class="card-body">
                <div class="card-value">{{ visitStats.category || 0 }}</div>
                <div class="card-icon">
                  <el-icon><ele-Menu /></el-icon>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="statistics-card">
              <template #header>
                <div class="card-header">
                  <span>文章访问量</span>
                </div>
              </template>
              <div class="card-body">
                <div class="card-value">{{ visitStats.article || 0 }}</div>
                <div class="card-icon">
                  <el-icon><ele-Document /></el-icon>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-card>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, getCurrentInstance, defineProps, defineEmits, watch } from 'vue';
import { ListCmsVisit } from "/@/api/cms/cmsVisit";

defineOptions({ name: "cmsVisitStats" });

const props = defineProps({
  type: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:loading']);

const { proxy } = <any>getCurrentInstance();
const loading = ref(false);
const total = ref(0);
const tableData = ref([]);

// 查询参数
const queryParams = reactive({
});

// 访问统计数据
const visitStats = reactive({
  total: 0,
  home: 0,
  category: 0,
  article: 0
});

// 获取访问统计列表
const getList = () => {
  loading.value = true;
  emit('update:loading', true);

  // 设置查询类型
  if (props.type) {
    queryParams.type = props.type;
  } else {
    queryParams.type = undefined;
  }

  ListCmsVisit(queryParams).then((res: any) => {
    // 处理表格数据
    tableData.value = res.data.list || [];
    total.value = res.data.total || 0;

    // 更新统计数据
    if (res.data) {
      // 直接使用接口返回的统计数据
      visitStats.total = res.data.total || 0;
      visitStats.home = res.data.home || 0;
      visitStats.category = res.data.category || 0;
      visitStats.article = res.data.article || 0;
    }

    loading.value = false;
    emit('update:loading', false);
  }).catch(() => {
    loading.value = false;
    emit('update:loading', false);
  });
};

// 监听类型变化
watch(() => props.type, (newVal, oldVal) => {
  // 只在类型真正变化时才调用
  if (newVal !== oldVal) {
    // 重置分页
    queryParams.pageNum = 1;
    getList();
  }
}, { immediate: false });

// 不在组件挂载时自动调用getList，而是由父组件控制调用时机
onMounted(() => {
  // 仅在初始加载时调用一次
  if (props.type === '') {
    getList();
  }
});

// 暴露方法给父组件
defineExpose({
  getList
});
</script>

<style scoped lang="scss">
.cms-visit-container {
  .statistics-cards {
    margin-bottom: 20px;

    .statistics-card {
      height: 100%;

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .card-body {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;

        .card-value {
          font-size: 28px;
          font-weight: bold;
          color: #409EFF;
        }

        .card-icon {
          font-size: 40px;
          color: #909399;
          opacity: 0.7;
        }
      }
    }
  }
}
</style>
