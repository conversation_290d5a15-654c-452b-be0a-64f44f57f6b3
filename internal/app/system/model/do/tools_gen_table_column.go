// =================================================================================
// Code generated by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// ToolsGenTableColumn is the golang structure of table tools_gen_table_column for DAO operations like Where/Data.
type ToolsGenTableColumn struct {
	g.Meta                `orm:"table:tools_gen_table_column, do:true"`
	ColumnId              interface{} // 编号
	TableId               interface{} // 归属表编号
	ColumnName            interface{} // 列名称
	ColumnComment         interface{} // 列描述
	ColumnType            interface{} // 列类型
	GoType                interface{} // Go类型
	TsType                interface{} // TS类型
	GoField               interface{} // Go字段名
	HtmlField             interface{} // html字段名
	IsPk                  interface{} // 是否主键（1是）
	IsIncrement           interface{} // 是否自增（1是）
	IsRequired            interface{} // 是否必填（1是）
	IsEdit                interface{} // 是否编辑字段（1是）
	IsList                interface{} // 是否列表字段（1是）
	IsDetail              interface{} // 是否详情字段
	IsQuery               interface{} // 是否查询字段（1是）
	SortOrderEdit         interface{} // 插入编辑显示顺序
	SortOrderList         interface{} // 列表显示顺序
	SortOrderDetail       interface{} // 详情显示顺序
	SortOrderQuery        interface{} // 查询显示顺序
	QueryType             interface{} // 查询方式（等于、不等于、大于、小于、范围）
	HtmlType              interface{} // 显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）
	DictType              interface{} // 字典类型
	LinkTableName         interface{} // 关联表名
	LinkTableClass        interface{} // 关联表类名
	LinkTableModuleName   interface{} // 关联表模块名
	LinkTableBusinessName interface{} // 关联表业务名
	LinkTablePackage      interface{} // 关联表包名
	LinkLabelId           interface{} // 关联表键名
	LinkLabelName         interface{} // 关联表字段值
	ColSpan               interface{} // 详情页占列数
	RowSpan               interface{} // 详情页占行数
	IsRowStart            interface{} // 详情页为行首
	MinWidth              interface{} // 表格最小宽度
	IsFixed               interface{} // 是否表格列左固定
	IsOverflowTooltip     interface{} // 是否过长自动隐藏
	IsCascade             interface{} // 是否级联查询
	ParentColumnName      interface{} // 上级字段名
	CascadeColumnName     interface{} // 级联查询字段
}
