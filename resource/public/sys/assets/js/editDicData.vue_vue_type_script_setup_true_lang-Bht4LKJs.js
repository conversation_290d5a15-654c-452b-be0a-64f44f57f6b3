import{Y as q,Z as z,$ as B}from"./index-BGmsw1a8.js";import{a as D}from"./element-plus-CUmVNDWO.js";import{d as E,r as N,X as M,ad as H,ag as r,c as P,o as W,P as t,H as d,u as l,L as m,a as X,M as Y,t as Z}from"./@vue-C21YZbHS.js";const $={class:"system-edit-dic-container"},j={class:"dialog-footer"},K=E({name:"systemEditDicData",__name:"editDicData",props:{dictType:{type:String,default:""}},emits:["dataList"],setup(y,{expose:v,emit:S}){const f=y,_=S,V=N(null),i=M({isShowDialog:!1,ruleForm:{dictCode:0,dictLabel:"",dictValue:"",dictSort:0,isDefault:0,status:1,remark:"",dictType:f.dictType},rules:{dictLabel:[{required:!0,message:"数据标签不能为空",trigger:"blur"}],dictValue:[{required:!0,message:"数据键值不能为空",trigger:"blur"}],dictSort:[{required:!0,message:"数据顺序不能为空",trigger:"blur"}]}}),{isShowDialog:p,ruleForm:o,rules:C}=H(i);v({openDialog:s=>{k(),s&&(q(s.dictCode).then(e=>{i.ruleForm=e.data.dict}),i.ruleForm=s),i.isShowDialog=!0}});const k=()=>{i.ruleForm={dictCode:0,dictLabel:"",dictValue:"",dictSort:0,isDefault:0,status:1,remark:"",dictType:f.dictType}},c=()=>{i.isShowDialog=!1},x=()=>{c()},L=()=>{const s=l(V);s&&s.validate(e=>{e&&(i.ruleForm.dictCode!==0?z(i.ruleForm).then(()=>{D.success("字典数据修改成功"),c(),_("dataList")}):B(i.ruleForm).then(()=>{D.success("字典数据添加成功"),c(),_("dataList")}))})};return(s,e)=>{const n=r("el-input"),u=r("el-form-item"),F=r("el-input-number"),T=r("el-switch"),g=r("el-radio"),U=r("el-radio-group"),w=r("el-form"),b=r("el-button"),R=r("el-dialog");return W(),P("div",$,[t(R,{title:(l(o).dictCode!==0?"修改":"添加")+"字典",modelValue:l(p),"onUpdate:modelValue":e[7]||(e[7]=a=>Z(p)?p.value=a:null),width:"769px"},{footer:d(()=>[X("span",j,[t(b,{onClick:x,size:"default"},{default:d(()=>e[10]||(e[10]=[m("取 消")])),_:1}),t(b,{type:"primary",onClick:L,size:"default"},{default:d(()=>[m(Y(l(o).dictCode!==0?"修 改":"添 加"),1)]),_:1})])]),default:d(()=>[t(w,{model:l(o),ref_key:"formRef",ref:V,rules:l(C),size:"default","label-width":"90px"},{default:d(()=>[t(u,{label:"字典类型"},{default:d(()=>[t(n,{modelValue:l(o).dictType,"onUpdate:modelValue":e[0]||(e[0]=a=>l(o).dictType=a),disabled:!0},null,8,["modelValue"])]),_:1}),t(u,{label:"数据标签",prop:"dictLabel"},{default:d(()=>[t(n,{modelValue:l(o).dictLabel,"onUpdate:modelValue":e[1]||(e[1]=a=>l(o).dictLabel=a),placeholder:"请输入数据标签"},null,8,["modelValue"])]),_:1}),t(u,{label:"数据键值",prop:"dictValue"},{default:d(()=>[t(n,{modelValue:l(o).dictValue,"onUpdate:modelValue":e[2]||(e[2]=a=>l(o).dictValue=a),placeholder:"请输入数据键值"},null,8,["modelValue"])]),_:1}),t(u,{label:"显示排序",prop:"dictSort"},{default:d(()=>[t(F,{modelValue:l(o).dictSort,"onUpdate:modelValue":e[3]||(e[3]=a=>l(o).dictSort=a),"controls-position":"right",min:0},null,8,["modelValue"])]),_:1}),t(u,{label:"系统默认"},{default:d(()=>[t(T,{modelValue:l(o).isDefault,"onUpdate:modelValue":e[4]||(e[4]=a=>l(o).isDefault=a),"active-text":"是","inactive-text":"否","active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1}),t(u,{label:"状态",prop:"status"},{default:d(()=>[t(U,{modelValue:l(o).status,"onUpdate:modelValue":e[5]||(e[5]=a=>l(o).status=a)},{default:d(()=>[t(g,{value:1},{default:d(()=>e[8]||(e[8]=[m("启用")])),_:1}),t(g,{value:0},{default:d(()=>e[9]||(e[9]=[m("禁用")])),_:1})]),_:1},8,["modelValue"])]),_:1}),t(u,{label:"备注",prop:"remark"},{default:d(()=>[t(n,{modelValue:l(o).remark,"onUpdate:modelValue":e[6]||(e[6]=a=>l(o).remark=a),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{K as _};
