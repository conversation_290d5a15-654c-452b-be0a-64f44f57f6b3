package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// CmsAudit is the golang structure for table cms_audit.
type CmsAuditRecord struct {
	gmeta.Meta `orm:"table:cms_audit_record, do:true"`
	Id         interface{} `orm:"id,primary" json:"id"`           // ID
	AuditId    interface{} `orm:"audit_id" json:"auditId"`        // 审核ID
	ArticleId  interface{} `orm:"article_id" json:"articleId"`    // 文章ID
	CategoryId interface{} `orm:"category_id" json:"categoryId"`  // 栏目ID
	UserId     interface{} `orm:"user_id" json:"userId"`          // 审核管理员ID
	StepId     interface{} `orm:"step_id" json:"stepId"`          // 步骤ID
	NextStepId interface{} `orm:"next_step_id" json:"nextStepId"` // 下个步骤ID
	Status     interface{} `orm:"status" json:"status"`           // 状态
	Content    interface{} `orm:"content" json:"content"`         // 审核意见
	Action     interface{} `orm:"action" json:"action"`           // 动作
	AuditedAt  *gtime.Time `orm:"audited_at" json:"auditedAt"`    // 审核时间

}
