/**
 * CMS 标签
 * @Company: 云南奇讯科技有限公司
 * @Author: yxf
 * @Description:
 * @Version: 1.0.0
 * @Date: 2021/8/6 14:18
 */

package logic

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/container/gvar"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/os/gview"
	"github.com/gogf/gf/v2/text/gregex"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/api/v1/cms"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/api/v1/home"
	"github.com/tiger1103/gfast/v3/internal/app/cms/model"
	cmsService "github.com/tiger1103/gfast/v3/internal/app/cms/service"
	model3 "github.com/tiger1103/gfast/v3/internal/app/common/model"
	service2 "github.com/tiger1103/gfast/v3/internal/app/common/service"
	model2 "github.com/tiger1103/gfast/v3/internal/app/home/<USER>"
	"github.com/tiger1103/gfast/v3/internal/app/home/<USER>"
	"github.com/tiger1103/gfast/v3/internal/app/home/<USER>"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"reflect"
)

func init() {
	service.RegisterCmsTemplateTags(NewCmsTemplateTagsServer())
}

func NewCmsTemplateTagsServer() *cmsTemplateTagsService {
	return &cmsTemplateTagsService{}
}

type cmsTemplateTagsService struct{}

// ParseTag 利用反射解析标签,暂未使用
func (s *cmsTemplateTagsService) ParseTag(tagName string, params ...string) (res string) {
	f := reflect.ValueOf(s).MethodByName(tagName)
	if f.Type() == nil {
		return
	}
	in := make([]reflect.Value, len(params))
	if len(params) > 0 {
		for i, param := range params {
			in[i] = reflect.ValueOf(param)
		}
	}
	resArr := f.Call(in)
	fmt.Println(resArr)
	return resArr[0].String()
}

// BindFunMap 绑定模板中用到的函数
func (s *cmsTemplateTagsService) BindFunMap(v *gview.View) (err error) {
	v.BindFuncMap(g.Map{
		"GetCmsConfig":          s.getCmsConfig,
		"GetMenu":               s.getMenu,
		"GetBreadcrumb":         s.getBreadcrumb,
		"GetArticleList":        s.getArticleList,
		"GetArticle":            s.getArticle,
		"GetVarsValue":          s.getVarsValue,
		"GetVideoFormat":        s.getVideoFormat,
		"GetComments":           s.getComments,
		"IsSon":                 s.isSon,
		"GetAdList":             s.getAdList,
		"DateTimeFormat":        s.dateTimeFormat,
		"GetPrevAndNextArticle": s.getPrevAndNextArticle,
		"Date":                  s.date,
		"Query":                 s.query,
		"SetStrDef":             s.setStrDef,
		"TypeOf":                s.typeOf,
		"JsonParse":             s.jsonParse,
		"CorrectionPath":        s.correctionPath,
		"FindArticleAttr":       s.findArticleAttr,
		"GetArticleTags":        s.getArticleTags,
		"GetCategory":           s.getCategory,
	})
	return
}

func (s *cmsTemplateTagsService) date() *gtime.Time {
	return gtime.Now()
}

// 获取CMS配置值
func (s *cmsTemplateTagsService) getCmsConfig(configName string) (res string) {
	configMap, err := cmsService.CmsConfig().GetCmsConfigMap(nil)
	if err != nil {
		return ""
	}
	if value, ok := configMap[configName]; ok {
		return value
	}
	return
}

// 获取导航菜单列表
func (s *cmsTemplateTagsService) getMenu(args ...string) []g.Map {
	argsMap := g.Map{}
	if len(args) > 0 {
		argsMap = util.ArgParseToMap(args[0])
	}
	menuMap := make([]g.Map, 0)
	menuList, err := service.Category().List(nil, &home.CategorySearchReq{
		ParentId: gconv.String(argsMap["parentId"]),
		Name:     "",
		Status:   "1",
		PageReq: commonApi.PageReq{
			PageReq: model3.PageReq{
				PageSize: util.ParseInt(argsMap["pageSize"], 200),
				PageNum:  1,
			},
		},
		Author: commonApi.Author{},
		Nav:    gconv.String(argsMap["nav"]),
	})
	if err != nil || menuList == nil {
		return nil
	}
	configMap, err := cmsService.CmsConfig().GetCmsConfigMap(nil)
	siteUrl := "/home"
	if value, ok := configMap["site_url"]; ok {
		siteUrl = value
	}
	for _, v := range menuList.List {
		switch v.Type {
		case "channel": //频道
			v.JumpUrl = fmt.Sprintf(siteUrl+"/channel/%d.html", v.Id)
		case "list": //发布栏目
			v.JumpUrl = fmt.Sprintf(siteUrl+"/list/%d.html", v.Id)
		case "page": //单页
			v.JumpUrl = fmt.Sprintf(siteUrl+"/page/%d.html", v.Id)
		}
		menu := g.Map{
			"Name":        v.Name,
			"Id":          v.Id,
			"ParentId":    v.ParentId,
			"Alias":       v.Alias,
			"ModuleId":    v.ModuleId,
			"JumpUrl":     v.JumpUrl,
			"Type":        v.Type,
			"ListOrder":   v.Sort,
			"Description": v.Description,
			"Children":    make([]g.Map, 0),
			"Thumb":       v.Thumb,
			"Active":      false,
		}
		if activeId, ok := argsMap["activeId"]; ok && gconv.Uint(activeId) == v.Id {
			menu["Active"] = true
		}
		menuMap = append(menuMap, menu)
	}
	parentId := "0"
	if _, ok := argsMap["parentId"]; ok {
		parentId = gconv.String(argsMap["parentId"])
	}
	return util.GetMapTree(menuMap, parentId, "Id", "ParentId", "Children")
}

// 获取文章分类信息
func (s *cmsTemplateTagsService) getCategory(cateId string) g.Map {
	categoryInfo, err := service.Category().GetById(nil, gconv.Uint(cateId))
	if err != nil || categoryInfo == nil {
		return nil
	}
	configMap, err := cmsService.CmsConfig().GetCmsConfigMap(nil)
	siteUrl := "/home"
	if value, ok := configMap["site_url"]; ok {
		siteUrl = value
	}
	switch categoryInfo.Type {
	case "channel": //频道
		categoryInfo.JumpUrl = fmt.Sprintf(siteUrl+"/channel/%d.html", categoryInfo.Id)
	case "list": //发布栏目
		categoryInfo.JumpUrl = fmt.Sprintf(siteUrl+"/list/%d.html", categoryInfo.Id)
	case "page": //单页
		categoryInfo.JumpUrl = fmt.Sprintf(siteUrl+"/page/%d.html", categoryInfo.Id)
	}
	return g.Map{
		"Name":        categoryInfo.Name,
		"Id":          categoryInfo.Id,
		"ParentId":    categoryInfo.ParentId,
		"Alias":       categoryInfo.Alias,
		"ModuleId":    categoryInfo.ModuleId,
		"JumpUrl":     categoryInfo.JumpUrl,
		"Type":        categoryInfo.Type,
		"ListOrder":   categoryInfo.Sort,
		"Description": categoryInfo.Description,
		"Thumb":       categoryInfo.Thumb,
		"SeoTitle":    categoryInfo.SeoTitle,
		"Keywords":    categoryInfo.Keywords,
		"Content":     categoryInfo.Content,
		"CreatedAt":   categoryInfo.CreatedAt,
		"UpdatedAt":   categoryInfo.UpdatedAt,
		"Banner":      categoryInfo.Banner,
	}
}

// 获取文章列表
func (s *cmsTemplateTagsService) getArticleList(args string) []*model2.ArticleListRes {
	argsMap := util.ArgParseToMap(args)
	var req *home.ArticleSearchReq
	err := gconv.Struct(argsMap, &req)
	if err != nil {
		return nil
	}
	if v, ok := argsMap["cateId"]; ok {
		cates := gconv.Ints(gstr.Split(gconv.String(v), ","))
		if cates != nil && len(cates) > 0 {
			req.CateIds = cates
			req.CateId = ""
		}
	}
	articleList, err := service.Article().List(nil, req)
	return articleList.List
}

func (s *cmsTemplateTagsService) getArticleTags(args string) []*model.CmsTagsListRes {
	argsMap := util.ArgParseToMap(args)
	var req *model.CmsTagsSearchReq
	err := gconv.Struct(argsMap, &req)
	if err != nil {
		return nil
	}
	if v, ok := argsMap["tagId"]; ok {
		tagIds := gconv.Ints(gstr.Split(gconv.String(v), ","))
		if tagIds != nil && len(tagIds) > 0 {
			req.TagIds = tagIds
		}
	}
	tags, err := cmsService.CmsTags().List(gctx.New(), req)
	if err != nil {
		return nil
	}
	if tags != nil {
		return tags.List
	}
	return nil
}

// 获取单条文章
func (s *cmsTemplateTagsService) getArticle(id string) (articleInfo *model2.ArticleInfoRes) {
	articleInfo, _ = service.Article().GetById(nil, gconv.Uint64(id))
	return
}

// 转换通用变量为map类型
func (s *cmsTemplateTagsService) getVarsValue(val *gvar.Var) map[string]string {
	return val.MapStrStr()
}

// 获取栏目面包树
func (s *cmsTemplateTagsService) getBreadcrumb(menuId interface{}) g.List {
	// 获取所有频道栏目
	currentCategory, _ := cmsService.CmsCategory().GetById(nil, gconv.Uint(menuId))
	if currentCategory == nil {
		return nil
	}
	menus, _ := cmsService.CmsCategory().List(nil, &cms.CmsCategorySearchReq{
		Type: "channel",
		PageReq: commonApi.PageReq{
			PageReq: model3.PageReq{
				PageNum:  1,
				PageSize: 1000,
			},
		},
	})
	menuList := make(g.List, len(menus.List))
	for k, m := range menus.List {
		menuList[k] = g.Map{
			"Name":     m.Name,
			"ParentId": m.ParentId,
			"Id":       m.Id,
			"Url":      m.JumpUrl,
		}
	}
	parents := libUtils.FindParentBySonPid(menuList, gconv.Int(currentCategory.ParentId), "", "ParentId", "", "Id")

	parents = append(parents, g.Map{
		"Name":     currentCategory.Name,
		"ParentId": currentCategory.ParentId,
		"Id":       currentCategory.Id,
		"Url":      currentCategory.JumpUrl,
	})
	return parents
}

// 判断是否是指定菜单ID的子级
func (s *cmsTemplateTagsService) isSon(id, menus interface{}) bool {
	i := gconv.Int(id)
	m := gconv.SliceMap(menus)
	for _, v := range m {
		if gconv.Int(v["parent_id"]) == i {
			return true
		}
	}
	return false
}

// 修正图片路径
func (s *cmsTemplateTagsService) correctionPath(in interface{}) string {
	imgUrl := gconv.String(in)
	if imgUrl == "" {
		return imgUrl
	}
	if gstr.Pos(imgUrl, "http") == 0 || gstr.Pos(imgUrl, "blob") == 0 {
		return imgUrl
	}
	if gstr.Pos(imgUrl, "/") == 0 {
		return imgUrl
	} else {
		return "/" + imgUrl
	}
}

// 取出视频格式
func (s *cmsTemplateTagsService) getVideoFormat(videoName interface{}) string {
	data := []byte(gconv.String(videoName))
	var data2 []byte
	for i := len(data) - 3; i < len(data); i++ {
		data2 = append(data2, data[i])
	}
	videoFormat := string(data2)
	return videoFormat
}

// 获取广告列表
func (s *cmsTemplateTagsService) getAdList(args string) (adList []*model.CmsAdListRes) {
	argMap := util.ArgParseToMap(args)
	adReq := &cms.CmsAdSearchReq{
		Id:       "",
		Name:     "",
		Position: "",
		PageReq:  commonApi.PageReq{},
		Author:   commonApi.Author{},
	}
	if v, ok := argMap["pageSize"]; ok {
		adReq.PageSize = gconv.Int(v)
	}
	if v, ok := argMap["position"]; ok {
		adReq.Position = gconv.String(v)
	}
	adListRes, _ := cmsService.CmsAd().List(nil, adReq)
	return adListRes.List
}

// 处理时间日期
func (s *cmsTemplateTagsService) dateTimeFormat(str interface{}, args ...string) (Articletr string) {
	format := "Y-m-d"
	if len(args) > 0 {
		format = args[0]
	}
	//today := gtime.Now().Format("Y-m-d")
	Articletr = gconv.String(str)
	t := gtime.New(Articletr)
	//if today == t.Format("Y-m-d") {
	//	return "今天"
	//}
	Articletr = t.Format(format)
	return
}

// 获取上一篇下一篇文章
func (s *cmsTemplateTagsService) getPrevAndNextArticle(ArticleId uint64) g.Map {
	return service.Article().GetPrevAndNextArticle(ArticleId)
}

// query 查询
func (s *cmsTemplateTagsService) query(sql string) gdb.Result {
	sql = gstr.ToLower(sql)
	// 只允许select查询,禁止查询sys_user表
	if gregex.IsMatchString(`drop|update|delete|insert|alert|exec|replace|sys_user`, sql) {
		return nil
	}
	result, err := g.DB().GetAll(nil, sql)
	if err != nil {
		return nil
	}
	return result
}

// 类型判断
func (s *cmsTemplateTagsService) typeOf(item interface{}) string {
	return reflect.TypeOf(item).String()
}

// 获取评论列表
func (s *cmsTemplateTagsService) getComments(args string) (res g.Map) {
	argMap := util.ArgParseToMap(args)
	req := &home.CommentSearchListReq{}
	if v, ok := argMap["pageSize"]; ok {
		req.PageSize = gconv.Int(v)
	}
	if v, ok := argMap["pageNum"]; ok {
		req.PageNum = gconv.Int(v)
	}
	if v, ok := argMap["orderBy"]; ok {
		req.OrderBy = gconv.String(v)
	}
	if v, ok := argMap["articleId"]; ok {
		req.ArticleId = gconv.String(v)
	}
	listRes, _ := service.Comment().List(context.TODO(), req)
	res = g.Map{
		"total":    listRes.Total,
		"page":     listRes.CurrentPage,
		"dataList": listRes.List,
	}
	return
}

// 数据转换为Json
func (s *cmsTemplateTagsService) jsonParse(data interface{}) (jsonStr string) {
	json := gjson.New(data)
	return json.MustToJsonString()
}
func (s *cmsTemplateTagsService) jsonToMap(str string) (data map[string]interface{}) {
	return gconv.Map(str)
}

// 给字符串设置默认值
func (s *cmsTemplateTagsService) setStrDef(value string, def ...string) string {
	if value == "" {
		if len(def) <= 0 {
			value = "/public/images/nopic.jpg"
		} else {
			value = def[0]
		}
	}
	return value
}

// findArticleAttr
//
//	@Description: 检查文章是否包含指定属性
//	@param str 文章字段的 flag
//	@param need 需要检查的标记
//	@return flagType 返回文章的属性
//	@example HasArticleAttr("1,2,4","置顶")
func (s *cmsTemplateTagsService) findArticleAttr(inStr string, need string) bool {
	articleDictData, err := service2.SysDictData().GetDictWithDataByType(nil, "cms_article_attr", "")
	dictValue := ""
	if err == nil {
		for _, value := range articleDictData.Values {
			if value.DictLabel == need {
				dictValue = value.DictValue
				break
			}
		}
		if gstr.SearchArray(gstr.Split(inStr, ","), dictValue) != -1 {
			return true
		}
	}
	return false
}

// parseModulesDataOptions
//
//	@Description:  转换
//	@receiver s
//	@param inStr
//	@return data
func (s *cmsTemplateTagsService) parseModulesDataOptions(inStr string) (data map[string]interface{}) {
	data = make(map[string]interface{}, 0)
	g.Try(nil, func(ctx context.Context) {
		tmpArr := gstr.Split(inStr, ",")
		for _, value := range tmpArr {
			tmpValue := gstr.Split(value, ":")
			data[tmpValue[0]] = tmpValue[1]
		}
	})
	return
}

// parseModulesData
//
//	@Description: 转换模型字段选项数据
//	@receiver s
//	@return res
func (s *cmsTemplateTagsService) parseModulesData(articleModulesData *systemModel.ModulesFieldValueRes) (res []string) {
	return
}
