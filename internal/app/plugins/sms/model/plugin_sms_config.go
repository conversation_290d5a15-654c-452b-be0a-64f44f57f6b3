// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2023-12-01 14:49:39
// 生成路径: internal/app/plugins/sms/model/plugin_sms_config.go
// 生成人：gfast
// desc:短信配置
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// PluginSmsConfigInfoRes is the golang structure for table plugin_sms_config.
type PluginSmsConfigInfoRes struct {
	gmeta.Meta `orm:"table:plugin_sms_config"`
	Id         uint   `orm:"id,primary" json:"id" dc:"ID"`      // ID
	SmsType    string `orm:"sms_type" json:"smsType" dc:"短信平台"` // 短信平台
	Remark     string `orm:"remark" json:"remark" dc:"备注"`      // 备注
	Status     int    `orm:"status" json:"status" dc:"状态"`      // 状态
	Config     string `orm:"config" json:"config" dc:"配置"`      // 配置
}

type PluginSmsConfigListRes struct {
	Id      uint   `json:"id" dc:"ID"`
	SmsType string `json:"smsType" dc:"短信平台"`
	Remark  string `json:"remark" dc:"备注"`
	Status  int    `json:"status" dc:"状态"`
}

// PluginSmsConfigSearchReq 分页请求参数
type PluginSmsConfigSearchReq struct {
	comModel.PageReq
	SmsType string `p:"smsType" dc:"短信平台"`                        //短信平台
	Status  string `p:"status" v:"status@integer#状态需为整数" dc:"状态"` //状态
}

// PluginSmsConfigSearchRes 列表返回结果
type PluginSmsConfigSearchRes struct {
	comModel.ListRes
	List []*PluginSmsConfigListRes `json:"list"`
}

// PluginSmsConfigAddReq 添加操作请求参数
type PluginSmsConfigAddReq struct {
	SmsType string `p:"smsType" v:"required#短信平台不能为空" dc:"短信平台"`
	Remark  string `p:"remark" v:"required#备注不能为空" dc:"备注"`
	Status  int    `p:"status" v:"required#状态不能为空" dc:"状态"`
	Config  string `p:"config" v:"required#配置不能为空" dc:"配置"`
}

// PluginSmsConfigEditReq 修改操作请求参数
type PluginSmsConfigEditReq struct {
	Id      uint   `p:"id" v:"required#主键ID不能为空" dc:"ID"`
	SmsType string `p:"smsType" v:"required#短信平台不能为空" dc:"短信平台"`
	Remark  string `p:"remark" v:"required#备注不能为空" dc:"备注"`
	Status  int    `p:"status" v:"required#状态不能为空" dc:"状态"`
	Config  string `p:"config" v:"required#配置不能为空" dc:"配置"`
}
