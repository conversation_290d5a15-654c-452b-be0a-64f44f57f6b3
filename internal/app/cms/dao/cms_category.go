// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2023-01-31 09:33:48
// 生成路径: internal/app/cms/dao/cms_category.go
// 生成人：yxf
// desc:文章分类
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/cms/dao/internal"
)

// cmsCategoryDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type cmsCategoryDao struct {
	*internal.CmsCategoryDao
}

var (
	// CmsCategory is globally public accessible object for table tools_gen_table operations.
	CmsCategory = cmsCategoryDao{
		internal.NewCmsCategoryDao(),
	}
)

// Fill with you ideas below.
