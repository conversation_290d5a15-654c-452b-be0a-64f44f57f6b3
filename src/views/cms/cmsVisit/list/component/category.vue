<template>
  <div class="cms-visit-category-container">
    <el-card shadow="hover">
      <el-table
        v-loading="loading"
        :data="categoryData"
        row-key="id"
        border
        default-expand-all
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <el-table-column label="分类名称" prop="name" min-width="180">
          <template #default="{row}">
            <span>
              <el-icon v-if="row.type === 'channel'"><ele-Folder /></el-icon>
              <el-icon v-else-if="row.type === 'list'"><ele-Document /></el-icon>
              <el-icon v-else-if="row.type === 'jump'"><ele-Link /></el-icon>
              <el-icon v-else-if="row.type === 'page'"><ele-Files /></el-icon>
              <el-icon v-else><ele-More /></el-icon>
              {{ row.name }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="类型" prop="type" width="100" align="center">
          <template #default="{row}">
            <el-tag v-if="row.type === 'channel'" type="success">频道栏目</el-tag>
            <el-tag v-else-if="row.type === 'list'" type="primary">发布栏目</el-tag>
            <el-tag v-else-if="row.type === 'jump'" type="warning">跳转栏目</el-tag>
            <el-tag v-else-if="row.type === 'page'" type="info">单页栏目</el-tag>
            <el-tag v-else type="danger">未知</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="访问量" prop="count" width="100" align="center" />
        <el-table-column label="占比" prop="percentage" width="120" align="center">
          <template #default="{row}">
            <el-progress :percentage="row.percentage || 0" :format="percentageFormat" />
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import { CategoryCmsVist } from "/@/api/cms/cmsVisit";

defineOptions({ name: "cmsVisitCategory" });

const { proxy } = <any>getCurrentInstance();
const loading = ref(false);
const categoryData = ref([]);

// 获取分类访问统计数据
const getCategoryVisitStats = async () => {
  loading.value = true;
  try {
    // 使用 CategoryCmsVist 接口获取分类访问统计数据
    const res = await CategoryCmsVist({});

    if (res.data && res.data.list) {
      // 计算总访问量，用于计算百分比
      const totalVisits = res.data.list.reduce((sum, item) => sum + (Number(item.count) || 0), 0);

      // 处理数据，添加百分比
      const processedData = res.data.list.map(item => ({
        ...item,
        percentage: totalVisits > 0 ? Math.round((item.count / totalVisits * 100) * 100) / 100 : 0
      }));

      // 转换为树形结构
      categoryData.value = proxy.handleTree(processedData, "id", "parentId");
    } else {
      categoryData.value = [];
    }
  } catch (error) {
    console.error('获取分类访问统计失败:', error);
    categoryData.value = [];
  } finally {
    loading.value = false;
  }
};



// 格式化百分比
const percentageFormat = (percentage: number) => {
  return percentage.toFixed(2) + '%';
};

// 不在组件挂载时自动调用获取数据的方法，而是由父组件控制调用时机
onMounted(() => {
  // 不自动调用
});

// 暴露方法给父组件
defineExpose({
  getCategoryVisitStats
});
</script>

<style scoped lang="scss">
.cms-visit-category-container {
  margin-top: 20px;
}
</style>
