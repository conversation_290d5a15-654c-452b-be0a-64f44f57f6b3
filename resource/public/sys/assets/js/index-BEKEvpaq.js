import{l as z,d as Q}from"./ucenterScores-COmmFgm9.js";import G from"./add-DbFKz01Z.js";import H from"./edit-DT0aDSoE.js";import X from"./detail-B2lMzxsV.js";import{a as A,E as j}from"./element-plus-CUmVNDWO.js";import{d as J,f as K,r as u,k as O,X as W,h as Y,ad as Z,ab as x,ag as n,aq as B,c as ee,o as _,P as e,H as a,a as $,I as S,L as g,G as D,M as te,Q as oe}from"./@vue-C21YZbHS.js";import{a as ae}from"./index-BGmsw1a8.js";import"./lodash-es-S0Y0Up6J.js";import"./@vueuse-D_5IXmcI.js";import"./@element-plus-_Cc-TEQX.js";import"./@popperjs-D_chPuIy.js";import"./@ctrl-r5W6hzzQ.js";import"./dayjs-CepoAVPu.js";import"./@intlify-D8kAWvSi.js";import"./async-validator-9PlIezaS.js";import"./memoize-one-BdPwpGay.js";import"./normalize-wheel-es-BQoi3Ox2.js";import"./@floating-ui-DwceP2Gb.js";import"./pinia-DwJf82dV.js";import"./vue-router-BDT9dCQ7.js";import"./js-cookie-Cz0CWeBA.js";import"./nprogress-Scw6VIZr.js";import"./axios-B62VicFi.js";import"./qs-D41HS0uK.js";import"./side-channel-DytTmCZg.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-DbsE5JW_.js";import"./crypto-js-oM26lpET.js";import"./side-channel-list-C_7Uk4RP.js";import"./side-channel-map-BUokNT8A.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-bSkmVQJL.js";import"./vue-i18n-BkLk9Ti1.js";import"./vue-iNFchrNl.js";import"./vue-clipboard3-Dr8V5xR7.js";import"./clipboard-DzJOpNE6.js";import"./mitt-DJ65BbbF.js";import"./vue-grid-layout-CIPjP0XN.js";import"./vue-simple-uploader-j6sX1TKo.js";import"./vue-ueditor-wrap-CJ5gjsd3.js";const ne=J({name:"apiV1UcenterUcenterScoresList",components:{apiV1UcenterUcenterScoresAdd:G,apiV1UcenterUcenterScoresEdit:H,apiV1UcenterUcenterScoresDetail:X},setup(){const{proxy:t}=K(),o=u(!1),E=u(),v=u(),C=u(),V=u(),c=u(!1),b=u(!0),h=u(!0),d=O(()=>c.value===!1?"展开搜索":"收起搜索");t.useDict();const r=W({ids:[],tableData:{data:[],total:0,loading:!1,param:{username:"",pageNum:1,pageSize:10,dateRange:[]}}});Y(()=>{y()});const y=()=>{m()},w=l=>{l&&(l.resetFields(),m())},m=()=>{o.value=!0,z(r.tableData.param).then(l=>{let p=l.data.list??[];r.tableData.data=p,r.tableData.total=l.data.total,o.value=!1})};return{proxy:t,addRef:v,editRef:C,detailRef:V,showAll:c,loading:o,single:b,multiple:h,word:d,queryRef:E,resetQuery:w,ucenterScoresList:m,toggleSearch:()=>{c.value=!c.value},handleSelectionChange:l=>{r.ids=l.map(p=>p.id),b.value=l.length!=1,h.value=!l.length},handleAdd:()=>{v.value.openDialog()},handleUpdate:l=>{l||(l=r.tableData.data.find(p=>p.id===r.ids[0])),C.value.openDialog(x(l))},handleDelete:l=>{let p="你确定要删除所选数据？",f=[];if(l?(p="此操作将永久删除数据，是否继续?",f=[l.id]):f=r.ids,f.length===0){A.error("请选择要删除的数据。");return}j.confirm(p,"提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(()=>{Q(f).then(()=>{A.success("删除成功"),m()})}).catch(()=>{})},...Z(r)}}}),le={class:"ucenter-ucenterScores-container"},re={class:"ucenter-ucenterScores-search mb15"};function ie(t,o,E,v,C,V){const c=n("el-input"),b=n("el-form-item"),h=n("ele-Search"),d=n("el-icon"),r=n("el-button"),y=n("ele-Refresh"),w=n("el-form"),m=n("ele-Plus"),k=n("el-col"),F=n("ele-Delete"),L=n("el-row"),s=n("el-table-column"),R=n("ele-EditPen"),l=n("ele-DeleteFilled"),p=n("el-table"),f=n("pagination"),N=n("el-card"),P=n("apiV1UcenterUcenterScoresAdd"),I=n("apiV1UcenterUcenterScoresEdit"),T=n("apiV1UcenterUcenterScoresDetail"),U=B("auth"),q=B("loading");return _(),ee("div",le,[e(N,{shadow:"hover"},{default:a(()=>[$("div",re,[e(w,{ref:"queryRef",inline:!0,model:t.tableData.param},{default:a(()=>[e(b,{label:"用户名",prop:"username"},{default:a(()=>[e(c,{modelValue:t.tableData.param.username,"onUpdate:modelValue":o[0]||(o[0]=i=>t.tableData.param.username=i),clearable:"",placeholder:"用户名",style:{width:"140px"}},null,8,["modelValue"])]),_:1}),e(b,null,{default:a(()=>[e(r,{type:"primary",onClick:t.ucenterScoresList},{default:a(()=>[e(d,null,{default:a(()=>[e(h)]),_:1}),o[5]||(o[5]=g(" 搜索 "))]),_:1},8,["onClick"]),e(r,{onClick:o[1]||(o[1]=i=>t.resetQuery(t.queryRef))},{default:a(()=>[e(d,null,{default:a(()=>[e(y)]),_:1}),o[6]||(o[6]=g(" 重置 "))]),_:1})]),_:1})]),_:1},8,["model"]),e(L,{gutter:10,class:"mb8"},{default:a(()=>[e(k,{span:1.5},{default:a(()=>[S((_(),D(r,{type:"primary",onClick:t.handleAdd},{default:a(()=>[e(d,null,{default:a(()=>[e(m)]),_:1}),o[7]||(o[7]=g(" 新增 "))]),_:1},8,["onClick"])),[[U,"api/v1/ucenter/ucenterScores/add"]])]),_:1}),e(k,{span:1.5},{default:a(()=>[S((_(),D(r,{disabled:t.multiple,type:"danger",onClick:o[2]||(o[2]=i=>t.handleDelete(null))},{default:a(()=>[e(d,null,{default:a(()=>[e(F)]),_:1}),o[8]||(o[8]=g(" 删除 "))]),_:1},8,["disabled"])),[[U,"api/v1/ucenter/ucenterScores/delete"]])]),_:1})]),_:1})]),S((_(),D(p,{border:"",stripe:"",data:t.tableData.data,onSelectionChange:t.handleSelectionChange},{default:a(()=>[e(s,{type:"selection",width:"55"}),e(s,{label:"ID",width:"100px",prop:"id"}),e(s,{label:"会员ID",width:"100px",prop:"memberId"}),e(s,{label:"会员账号",width:"160px",prop:"memberName"}),e(s,{label:"积分",width:"100px",prop:"score"}),e(s,{label:"来源",width:"100px",prop:"dataSrc"}),e(s,{label:"描述","min-width":"100px",prop:"description"}),e(s,{label:"类型",width:"100px",prop:"type"}),e(s,{label:"创建日期",width:"160px",prop:"createdAt"},{default:a(i=>[$("span",null,te(t.proxy.parseTime(i.row.createdAt,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),e(s,{fixed:"right",label:"操作",width:"160px"},{default:a(i=>[S((_(),D(r,{link:"",type:"primary",onClick:M=>t.handleUpdate(i.row)},{default:a(()=>[e(d,null,{default:a(()=>[e(R)]),_:1}),o[9]||(o[9]=g(" 修改 "))]),_:2},1032,["onClick"])),[[U,"api/v1/ucenter/ucenterScores/edit"]]),S((_(),D(r,{link:"",type:"primary",onClick:M=>t.handleDelete(i.row)},{default:a(()=>[e(d,null,{default:a(()=>[e(l)]),_:1}),o[10]||(o[10]=g(" 删除 "))]),_:2},1032,["onClick"])),[[U,"api/v1/ucenter/ucenterScores/delete"]])]),_:1})]),_:1},8,["data","onSelectionChange"])),[[q,t.loading]]),S(e(f,{limit:t.tableData.param.pageSize,"onUpdate:limit":o[3]||(o[3]=i=>t.tableData.param.pageSize=i),page:t.tableData.param.pageNum,"onUpdate:page":o[4]||(o[4]=i=>t.tableData.param.pageNum=i),total:t.tableData.total,onPagination:t.ucenterScoresList},null,8,["limit","page","total","onPagination"]),[[oe,t.tableData.total>0]])]),_:1}),e(P,{ref:"addRef",onUcenterScoresList:t.ucenterScoresList},null,8,["onUcenterScoresList"]),e(I,{ref:"editRef",onUcenterScoresList:t.ucenterScoresList},null,8,["onUcenterScoresList"]),e(T,{ref:"detailRef",onUcenterScoresList:t.ucenterScoresList},null,8,["onUcenterScoresList"])])}const lt=ae(ne,[["render",ie],["__scopeId","data-v-0376a9bb"]]);export{lt as default};
