// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2023-03-09 16:50:38
// 生成路径: internal/app/ucenter/dao/internal/ucenter_scores.go
// 生成人：yxf
// desc:用户积分
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// UCenterScoresDao is the manager for logic model data accessing and custom defined data operations functions management.
type UCenterScoresDao struct {
	table   string               // Table is the underlying table name of the DAO.
	group   string               // Group is the database configuration group name of current DAO.
	columns UCenterScoresColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// UCenterScoresColumns defines and stores column names for table ucenter_scores.
type UCenterScoresColumns struct {
	Id          string // ID
	MemberId    string // 会员ID
	Score       string // 积分
	DataSrc     string // 来源
	Description string // 描述
	Type        string // 类型0:收入，1:支出
	CreatedAt   string // 创建日期
}

var ucenterScoresColumns = UCenterScoresColumns{
	Id:          "id",
	MemberId:    "member_id",
	Score:       "score",
	DataSrc:     "data_src",
	Description: "description",
	Type:        "type",
	CreatedAt:   "created_at",
}

// NewUCenterScoresDao creates and returns a new DAO object for table data access.
func NewUCenterScoresDao() *UCenterScoresDao {
	return &UCenterScoresDao{
		group:   "default",
		table:   "ucenter_scores",
		columns: ucenterScoresColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *UCenterScoresDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *UCenterScoresDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *UCenterScoresDao) Columns() UCenterScoresColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *UCenterScoresDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *UCenterScoresDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *UCenterScoresDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
