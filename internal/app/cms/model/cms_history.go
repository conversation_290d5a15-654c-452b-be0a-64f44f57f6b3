// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2023-01-31 09:44:00
// 生成路径: internal/app/cms/model/cms_article.go
// 生成人：yxf
// desc:文章
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
)

type CmsHistoryListRes struct {
	gmeta.Meta `orm:"table:cms_history"`
	Id         uint64            `json:"id"`
	ArticleId  uint64            `json:"articleId"`
	MemberId   uint64            `json:"memberId"`
	CreatedAt  *gtime.Time       `json:"createdAt"`
	Article    *CmsArticleSimple `orm:"with:id=articleId" json:"article"`
}

// CmsHistorySearchRes 列表返回结果
type CmsHistorySearchRes struct {
	commonApi.ListRes
	List []*CmsHistoryListRes `json:"list"`
}
