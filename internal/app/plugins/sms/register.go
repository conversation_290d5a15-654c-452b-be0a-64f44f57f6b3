/*
* @desc:短信注册
* @company:云南奇讯科技有限公司
* @Author: y<PERSON><PERSON><PERSON><PERSON><<EMAIL>>
* @Date:   2023/12/5 17:23
 */

package sms

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gctx"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	"github.com/tiger1103/gfast/v3/internal/app/plugins/sms/driver"
	driverService "github.com/tiger1103/gfast/v3/internal/app/plugins/sms/driver/service"
	"github.com/tiger1103/gfast/v3/internal/mounter"
)

func init() {
	//挂载组件
	mounter.Mount(func(ctx context.Context, s *ghttp.Server) {
		Register()
	})
}
func Register() {
	ctx := gctx.New()
	defer func() {
		if v := recover(); v != nil {
			g.Log().Errorf(ctx, "短信配置失败：%v", v)
		}
	}()
	err := commonService.EventBus().Subscribe("sms_plugin:registerService", func(smsType string) {
		var err error
		switch smsType {
		case "demo":
			driver.CommonSms = driverService.Demo
		case "yunxin": //网易云信
			driverService.Yunxin.YunxinConfig, err = driverService.Yunxin.GetConfig(ctx)
			if err != nil {
				panic(err)
			}
			driver.CommonSms = driverService.Yunxin
		case "aly": //阿里云短信
			driverService.Aly.AlyConfig, err = driverService.Aly.GetConfig(ctx)
			if err != nil {
				panic(err)
			}
			driver.CommonSms = driverService.Aly
		case "ten": //腾讯云短信
			driverService.Ten.TenConfig, err = driverService.Ten.GetConfig(ctx)
			if err != nil {
				panic(err)
			}
			driver.CommonSms = driverService.Ten
		case "yunpian": //云片短信
			driverService.Yunpian.YunpianConfig, err = driverService.Yunpian.GetConfig(ctx)
			if err != nil {
				panic(err)
			}
			driver.CommonSms = driverService.Yunpian
		}
	})
	if err != nil {
		panic(err)
	}
	err = driverService.Sms.RegisterSmsService(ctx)
	if err != nil {
		panic(err)
	}
}
