<div id="wrap">
    <div class="wrap container">
        <ol class="breadcrumb">
            <li class="home">
                <a href="${.cmsConfig.site_url}">首页</a>
            </li>
            ${if ne .categoryInfo nil}
            ${range $breadcrumb := GetBreadcrumb .categoryInfo.Id}
            <li>
                <span class="mr5 ml5">/</span>
                <a href="${$.cmsConfig.site_url}/list/${$breadcrumb.Id}.html">${$breadcrumb.Name}</a>
            </li>
            ${end}
            ${end}
        </ol>
        <!--文章内容-->
        <main class="main">
            <article class="main-content">
                <div class="content-body">
                    <div class="content-body-head">
                        <h1 class="content-body-title">${.article.Title}</h1>
                        <div style="display: flex; justify-content: space-between">
                            <div class="content-body-info">
                                <span class="author">作者 ${if .article.UserInfo} <a href="${$.cmsConfig.site_url}/member/${.article.UserInfo.Id}"> ${.article.UserInfo.UserNickname} </a> ${end}</span>
                                <span class="content-body-date published mr10 ml10"> ${DateTimeFormat .article.PublishedAt "Y年m月d日 H:i"}</span>
                                <span>阅读 ${.article.Hits}</span>
                            </div>
                            <div class="content-body-info">
                                <a class="favorite-btn" data-id="${.article.Id}" href="javascript:void(0)"><i class="iconfont icon-heart"></i> <span class="favorite-num">${.article.FavoriteCount}</span></a>
                                <a class="ml10" href="#comments"><i class="iconfont icon-message"></i> <span class="comment-num">${.article.CommentCount}</span></a>
                                <a class="btn-zan ml10" data-id="${.article.Id}" href="javascript:void(0)"><i class="iconfont icon-star"></i> <span class="liked-num">${.article.LikeCount}</span></a>
                            </div>
                        </div>
                    </div>
                    ${if ne .article.Summary ""}
                    <div class="content-body-excerpt content-body-summary"><p>${.article.Summary}</p></div>
                    ${end}
                    <div class="content-body-content">
                        <div class="content-container">${.article.Content}</div>
                        <!--动态模型数据, .article不是Map[string]interface{} 类，需要转换一下。-->
                        ${$tmpArticle := map .article}
                        ${include "/common/modules_data.html" $tmpArticle}
                    </div>
                    <div class="content-body-footer">
                        <div class="content-body-action hidden">
                            <div class="btn-zan" data-id="${.article.Id}">
                                <i class="iconfont icon-like"></i> 赞 <span class="content-body-action-num liked-num">(${.articleLikeCount})</span></div>
                        </div>
                        <div class="border-bottom-dot"></div>
                        <div class="prev-next">
                            ${$prevAndNext := GetPrevAndNextArticle .article.Id}
                            <div class="page-prev">
                                上一篇：
                                <a href="${$prevAndNext.prev.Url}"><span>${$prevAndNext.prev.Title}</span></a>
                            </div>
                            <div class="page-next">
                                下一篇：
                                <a href="${$prevAndNext.next.Url}"> <span>${$prevAndNext.next.Title}</span></a>
                            </div>
                        </div>
                    </div>
                    ${if eq .article.AllowComment 1}
                    <div class="content-body-comments" id="comments">
                        <div class="comment-respond" id="respond">
                            <h3 class="comment-reply-title" id="reply-title">发表评论 </h3>
                            <div class="comment-form" id="comment-form"></div>
                        </div>
                        <div id="comment_list_container"></div>
                    </div>
                    ${end}
                </div>
            </article>
        </main>
        <aside class="sidebar">
            ${range $ad := GetAdList "position:25"}
            <div class="widget widget_image_ad">
                <a href="${$ad.Url}" rel="noopener" target="_blank">
                    <img class="j-lazy" src="${$ad.Images}" style="display: inline;">
                </a>
            </div>
            ${end}
            ${include "widget/new_articles.html"}
            ${include "widget/new_comments.html"}
        </aside>
    </div>
</div>
<script id="comment-list" type="text/html">
    <h3 class="comments-title"> 评论列表(<span id="comment_num"><%= total %></span>条评论)</h3>
    <ul class="comments-list">
        <% for(var i=0;i < list.length;i++){ %>
        <li class="comment byuser odd alt thread-odd thread-alt depth-1 parent">
            <div class="comment-inner">
                <div class="comment-author vcard">
                    <img class="avatar avatar-60 photo" height="60" src="<%=$imports.CorrectionPath(list[i].member.avatar,'/static/images/avatar.jpg')%>" width="60">
                </div>
                <div class="comment-body">
                    <div class="nickname">
                        <a class="j-user-card" href="${.cmsConfig.site_url}/member/<%= list[i].member.id %>" target="_blank"><%= list[i].member.nickname %></a>
                        <span class="comment-time"><%=list[i].createdAt%></span></div>
                    <div class="comment-text"><p><%=#parseFace(list[i].content)%></p></div>
                </div>
                ${if .Session.memberInfo}
                <div class="reply reply-btn">
                    <a onclick="replyComment(this)" data-member-nickname="<%= list[i].member.nickname%>" data-member-id="<%=list[i].member.id%>" data-comment-id="<%=list[i].id%>"
                            data-id="comment-reply-<%=i%>" href="javascript:void(0);" rel="nofollow" style="display: inline-block">
                        <i class="iconfont icon-message"></i>回复
                    </a>
                    <% if(list[i].member.id === ${.Session.memberInfo.Id}) {%>
                    <a href="javascript:void(0);" onclick="deleteComment(<%=list[i].id%>)" rel="nofollow" style="display: inline-block;margin-left: 20px;"><i class="iconfont icon-delete"></i>删除</a>
                    <% } %>
                </div>
                ${end}
            
            </div>
            <div class="comment-respond comment-reply-container" id="comment-reply-<%=i%>"></div>
            <% if(list[i].children){ %>
            <ul class="comment-children">
                <% for(var j=0;j < list[i].children.length; j++) { %>
                <li class="comment byuser even depth-2">
                    <div class="comment-inner">
                        <div class="comment-author vcard">
                            <img alt="" class="avatar avatar-60 photo" height="60" src="<%=$imports.CorrectionPath(list[i].children[j].member.avatar,'/static/images/avatar.jpg')%>"
                                    width="60">
                        </div>
                        <div class="comment-body">
                            <div class="nickname">
                                <a class="j-user-card" href="${.cmsConfig.site_url}/member/<%=list[i].children[j].member.id%>" target="_blank"><%=list[i].children[j].member.nickname%></a>
                                <span class="comment-text-reply">
                                    <% if(list[i].children[j].replyMember){ %>
                                    回复:<a class="j-user-card" href="${.cmsConfig.site_url}/member/<%=list[i].children[j].replyMemberId%>" target="_blank"><%=list[i].children[j].replyMember.nickname%></a>:</span>
                                <% } %>
                                <span class="comment-time"><%=list[i].children[j].createdAt%></span></div>
                            <div class="comment-text">
                                <p><%=#parseFace(list[i].children[j].content)%></p></div>
                        </div>
                        ${if .Session.memberInfo}
                        <div class="reply reply-btn">
                            <a data-member-nickname="<%= list[i].children[j].member.nickname%>" data-member-id="<%=list[i].children[j].member.id%>"
                                    data-comment-id="<%=list[i].id%>" data-id="comment-reply-<%=i+'_'+j%>" href="javascript:void(0);" onclick="replyComment(this)" rel="nofollow"
                                    style="display: inline-block;">
                                <i class="iconfont icon-message"></i>回复
                            </a>
                            <% if(list[i].children[j].member.id === ${.Session.memberInfo.Id}) {%>
                            <a href="javascript:void(0);" onclick="deleteComment(<%=list[i].children[j].id%>)" rel="nofollow" style="display: inline-block;margin-left: 20px;"><i
                                    class="iconfont icon-delete"></i>删除</a>
                            <% } %>
                        </div>
                        <div class="comment-respond comment-reply-container" id="comment-reply-<%=i+'_'+j%>"></div>
                        ${end}
                    </div>
                </li>
                <% } %>
            </ul>
            <% } %>
        </li>
        <% } %>
    </ul>
    <ul class="pagination" id="comment-pagination">
    
    </ul>
</script>
<script id="comment-form-template" type="text/html">
    ${include "widget/comment_input.html" .}
</script>
<script id="comment-reply-form-template" type="text/html">
    <h3 class="comment-reply-title" style="max-width: 100%">回复给 <%=replyMemberNickname%><i class="iconfont icon-close pull-right" style="cursor:pointer;"
            onclick="closeReply()"></i></h3>
    <div class="comment-form">
        <input type="hidden" id="reply_member_id" value="<%=replyMemberId%>">
        <input type="hidden" id="comment_id" value="<%=commentId%>">
        <input type="hidden" id="is_reply" value="true">
        ${include "widget/comment_input.html" .}
    </div>
</script>
<script id="comment-pagination-template" type="text/html">
    <% if(page > 1){ %>
    <li class="prev">
        <a href="javascript:void(0)" onclick="getCommentList(articleId,1)" class="page-numbers"><i class="iconfont icon-left"></i></a>
    </li><%}%><% for(var i=1;i <= totalPage; i++) { %><% if(page===i) { %>
    <li class="active">
        <% }else{ %>
    <li>
        <% } %>
        <a href="javascript:void(0)" onclick="getCommentList(articleId,<%=i%>)" class="page-numbers"><%=i%></a>
    </li><% } %><% if(page < totalPage) { %>
    <li class="next">
        <a href="javascript:void(0)" onclick="getCommentList(articleId,<%=totalPage%>)" class="page-numbers"><i class="iconfont icon-right"></i></a>
    </li><%}%>
</script><!-- load ace -->
<script src="${.cmsTheme}/libs/ace/ace.js"></script><!-- load ace static_highlight extension -->
<script src="${.cmsTheme}/libs/ace/ext-static_highlight.js"></script>
<script>
    var articleId = ${.article.Id};
    var commentPage = 1
    var pageSize = 10
    $(function () {
        getCommentList(articleId, commentPage)
        addCommentForm()
    })

    function initCommentForm() {
        //点击小图片，显示表情
        $(".face-btn").click(function (e) {
            $(".face-list").slideDown(); //慢慢向下展开
            e.stopPropagation(); //阻止冒泡事件
        });
        //在桌面任意地方点击，关闭表情框
        $(document).click(function () {
            $(".face-list").slideUp(); //慢慢向上收
        });
        //点击小图标时，添加功能
        $(".face-list ul li").click(function () {
            var simg = $(this).find("img").clone();
            $(".message").append(simg); //将表情添加到输入框
        });
        //点击发表按扭，发表内容
        $("span.submit").click(function () {
            var isReply = $("#is_reply").val()
            var data = {
                articleId: $("#articleId").val(),
                content: $(".message").html(),
            }
            if (!data.content) {
                layer.msg("请输入评论内容")
                $(".message").focus()
                return;
            }
            if (isReply) {
                data.pid = $("#comment_id").val()
                data.replyMemberId = $("#reply_member_id").val()
            }
            $.post(APIBASE + 'home/comment/add', data, function (res) {
                if (res.code === 0) {
                    try {
                        $(".comment-num").text(res.data.commentCount)
                        $('.message').html('') // 清空输入框
                        getCommentList(data.articleId, commentPage)
                        if (isReply) {
                            closeReply()
                            addCommentForm()
                        }
                        layer.msg(res.message)
                    } catch (err) {
                        console.error(err)
                        console.error('设置评论数量失败')
                    }
                } else {
                    layer.msg(res.message,{icon:2})
                }
            })
        });
    }

    function addCommentForm() {
        var commentFormHtml = template("comment-form-template")
        $("#comment-form").html(commentFormHtml)
        initCommentForm()
    }

    function removeCommentForm() {
        $("#comment-form").html('')
    }
    
    //获取评论列表
    function getCommentList(articleId, page) {
        if (!page) {
            page = commentPage
        }
        commentPage = page
        $.get(APIBASE + "home/comment/list", {articleId: articleId, pageNum: page, pageSize: pageSize}, function (res) {
            if (res.code === 0) {
                if (res.data.list) {
                    $("#comment_list_container").html(template("comment-list", res.data))
                    if (res.data.total > pageSize) {
                        $("#comment-pagination").html(template("comment-pagination-template", {page: res.data.currentPage, totalPage: Math.ceil(res.data.total / 10)}))
                    }
                }
            }
        })
    }
    
    function deleteComment(id){
        $.ajax({
            url:APIBASE+"home/comment/delete?id="+id,
            method:"delete",
            success:function (res){
                if(res.code === 0) {
                    layer.msg('删除成功')
                    getCommentList(articleId)
                } else {
                    layer.msg(res.message,{icon:2})
                }
            }
        })
    }

    function replyComment(obj) {
        closeReply()
        var id = $(obj).data("id")
        var replyMemberId = $(obj).data("member-id")
        var commentId = $(obj).data("comment-id")
        var replyMemberNickname = $(obj).data("member-nickname")
        removeCommentForm()
        $("#" + id).html(template("comment-reply-form-template", {commentId: commentId, replyMemberId: replyMemberId, replyMemberNickname: replyMemberNickname}))
        initCommentForm()
    }

    function closeReply() {
        $(".comment-reply-container").each(function (index, item) {
            $(item).html('')
            addCommentForm()
        })
    }

    var highlight = ace.require("ace/ext/static_highlight")
    var dom = ace.require("ace/lib/dom")

    function qsa(sel) {
        return Array.apply(null, document.querySelectorAll(sel));
    }

    qsa(".ace-code").forEach(function (codeEl) {
        highlight(codeEl, {
            mode: 'ace/mode/' + codeEl.getAttribute("data-pbcklang"),
            theme: 'ace/theme/twilight',
            startLineNumber: 1,
            showGutter: true,
            trim: false,
        }, function (highlighted) {

        });
    });
</script>