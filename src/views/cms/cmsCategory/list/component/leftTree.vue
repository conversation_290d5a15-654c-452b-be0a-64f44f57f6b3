<template>
  <div class="left-tree">
    <div class="tree-header">
      <div class="search-box">
        <el-input
          v-model="filterText"
          placeholder="输入关键字搜索"
          clearable
          class="tree-filter"
          @keyup.enter="handleTreeSearch"
        >
          <template #append>
            <el-button @click="handleTreeSearch">
              <el-icon><ele-Search /></el-icon>
            </el-button>
          </template>
        </el-input>
      </div>
      <div class="tree-buttons">
        <el-button link @click="refreshTree">
          <el-icon><ele-Refresh /></el-icon>
          刷新
        </el-button>
        <el-button link @click="toggleExpand">
          <el-icon><ele-ArrowDown v-if="isExpanded" /><ele-ArrowRight v-else /></el-icon>
          {{ isExpanded ? '收缩' : '展开' }}
        </el-button>
      </div>
    </div>
    <el-tree
      ref="treeRef"
      :data="treeData"
      :props="{
        label: 'name',
        children: 'children',
        disabled: (data) => data.type === 'channel'
      }"
      node-key="id"
      highlight-current
      @node-click="handleNodeClick"
      :expand-on-click-node="false"
      :filter-node-method="filterNode"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, watch, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { listCmsCategory } from "/@/api/cms/cmsCategory";

interface TreeNodeData {
  id: number;
  name: string;
  parentId: number;
  type: string;
  moduleId: number;
  linkedModuleId: any;
  sort: number;
  thumb: string;
  status: string;
  jumpUrl: string;
  description: string;
  alias: string;
  url: string;
  children?: TreeNodeData[];
}

export default defineComponent({
  name: "CmsCategoryLeftTree",
  emits: ["node-click"],
  setup(props, { emit }) {
    const filterText = ref('');
    const treeRef = ref();
    const treeData = ref<TreeNodeData[]>([]);
    const currentNode = ref<TreeNodeData | null>(null);
    const isExpanded = ref(false);

    // 监听搜索关键字变化
    watch(filterText, (val) => {
      if (treeRef.value) {
        treeRef.value.filter(val);
      }
    });

    // 树节点过滤方法
    const filterNode = (value: string, data: any) => {
      if (!value) return true;
      return data.name.toLowerCase().includes(value.toLowerCase());
    };

    // 处理树搜索
    const handleTreeSearch = () => {
      if (treeRef.value) {
        treeRef.value.filter(filterText.value);
      }
    };

    // 初始化分类树数据
    const initTreeData = async () => {
      try {
        const res = await listCmsCategory({});
        if (res.code === 0) {
          let list = [];
          // 检查数据结构
          if (res.data && Array.isArray(res.data)) {
            list = res.data;
          } else if (res.data && res.data.list && Array.isArray(res.data.list)) {
            list = res.data.list;
          }

          // 使用递归方法构建树形结构
          treeData.value = buildTree(list, 0);
        }
      } catch (error) {
        console.error('Failed to load category tree:', error);
      }
    };

    // 递归构建树形结构
    const buildTree = (list: TreeNodeData[], parentId: number): TreeNodeData[] => {
      return list
        .filter((item) => item.parentId === parentId)
        .map((item) => ({
          ...item,
          children: buildTree(list, item.id),
        }));
    };

    // 添加节点点击处理
    const handleNodeClick = (node: any) => {
      // 更新当前选中节点
      currentNode.value = node;
      emit('node-click', node);
    };

    // 添加刷新树的方法
    const refreshTree = async () => {
      try {
        filterText.value = '';
        await initTreeData();
        // 如果当前有选中节点，需要重新选中
        if (currentNode.value) {
          const node = treeRef.value.getNode(currentNode.value.id);
          if (node) {
            treeRef.value.setCurrentKey(currentNode.value.id);
          } else {
            currentNode.value = null;
          }
        }
      } catch (error) {
        console.error('Failed to refresh tree:', error);
      }
    };

    // 展开/收缩所有节点
    const toggleExpand = () => {
      if (!treeRef.value) return;

      // 获取所有节点
      const nodes = treeRef.value.store.nodesMap;

      // 切换展开状态
      isExpanded.value = !isExpanded.value;

      // 遍历所有节点进行展开或收缩操作
      for (const nodeKey in nodes) {
        if (nodes[nodeKey].childNodes.length > 0) {
          if (isExpanded.value) {
            nodes[nodeKey].expand();
          } else {
            nodes[nodeKey].collapse();
          }
        }
      }
    };

    onMounted(() => {
      initTreeData();
    });

    return {
      filterText,
      treeRef,
      treeData,
      isExpanded,
      handleTreeSearch,
      filterNode,
      handleNodeClick,
      refreshTree,
      toggleExpand,
    };
  }
});
</script>

<style lang="scss" scoped>
.left-tree {
  width: 250px;
  padding: 10px;
  border-right: 1px solid #dcdfe6;
  overflow: auto;

  .tree-header {
    padding-bottom: 8px;
    margin-bottom: 8px;
    border-bottom: 1px solid #eee;

    .search-box {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      .tree-filter {
        flex: 1;
      }
    }

    .tree-buttons {
      display: flex;
      justify-content: space-between;

      .el-button {
        padding: 4px 0;

        .el-icon {
          margin-right: 4px;
        }
      }
    }
  }
}
</style>
