<template>
  <div class="cms-cmsAd-container">
    <el-card shadow="hover">
      <div class="cms-cmsAd-search mb15">
        <el-form ref="queryRef" :inline="true" :model="tableData.param" label-width="100px">
          <el-form-item label="广告名称" prop="name">
            <el-input
                v-model="tableData.param.name"
                clearable
                placeholder="请输入广告名称"
                @keyup.enter.native="cmsAdList"
            />
          </el-form-item>
          <el-form-item label="广告位置" prop="position">
            <el-select v-model="tableData.param.position" clearable placeholder="请选择广告位置" @click.native="getCmsAdPositionItemsPosition">
              <el-option
                  v-for="item in positionOptions"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="cmsAdList">
              <el-icon>
                <ele-Search/>
              </el-icon>
              搜索
            </el-button>
            <el-button @click="resetQuery(queryRef)">
              <el-icon>
                <ele-Refresh/>
              </el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
                v-auth="'api/v1/cms/cmsAd/add'"
                type="primary"
                @click="handleAdd"
            >
              <el-icon>
                <ele-Plus/>
              </el-icon>
              新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                v-auth="'api/v1/cms/cmsAd/delete'"
                :disabled="multiple"
                type="danger"
                @click="handleDelete(null)"
            >
              <el-icon>
                <ele-Delete/>
              </el-icon>
              删除
            </el-button>
          </el-col>
        </el-row>
      </div>
      <el-table v-loading="loading" :data="tableData.data" border @selection-change="handleSelectionChange">
        <el-table-column align="center" type="selection" width="55"/>
        <el-table-column align="center" label="ID" prop="id" width="80px"/>
        <el-table-column label="广告位置" prop="linkedPosition.name" min-width="200px"/>
        <el-table-column label="广告名称" prop="name" min-width="200px"/>
        <el-table-column align="center" label="类型" prop="type" width="100px" :formatter="typeFormat"/>
        <el-table-column label="跳转链接" prop="url" min-width="200px"/>
        <el-table-column align="center" label="排序" prop="sort" width="100px"/>
        <el-table-column :formatter="statusFormat" align="center" label="状态" prop="status" width="100px"/>
        <el-table-column align="center" class-name="small-padding" fixed="right" label="操作" width="160px">
          <template #default="scope">
            <el-button v-auth="'api/v1/cms/cmsAd/edit'" link type="primary" @click="handleUpdate(scope.row)">
              <el-icon>
                <ele-EditPen/>
              </el-icon>
              修改
            </el-button>
            <el-button v-auth="'api/v1/cms/cmsAd/delete'" link type="primary" @click="handleDelete(scope.row)">
              <el-icon>
                <ele-DeleteFilled/>
              </el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
          v-show="tableData.total>0"
          v-model:limit="tableData.param.pageSize"
          v-model:page="tableData.param.pageNum"
          :total="tableData.total"
          @pagination="cmsAdList"
      />
    </el-card>
    <apiV1CmsCmsAdEdit
        ref="editRef"
        :positionOptions="positionOptions"
        :statusOptions="sys_normal_disable"
        @cmsAdList="cmsAdList"
        @getCmsAdPositionItemsPosition="getCmsAdPositionItemsPosition"
    ></apiV1CmsCmsAdEdit>
    <apiV1CmsCmsAdDetail
        ref="detailRef"
        :positionOptions="positionOptions"
        :statusOptions="sys_normal_disable"
        @cmsAdList="cmsAdList"
        @getCmsAdPositionItemsPosition="getCmsAdPositionItemsPosition"
    ></apiV1CmsCmsAdDetail>
  </div>
</template>
<script lang="ts">
import {ItemOptions} from "/@/api/items";
import {computed, defineComponent, getCurrentInstance, onMounted, reactive, ref, toRaw, toRefs} from "vue";
import {ElMessage, ElMessageBox, FormInstance} from "element-plus";
import {delCmsAd, listCmsAd, listCmsAdPosition} from "/@/api/cms/cmsAd";
import {CmsAdInfoData, CmsAdTableColumns, CmsAdTableDataState} from "/@/views/cms/cmsAd/list/component/model";
import apiV1CmsCmsAdEdit from "/@/views/cms/cmsAd/list/component/edit.vue";
import apiV1CmsCmsAdDetail from "/@/views/cms/cmsAd/list/component/detail.vue";

export default defineComponent({
  name: "apiV1CmsCmsAdList",
  components: {
    apiV1CmsCmsAdEdit,
    apiV1CmsCmsAdDetail
  },
  setup() {
    const {proxy} = <any>getCurrentInstance();
    const loading = ref(false);
    const queryRef = ref();
    const editRef = ref();
    const detailRef = ref();
    // 是否显示所有搜索选项
    const showAll = ref(false);
    // 非单个禁用
    const single = ref(true);
    // 非多个禁用
    const multiple = ref(true);
    const word = computed(() => {
      if (showAll.value === false) {
        //对文字进行处理
        return "展开搜索";
      } else {
        return "收起搜索";
      }
    });
    // 字典选项数据
    const {sys_normal_disable,cms_ad_type} = proxy.useDict("sys_normal_disable","cms_ad_type");
    // positionOptions关联表数据
    const positionOptions = ref<Array<ItemOptions>>([]);
    const state = reactive<CmsAdTableDataState>({
      ids: [],
      tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
          pageNum: 1,
          pageSize: 10,
          name: undefined,
          position: undefined,
          dateRange: []
        }
      }
    });
    // 页面加载时
    onMounted(() => {
      initTableData();
    });
    // 初始化表格数据
    const initTableData = () => {
      cmsAdList();
    };
    /** 重置按钮操作 */
    const resetQuery = (formEl: FormInstance | undefined) => {
      if (!formEl) return;
      formEl.resetFields();
      cmsAdList();
    };
    // 获取列表数据
    const cmsAdList = () => {
      loading.value = true;
      listCmsAd(state.tableData.param).then((res: any) => {
        let list = res.data.list ?? [];
        state.tableData.data = list;
        state.tableData.total = res.data.total;
        loading.value = false;
      });
    };
    const toggleSearch = () => {
      showAll.value = !showAll.value;
    };
    // 状态字典翻译
    const statusFormat = (row: CmsAdTableColumns) => {
      return proxy.selectDictLabel(sys_normal_disable.value, row.status);
    };
    // 类型字典翻译
    const typeFormat = (row: CmsAdTableColumns) => {
      return proxy.selectDictLabel(cms_ad_type.value, row.type);
    };
    //关联cms_ad_position表选项
    const getCmsAdPositionItemsPosition = () => {
      if (positionOptions.value && positionOptions.value.length > 0) {
        return;
      }
      proxy.getItems(listCmsAdPosition, {pageSize: 10000}).then((res: any) => {
        positionOptions.value = proxy.setItems(res, "id", "name");
      });
    };
    // 多选框选中数据
    const handleSelectionChange = (selection: Array<CmsAdInfoData>) => {
      state.ids = selection.map(item => item.id);
      single.value = selection.length != 1;
      multiple.value = !selection.length;
    };
    const handleAdd = () => {
      editRef.value.openDialog();
    };
    const handleUpdate = (row: CmsAdTableColumns) => {
      if (!row) {
        row = state.tableData.data.find((item: CmsAdTableColumns) => {
          return item.id === state.ids[0];
        }) as CmsAdTableColumns;
      }
      editRef.value.openDialog(toRaw(row));
    };
    const handleDelete = (row: CmsAdTableColumns) => {
      let msg = "你确定要删除所选数据？";
      let id: number[] = [];
      if (row) {
        msg = `此操作将永久删除数据，是否继续?`;
        id = [row.id];
      } else {
        id = state.ids;
      }
      if (id.length === 0) {
        ElMessage.error("请选择要删除的数据。");
        return;
      }
      ElMessageBox.confirm(msg, "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      })
          .then(() => {
            delCmsAd(id).then(() => {
              ElMessage.success("删除成功");
              cmsAdList();
            });
          })
          .catch(() => {
          });
    };
    const handleView = (row: CmsAdTableColumns) => {
      detailRef.value.openDialog(toRaw(row));
    };
    return {
      proxy,
      editRef,
      detailRef,
      showAll,
      loading,
      single,
      multiple,
      word,
      queryRef,
      resetQuery,
      cmsAdList,
      toggleSearch,
      statusFormat,
      typeFormat,
      sys_normal_disable,
      //关联表数据选项
      positionOptions,
      //关联cms_ad_position表选项获取数据方法
      getCmsAdPositionItemsPosition,
      handleSelectionChange,
      handleAdd,
      handleUpdate,
      handleDelete,
      ...toRefs(state)
    };
  }
});
</script>
<style lang="scss" scoped>
.colBlock {
  display: block;
}

.colNone {
  display: none;
}
</style>
