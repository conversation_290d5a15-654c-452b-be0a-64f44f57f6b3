<template>
  <div class="cms-cmsAd-edit">
    <!-- 添加或修改广告对话框 -->
    <el-dialog v-model="isShowDialog" :close-on-click-modal="false" :destroy-on-close="true" width="769px">
      <template #header>
        <div v-drag="['.cms-cmsAd-edit .el-dialog', '.cms-cmsAd-edit .el-dialog__header']">{{ (!formData.id || formData.id == 0 ? '添加' : '修改') + '广告' }}
        </div>
      </template>
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="90px">
        <el-form-item label="广告位置" prop="position">
          <el-select v-model="formData.position" placeholder="请选择广告位置" @click.native="getCmsAdPositionItemsPosition">
            <el-option v-for="item in positionOptions" :key="item.key" :label="item.value" :value="parseInt(item.key)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="广告名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入广告名称"/>
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-radio-group v-model="formData.type">
            <el-radio
                v-for="dict in cms_ad_type"
                :key="dict.value"
                :label="dict.value"
            >{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="内容" prop="content">
          <el-input v-if="formData.type==='text'" v-model="formData.content" type="textarea" :rows="4" placeholder="请输入广告内容"/>
          <upload-img
              v-else-if="formData.type==='image'"
              v-model="tempValue"
              :limit="10"
              :action="baseURL+'api/v1/system/upload/singleImg'"
              @uploadData="setUpImgList">
          </upload-img>
          <my-code-mirror v-else-if="formData.type==='js'" :height="250" v-model="formData.content" style="width: 100%;"></my-code-mirror>
        </el-form-item>
        <el-form-item label="跳转链接" prop="url">
          <el-input v-model="formData.url" placeholder="请输入跳转链接"/>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input v-model="formData.sort" placeholder="请输入排序"/>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio
                v-for="dict in statusOptions"
                :key="dict.value"
                :label="dict.value"
            >{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="onSubmit">确 定</el-button>
          <el-button @click="onCancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts">
import {defineComponent, getCurrentInstance, reactive, ref, toRefs, unref} from "vue";
import {ElMessage} from "element-plus";
import MyCodeMirror from "/@/components/myCodeMirror/index.vue";
import {addCmsAd, getCmsAd, updateCmsAd} from "/@/api/cms/cmsAd";
import {CmsAdEditState, CmsAdInfoData} from "/@/views/cms/cmsAd/list/component/model";
import uploadImg from "/@/components/uploadImg/index.vue";

export default defineComponent({
  name: "apiV1CmsCmsAdEdit",
  components: {MyCodeMirror,uploadImg},
  props: {
    statusOptions: {
      type: Array,
      default: () => []
    },
    positionOptions: {
      type: Array,
      default: () => []
    }
  },
  setup(props, {emit}) {
    const baseURL: string | undefined | boolean = import.meta.env.VITE_API_URL;
    const {proxy} = <any>getCurrentInstance();
    const formRef = ref<HTMLElement | null>(null);
    const menuRef = ref();
    const tempValue = ref([]);
    const state = reactive<CmsAdEditState>({
      loading: false,
      isShowDialog: false,
      formData: {
        id: undefined,
        name: undefined,
        type: 'image',
        content: undefined,
        url: undefined,
        createdAt: undefined,
        updatedAt: undefined,
        sort: undefined,
        status: '1',
        position: undefined,
        linkedCmsAdCmsAdPosition: {
          id: undefined,    // ID
          name: undefined    // 位置名称
        }
      },
      // 表单校验
      rules: {
        name: [
          {required: true, message: "广告名称不能为空", trigger: "blur"}
        ],
        position: [
          {required: true, message: "广告位置不能为空", trigger: "blur"}
        ]
      }
    });
    const { cms_ad_type } = proxy.useDict('cms_ad_type');
    // 打开弹窗
    const openDialog = (row?: CmsAdInfoData) => {
      resetForm();
      tempValue.value = []
      if (row) {
        getCmsAdPositionItemsPosition();
        getCmsAd(row.id!).then((res: any) => {
          const data = res.data;
          if(data.type==='image' && data.content!==''){
            tempValue.value = JSON.parse(data.content)
          }
          data.status = "" + data.status;
          state.formData = data;
        });
      }
      state.isShowDialog = true;
    };
    // 关闭弹窗
    const closeDialog = () => {
      state.isShowDialog = false;
    };
    // 取消
    const onCancel = () => {
      closeDialog();
    };
    const setUpImgList = (val: any) => {
      state.formData.content = val;
    };
    // 提交
    const onSubmit = () => {
      const formWrap = unref(formRef) as any;
      if (!formWrap) return;
      if(state.formData.type==='image'){
        if(Array.isArray(state.formData.content)){
          state.formData.content = JSON.stringify(state.formData.content)
        }
      }
      formWrap.validate((valid: boolean) => {
        if (valid) {
          state.loading = true;
          if (!state.formData.id || state.formData.id === 0) {
            //添加
            addCmsAd(state.formData).then(() => {
              ElMessage.success("添加成功");
              closeDialog(); // 关闭弹窗
              emit("cmsAdList");
            }).finally(() => {
              state.loading = false;
            });
          } else {
            //修改
            updateCmsAd(state.formData).then(() => {
              ElMessage.success("修改成功");
              closeDialog(); // 关闭弹窗
              emit("cmsAdList");
            }).finally(() => {
              state.loading = false;
            });
          }
        }
      });
    };
    const resetForm = () => {
      state.formData = {
        id: undefined,
        name: undefined,
        type: 'image',
        content: '',
        url: undefined,
        createdAt: undefined,
        updatedAt: undefined,
        sort: undefined,
        status: '1',
        position: undefined,
        linkedCmsAdCmsAdPosition: {
          id: undefined,    // ID
          name: undefined    // 位置名称
        }
      };
    };
    //关联cms_ad_position表选项
    const getCmsAdPositionItemsPosition = () => {
      emit("getCmsAdPositionItemsPosition");
    };
    return {
      proxy,
      baseURL,
      openDialog,
      closeDialog,
      onCancel,
      cms_ad_type,
      onSubmit,
      setUpImgList,
      menuRef,
      formRef,
      tempValue,
      getCmsAdPositionItemsPosition,
      ...toRefs(state)
    };
  }
});
</script>
<style scoped>
</style>
