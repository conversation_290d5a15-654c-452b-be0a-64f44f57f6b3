/**
 * @Company: 云南奇讯科技有限公司
 * @Author: yxf
 * @Description:
 * @Version: 1.0.0
 * @Date: 2023/2/16 17:07
 */

package member

import (
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/internal/app/home/<USER>"
)

type notificationsController struct{}

var NotificationsController = new(notificationsController)

// List
//
//	@Description: 前台会员中心通知列表
//	@receiver c
//	@param r
func (c *notificationsController) List(r *ghttp.Request) {
	view := service.CmsTemplate().GetCmsView(r)
	service.HomeCommon().Success(r, view, "member/notifications/list.html")
}

// Show
//
//	@Description: 前台会员通知详情页面
//	@receiver c
//	@param r
func (c *notificationsController) Show(r *ghttp.Request) {
	view := service.CmsTemplate().GetCmsView(r)
	service.HomeCommon().Success(r, view, "member/notifications/show.html")
}
