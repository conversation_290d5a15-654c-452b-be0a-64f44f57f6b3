<template>
  <div class="audit-cmsAudit-audit">
    <!-- 审核文章对话框 -->
    <el-dialog v-model="isShowDialog" width="800px" :close-on-click-modal="false" :destroy-on-close="true">
      <template #header>
        <div v-drag="['.audit-cmsAudit-audit .el-dialog', '.audit-cmsAudit-audit .el-dialog__header']">审核文章</div>
      </template>

      <!-- 审核步骤条 -->
      <div class="audit-steps-container" v-if="auditSteps.length > 0">
        <el-steps :active="activeStep" align-center process-status="process" finish-status="success">
          <el-step
            v-for="(step, index) in auditSteps"
            :key="step.id"
            :title="step.title"
            :description="`第${step.step}审`"
            :status="getStepStatus(index)"
          />
        </el-steps>
      </div>

      <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px">
        <el-form-item label="文章标题" prop="articleId">
          <div>{{ formData.articleInfo?.title || '无标题' }}</div>
        </el-form-item>
        <el-form-item label="栏目" prop="categoryId">
          <div>{{ formData.categoryInfo?.name || '无栏目' }}</div>
        </el-form-item>

        <el-form-item label="当前步骤" prop="stepId">
          <div>{{ formData.stepInfo?.title || '无' }}</div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">审核通过</el-button>
          <el-button type="warning" @click="showRejectDialog">驳回</el-button>
          <el-button @click="onCancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 驳回对话框 -->
    <el-dialog v-model="isRejectDialogVisible" title="请输入驳回原因" width="500px" append-to-body>
      <el-form>
        <el-form-item>
          <el-input
            v-model="rejectReason"
            type="textarea"
            :rows="4"
            placeholder="请输入驳回原因"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitReject">确 定</el-button>
          <el-button @click="isRejectDialogVisible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
  import { defineComponent, reactive, ref, toRefs, getCurrentInstance, PropType } from "vue";
  import { ElMessage, FormInstance } from "element-plus";
  import {
    getCmsAudit,
    auditCmsAudit,
  } from "/@/api/cms/audit/cmsAudit";
  import { listCmsAuditStep } from "/@/api/cms/audit/cmsAuditStep";
  import { CmsAuditInfoData } from "./model";

  interface CmsAuditEditState {
    loading:boolean;
    isShowDialog: boolean;
    formData: {
      id?: number;
      articleId?: string;
      categoryId?: string;
      stepId?: string;
      status?: string;
      content?: string;
      articleInfo?: any;
      categoryInfo?: any;
      stepInfo?: any;
    };
    // 表单校验
    rules: {
      [key: string]: Array<any>;
    };
  }

  export default defineComponent({
    name: "ApiV1CmsAuditCmsAuditAudit",
    props: {
      statusOptions: {
        type: Array as PropType<Array<{ value: string; label: string }>>,
        required: true
      }
    },
    emits: ["cmsAuditList"],
    setup(props, { emit }) {
      const { proxy } = <any>getCurrentInstance();
      const formRef = ref<FormInstance>();

      // 驳回对话框相关状态
      const isRejectDialogVisible = ref(false);
      const rejectReason = ref('');

      // 审核步骤数据
      const auditSteps = ref([]);
      const activeStep = ref(0);

      // 加载审核步骤数据
      const loadAuditSteps = async () => {
        try {
          const res = await listCmsAuditStep({});
          if (res.code === 0 && res.data && res.data.list) {
            // 按照步骤排序
            auditSteps.value = res.data.list.sort((a, b) => a.step - b.step);
          }
        } catch (error) {
          console.error('加载审核步骤失败:', error);
        }
      };

      // 根据当前审核记录设置活动步骤
      const setActiveStep = () => {
        // 如果没有 nextStepId，可能是审核流程已完成
        if (!state.formData.nextStepId) {
          // 如果状态为已审核通过，则设置最后一个步骤为当前步骤
          if (state.formData.status === '1' && auditSteps.value.length > 0) {
            // 按照步骤值排序
            const sortedSteps = [...auditSteps.value].sort((a, b) => a.step - b.step);
            // 设置最后一个步骤为当前步骤
            const lastStepIndex = auditSteps.value.findIndex(step => step.id === sortedSteps[sortedSteps.length - 1].id);
            if (lastStepIndex !== -1) {
              activeStep.value = lastStepIndex;
            }
          }
          return;
        }

        // 找到当前步骤对应的对象
        const currentStepObj = auditSteps.value.find(step => step.id === state.formData.nextStepId);
        if (!currentStepObj) return;

        // 获取当前审核步骤值
        const currentStep = currentStepObj.step;

        // 设置当前步骤索引
        const currentStepIndex = auditSteps.value.findIndex(step => step.id === state.formData.nextStepId);
        if (currentStepIndex !== -1) {
          activeStep.value = currentStepIndex;
        }
      };

      // 获取步骤状态，用于设置步骤条颜色
      const getStepStatus = (index: number) => {
        // 如果没有活动步骤，则所有步骤都是等待状态
        if (activeStep.value === -1) return 'wait';

        // 如果审核流程已完成
        if (!state.formData.nextStepId && state.formData.status === '1') {
          // 最后一个步骤显示为当前步骤（蓝色）
          if (index === activeStep.value) return 'process';
          // 其他步骤显示为完成（绿色）
          if (index < activeStep.value) return 'success';
        }

        // 当前步骤显示为进行中（蓝色）
        if (index === activeStep.value) return 'process';

        // 当前步骤之前的步骤显示为完成（绿色）
        if (index < activeStep.value) return 'success';

        // 当前步骤之后的步骤显示为等待（灰色）
        return 'wait';
      };

      const state = reactive<CmsAuditEditState>({
        loading:false,
        isShowDialog: false,
        formData: {
          id: undefined,
          articleId: undefined,
          categoryId: undefined,
          stepId: undefined,
          status: undefined,
          content: undefined,
          articleInfo: undefined,
          categoryInfo: undefined,
          stepInfo: undefined,
        },
        // 表单校验
        rules: {}
      });
      const { isShowDialog,formData } = toRefs(state);
      // 打开弹窗
      const openDialog = async (row?: CmsAuditInfoData) => {
        resetForm();
        // 加载审核步骤数据
        await loadAuditSteps();

        if(row && row.id) {
          try {
            const res = await getCmsAudit(row.id);
            const data = res.data;
            state.formData = data;

            // 输出调试信息
            console.log('审核记录数据:', data);
            console.log('当前步骤ID:', data.stepId);
            console.log('下一步骤ID:', data.nextStepId);

            // 设置当前活动步骤
            setActiveStep();
          } catch (error) {
            console.error('获取审核记录失败:', error);
          }
        }
        state.isShowDialog = true;
      };
      // 关闭弹窗
      const closeDialog = () => {
        state.isShowDialog = false;
      };

      // 取消
      const onCancel = () => {
        closeDialog();
      };

      const resetForm = ()=>{
        state.formData = {
          id: undefined,
          articleId: undefined,
          categoryId: undefined,
          stepId: undefined,
          status: undefined,
          content: undefined,
          articleInfo: undefined,
          categoryInfo: undefined,
          stepInfo: undefined,
        }
      };

      // 提交表单
      const submitForm = () => {
        state.loading = true;
        // 设置状态为审核通过
        state.formData.status = '1';
        // 设置审核意见
        state.formData.content = '审核通过';

        if (state.formData.id) {
          auditCmsAudit(state.formData).then((res:any) => {
            ElMessage.success("审核通过");
            closeDialog();
            emit("cmsAuditList");
          }).catch((error) => {
            ElMessage.error('审核失败');
          }).finally(() => {
            state.loading = false;
          });
        }
      };

      // 显示驳回对话框
      const showRejectDialog = () => {
        rejectReason.value = '';
        isRejectDialogVisible.value = true;
      };

      // 提交驳回
      const submitReject = () => {
        if (!rejectReason.value.trim()) {
          ElMessage.warning('请输入驳回原因');
          return;
        }

        state.loading = true;
        // 设置状态为驳回
        state.formData.status = '2';
        // 设置审核意见为用户输入的驳回原因
        state.formData.content = rejectReason.value;

        if (state.formData.id) {
          auditCmsAudit(state.formData).then((res:any) => {
            ElMessage.success('驳回成功');
            isRejectDialogVisible.value = false;
            closeDialog();
            emit('cmsAuditList');
          }).catch((error) => {
            ElMessage.error('驳回失败');
          }).finally(() => {
            state.loading = false;
          });
        }
      };

      // 在 setup 函数中返回需要暴露的方法
      return {
        formRef,
        isShowDialog,
        formData,
        onCancel,
        submitForm,
        openDialog,
        // 驳回相关
        isRejectDialogVisible,
        rejectReason,
        showRejectDialog,
        submitReject,
        // 步骤条相关
        auditSteps,
        activeStep,
        getStepStatus,
      };
    },
  });
</script>

<style scoped>
  .audit-cmsAudit-audit :deep(.el-form-item--large .el-form-item__label){
    font-weight: bolder;
  }
  .pic-block{
    margin-right: 8px;
  }
  .file-block{
    width: 100%;
    border: 1px solid var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
    margin-bottom: 5px;
    padding: 3px 6px;
  }
  .ml-2{margin-right: 5px;}

  /* 审核步骤条样式 */
  .audit-steps-container {
    padding: 20px;
    margin-bottom: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }
</style>
