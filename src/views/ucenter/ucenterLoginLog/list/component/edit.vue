<template>  
  <div class="ucenter-ucenterLoginLog-edit">
    <!-- 添加或修改系统访问记录对话框 -->
    <el-dialog v-model="isShowDialog" width="769px" :close-on-click-modal="false" :destroy-on-close="true">
      <template #header>
        <div v-drag="['.ucenter-ucenterLoginLog-edit .el-dialog', '.ucenter-ucenterLoginLog-edit .el-dialog__header']">{{(!formData.id || formData.id==0?'添加':'修改')+'系统访问记录'}}</div>
      </template>
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="90px">        
        <el-form-item label="登录账号" prop="loginName">
          <el-input v-model="formData.loginName" placeholder="请输入登录账号" />
        </el-form-item>        
        <el-form-item label="登录IP地址" prop="ipaddr">
          <el-input v-model="formData.ipaddr" placeholder="请输入登录IP地址" />
        </el-form-item>        
        <el-form-item label="登录地点" prop="loginLocation">
          <el-input v-model="formData.loginLocation" placeholder="请输入登录地点" />
        </el-form-item>        
        <el-form-item label="浏览器类型" prop="browser">
          <el-input v-model="formData.browser" placeholder="请输入浏览器类型" />
        </el-form-item>        
        <el-form-item label="操作系统" prop="os">
          <el-input v-model="formData.os" placeholder="请输入操作系统" />
        </el-form-item>        
        <el-form-item label="登录状态（0成功 1失败）" prop="status">
          <el-input v-model="formData.status" placeholder="请输入登录状态（0成功 1失败）" />
        </el-form-item>        
        <el-form-item label="提示消息" prop="msg">
          <el-input v-model="formData.msg" placeholder="请输入提示消息" />
        </el-form-item>        
        <el-form-item label="登录时间" prop="loginTime">
          <el-date-picker clearable  style="width: 200px"
            v-model="formData.loginTime"
            type="datetime"
            placeholder="选择登录时间">
          </el-date-picker>
        </el-form-item>       
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="onSubmit">确 定</el-button>
          <el-button @click="onCancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts">
import { reactive, toRefs, defineComponent,ref,unref,getCurrentInstance } from 'vue';
import {ElMessageBox, ElMessage, FormInstance,UploadProps} from 'element-plus';
import {
  listUcenterLoginLog,
  getUcenterLoginLog,
  delUcenterLoginLog,
  addUcenterLoginLog,
  updateUcenterLoginLog,  
} from "/@/api/ucenter/ucenterLoginLog";
import {
  UcenterLoginLogTableColumns,
  UcenterLoginLogInfoData,
  UcenterLoginLogTableDataState,
  UcenterLoginLogEditState,  
} from "/@/views/ucenter/ucenterLoginLog/list/component/model"
export default defineComponent({
  name:"apiV1UcenterUcenterLoginLogEdit",
  components:{    
  },
  props:{    
  },
  setup(props,{emit}) {    
    const {proxy} = <any>getCurrentInstance()
    const formRef = ref<HTMLElement | null>(null);
    const menuRef = ref();    
    const state = reactive<UcenterLoginLogEditState>({
      loading:false,
      isShowDialog: false,
      formData: {        
        id: undefined,        
        loginName: undefined,        
        ipaddr: undefined,        
        loginLocation: undefined,        
        browser: undefined,        
        os: undefined,        
        status: undefined,        
        msg: undefined,        
        loginTime: undefined,        
      },
      // 表单校验
      rules: {        
        id : [
            { required: true, message: "访问ID不能为空", trigger: "blur" }
        ],        
        loginName : [
            { required: true, message: "登录账号不能为空", trigger: "blur" }
        ],        
      }
    });
    // 打开弹窗
    const openDialog = (row?: UcenterLoginLogInfoData) => {
      resetForm();
      if(row) {        
        getUcenterLoginLog(row.id!).then((res:any)=>{
          const data = res.data;          
          state.formData = data;
      })
    }
      state.isShowDialog = true;
    };
    // 关闭弹窗
    const closeDialog = () => {
      state.isShowDialog = false;
    };
    // 取消
    const onCancel = () => {
      closeDialog();
    };
    // 提交
    const onSubmit = () => {
      const formWrap = unref(formRef) as any;
      if (!formWrap) return;
      formWrap.validate((valid: boolean) => {
        if (valid) {
          state.loading = true;
          if(!state.formData.id || state.formData.id===0){
            //添加
          addUcenterLoginLog(state.formData).then(()=>{
              ElMessage.success('添加成功');
              closeDialog(); // 关闭弹窗
              emit('ucenterLoginLogList')
            }).finally(()=>{
              state.loading = false;
            })
          }else{
            //修改
          updateUcenterLoginLog(state.formData).then(()=>{
              ElMessage.success('修改成功');
              closeDialog(); // 关闭弹窗
              emit('ucenterLoginLogList')
            }).finally(()=>{
              state.loading = false;
            })
          }
        }
      });
    };
    const resetForm = ()=>{
      state.formData = {        
        id: undefined,        
        loginName: undefined,        
        ipaddr: undefined,        
        loginLocation: undefined,        
        browser: undefined,        
        os: undefined,        
        status: undefined,        
        msg: undefined,        
        loginTime: undefined,        
      }
    };    
    return {
      proxy,
      openDialog,
      closeDialog,
      onCancel,
      onSubmit,
      menuRef,
      formRef,      
      ...toRefs(state),
    };
  }
})
</script>
<style scoped>  
</style>