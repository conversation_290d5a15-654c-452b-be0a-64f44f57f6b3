// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2023-11-08 10:11:31
// 生成路径: api/v1/mail/plugin_mail.go
// 生成人：gfast
// desc:邮件发送相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package mail

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/plugins/mail/model"
)

// PluginMailSearchReq 分页请求参数
type PluginMailSearchReq struct {
	g.Meta `path:"/list" tags:"插件接口/邮箱管理" method:"get" summary:"邮箱列表"`
	commonApi.Author
	model.PluginMailSearchReq
}

// PluginMailSearchRes 列表返回结果
type PluginMailSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.PluginMailSearchRes
}

// PluginMailAddReq 添加操作请求参数
type PluginMailAddReq struct {
	g.Meta `path:"/add" tags:"插件接口/邮箱管理" method:"post" summary:"邮箱添加"`
	commonApi.Author
	*model.PluginMailAddReq
}

// PluginMailAddRes 添加操作返回结果
type PluginMailAddRes struct {
	commonApi.EmptyRes
}

// PluginMailEditReq 修改操作请求参数
type PluginMailEditReq struct {
	g.Meta `path:"/edit" tags:"插件接口/邮箱管理" method:"put" summary:"邮箱修改"`
	commonApi.Author
	*model.PluginMailEditReq
}

// PluginMailEditRes 修改操作返回结果
type PluginMailEditRes struct {
	commonApi.EmptyRes
}

// PluginMailGetReq 获取一条数据请求
type PluginMailGetReq struct {
	g.Meta `path:"/get" tags:"插件接口/邮箱管理" method:"get" summary:"邮箱信息"`
	commonApi.Author
	Id uint `p:"id" v:"required#主键必须"` //通过主键获取
}

// PluginMailGetRes 获取一条数据结果
type PluginMailGetRes struct {
	g.Meta `mime:"application/json"`
	*model.PluginMailInfoRes
}

// PluginMailDeleteReq 删除数据请求
type PluginMailDeleteReq struct {
	g.Meta `path:"/delete" tags:"插件接口/邮箱管理" method:"delete" summary:"删除邮箱"`
	commonApi.Author
	Ids []uint `p:"ids" v:"required#主键必须"` //通过主键删除
}

// PluginMailDeleteRes 删除数据返回
type PluginMailDeleteRes struct {
	commonApi.EmptyRes
}

// PluginMailSendDataReq 发送邮件
type PluginMailSendDataReq struct {
	g.Meta `path:"/sendData" tags:"插件接口/邮箱管理" method:"post" summary:"发送邮件"`
	commonApi.Author
	*model.MailReq
}

// PluginMailSendDataRes 发送邮件返回
type PluginMailSendDataRes struct {
	commonApi.EmptyRes
}
