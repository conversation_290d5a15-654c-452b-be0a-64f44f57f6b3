// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-05-07 08:21:10
// 生成路径: internal/app/cms/audit/dao/cms_audit.go
// 生成人：zhanghui
// desc:文章审核
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/cms/audit/dao/internal"
)

// cmsAuditDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type cmsAuditDao struct {
	*internal.CmsAuditDao
}

var (
	// CmsAudit is globally public accessible object for table tools_gen_table operations.
	CmsAudit = cmsAuditDao{
		internal.NewCmsAuditDao(),
	}
)

// Fill with you ideas below.
