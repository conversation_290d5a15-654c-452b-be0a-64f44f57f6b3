import{s as f}from"./pinia-DwJf82dV.js";import{u as d,a as u}from"./index-BGmsw1a8.js";import{d as _,X as T,k,ad as C,ag as g,c as e,o as i,a as s,M as h,O as v,a6 as w,P as N}from"./@vue-C21YZbHS.js";import"./vue-router-BDT9dCQ7.js";import"./js-cookie-Cz0CWeBA.js";import"./@element-plus-_Cc-TEQX.js";import"./nprogress-Scw6VIZr.js";import"./@intlify-D8kAWvSi.js";import"./axios-B62VicFi.js";import"./qs-D41HS0uK.js";import"./side-channel-DytTmCZg.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-DbsE5JW_.js";import"./crypto-js-oM26lpET.js";import"./side-channel-list-C_7Uk4RP.js";import"./side-channel-map-BUokNT8A.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-bSkmVQJL.js";import"./element-plus-CUmVNDWO.js";import"./lodash-es-S0Y0Up6J.js";import"./@vueuse-D_5IXmcI.js";import"./@popperjs-D_chPuIy.js";import"./@ctrl-r5W6hzzQ.js";import"./dayjs-CepoAVPu.js";import"./async-validator-9PlIezaS.js";import"./memoize-one-BdPwpGay.js";import"./normalize-wheel-es-BQoi3Ox2.js";import"./@floating-ui-DwceP2Gb.js";import"./vue-i18n-BkLk9Ti1.js";import"./vue-iNFchrNl.js";import"./vue-clipboard3-Dr8V5xR7.js";import"./clipboard-DzJOpNE6.js";import"./mitt-DJ65BbbF.js";import"./vue-grid-layout-CIPjP0XN.js";import"./vue-simple-uploader-j6sX1TKo.js";import"./vue-ueditor-wrap-CJ5gjsd3.js";const $=_({name:"pagesWorkflowTool",setup(t,{emit:r}){const m=d(),{themeConfig:l}=f(m),n=T({toolList:[{icon:"ele-Help",title:"帮助",fnName:"help"},{icon:"ele-Download",title:"下载",fnName:"download"},{icon:"ele-Check",title:"提交",fnName:"submit"},{icon:"ele-DocumentCopy",title:"复制",fnName:"copy"},{icon:"ele-Delete",title:"删除",fnName:"del"},{icon:"ele-FullScreen",title:"全屏",fnName:"fullscreen"}]});return{setToolTitle:k(()=>{let{globalTitle:o}=l.value;return`${o}工作流`}),onToolClick:o=>{r("tool",o)},...C(n)}}}),y={class:"workflow-tool"},D={class:"pl15"},S={class:"workflow-tool-right"},b=["title","onClick"];function B(t,r,m,l,n,a){const p=g("SvgIcon");return i(),e("div",y,[s("div",D,h(t.setToolTitle),1),s("div",S,[(i(!0),e(v,null,w(t.toolList,(o,c)=>(i(),e("div",{class:"workflow-tool-icon",key:c,title:o.title,onClick:I=>t.onToolClick(o.fnName)},[N(p,{name:o.icon},null,8,["name"])],8,b))),128))])])}const yo=u($,[["render",B],["__scopeId","data-v-28fefbdd"]]);export{yo as default};
