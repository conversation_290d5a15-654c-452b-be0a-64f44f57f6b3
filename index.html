<!DOCTYPE html>
<html lang="zh-CN">
	<head>
		<meta charset="utf-8" />
		<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<meta
			name="keywords"
			content="vue-next-admin，vue-prev-admin，vue-admin-wonderful，后台管理系统一站式平台模板，希望可以帮你完成快速开发。vue2.x，vue2.0，vue2，vue3，vue3.x，vue3.0，CompositionAPI，typescript，element plus，element，plus，admin，wonderful，wonderful-next，vue-next-admin，vite，vite-admin，快速，高效，后台模板，后台系统，管理系统"
		/>
		<meta
			name="description"
			content="vue-next-admin，基于 vue3 + CompositionAPI + typescript + vite + element plus，适配手机、平板、pc 的后台开源免费管理系统模板！vue-prev-admin，基于 vue2 +  element ui，适配手机、平板、pc 的后台开源免费管理系统模板！"
		/>
		<link rel="icon" href="/favicon.ico" />
		<title>gfast3.2后台管理系统</title>
	</head>
	<body>
		<div id="app"></div>
		<script type="module" src="/src/main.ts"></script>
	</body>
</html>
