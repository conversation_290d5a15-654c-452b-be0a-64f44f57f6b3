package libCms

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/crypto/gmd5"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
	cmsAuditDao "github.com/tiger1103/gfast/v3/internal/app/cms/audit/dao"
	cmsAuditDo "github.com/tiger1103/gfast/v3/internal/app/cms/audit/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/cms/dao"
	cmsModel "github.com/tiger1103/gfast/v3/internal/app/cms/model"
	cmsService "github.com/tiger1103/gfast/v3/internal/app/cms/service"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/liberr"
	"net"
	"net/url"
	"strings"
)

const (
	urlSuffix = ".html"
)

// RecordVisitLog 记录访问日志并进行访问统计
// 参数:
//
//	ctx: 上下文，用于传递请求、用户等信息
//	typeName: 访问类型，决定访问统计的类型（"home", "category", "article"）
//	categoryId: 文章分类ID，用于统计特定分类的访问情况
//	articleId: 文章ID，用于统计特定文章的访问情况
//
// 返回值:
//
//	bool: 日志记录是否成功
func RecordVisitLog(ctx context.Context, typeName string, categoryId int, articleId int) bool {
	//获取ip
	ipStr := ghttp.RequestFromCtx(ctx).GetRemoteIp()
	//获取浏览器 agent
	agent := ghttp.RequestFromCtx(ctx).GetHeader("User-Agent")
	//获取url
	urls := ghttp.RequestFromCtx(ctx).Request.URL.String()

	// 创建访问日志记录
	err := cmsService.CmsVisitLog().Add(ctx, &cmsModel.CmsVisitLogAddReq{
		Ip:         ipStr,
		Url:        urls,
		Agent:      agent,
		ArticleId:  articleId,
		CategoryId: categoryId,
	})
	// 访问统计
	switch typeName {
	case "home":
		_ = cmsService.CmsVisit().Add(ctx, &cmsModel.CmsVisitAddReq{
			Type:       "home",
			CategoryId: categoryId,
			ArticleId:  articleId,
		})
	case "category":
		_ = cmsService.CmsVisit().Add(ctx, &cmsModel.CmsVisitAddReq{
			Type:       "category",
			CategoryId: categoryId,
			ArticleId:  articleId,
		})
	case "article":
		_ = cmsService.CmsVisit().Add(ctx, &cmsModel.CmsVisitAddReq{
			Type:       "article",
			CategoryId: categoryId,
			ArticleId:  articleId,
		})
	}
	return err == nil
}

// CheckCategoryIpAccess 检查给定类别ID的IP访问权限。
// 该函数从cmsService中获取与类别ID关联的IP表格，并根据客户端IP判断访问权限。
// ctx: 上下文，用于传递请求相关的信息。
// categoryId: 类别ID，用于获取特定类别的IP访问规则。
// 返回值: 如果客户端IP有访问权限，则返回true；否则返回false。
func CheckCategoryIpAccess(ctx context.Context, categoryId uint) bool {
	// 获取与类别ID关联的IP表格。
	ipTable, _ := cmsService.CmsCategoryIpaccess().GetIptableByCategoryId(ctx, categoryId)
	// 获取客户端IP。
	ipStr := ghttp.RequestFromCtx(ctx).GetRemoteIp()

	// 如果客户端IP为空，则允许访问。
	if ipStr == "" {
		return true
	}
	// 如果IP表格存在且不为空，则进一步检查客户端IP是否在允许列表中。
	if ipTable != nil && ipTable.Iptable != "" {
		// 将回车分隔的IP地址分割成数组。
		ips := strings.Split(ipTable.Iptable, "\n")
		// 将IP地址转换为长整数类型以便比较。
		ipInt, _ := IPv4ToInt(ipStr)
		// 遍历IP表格中的每个IP地址或IP范围。
		for _, ip := range ips {
			// 将"-"分隔的IP范围分割成数组。
			ips2 := strings.Split(ip, "-")
			// 如果是IP范围，检查客户端IP是否在范围内。
			if len(ips2) == 2 {
				ipInt2, _ := IPv4ToInt(ips2[0])
				ipInt3, _ := IPv4ToInt(ips2[1])
				if ipInt >= ipInt2 && ipInt <= ipInt3 {
					return true
				}
			}
			// 如果是单个IP地址，检查客户端IP是否与之相等。
			if len(ips2) == 1 {
				ipInt2, _ := IPv4ToInt(ips2[0])
				if ipInt == ipInt2 {
					return true
				}
			}
		}
		// 如果客户端IP不在允许列表中，则拒绝访问。
		return false
	}
	// 如果IP表格不存在或为空，则允许访问。
	return true
}

// IPv4ToInt 将IPv4地址字符串转换为无符号32位整数。
// 这个函数接收一个IPv4地址字符串ipStr作为输入参数，
// 并返回一个uint32类型的整数和一个错误值。
// 如果输入的字符串无法解析为有效的IPv4地址，函数将返回错误。
func IPv4ToInt(ipStr string) (uint32, error) {
	ip := net.ParseIP(ipStr)
	if ip == nil {
		return 0, fmt.Errorf("invalid IPv4 address: %s", ipStr)
	}
	ip = ip.To4()
	if ip == nil {
		return 0, fmt.Errorf("%s is not an IPv4 address", ipStr)
	}
	return uint32(ip[0])<<24 | uint32(ip[1])<<16 | uint32(ip[2])<<8 | uint32(ip[3]), nil
}

// GetCmsUrl 获取完整的CMS网址
// 根据提供的分类、初步URL和标识符构建最终的URL
// 如果提供的URL已经完整，则直接返回该URL
// 否则，通过查询CMS配置信息来构建完整的URL
// 参数:
//
//	cate - 内容分类，用于构建URL
//	url - 初步的URL，如果为空则需要构建
//	id - 内容的标识符，用于构建URL
//
// 返回值:
//
//	fullUrl - 最终构建的完整URL
func GetCmsUrl(cate string, url string, id int) (fullUrl string) {
	if url != "" {
		return url
	} else {
		ctx := gctx.New()
		cmsConfigMap, _ := cmsService.CmsConfig().GetCmsConfigMap(ctx)
		server := cmsConfigMap["site_url"]
		url = buildURL(cate, server, gconv.String(id))
		return url
	}
}

// GetPreviewUrl 生成预览URL。
// 该函数接收上下文ctx、分类cate、原始URL和一个ID作为输入参数，
// 并返回一个带有预览密钥的URL。
// 函数首先调用GetCmsUrl来获取基于输入参数的CMS URL，
// 然后生成一个唯一的密钥并与ID关联存储在Redis中，
// 最后将该密钥附加到URL作为查询参数返回。
func GetPreviewUrl(ctx context.Context, cate string, url string, id int) (previewUrl string) {
	url = GetCmsUrl(cate, url, id)
	// 生成唯一的键
	key, _ := gmd5.EncryptString("preview_url_" + gconv.String(id))
	// 在Redis中设置带有"preview:"前缀的键
	cfg, _ := cmsService.CmsConfig().GetByName(ctx, "preview_key_timeout")
	_ = g.Redis().SetEX(ctx, "preview:"+key, id, gconv.Int64(cfg.Value))
	return url + "?key=" + key
}

func buildURL(cate string, server string, id string) string {
	// 确保 server 和 id 非空
	if server == "" || id == "" {
		return ""
	}

	// 确保 server 以斜杠结尾
	if !strings.HasSuffix(server, "/") {
		server += "/"
	}
	// 对 id 进行转义，确保安全性
	escapedID := url.PathEscape(id)
	switch cate {
	case "show":
		return server + "show/" + id + urlSuffix
	case "channel":
		// 构建完整的 URL
		return server + cate + "/" + escapedID + urlSuffix
	case "list":
		// 构建完整的 URL
		return server + cate + "/" + escapedID + urlSuffix
	case "page":
		// 构建完整的 URL
		return server + cate + "/" + escapedID + urlSuffix
	}

	return ""
}

// IsSuperAdmin 判断当前用户是否为超级管理员
//
// 该函数检查当前登录用户是否具有超级管理员权限
// 超级管理员拥有系统的最高权限，可以访问和操作所有功能
//
// 参数:
//   - ctx: 上下文对象，用于获取当前用户ID和权限检查
//
// 返回:
//   - bool: 如果当前用户是超级管理员返回 true，否则返回 false
func IsSuperAdmin(ctx context.Context) bool {
	// 获取当前登录用户的ID
	userId := sysService.Context().GetUserId(ctx)
	if userId == 0 {
		// 用户ID为0表示未登录或会话无效
		g.Log().Debug(ctx, "用户未登录或会话无效，非超级管理员")
		return false
	}

	// 调用系统服务判断用户是否为超级管理员
	isSuperAdmin := sysService.SysUser().IsSupperAdmin(ctx, userId)
	g.Log().Debug(ctx, "检查用户是否为超级管理员", "userId:", userId, "isSuperAdmin:", isSuperAdmin)

	return isSuperAdmin
}

// CheckCategoryPermission 检测用户对指定分类的按钮权限
//
// 该函数检查当前登录用户是否具有对指定分类的特定按钮权限
// 超级管理员对类型为"list"的分类自动拥有所有权限
//
// 参数:
//   - ctx: 上下文对象，用于获取当前用户信息和数据库操作
//   - permission: 要检查的权限名称，如"publish"、"list"、"status"等
//   - categoryId: 要检查权限的分类 ID
//
// 返回:
//   - bool: 如果用户有指定权限返回 true，否则返回 false
func CheckCategoryPermission(ctx context.Context, permission string, categoryId uint) (res bool) {
	// 首先检查用户是否为超级管理员
	if IsSuperAdmin(ctx) {
		// 超级管理员需要检查分类类型是否为"list"
		category, err := dao.CmsCategory.Ctx(ctx).
			Where(dao.CmsCategory.Columns().Id, categoryId).
			Where(dao.CmsCategory.Columns().Type, "list").
			One()

		if err != nil {
			g.Log().Warning(ctx, "获取分类信息失败:", err, "categoryId:", categoryId)
			return false
		}

		// 如果分类存在且类型为"list"，超级管理员有权限
		if category != nil {
			g.Log().Debug(ctx, "超级管理员对list类型分类有权限", "categoryId:", categoryId, "permission:", permission)
			return true
		}

		// 如果分类不存在或类型不是"list"，返回 false
		g.Log().Debug(ctx, "超级管理员对非list类型分类无权限", "categoryId:", categoryId, "permission:", permission)
		return false
	}

	// 非超级管理员需要检查具体权限
	// 获取当前用户ID
	userId := sysService.Context().GetUserId(ctx)
	if userId == 0 {
		g.Log().Debug(ctx, "用户未登录或会话无效，无权限")
		return false
	}

	// 获取用户角色ID
	roleIds, err := sysService.SysUser().GetAdminRoleIds(ctx, userId, false)
	if err != nil {
		g.Log().Warning(ctx, "获取用户角色失败:", err, "userId:", userId)
		return false
	}

	if len(roleIds) == 0 {
		g.Log().Debug(ctx, "用户没有分配角色，无权限", "userId:", userId)
		return false
	}

	// 查询用户角色对应的分类权限
	permissions, err := dao.CmsCategoryPermissions.Ctx(ctx).
		Fields("permissions").
		Where(dao.CmsCategoryPermissions.Columns().CategoryId, categoryId).
		WhereIn(dao.CmsCategoryPermissions.Columns().RoleId, roleIds).
		Array()

	if err != nil {
		g.Log().Warning(ctx, "获取分类权限失败:", err, "categoryId:", categoryId, "roleIds:", roleIds)
		return false
	}

	// 遍历所有权限记录，检查是否包含指定权限
	for _, v := range permissions {
		// 将逗号分隔的权限字符串转换为数组
		pers := gstr.Explode(",", gconv.String(v))
		for _, p := range pers {
			if permission == p {
				g.Log().Debug(ctx, "用户具有指定权限", "userId:", userId, "categoryId:", categoryId, "permission:", permission)
				return true
			}
		}
	}

	g.Log().Debug(ctx, "用户无指定权限", "userId:", userId, "categoryId:", categoryId, "permission:", permission)
	return false
}

// AuditCategory 检测栏目是否分配了审核员
//
// 该函数检查指定的栏目是否已经分配了审核员
//
// 参数:
//   - ctx: 上下文对象，用于数据库操作
//   - categoryId: 要检查的栏目ID
//
// 返回:
//   - bool: 如果栏目已分配审核员返回 true，否则返回 false
func AuditCategory(ctx context.Context, categoryId uint) bool {
	// 查询审核分类表，检查是否存在该栏目的记录
	result, err := cmsAuditDao.CmsAuditCategory.Ctx(ctx).
		Where(cmsAuditDao.CmsAuditCategory.Columns().CategoryId, categoryId).
		Exist()

	if err != nil {
		g.Log().Warning(ctx, "检查栏目审核员分配状态失败:", err, "categoryId:", categoryId)
		return false // 查询出错时默认返回 false
	}

	return result
}

// AuditCategoryPermission 检测当前用户是否有指定栏目的审核权限
//
// 该函数检查当前登录用户是否被分配为指定栏目的审核员
//
// 参数:
//   - ctx: 上下文对象，用于获取当前用户ID和数据库操作
//   - categoryId: 要检查的栏目ID
//
// 返回:
//   - bool: 如果当前用户有该栏目的审核权限返回 true，否则返回 false
func AuditCategoryPermission(ctx context.Context, categoryId uint) bool {
	// 获取当前登录用户的ID
	userId := sysService.Context().GetUserId(ctx)
	g.Log().Debug(ctx, "检查用户审核权限", "userId:", userId, "categoryId:", categoryId)

	// 查询审核分类表，检查当前用户是否是该栏目的审核员
	result, err := cmsAuditDao.CmsAuditCategory.Ctx(ctx).
		Where(cmsAuditDao.CmsAuditCategory.Columns().CategoryId, categoryId).
		Where(cmsAuditDao.CmsAuditCategory.Columns().AuditUserId, userId).
		Exist()

	if err != nil {
		g.Log().Warning(ctx, "检查用户审核权限失败:", err, "userId:", userId, "categoryId:", categoryId)
		return false // 查询出错时默认返回 false
	}

	return result
}

// AuditWriteBefore 处理文章写入前的审核状态判断
//
// 该函数在文章写入前调用，用于判断文章是否需要进入审核流程
// 根据用户角色、系统配置和栏目设置决定返回的状态值
//
// 参数:
//   - ctx: 上下文对象，用于获取当前用户信息和数据库操作
//   - categoryId: 文章所属的栏目ID
//
// 返回:
//   - string: 返回文章状态，"0"表示需要审核，"1"表示直接发布
func AuditWriteBefore(ctx context.Context, categoryId uint, status string) (result string) {
	// 检查用户是否为超级管理员
	isSuperAdmin := IsSuperAdmin(ctx)
	g.Log().Debug(ctx, "检查用户是否为超级管理员", "isSuperAdmin:", isSuperAdmin)

	// 超级管理员直接返回状态为已发布(1)
	if isSuperAdmin {
		g.Log().Debug(ctx, "超级管理员无需审核，直接发布")
		return status
	}

	// 获取CMS系统配置
	cmsConfigMap, err := cmsService.CmsConfig().GetCmsConfigMap(ctx)
	if err != nil {
		g.Log().Error(ctx, "获取CMS配置失败:", err)
		// 配置获取失败时，默认设置为需要审核，以确保安全
		return "0"
	}

	// 检查用户是否有发布权限的函数
	hasPublishPermission := func() bool {
		return CheckCategoryPermission(ctx, "publish", categoryId)
	}

	// 检查栏目是否需要审核的函数
	categoryNeedsAudit := func() bool {
		return AuditCategory(ctx, categoryId)
	}

	// 判断逻辑开始
	if cmsConfigMap["audit_open"] == "1" {
		// 审核功能已开启
		g.Log().Debug(ctx, "审核功能已开启，检查审核设置")

		// 注意：这里使用 audit_setting 而不是 audit_set，与其他审核函数不同
		if cmsConfigMap["audit_setting"] == "1" {
			// 系统设置为所有栏目都需要审核
			g.Log().Debug(ctx, "系统设置为所有栏目都需要审核")
			result = "0" // 设置状态为待审核
		} else {
			// 系统设置为按栏目配置决定是否需要审核
			g.Log().Debug(ctx, "系统设置为按栏目配置决定是否需要审核")

			// 如果栏目需要审核，则设置状态为待审核
			if categoryNeedsAudit() {
				g.Log().Debug(ctx, "栏目需要审核，设置状态为待审核", "categoryId:", categoryId)
				result = "0"
			} else {
				// 栏目不需要审核，检查用户是否有发布权限
				if !hasPublishPermission() {
					// 用户没有发布权限，设置状态为待审核
					g.Log().Debug(ctx, "用户没有发布权限，设置状态为待审核", "categoryId:", categoryId)
					result = "0"
				} else {
					// 用户有发布权限, 用户提交过来的状态
					result = status
				}
			}
		}
	} else {
		// 审核功能未开启，仅检查发布权限
		g.Log().Debug(ctx, "审核功能未开启，仅检查发布权限")

		// 如果用户没有发布权限，则需要审核
		if !hasPublishPermission() {
			g.Log().Debug(ctx, "用户没有发布权限，设置状态为待审核", "categoryId:", categoryId)
			result = "0"
		} else {
			// 用户提交的状态
			result = status
		}
	}

	return result
}

// AuditAddAfter 处理文章添加后的审核流程
//
// 该函数在文章添加后调用，根据系统配置、用户角色和栏目设置决定是否需要进入审核流程
//
// 参数:
//   - ctx: 上下文对象，用于数据库操作和错误处理
//   - articleId: 新添加的文章ID
//   - categoryId: 文章所属的栏目ID
//   - status: 文章当前状态，"0"表示需要审核
//
// 返回:
//   - err: 操作过程中可能发生的错误
func AuditAddAfter(ctx context.Context, articleId uint, categoryId uint, status string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 获取CMS系统配置
		cmsConfigMap, err := cmsService.CmsConfig().GetCmsConfigMap(ctx)
		if err != nil {
			g.Log().Error(ctx, "获取CMS配置失败:", err)
			return
		}

		// 获取第一个审核步骤的ID
		nextStepId, err := cmsAuditDao.CmsAuditStep.Ctx(ctx).
			Fields(cmsAuditDao.CmsAuditStep.Columns().Id).
			Order("step asc").
			Value()
		if err != nil {
			g.Log().Error(ctx, "获取第一个审核步骤失败:", err)
			return
		}

		// 创建审核记录的通用函数
		createAuditRecord := func() error {
			_, err := cmsAuditDao.CmsAudit.Ctx(ctx).Insert(cmsAuditDo.CmsAudit{
				ArticleId:  articleId,
				CategoryId: categoryId,
				UserId:     0,          // 初始时没有指定审核人员
				StepId:     0,          // 初始步骤为0
				NextStepId: nextStepId, // 下一个审核步骤
				Status:     0,          // 状态为待审核
				Content:    "",         // 初始无审核意见
				Action:     "add",      // 操作类型为添加
			})
			if err != nil {
				g.Log().Error(ctx, "创建审核记录失败:", err)
				return err
			}
			return nil
		}

		// 只有当审核功能开启且文章状态为待审核(0)时才进行处理
		if cmsConfigMap["audit_open"] != "1" || status != "0" {
			g.Log().Debug(ctx, "审核未开启或文章状态不需要审核",
				"audit_open:", cmsConfigMap["audit_open"], "status:", status)
			return
		}

		// 检查用户是否为超级管理员
		isSuperAdmin := IsSuperAdmin(ctx)
		g.Log().Debug(ctx, "检查用户是否为超级管理员", "isSuperAdmin:", isSuperAdmin)

		// 根据审核设置决定是否需要审核
		if cmsConfigMap["audit_set"] == "1" {
			// 系统设置为所有栏目都需要审核
			g.Log().Debug(ctx, "系统设置为所有栏目都需要审核")
			err = createAuditRecord()
			liberr.ErrIsNil(ctx, err, "添加审核记录失败")
		} else {
			// 系统设置为按栏目配置决定是否需要审核
			g.Log().Debug(ctx, "系统设置为按栏目配置决定是否需要审核")

			// 检查栏目是否需要审核
			needAudit := AuditCategory(ctx, categoryId)

			// 如果是超级管理员，只要栏目需要审核就创建审核记录
			// 如果是普通管理员，需要同时满足栏目需要审核且文章状态为待审核
			if (isSuperAdmin && needAudit) || (needAudit && status == "0") {
				g.Log().Debug(ctx, "栏目需要审核，创建审核记录", "categoryId:", categoryId)
				err = createAuditRecord()
				liberr.ErrIsNil(ctx, err, "添加审核记录失败")
			} else {
				g.Log().Debug(ctx, "栏目无需审核或用户有特殊权限，跳过审核流程",
					"categoryId:", categoryId, "needAudit:", needAudit, "isSuperAdmin:", isSuperAdmin)
			}
		}
	})
	return
}

// AuditEditAfter 处理文章编辑后的审核流程
//
// 该函数在文章被编辑后调用，根据系统配置、用户角色和栏目设置决定是否需要进入审核流程
// 如果文章需要审核，会创建或更新相应的审核记录
//
// 参数:
//   - ctx: 上下文对象，用于数据库操作和错误处理
//   - articleId: 被编辑的文章ID
//   - categoryId: 文章所属的栏目ID
//   - status: 文章当前状态，"0"表示需要审核
//
// 返回:
//   - err: 操作过程中可能发生的错误
func AuditEditAfter(ctx context.Context, articleId uint, categoryId uint, status string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 获取CMS系统配置
		cmsConfigMap, err := cmsService.CmsConfig().GetCmsConfigMap(ctx)
		if err != nil {
			g.Log().Error(ctx, "获取CMS配置失败:", err)
			return
		}

		// 如果审核功能未开启，直接返回
		if cmsConfigMap["audit_open"] != "1" {
			g.Log().Debug(ctx, "审核功能未开启，跳过审核流程", "audit_open:", cmsConfigMap["audit_open"])
			return
		}

		// 获取第一个审核步骤的ID
		nextStepId, err := cmsAuditDao.CmsAuditStep.Ctx(ctx).
			Fields(cmsAuditDao.CmsAuditStep.Columns().Id).
			Order("step asc").
			Value()
		if err != nil {
			g.Log().Error(ctx, "获取第一个审核步骤失败:", err)
			return
		}

		// 检查文章是否已有审核记录
		row, err := cmsAuditDao.CmsAudit.Ctx(ctx).
			Where(cmsAuditDao.CmsAudit.Columns().ArticleId, articleId).
			Fields(cmsAuditDao.CmsAudit.Columns().Id).
			Value()
		if err != nil {
			g.Log().Warning(ctx, "查询文章审核记录失败:", err, "articleId:", articleId)
		}

		// 创建审核记录的通用函数
		createAuditRecord := func() error {
			_, err := cmsAuditDao.CmsAudit.Ctx(ctx).Insert(cmsAuditDo.CmsAudit{
				ArticleId:  articleId,
				CategoryId: categoryId,
				UserId:     0,          // 初始时没有指定审核人员
				StepId:     0,          // 初始步骤为0
				NextStepId: nextStepId, // 下一个审核步骤
				Status:     0,          // 状态为待审核
				Content:    "",         // 初始无审核意见
				Action:     "edit",     // 操作类型为编辑
			})
			if err != nil {
				g.Log().Error(ctx, "创建审核记录失败:", err)
				return err
			}
			return nil
		}

		// 更新审核记录的通用函数
		updateAuditRecord := func() error {
			_, err := cmsAuditDao.CmsAudit.Ctx(ctx).
				Where(cmsAuditDao.CmsAudit.Columns().Id, row).
				Update(cmsAuditDo.CmsAudit{
					StepId:     0,          // 重置步骤为0
					NextStepId: nextStepId, // 下一个审核步骤
					Status:     0,          // 状态为待审核
					Action:     "edit",     // 操作类型为编辑
				})
			if err != nil {
				g.Log().Error(ctx, "更新审核记录失败:", err)
				return err
			}
			return nil
		}

		// 处理审核记录的通用函数
		processAuditRecord := func() error {
			if row == nil || row.IsEmpty() {
				// 如果没有审核记录，创建新记录
				return createAuditRecord()
			} else {
				// 如果已有审核记录，更新记录
				return updateAuditRecord()
			}
		}

		// 检查用户是否为超级管理员
		isSuperAdmin := IsSuperAdmin(ctx)
		g.Log().Debug(ctx, "检查用户是否为超级管理员", "isSuperAdmin:", isSuperAdmin)

		// 根据审核设置和用户角色决定是否需要审核
		if isSuperAdmin {
			// 超级管理员的处理逻辑
			if cmsConfigMap["audit_set"] == "1" {
				// 系统设置为所有栏目都需要审核
				g.Log().Debug(ctx, "系统设置为所有栏目都需要审核")
				err = processAuditRecord()
				liberr.ErrIsNil(ctx, err, "处理审核记录失败")
			} else {
				// 系统设置为按栏目配置决定是否需要审核
				g.Log().Debug(ctx, "系统设置为按栏目配置决定是否需要审核")

				// 检查栏目是否需要审核
				if AuditCategory(ctx, categoryId) {
					g.Log().Debug(ctx, "栏目需要审核，处理审核记录", "categoryId:", categoryId)
					err = processAuditRecord()
					liberr.ErrIsNil(ctx, err, "处理审核记录失败")
				} else {
					g.Log().Debug(ctx, "栏目无需审核，跳过审核流程", "categoryId:", categoryId)
				}
			}
		} else {
			// 普通管理员的处理逻辑
			if cmsConfigMap["audit_set"] == "1" {
				// 系统设置为所有栏目都需要审核
				g.Log().Debug(ctx, "系统设置为所有栏目都需要审核")

				// 只有当文章状态为待审核(0)时才进行处理
				if status == "0" {
					g.Log().Debug(ctx, "文章状态为待审核，处理审核记录", "status:", status)
					err = processAuditRecord()
					liberr.ErrIsNil(ctx, err, "处理审核记录失败")
				} else {
					g.Log().Debug(ctx, "文章状态不需要审核，跳过审核流程", "status:", status)
				}
			} else {
				// 系统设置为按栏目配置决定是否需要审核
				g.Log().Debug(ctx, "系统设置为按栏目配置决定是否需要审核")

				// 检查栏目是否需要审核
				if AuditCategory(ctx, categoryId) {
					g.Log().Debug(ctx, "栏目需要审核，处理审核记录", "categoryId:", categoryId)
					err = processAuditRecord()
					liberr.ErrIsNil(ctx, err, "处理审核记录失败")
				} else {
					g.Log().Debug(ctx, "栏目无需审核，跳过审核流程", "categoryId:", categoryId)
				}
			}
		}
	})
	return
}

// AuditDeleteAfter 处理文章删除后的审核状态更新
//
// 该函数在文章被删除后调用，用于更新相关审核记录的状态
//
// 参数:
//   - ctx: 上下文对象，用于数据库操作和错误处理
//   - articleId: 被删除的文章ID
//
// 返回:
//   - err: 操作过程中可能发生的错误
func AuditDeleteAfter(ctx context.Context, articleId uint64) (err error) {
	// 获取CMS系统配置
	cmsConfigMap, err := cmsService.CmsConfig().GetCmsConfigMap(ctx)
	if err != nil {
		g.Log().Error(ctx, "获取CMS配置失败:", err)
		return err
	}

	err = g.Try(ctx, func(ctx context.Context) {
		// 只有当审核功能开启时才进行处理
		if cmsConfigMap["audit_open"] == "1" {
			g.Log().Debug(ctx, "审核功能已开启，更新审核记录状态", "articleId:", articleId)

			// 更新审核记录的状态为9（已删除）
			result, err := cmsAuditDao.CmsAudit.Ctx(ctx).
				Where(cmsAuditDao.CmsAudit.Columns().ArticleId, articleId).
				Update(cmsAuditDo.CmsAudit{
					Status: "9", // 状态码 9 表示文章已删除
				})

			if err != nil {
				g.Log().Error(ctx, "更新审核记录状态失败:", err, "articleId:", articleId)
				liberr.ErrIsNil(ctx, err, "更新审核记录状态失败")
			} else {
				g.Log().Debug(ctx, "审核记录状态更新成功", "articleId:", articleId, "affected:", result)
			}
		} else {
			g.Log().Debug(ctx, "审核功能未开启，无需更新审核记录", "audit_open:", cmsConfigMap["audit_open"])
		}
	})

	return
}

// AuditPushBefore 处理文章推送前的审核状态判断
//
// 该函数在文章推送到新栏目前调用，用于判断文章推送后是否需要进入审核流程
//
// 参数:
//   - ctx: 上下文对象，用于数据库操作和错误处理
//   - articleId: 要推送的文章ID
//   - categoryId: 文章推送到的目标栏目ID
//
// 返回:
//   - status: 返回文章状态，"0"表示需要审核，空字符串表示无需审核
func AuditPushBefore(ctx context.Context, categoryId uint) (status string) {
	// 默认状态为空字符串，表示无需审核
	status = ""

	// 获取CMS系统配置
	cmsConfigMap, err := cmsService.CmsConfig().GetCmsConfigMap(ctx)
	if err != nil {
		g.Log().Error(ctx, "获取CMS配置失败:", err)
		// 配置获取失败时，默认设置为需要审核，以确保安全
		return "0"
	}

	// 检查用户是否有发布权限的函数
	hasPublishPermission := func() bool {
		return CheckCategoryPermission(ctx, "publish", categoryId)
	}

	// 检查用户是否有状态更改权限的函数
	hasStatusPermission := func() bool {
		return CheckCategoryPermission(ctx, "status", categoryId)
	}

	// 检查用户是否有审核权限的函数
	hasAuditPermission := func() bool {
		return AuditCategoryPermission(ctx, categoryId)
	}

	// 检查栏目是否需要审核的函数
	categoryNeedsAudit := func() bool {
		return AuditCategory(ctx, categoryId)
	}

	// 判断逻辑开始
	if cmsConfigMap["audit_open"] == "1" {
		// 审核功能已开启
		g.Log().Debug(ctx, "审核功能已开启，检查审核设置")

		if cmsConfigMap["audit_set"] == "1" {
			// 系统设置为所有栏目都需要审核
			g.Log().Debug(ctx, "系统设置为所有栏目都需要审核")

			// 如果用户没有审核权限，则需要审核
			if !hasAuditPermission() {
				g.Log().Debug(ctx, "用户没有审核权限，需要审核", "categoryId:", categoryId)
				status = "0"
			} else {
				g.Log().Debug(ctx, "用户有审核权限，无需审核", "categoryId:", categoryId)
			}
		} else {
			// 系统设置为按栏目配置决定是否需要审核
			g.Log().Debug(ctx, "系统设置为按栏目配置决定是否需要审核")

			// 如果栏目需要审核且用户没有审核权限，则需要审核
			if categoryNeedsAudit() && !hasAuditPermission() {
				g.Log().Debug(ctx, "栏目需要审核且用户没有审核权限，需要审核", "categoryId:", categoryId)
				status = "0"
			} else if !hasPublishPermission() {
				// 如果用户没有发布权限，则需要审核
				g.Log().Debug(ctx, "用户没有发布权限，需要审核", "categoryId:", categoryId)
				status = "0"
			} else {
				g.Log().Debug(ctx, "用户有发布权限或栏目无需审核，无需审核", "categoryId:", categoryId)
			}
		}
	} else {
		// 审核功能未开启，仅检查状态权限
		g.Log().Debug(ctx, "审核功能未开启，仅检查状态权限")

		// 如果用户没有状态更改权限，则需要审核
		if !hasStatusPermission() {
			g.Log().Debug(ctx, "用户没有状态更改权限，需要审核", "categoryId:", categoryId)
			status = "0"
		} else {
			g.Log().Debug(ctx, "用户有状态更改权限，无需审核", "categoryId:", categoryId)
		}
	}

	return
}

// AuditPushAfter 处理文章推送后的审核流程
//
// 该函数在文章被推送到新栏目后调用，根据系统配置和栏目设置决定是否需要进入审核流程
//
// 参数:
//   - ctx: 上下文对象，用于数据库操作和错误处理
//   - articleId: 被推送的文章ID
//   - categoryId: 文章推送到的目标栏目ID
//   - status: 文章当前状态，"0"表示需要审核
//
// 返回:
//   - err: 操作过程中可能发生的错误
func AuditPushAfter(ctx context.Context, articleId uint, categoryId uint, status string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 获取CMS系统配置
		cmsConfigMap, err := cmsService.CmsConfig().GetCmsConfigMap(ctx)
		if err != nil {
			g.Log().Error(ctx, "获取CMS配置失败:", err)
			return
		}

		// 获取第一个审核步骤的ID
		nextStepId, err := cmsAuditDao.CmsAuditStep.Ctx(ctx).
			Fields(cmsAuditDao.CmsAuditStep.Columns().Id).
			Where(cmsAuditDao.CmsAuditStep.Columns().Type, "first").
			Value()
		if err != nil {
			g.Log().Error(ctx, "获取第一个审核步骤失败:", err)
			return
		}

		// 创建审核记录的通用函数
		createAuditRecord := func() error {
			_, err := cmsAuditDao.CmsAudit.Ctx(ctx).Insert(cmsAuditDo.CmsAudit{
				ArticleId:  articleId,
				CategoryId: categoryId,
				UserId:     0,          // 初始时没有指定审核人员
				StepId:     0,          // 初始步骤为0
				NextStepId: nextStepId, // 下一个审核步骤
				Status:     0,          // 状态为待审核
				Content:    "",         // 初始无审核意见
				Action:     "push",     // 操作类型为推送
			})
			if err != nil {
				g.Log().Error(ctx, "创建审核记录失败:", err)
				return err
			}
			return nil
		}

		// 只有当审核功能开启且文章状态为待审核(0)时才进行处理
		if cmsConfigMap["audit_open"] == "1" && status == "0" {
			g.Log().Debug(ctx, "审核功能已开启且文章状态为待审核",
				"audit_open:", cmsConfigMap["audit_open"], "status:", status)

			// 根据审核设置决定是否需要审核
			if cmsConfigMap["audit_set"] == "1" {
				// 系统设置为所有栏目都需要审核
				g.Log().Debug(ctx, "系统设置为所有栏目都需要审核")
				err = createAuditRecord()
				liberr.ErrIsNil(ctx, err, "添加审核记录失败")
			} else {
				// 系统设置为按栏目配置决定是否需要审核
				g.Log().Debug(ctx, "系统设置为按栏目配置决定是否需要审核")

				// 检查栏目是否需要审核
				if AuditCategory(ctx, categoryId) {
					g.Log().Debug(ctx, "栏目需要审核，创建审核记录", "categoryId:", categoryId)
					err = createAuditRecord()
					liberr.ErrIsNil(ctx, err, "添加审核记录失败")
				} else {
					g.Log().Debug(ctx, "栏目无需审核，跳过审核流程", "categoryId:", categoryId)
				}
			}
		} else {
			g.Log().Debug(ctx, "审核未开启或文章状态不需要审核",
				"audit_open:", cmsConfigMap["audit_open"], "status:", status)
		}
	})
	return
}

// AuditMoveBefore 处理文章移动前的审核状态判断
//
// 该函数在文章移动到新栏目前调用，用于判断文章移动后是否需要进入审核流程
//
// 参数:
//   - ctx: 上下文对象，用于数据库操作和错误处理
//   - categoryId: 文章移动到的目标栏目ID
//
// 返回:
//   - status: 返回文章状态，"0"表示需要审核，空字符串表示无需审核
func AuditMoveBefore(ctx context.Context, categoryId uint) (status string) {
	// 默认状态为空字符串，表示无需审核
	status = ""

	// 获取CMS系统配置
	cmsConfigMap, err := cmsService.CmsConfig().GetCmsConfigMap(ctx)
	if err != nil {
		g.Log().Error(ctx, "获取CMS配置失败:", err)
		// 配置获取失败时，默认设置为需要审核，以确保安全
		return "0"
	}

	// 检查用户是否有发布权限的函数
	hasPublishPermission := func() bool {
		return CheckCategoryPermission(ctx, "publish", categoryId)
	}

	// 检查用户是否有审核权限的函数
	hasAuditPermission := func() bool {
		return AuditCategoryPermission(ctx, categoryId)
	}

	// 检查栏目是否需要审核的函数
	categoryNeedsAudit := func() bool {
		return AuditCategory(ctx, categoryId)
	}

	// 判断逻辑开始
	if cmsConfigMap["audit_open"] == "1" {
		// 审核功能已开启
		g.Log().Debug(ctx, "审核功能已开启，检查审核设置")

		if cmsConfigMap["audit_set"] == "1" {
			// 系统设置为所有栏目都需要审核
			g.Log().Debug(ctx, "系统设置为所有栏目都需要审核")

			// 如果用户没有审核权限，则需要审核
			if !hasAuditPermission() {
				g.Log().Debug(ctx, "用户没有审核权限，需要审核", "categoryId:", categoryId)
				status = "0"
			} else {
				g.Log().Debug(ctx, "用户有审核权限，无需审核", "categoryId:", categoryId)
			}
		} else {
			// 系统设置为按栏目配置决定是否需要审核
			g.Log().Debug(ctx, "系统设置为按栏目配置决定是否需要审核")

			// 如果栏目需要审核且用户没有审核权限，则需要审核
			if categoryNeedsAudit() && !hasAuditPermission() {
				g.Log().Debug(ctx, "栏目需要审核且用户没有审核权限，需要审核", "categoryId:", categoryId)
				status = "0"
			} else if !hasPublishPermission() {
				// 如果用户没有发布权限，则需要审核
				g.Log().Debug(ctx, "用户没有发布权限，需要审核", "categoryId:", categoryId)
				status = "0"
			} else {
				g.Log().Debug(ctx, "用户有发布权限或栏目无需审核，无需审核", "categoryId:", categoryId)
			}
		}
	} else {
		// 审核功能未开启，仅检查发布权限
		g.Log().Debug(ctx, "审核功能未开启，仅检查发布权限")

		// 如果用户没有发布权限，则需要审核
		if !hasPublishPermission() {
			g.Log().Debug(ctx, "用户没有发布权限，需要审核", "categoryId:", categoryId)
			status = "0"
		} else {
			g.Log().Debug(ctx, "用户有发布权限，无需审核", "categoryId:", categoryId)
		}
	}

	return
}

// AuditMoveAfter 处理文章移动后的审核流程
//
// 该函数在文章被移动到新栏目后调用，根据系统配置和栏目设置决定是否需要进入审核流程
//
// 参数:
//   - ctx: 上下文对象，用于数据库操作和错误处理
//   - articleId: 被移动的文章ID
//   - categoryId: 文章移动到的目标栏目ID
//   - status: 文章当前状态，"0"表示需要审核
//
// 返回:
//   - err: 操作过程中可能发生的错误
func AuditMoveAfter(ctx context.Context, articleId uint, categoryId uint, status string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 获取CMS系统配置
		cmsConfigMap, err := cmsService.CmsConfig().GetCmsConfigMap(ctx)
		if err != nil {
			g.Log().Error(ctx, "获取CMS配置失败:", err)
			return
		}
		// 获取第一个审核步骤的ID
		nextStepId, err := cmsAuditDao.CmsAuditStep.Ctx(ctx).
			Fields(cmsAuditDao.CmsAuditStep.Columns().Id).
			Where(cmsAuditDao.CmsAuditStep.Columns().Type, "first").
			Value()
		if err != nil {
			g.Log().Error(ctx, "获取第一个审核步骤失败:", err)
			return
		}
		// 只有当审核功能开启且文章状态为待审核(0)时才进行处理
		if cmsConfigMap["audit_open"] == "1" && status == "0" {
			// 创建审核记录的通用函数
			createAuditRecord := func() error {
				_, err := cmsAuditDao.CmsAudit.Ctx(ctx).Insert(cmsAuditDo.CmsAudit{
					ArticleId:  articleId,
					CategoryId: categoryId,
					UserId:     0,          // 初始时没有指定审核人员
					StepId:     0,          // 初始步骤为0
					NextStepId: nextStepId, // 下一个审核步骤
					Status:     0,          // 状态为待审核
					Content:    "",         // 初始无审核意见
					Action:     "move",     // 操作类型为移动
				})
				if err != nil {
					g.Log().Error(ctx, "创建审核记录失败:", err)
					return err
				}
				return nil
			}

			// 根据审核设置决定是否需要审核
			if cmsConfigMap["audit_set"] == "1" {
				// 系统设置为所有栏目都需要审核
				err = createAuditRecord()
				liberr.ErrIsNil(ctx, err, "添加审核记录失败")
			} else {
				// 系统设置为按栏目配置决定是否需要审核
				if AuditCategory(ctx, categoryId) { // 检查该栏目是否需要审核
					err = createAuditRecord()
					liberr.ErrIsNil(ctx, err, "添加审核记录失败")
				} else {
					g.Log().Debug(ctx, "栏目无需审核，跳过审核流程", "categoryId:", categoryId)
				}
			}
		} else {
			g.Log().Debug(ctx, "审核未开启或文章状态不需要审核",
				"audit_open:", cmsConfigMap["audit_open"], "status:", status)
		}
	})
	return
}
