<div id="wrap">
    <div class="container wrap">
        <main class="main">
            <section class="sec-panel sec-panel-default">
                <div class="sec-panel-body">
                    <ul class="articles-list articles-list-default cols-0" id="article_list_container">
                        ${range $articleItem := .articleList}
                        <li class="item">
                            ${if $articleItem.Thumb}
                            <div class="item-img">
                                <a class="item-img-inner" href="${$articleItem.Url}" target="_blank" title="${$articleItem.Title}">
                                    <img alt="${$articleItem.Title}" class="attachment-default size-default " src="${CorrectionPath $articleItem.Thumb}">
                                </a>
                            </div>
                            ${end}
                            <div class="item-content">
                                <h2 class="item-title">
                                    ${if $articleItem.Category}
                                    <a class="article-category-item" href="${$.cmsConfig.site_url}/list?id=${$articleItem.Category.Id}" target="_blank">[${$articleItem.Category.Name}]</a>
                                    ${end}
                                    <a class="article-item" href="${$articleItem.Url}" rel="bookmark" target="_blank">${$articleItem.Title}</a>
                                </h2>
                                <div class="item-excerpt">
                                    <p>${$articleItem.Summary}</p>
                                </div>
                                <div class="item-meta">
                                    <div class="item-meta-li author">
                                        <a class="avatar j-user-card" target="_blank">
                                            <span>作者：${if $articleItem.UserInfo} <a href="${$.cmsConfig.site_url}/member/${$articleItem.UserInfo.Id}"> ${$articleItem.UserInfo.UserNickname} </a> ${end}</span>
                                        </a>
                                    </div>
                                    <span class="item-meta-li date">${$articleItem.PublishedAt}</span>
                                    <div class="item-meta-right">
                                        <span class="item-meta-li views" title="阅读数"><i class="iconfont icon-eye"></i> ${$articleItem.Hits}</span>
                                        <a class="item-meta-li comments" href="${$articleItem.Url}#comments" target="_blank" title="评论数">
                                            <i class="iconfont icon-comment"></i> ${$articleItem.CommentCount}</a>
                                        <span class="item-meta-li stars" title="收藏数"><i class="iconfont icon-star"></i> ${$articleItem.FavoriteCount}</span>
                                        <span class="item-meta-li likes" title="点赞数"><i class="iconfont icon-like"></i> ${$articleItem.LikeCount}</span>
                                    </div>
                                </div>
                            </div>
                        </li>
                        ${end}
                    </ul>
                    <div id="pagination-container">
                    
                    </div>
                </div>
            </section>
            <div class="no-more-item">没有更多了</div>
        </main>
        <aside class="sidebar">
            ${range $ad := GetAdList "position:25"}
            <div class="widget widget_image_ad">
                <a href="${$ad.Url}" rel="noopener" target="_blank">
                    <img class="j-lazy" src="${$ad.Images}" style="display: inline;">
                </a>
            </div>
            ${end}
            ${include "widget/new_articles.html"}
            ${include "widget/new_comments.html"}
        </aside>
    </div>
</div>
<script id="pagination-template" type="text/html">
    <ul class="pagination">
        <li class="total">共<%=total%>条</li>
        <% if(page > 1){ %>
        <li class="prev">
            <a href="${.cmsConfig.site_url}/search?keywords=${.keywords}&pageNum=1" class="page-numbers"><i class="iconfont icon-left"></i></a>
        </li>
        <%}%>
    <% for(var i=1;i <= totalPage; i++) { %>
        <% if(i===currentPage) { %>
        <li class="active"><a href="javascript:void(0)"><%=i%></a></li>
        <% } else { %>
        <li ><a href="${.cmsConfig.site_url}/search?keywords=${.keywords}&pageNum=<%=i%>"><%=i%></a></li>
        <% } %>
    <% } %>
        <% if(page < totalPage) { %>
        <li class="next">
            <a href="${.cmsConfig.site_url}/search?keywords=${.keywords}&pageNum=<%=totalPage%>" class="page-numbers"><i class="iconfont icon-right"></i></a>
        </li>
        <%}%>
    </ul>
</script>
<script>
    $(function (){
       var totalPage = Math.round(${.total}/10)
           console.log(totalPage)
        $("#pagination-container").html(template("pagination-template",{total:${.total},page:${.currentPage},totalPage:Math.round(${.total}/10),currentPage:${.currentPage}}))
    })
</script>