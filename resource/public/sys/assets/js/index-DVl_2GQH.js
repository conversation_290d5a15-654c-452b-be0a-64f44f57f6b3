import{I as c}from"./index-Crq2syth.js";import{d,X as E,ad as b,ag as l,c as f,o as _,P as o,H as e}from"./@vue-C21YZbHS.js";import{a as B}from"./index-BGmsw1a8.js";import"./getStyleSheets-CiOwvIk9.js";import"./@element-plus-_Cc-TEQX.js";import"./pinia-DwJf82dV.js";import"./vue-router-BDT9dCQ7.js";import"./js-cookie-Cz0CWeBA.js";import"./nprogress-Scw6VIZr.js";import"./@intlify-D8kAWvSi.js";import"./axios-B62VicFi.js";import"./qs-D41HS0uK.js";import"./side-channel-DytTmCZg.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-DbsE5JW_.js";import"./crypto-js-oM26lpET.js";import"./side-channel-list-C_7Uk4RP.js";import"./side-channel-map-BUokNT8A.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-bSkmVQJL.js";import"./element-plus-CUmVNDWO.js";import"./lodash-es-S0Y0Up6J.js";import"./@vueuse-D_5IXmcI.js";import"./@popperjs-D_chPuIy.js";import"./@ctrl-r5W6hzzQ.js";import"./dayjs-CepoAVPu.js";import"./async-validator-9PlIezaS.js";import"./memoize-one-BdPwpGay.js";import"./normalize-wheel-es-BQoi3Ox2.js";import"./@floating-ui-DwceP2Gb.js";import"./vue-i18n-BkLk9Ti1.js";import"./vue-iNFchrNl.js";import"./vue-clipboard3-Dr8V5xR7.js";import"./clipboard-DzJOpNE6.js";import"./mitt-DJ65BbbF.js";import"./vue-grid-layout-CIPjP0XN.js";import"./vue-simple-uploader-j6sX1TKo.js";import"./vue-ueditor-wrap-CJ5gjsd3.js";const C=d({name:"makeSelector",components:{IconSelector:c},setup(){const t=E({modelIcon:"",tableData:[{a1:"prepend",a2:"输入框前置内容，只能字体图标",a3:"string",a4:"",a5:"ele-Pointer"},{a1:"placeholder",a2:"输入框占位文本",a3:"string",a4:"",a5:"请输入内容搜索图标或者选择图标"},{a1:"size",a2:"尺寸",a3:"string",a4:"large / default / small",a5:"default"},{a1:"title",a2:"弹窗标题",a3:"string",a4:"",a5:"请选择图标"},{a1:"type",a2:"icon 图标类型",a3:"string",a4:"ali / ele / awe / all",a5:"ele"},{a1:"disabled",a2:"禁用",a3:"boolean",a4:"true",a5:"false"},{a1:"clearable",a2:"是否可清空",a3:"boolean",a4:"false",a5:"true"},{a1:"emptyDescription",a2:"自定义空状态描述文字",a3:"String",a4:"",a5:"无相关图标"}],tableData1:[{a1:"get",a2:"获取当前点击的 icon 图标",a3:"function",a4:"(icon: string)"},{a1:"clear",a2:"清空当前点击的 icon 图标",a3:"function",a4:"(icon: string)"}]});return{onGetIcon:r=>{console.log(r)},onClearIcon:r=>{console.log(r)},...b(t)}}}),F={class:"selector-container"};function g(t,n,u,r,h,A){const m=l("IconSelector"),p=l("el-card"),a=l("el-table-column"),i=l("el-table");return _(),f("div",F,[o(p,{shadow:"hover",header:"图标选择器(宽度自动)："},{default:e(()=>[o(m,{onGet:t.onGetIcon,onClear:t.onClearIcon,modelValue:t.modelIcon,"onUpdate:modelValue":n[0]||(n[0]=s=>t.modelIcon=s)},null,8,["onGet","onClear","modelValue"])]),_:1}),o(p,{shadow:"hover",header:"图标选择器(宽度自动)：参数",class:"mt15"},{default:e(()=>[o(i,{data:t.tableData,style:{width:"100%"}},{default:e(()=>[o(a,{prop:"a1",label:"参数"}),o(a,{prop:"a2",label:"说明"}),o(a,{prop:"a3",label:"类型"}),o(a,{prop:"a4",label:"可选值"}),o(a,{prop:"a5",label:"默认值"})]),_:1},8,["data"])]),_:1}),o(p,{shadow:"hover",header:"图标选择器(宽度自动)：事件",class:"mt15"},{default:e(()=>[o(i,{data:t.tableData1,style:{width:"100%"}},{default:e(()=>[o(a,{prop:"a1",label:"事件名称"}),o(a,{prop:"a2",label:"说明"}),o(a,{prop:"a3",label:"类型"}),o(a,{prop:"a4",label:"回调参数"})]),_:1},8,["data"])]),_:1})])}const Fo=B(C,[["render",g]]);export{Fo as default};
