@-webkit-keyframes rotating {
    0% {-webkit-transform: rotate(0deg);transform: rotate(0deg)}
    to {-webkit-transform: rotate(1turn);transform: rotate(1turn)}
}
@keyframes rotating {
    0% {-webkit-transform: rotate(0deg);transform: rotate(0deg)}
    to {-webkit-transform: rotate(1turn);transform: rotate(1turn)}
}

body > header.header .nav > li.dropdown > a {padding-right: 28px}
header.header .hotnews-children-menu > .dropdown > .dropdown-menu li, body > header.header .hotnews-children-menu > .menu-item-style > .menu-item-wrap .dropdown-menu li {display: block}
.dropdown:hover:after {-webkit-transform: rotate(-180deg);-ms-transform: rotate(-180deg);transform: rotate(-180deg);-webkit-transition: -webkit-transform .3s;-o-transition: transform .3s;transition: transform .3s;transition: transform .3s, -webkit-transform .3s}
body > header.header .navbar-nav > .dropdown:after {-webkit-transition: -webkit-transform .3s;-o-transition: transform .3s;transition: transform .3s;transition: transform .3s, -webkit-transform .3s}
.navbar-action .profile .dropdown:hover:after, body > header.header .navbar-nav > .dropdown:hover:after {-webkit-transform: rotate(-180deg);-ms-transform: rotate(-180deg);transform: rotate(-180deg);-webkit-transition: -webkit-transform .3s;-o-transition: transform .3s;transition: transform .3s;transition: transform .3s, -webkit-transform .3s}
.m-dropdown, .navbar-on-shadow {display: none;cursor: pointer}
@media (max-width: 991px) {
    .m-dropdown {display: block;position: absolute;right: 3px;top: 0;width: 50px;text-align: center;color: #fff}
    .m-dropdown:after {position: absolute;left: 0;top: 7px;width: 1px;height: 34px;content: "";background: rgba(0, 0, 0, .07)}
    .m-dropdown i {font-size: 24px;line-height: 48px;opacity: .7}
    .dropdown-open > .m-dropdown i, .m-dropdown, .m-dropdown i, .navbar-on {-webkit-transition: all .3s ease-out 0s;-o-transition: all .3s ease-out 0s;transition: all .3s ease-out 0s}
    .dropdown-open > .m-dropdown i {-webkit-transform: rotate(180deg);-ms-transform: rotate(180deg);transform: rotate(180deg)}
    .navbar-on {position: fixed;top: 0;left: 0;right: 0;bottom: 0;overflow: hidden;-webkit-transform: translateX(80%);-ms-transform: translateX(80%);transform: translateX(80%)}
    .navbar-on.admin-bar .header {margin-top: 46px}
    .navbar-on .navbar-on-shadow {display: block;position: fixed;z-index: 10000000000;width: 100%;height: 100%;left: 0;top: 0;content: "";background: rgba(0, 0, 0, .3)}
}
body > header.header {position: fixed;left: 0;z-index: 999;width: 100%;border: 0;background: #fff;-webkit-box-shadow: 0 0 3px 1px rgba(0, 0, 0, .1);box-shadow: 0 0 3px 1px rgba(0, 0, 0, .1)}
@media (max-width: 767px) {
    .page-no-sidebar.member-lostpassword body > header.header .member-lp-process, body > header.header .container, body > header.header .page-no-sidebar.member-lostpassword .member-lp-process {padding: 0 10px 0 15px !important}
}
body > header.header .logo, body > header.header .logo img {-webkit-transition: all .3s ease-out 0s;-o-transition: all .3s ease-out 0s;transition: all .3s ease-out 0s}
body > header.header .logo {height: 64px;float: none;display: table-cell;padding: 0;margin: 0;font-size: 20px;line-height: 1;vertical-align: middle}
@media (max-width: 767px) {
    body > header.header .logo {height: 50px}
}
body > header.header .logo img {width: auto;max-height: 32px;vertical-align: top}
@media (max-width: 767px) {
    body > header.header .logo img {max-height: 26px}
}
body > header.header .primary-menu {margin-left: 40px}
body > header.header .nav {
    font-size: 14px;
    font-family: "SF Pro Display", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Micro Hei", "Helvetica Neue", Arial, sans-serif;
}
@media (min-width: 992px) and (max-width: 1199px) {
    body > header.header .nav > li.dropdown > a {padding-right: 22px}
}
@media (max-width: 767px) {
    body > header.header .nav > li {margin: 0}
    body > header.header .nav {
        font-size: 14px;
    }
}
body > header.header .nav > li.active > a {color: var(--theme-color)}
body > header.header .nav > li > a {padding: 0 14px;color: #333;line-height: 64px;background: 0 0}
@media (min-width: 1200px) and (max-width: 1239px) {
    body > header.header .nav > li > a {padding: 0 10px}
}
@media (min-width: 992px) and (max-width: 1199px) {
    body > header.header .nav > li > a {padding: 0 8px}
}
body > header.header .nav > li > a:hover {color: var(--theme-hover);background: 0 0}
body > header.header .nav .material-icons {font-size: 16px;vertical-align: top;line-height: inherit}
body > header.header .nav svg.icon {width: 1em;height: 1em;vertical-align: -.15em;fill: currentColor;overflow: hidden}
body > header.header .navbar-nav > .dropdown {position: relative}
body > header.header .navbar-nav > .dropdown:after {position: absolute;right: 11px;top: 0;content: "";height: 64px;width: 16px;opacity: .5;background-image: url(data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCI+PHBhdGggZD0iTTUxMiA1NjIuMDA1bDIxMS4yLTIxMS4yIDYwLjMzIDYwLjMzMUw1MTIgNjgyLjY2NmwtMjcxLjUzLTI3MS41MyA2MC4zMy02MC4zM3oiLz48L3N2Zz4=);background-size: 16px;background-position: 50%;background-repeat: no-repeat}
@media (min-width: 992px) and (max-width: 1199px) {
    body > header.header .navbar-nav > .dropdown:after {right: 8px}
}
body > header.header .navbar-nav > .dropdown > a {position: relative;z-index: 1001}
@media (max-width: 991px) {
    body > header.header .navbar-nav > .dropdown > a {z-index: auto}
}
body > header.header .navbar-nav > .dropdown > a:after, body > header.header .navbar-nav > .dropdown > a:before {opacity: 0;position: absolute;left: 50%;width: 0;height: 0;margin-left: -8px;border: 8px solid transparent;content: "";-webkit-transition: all .2s ease-out;-o-transition: all .2s ease-out;transition: all .2s ease-out}
body > header.header .navbar-nav > .dropdown > a:before {bottom: 0;border-bottom-color: #eee}
@media (max-width: 991px) {
    body > header.header .navbar-nav > .dropdown > a:after, body > header.header .navbar-nav > .dropdown > a:before {display: none}
}
body > header.header .navbar-nav > .dropdown > a:after {bottom: -1px;border-bottom-color: #fff}
body > header.header .dropdown-menu {min-width: 120px}
body > header.header .dropdown-menu > li > a {padding: 9px 15px}
body > header.header .dropdown-menu > .active > a {color: var(--theme-color);background: 0 0}
body > header.header .dropdown-menu > .active > a:focus, body > header.header .dropdown-menu > .active > a:hover, body > header.header .dropdown-menu > li > a:hover {color: #fff;background: var(--theme-hover)}
body > header.header .hotnews-children-menu {position: relative}
@media (max-width: 991px) {
    body > header.header .hotnews-children-menu {padding: 0;margin: 0;-webkit-box-sizing: border-box;box-sizing: border-box;border-top: 1px solid rgba(0, 0, 0, .05)}
    body > header.header .hotnews-children-menu li {display: block !important;width: 100% !important}
    body > header.header .hotnews-children-menu li a {display: block !important;padding: 0 20px;font-weight: 400;line-height: 48px !important;color: #fff;border-bottom: 1px solid rgba(0, 0, 0, .05)}
    body > header.header .hotnews-children-menu li a:active, body > header.header .hotnews-children-menu li a:focus, body > header.header .hotnews-children-menu li a:hover {color: #fff !important;background: rgba(255, 255, 255, .1) !important}
}
body > header.header .hotnews-children-menu li img {display: inline-block;max-height: 20px;margin-top: -1px;margin-right: 5px;width: auto;vertical-align: middle}
@media (max-width: 991px) {
    body > header.header .hotnews-children-menu li img {border-radius: 2px}
    body > header.header .hotnews-children-menu .active > a {color: #fff !important;background: rgba(255, 255, 255, .1) !important}
    body > header.header .hotnews-children-menu .active > .m-dropdown {color: #fff}
}
body > header.header .hotnews-children-menu .menu-item-icon {margin-right: 3px;font-size: inherit;line-height: inherit;vertical-align: top}
body > header.header .hotnews-children-menu > .dropdown .dropdown-menu {display: block;padding: 0;opacity: 0;-webkit-transform: rotateX(-90deg);transform: rotateX(-90deg);-webkit-transform-origin: 0 0;-ms-transform-origin: 0 0;transform-origin: 0 0;-webkit-transition: opacity .1s .2s, visibility .1s .3s, -webkit-transform .3s;-o-transition: transform .3s, opacity .1s .2s, visibility .1s .3s;transition: transform .3s, opacity .1s .2s, visibility .1s .3s;transition: transform .3s, opacity .1s .2s, visibility .1s .3s, -webkit-transform .3s;visibility: hidden;-webkit-backface-visibility: hidden;backface-visibility: hidden;border-radius: 0;border: 0}
@media (max-width: 991px) {
    body > header.header .hotnews-children-menu > .dropdown .dropdown-menu {position: relative;display: none;float: none;width: 100% !important;left: 0 !important;padding: 1px 0 !important;-webkit-transform: rotateX(0);transform: rotateX(0);visibility: visible;background: rgba(0, 0, 0, .05);opacity: 1;-webkit-box-shadow: none;box-shadow: none;border-top: 0;border-left: 5px solid rgba(0, 0, 0, .04) !important;-webkit-box-sizing: border-box !important;box-sizing: border-box !important}
}
body > header.header .hotnews-children-menu > .dropdown > .dropdown-menu {padding: 2px 0}
.navbar-action .profile .dropdown:hover .dropdown-menu, body > header.header .hotnews-children-menu > .dropdown:hover > .dropdown-menu, body > header.header .hotnews-children-menu > .dropdown > .dropdown-menu > .dropdown:hover > .dropdown-menu {opacity: 1;-webkit-transform: rotateX(0);transform: rotateX(0);-webkit-transition: opacity .1s, -webkit-transform .3s;-o-transition: transform .3s, opacity .1s;transition: transform .3s, opacity .1s;transition: transform .3s, opacity .1s, -webkit-transform .3s;visibility: visible}
body > header.header .hotnews-children-menu > .dropdown > .dropdown-menu > .dropdown > .dropdown-menu {left: 100%;top: 0}
body > header.header .hotnews-children-menu > .dropdown.menu-item-style .dropdown {display: inline-block}
body > header.header .hotnews-children-menu > .dropdown.menu-item-style > .dropdown-menu .dropdown-menu {position: static;opacity: 1;-webkit-transform: rotateX(0);transform: rotateX(0);visibility: visible}
@media (max-width: 991px) {
    body > header.header .hotnews-children-menu > .menu-item-style {position: relative}
}
body > header.header .hotnews-children-menu > .menu-item-style > .menu-item-wrap {padding: 30px 20px;font-size: 0;-webkit-box-sizing: content-box;box-sizing: content-box}
body > header.header .hotnews-children-menu > .menu-item-style > .menu-item-wrap > li {display: inline-block;width: 240px;vertical-align: top;text-align: left}
body > header.header .hotnews-children-menu > .menu-item-style > .menu-item-wrap > li > a {display: inline-block;margin-bottom: 5px;font-size: 16px;font-weight: 600;padding: 0 15px;line-height: 40px}
@media (max-width: 991px) {
    body > header.header .hotnews-children-menu > .menu-item-style > .menu-item-wrap > li > a {font-weight: 400;font-size: 16px}
}
body > header.header .hotnews-children-menu > .menu-item-style > .menu-item-wrap .dropdown-menu {display: block;position: relative;padding: 0;margin: 0;float: none;-webkit-box-shadow: none;box-shadow: none;background: 0 0;border: 0}
@media (max-width: 991px) {
    body > header.header .hotnews-children-menu > .menu-item-style > .menu-item-wrap .dropdown-menu {display: none}
}
body > header.header .hotnews-children-menu > .menu-item-style > .menu-item-wrap .dropdown-menu li a {padding: 0 15px;font-size: 15px;line-height: 40px}
@media (max-width: 991px) {
    body > header.header .hotnews-children-menu > .menu-item-style > .menu-item-wrap .dropdown-menu li a {font-size: 16px}
}
body > header.header .hotnews-children-menu > .menu-item-style > .menu-item-wrap .dropdown-menu li a:hover {background: rgba(0, 0, 0, .03);background: var(--theme-hover)}
body > header.header .hotnews-children-menu > .menu-item-style .menu-item-col-2 {width: 480px}
body > header.header .hotnews-children-menu > .menu-item-style .menu-item-col-3 {width: 720px}
body > header.header .hotnews-children-menu > .menu-item-style .menu-item-col-4 {width: 100%;text-align: center;-webkit-box-sizing: border-box;box-sizing: border-box}
body > header.header .hotnews-children-menu > .menu-item-style .menu-item-col-5 {width: 100%;-webkit-box-sizing: border-box;box-sizing: border-box}
body > header.header .hotnews-children-menu > .menu-item-style3 > .menu-item-wrap {-webkit-box-sizing: border-box;box-sizing: border-box}
body > header.header .hotnews-children-menu > .menu-item-style .menu-item-col-5 > li {width: 19.99%}
body > header.header .hotnews-children-menu > .menu-item-style2 > .menu-item-wrap .dropdown-menu .menu-item-has-image a, body > header.header .hotnews-children-menu > .menu-item-style2 > .menu-item-wrap > .menu-item-has-image a {padding: 10px 15px}
@media (max-width: 991px) {
    body > header.header .hotnews-children-menu > .menu-item-style2 > .menu-item-wrap .dropdown-menu .menu-item-has-image a, body > header.header .hotnews-children-menu > .menu-item-style2 > .menu-item-wrap > .menu-item-has-image a {line-height: 40px !important}
}
body > header.header .hotnews-children-menu > .menu-item-style2 > .menu-item-wrap .dropdown-menu .menu-item-has-image a .menu-item-image, body > header.header .hotnews-children-menu > .menu-item-style2 > .menu-item-wrap > .menu-item-has-image a .menu-item-image {max-height: 40px;margin-right: 15px}
@media (max-width: 991px) {
    body > header.header .hotnews-children-menu > .menu-item-style2 > .menu-item-wrap .dropdown-menu .menu-item-has-image a .menu-item-image, body > header.header .hotnews-children-menu > .menu-item-style2 > .menu-item-wrap > .menu-item-has-image a .menu-item-image {margin-right: 10px}
}
body > header.header .hotnews-children-menu > .menu-item-style3 > .menu-item-wrap .dropdown {width: 100%}
body > header.header .hotnews-children-menu > .menu-item-style3 > .menu-item-wrap .dropdown-menu {margin-bottom: 10px;font-size: 0}
@media (max-width: 991px) {
    body > header.header .hotnews-children-menu > .menu-item-style3 > .menu-item-wrap .dropdown-menu {margin-bottom: 0}
}
body > header.header .hotnews-children-menu > .menu-item-style3 > .menu-item-wrap .dropdown-menu li, body > header.header .hotnews-children-menu > .menu-item-style3 > .menu-item-wrap > .menu-item-has-image {width: 19.99%;display: inline-block}
body > header.header .hotnews-children-menu > .menu-item-style3 > .menu-item-wrap .dropdown-menu li a, body > header.header .hotnews-children-menu > .menu-item-style3 > .menu-item-wrap > .menu-item-has-image a {padding: 10px 15px 0;margin-bottom: 10px;text-align: center}
@media (max-width: 991px) {
    body > header.header .hotnews-children-menu > .menu-item-style3 > .menu-item-wrap .dropdown-menu li a, body > header.header .hotnews-children-menu > .menu-item-style3 > .menu-item-wrap > .menu-item-has-image a {padding: 10px 15px;text-align: left;margin: 0;line-height: 50px !important}
}
body > header.header .hotnews-children-menu > .menu-item-style3 > .menu-item-wrap .dropdown-menu li a .menu-item-image, body > header.header .hotnews-children-menu > .menu-item-style3 > .menu-item-wrap > .menu-item-has-image a .menu-item-image {display: block;width: 100%;height: auto;margin: 0 auto;max-height: none}
@media (max-width: 991px) {
    body > header.header .hotnews-children-menu > .menu-item-style3 > .menu-item-wrap .dropdown-menu li a .menu-item-image, body > header.header .hotnews-children-menu > .menu-item-style3 > .menu-item-wrap > .menu-item-has-image a .menu-item-image {width: 50px;display: inline-block !important;margin-right: 10px;vertical-align: middle}
}
@media (min-width: 992px) {
    body > header.header .hotnews-children-menu > .menu-item-style > .menu-item-wrap > li > a {color: #333}
}
body > header.header .hotnews-children-menu > .menu-item-style > .menu-item-wrap > li > a:hover {color: var(--theme-hover)}
body > header.header .hotnews-children-menu > .menu-item-style > .menu-item-wrap > li > a:focus {background: 0 0}
body > header.header .hotnews-children-menu > .menu-item-style3 > .menu-item-wrap, body > header.header .hotnews-children-menu > .menu-item-style > .menu-item-wrap.menu-item-col-4, body > header.header .hotnews-children-menu > .menu-item-style > .menu-item-wrap.menu-item-col-5 {width: 1180px}
@media (max-width: 1199px) {
    body > header.header .hotnews-children-menu > .menu-item-style > .menu-item-wrap.menu-item-col-4, body > header.header .hotnews-children-menu > .menu-item-style > .menu-item-wrap.menu-item-col-5 {width: 960px}
    body > header.header .hotnews-children-menu > .menu-item-style > .menu-item-wrap.menu-item-col-4 > li {width: 228px}
}
@media (max-width: 1199px) {
    body > header.header .hotnews-children-menu > .menu-item-style3 > .menu-item-wrap {width: 960px}
}

.navbar-collapse {padding-right: 0}
.navbar-action {position: relative;z-index: 1002;height: 26px;font-size: 0;margin-top: 19px;margin-left: 20px}
.navbar-action .header-member-wrap {display: inline-block;margin-left: 20px}
.navbar-action .login {display: none;font-size: 14px;color: #5a5a5a;line-height: 26px}
.navbar-action .login:first-child {position: relative;padding-right: 9px;margin-right: 8px}
.navbar-action .login:first-child:after {position: absolute;right: 0;top: 6px;width: 1px;height: 14px;content: "";background: #5a5a5a;opacity: .9}
@media (max-width: 991px) {
    .navbar-action .login:first-child:after {display: none}
}
.navbar-action .login.cur {display: inline-block}
@media (max-width: 991px) {
    .navbar-action .login.cur {margin: 0}
}
.navbar-action .login:focus, .navbar-action .login:hover {color: var(--theme-hover);text-decoration: none}
@media (max-width: 991px) {
    .navbar-action .login {width: 48%;margin: 20px;padding: 8px 30px !important;line-height: 22px;text-align: center;color: rgba(255, 255, 255, .8);background: rgba(255, 255, 255, .1)}
    .navbar-action .login:hover {color: #fff;-webkit-box-shadow: inset 0 0 5px 30px rgba(255, 255, 255, .08);box-shadow: inset 0 0 5px 30px rgba(255, 255, 255, .08)}
}
.navbar-action .publish {margin-left: 20px;padding-top: 5px;padding-bottom: 5px;font-weight: 400;vertical-align: top}
.navbar-action .profile {display: inline-block;height: 45px;margin-bottom: 0;padding: 0;list-style: none;font-size: 13px;vertical-align: top}
.navbar-action .profile .menu-item {height: 45px}
.navbar-action .profile .dropdown {padding-right: 18px}
.navbar-action .profile .dropdown:after {position: absolute;right: 2px;top: 0;content: "";height: 26px;width: 16px;-webkit-transition: -webkit-transform .3s;-o-transition: transform .3s;transition: transform .3s;transition: transform .3s, -webkit-transform .3s;opacity: .5;background-image: url(data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCI+PHBhdGggZD0iTTUxMiA1NjIuMDA1bDIxMS4yLTIxMS4yIDYwLjMzIDYwLjMzMUw1MTIgNjgyLjY2NmwtMjcxLjUzLTI3MS41MyA2MC4zMy02MC4zM3oiLz48L3N2Zz4=);background-size: 16px;background-position: 50%;background-repeat: no-repeat}
.navbar-action .profile .dropdown:hover .dropdown-menu:after, .navbar-action .profile .dropdown:hover .dropdown-menu:before {opacity: 1;-webkit-transition: opacity .1s, -webkit-transform .2s;-o-transition: transform .2s, opacity .1s;transition: transform .2s, opacity .1s;transition: transform .2s, opacity .1s, -webkit-transform .2s}
.navbar-action .profile .dropdown-menu {display: block;opacity: 0;margin: 0;min-width: 120px;-webkit-transform: rotateX(-90deg);transform: rotateX(-90deg);-webkit-transform-origin: 0 0;-ms-transform-origin: 0 0;transform-origin: 0 0;-webkit-transition: opacity .1s .2s, visibility .1s .3s, -webkit-transform .3s;-o-transition: transform .3s, opacity .1s .2s, visibility .1s .3s;transition: transform .3s, opacity .1s .2s, visibility .1s .3s;transition: transform .3s, opacity .1s .2s, visibility .1s .3s, -webkit-transform .3s;visibility: hidden;-webkit-backface-visibility: hidden;backface-visibility: hidden;border-radius: 0;border: 0;-webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, .175);box-shadow: 0 6px 12px rgba(0, 0, 0, .175)}
.navbar-action .profile .dropdown-menu:after, .navbar-action .profile .dropdown-menu:before {position: absolute;left: 40px;margin-left: -7px;border: 7px solid transparent;content: "";opacity: 0;-webkit-transition: opacity .2s 0s;-o-transition: opacity .2s 0s;transition: opacity .2s 0s}
.navbar-action .profile .dropdown-menu:before {top: -14px;border-bottom-color: #eee}
.navbar-action .profile .dropdown-menu:after {z-index: 1;top: -13px;border-bottom: 7px solid #fff}
.navbar-action .profile .dropdown-menu a {position: relative;display: block;line-height: 20px}
.navbar-action .profile a {display: inline-block;line-height: 26px;color: #5a5a5a;text-decoration: none;vertical-align: top}
.navbar-action .profile a:hover, .navbar-search-btn:hover, .navbar-search-close:hover {color: var(--theme-hover)}
.navbar-action .profile .menu-item-name {display: inline-block;max-width: 120px;overflow: hidden;white-space: nowrap;-o-text-overflow: ellipsis;text-overflow: ellipsis;vertical-align: top}
.navbar-action .profile .menu-item-avatar {position: relative;display: inline-block;width: 26px;height: 26px;margin-right: 5px;vertical-align: top}
.navbar-action .profile .menu-item-unread, .navbar-action .profile .num-count {position: absolute;height: 16px;min-width: 16px;line-height: 16px;background: #ff5c64;color: #fff;font-size: 12px;text-align: center;border-radius: 8px}
.navbar-action .profile .menu-item-unread {right: -4px;top: -4px;padding: 0 2px}
.navbar-action .profile img {display: block;width: 100%;height: 100%;border-radius: 50%}
.navbar-action .profile .num-count {right: 8px;padding: 0 3px;margin-top: 2px}
.content-body-action .btn-dashang.liked i, .content-body-action .btn-zan.liked i, .is-search > .container, .page-no-sidebar.member-lostpassword .is-search > .member-lp-process {position: relative}
.content-body .mce-top-part:before, .is-search .navbar-search-icon, .is-search .primary-menu {display: none}
.navbar-search-form { width: 200px;display: inline-block; }
.navbar-search-icon {display: inline-block;text-align: center;color: #5a5a5a;vertical-align: top;cursor: pointer}
.navbar-search-icon .wi {display: inline-block;width: 22px;font-size: 18px;line-height: 26px}
.navbar-search-icon:focus, .navbar-search-icon:hover {color: var(--theme-hover);text-decoration: none}
@media (max-width: 991px) {
    body > header.header .nav {font-size: 16px}
    .navbar-nav, body > header.header .navbar-header {margin: 0;float: none}
    body > header.header .primary-menu {margin: 0;float: none !important}
    body > header.header .dropdown:after {display: none}
    body > header.header.header-style-2 .icon-bar {background: rgba(255, 255, 255, .85)}
    .navbar-nav {width: 100%}
    .navbar-nav > li {float: none}
    .navbar-nav li {background-image: none !important}
    .navbar-toggle {display: block;margin: 14px 0 0;-webkit-transition: all .3s ease-out 0s;-o-transition: all .3s ease-out 0s;transition: all .3s ease-out 0s}
}
@media (max-width: 991px) and (max-width: 767px) {
    .navbar-toggle {margin-top: 8px}
}
.navbar-search {
    display: inline-block;
    vertical-align: top;
}
.navbar-search-input{
    width: 200px;
    height: 30px;
    border:none;
    background: #f1f1f1;
    padding: 6px 4px 6px 10px;
    border-radius: 5px;
    outline: none;
    font-size: 14px;
}
.navbar-search-icon{
    margin-left: -25px;
    margin-top: 5px;
}
.navbar-search-btn{
    cursor: pointer;
    border: none;
    outline: none;
}
@media (max-width: 991px) {
    .navbar-unread {position: absolute;right: -2px;top: -2px;height: 16px;min-width: 16px;padding: 0 2px;text-align: center;line-height: 16px;font-size: 12px;color: #fff;background: #ff5c64;border-radius: 8px}
    .navbar-collapse {position: fixed;bottom: 0;top: 0;right: 100%;z-index: 998;padding: 50px 0;width: 80%;height: 100% !important;margin: 0 !important;background: var(--theme-color);-webkit-transition: all .3s ease-out 0s;-o-transition: all .3s ease-out 0s;transition: all .3s ease-out 0s;overflow: auto;border: 0}
    .navbar-collapse.collapse {display: none !important}
    .collapse.in {display: block !important}
    .icon-bar {-webkit-transition: all .3s ease-out .3s;-o-transition: all .3s ease-out .3s;transition: all .3s ease-out .3s;background: #999}
    .navbar-on {-webkit-transition: all .3s ease-out 0s;-o-transition: all .3s ease-out 0s;transition: all .3s ease-out 0s;position: fixed;top: 0;left: 0;right: 0;bottom: 0;overflow: hidden;-webkit-transform: translateX(80%);-ms-transform: translateX(80%);transform: translateX(80%)}
    .navbar-on .navbar-on-shadow {position: fixed;z-index: 10000000000;width: 100%;height: 100%;left: 0;top: 0;content: "";background: rgba(0, 0, 0, .3)}
    .navbar-on .icon-bar {-webkit-transition: all .3s ease-out .3s;-o-transition: all .3s ease-out .3s;transition: all .3s ease-out .3s;background: #fff !important}
    .navbar-on .icon-bar-1 {margin-bottom: 0;-webkit-transform: rotate(-45deg) translate(-3px, 5px);-ms-transform: rotate(-45deg) translate(-3px, 5px);transform: rotate(-45deg) translate(-3px, 5px)}
    .navbar-on .icon-bar-2 {opacity: 0}
    .navbar-on .icon-bar-3 {-webkit-transform: rotate(45deg) translate(-3px, -5px);-ms-transform: rotate(45deg) translate(-3px, -5px);transform: rotate(45deg) translate(-3px, -5px)}
    .navbar-on .navbar-toggle {position: fixed;top: 0;z-index: 1000;right: 100%;background: 0 0 !important;border: 0 !important;margin-top: 8px;color: #fff;-webkit-transition: all .3s ease-out 0s;-o-transition: all .3s ease-out 0s;transition: all .3s ease-out 0s}
    .navbar-action .profile .dropdown-menu:after, .navbar-action .profile .dropdown-menu:before, .navbar-on .navbar-unread {display: none}
    .navbar-action {float: none !important;margin: 0;height: auto;padding: 20px 0 30px;overflow: hidden}
    .navbar-action #j-user-wrap {display: -webkit-box;display: -webkit-flex;display: -ms-flexbox;display: flex;width: 100%;padding: 20px;margin: 0;-webkit-box-pack: justify;-webkit-justify-content: space-between;-ms-flex-pack: justify;justify-content: space-between}
    .navbar-action .profile {width: 100%;height: auto}
    .navbar-action .profile a {display: block;color: #fff}
    .navbar-action .profile .menu-item {height: auto;padding: 0}
    .navbar-action .profile .menu-item > a {padding: 7px 0}
    .navbar-action .profile .m-dropdown {right: 0;height: 38px}
    .navbar-action .profile .m-dropdown .wi {line-height: 38px}
    .navbar-action .profile .dropdown-menu {position: relative;float: none;display: none;width: 100%;padding: 1px 0 !important;visibility: visible;opacity: 1;-webkit-transform: rotateX(0);transform: rotateX(0);background: rgba(0, 0, 0, .1);border: 0;-webkit-box-sizing: border-box !important;box-sizing: border-box !important;border-radius: 0;-webkit-box-shadow: none;box-shadow: none}
    .navbar-action .profile .dropdown-menu li {border-bottom: 1px solid rgba(10, 10, 10, .05) !important}
    .navbar-action .profile .dropdown-menu li:last-child {border-bottom: 0 !important}
    .navbar-action .profile .dropdown-menu li > a:hover {color: #fff !important;background: rgba(255, 255, 255, .1) !important}
    .navbar-action .publish {margin: 0 20px;padding: 8px 30px;line-height: 22px;color: #fff;border: 0;display: block;text-align: center;border-radius: 0;background-color: rgba(255, 255, 255, .1) !important}
    .navbar-search {margin-bottom:20px;position: relative;display: block !important;width: 100%;padding: 0 20px;height: auto;border: 0;background: 0 0}
    .navbar-search ::-webkit-input-placeholder {color: #999 !important}
    .navbar-search :-moz-placeholder, .navbar-search ::-moz-placeholder {color: #999 !important}
    .navbar-search :-ms-input-placeholder {color: #999 !important}
    .navbar-search-input {width: 100%;height: 30px;padding: 5px 10px;line-height: 20px;margin: 0;color: #333 !important;background: #fff;border: 1px solid #fff}
    .navbar-action .header-member-wrap{margin-left: 0;display: block}
    .navbar-action .header-member-wrap .profile{
        padding-left: 20px;
    }
}
@media screen and (max-width: 600px) {
    #wpadminbar {position: fixed !important}
}
.main-content {margin-bottom: 20px}
.body-box .main-content {background: #fff;padding: 30px}
@media (max-width: 1239px) {
    .body-box .main-content {padding: 20px}
}
@media (max-width: 767px) {
    .body-box .main-content {padding: 0;background: 0 0}
}
.type-kuaixun .content-body-content p {text-indent: 0}
@media (max-width: 767px) {
    .has-video .breadcrumb {display: none}
}
.content-body .mce-tinymce {-webkit-box-shadow: none;box-shadow: none}
.content-body .content-body-video {position: relative;margin: 0 0 20px}
.content-body .content-body-video embed, .content-body .content-body-video iframe, .content-body .content-body-video video {display: block;width: 100% !important;height: 100%;vertical-align: top}
.content-body .content-body-video .mejs-container, .content-body .content-body-video .mejs-container embed, .content-body .content-body-video .mejs-container iframe, .content-body .content-body-video .mejs-container video, .content-body .content-body-video .wp-video, .content-body .content-body-video .wp-video embed, .content-body .content-body-video .wp-video iframe, .content-body .content-body-video .wp-video video {height: 100% !important}
.content-body .content-body-content figure, .content-body .content-body-head {margin-bottom: 20px}
.content-body .content-body-title {margin-top: 0;margin-bottom: 20px;font-size: 24px;line-height: 1.4;text-align: center;}
@media (max-width: 767px) {
    .content-body .content-body-title {font-size: 22px}
}
.content-body .content-body-info {padding-bottom: 10px;color: #828282}
.content-body .content-body-info a {color: #828282}
.content-body .content-body-info a:focus, .content-body .content-body-info a:hover {color: var(--theme-hover);text-decoration: none}
.content-body .content-body-info .nickname {color: var(--theme-color)}
.content-body .content-body-info .dot {margin: 0 6px;color: #ccc}
.content-body .content-body-excerpt {padding: 15px;margin-bottom: 20px;font-size: 16px;line-height: 1.8;color: #828282;background: #fbfbfb;border: 1px solid #efefef;border-radius: 3px}
.content-body .content-body-content {position: relative;font-size: 16px;color: #262626;line-height: 1.8}
.content-body .content-body-content .h1, .content-body .content-body-content .h2, .content-body .content-body-content .h3, .content-body .content-body-content h1, .content-body .content-body-content h2, .content-body .content-body-content h3 {margin: 30px 0 20px;line-height: 1.4;font-weight: 500}
.content-body .content-body-content h4, .content-body .content-body-content h5 {font-weight: 500}
.content-body .content-body-content .h1, .content-body .content-body-content h1 {font-size: 24px}
.content-body .content-body-content .h2, .content-body .content-body-content h2 {padding-bottom: 12px;font-size: 20px;border-bottom: 1px solid #efefef}
.content-body .content-body-content .h3, .content-body .content-body-content h3 {font-size: 18px}
.content-body .content-body-content > .h3, .content-body .content-body-content > h3 {position: relative;padding-left: 18px;line-height: 1.46;border: 0}
.content-body .content-body-content > .h3:before, .content-body .content-body-content > h3:before {position: absolute;top: 4px;left: 0;content: "";width: 3px;height: calc(100% - 8px);border-radius: 3px;background-color: var(--theme-color);background-image: -o-linear-gradient(top, rgba(255, 255, 255, .15), transparent);background-image: linear-gradient(180deg, rgba(255, 255, 255, .15), transparent)}
.content-body .content-body-content p {margin-bottom: 20px;line-height: 1.8;word-wrap: break-word}
@media (max-width: 767px) {
    .content-body .content-body-content p {text-align: justify}
    .content-body .content-body-content p.aligncenter, .content-body .content-body-content p.has-text-align-center {text-align: center}
    .content-body .content-body-content p.has-text-align-left {text-align: left}
    .content-body .content-body-content p.has-text-align-right {text-align: right}
}
.content-body .content-body-content > p {white-space: pre-wrap}
.content-body .content-body-content p.has-drop-cap:not(:focus):first-letter {font-size: 3em;line-height: 1;font-weight: 300}
.content-body .content-body-content .woocommerce p {text-indent: 0 !important}
.content-body .content-body-content blockquote {color: #444;background: #fbfbfb}
.content-body .content-body-content ol, .content-body .content-body-content ul {padding-left: 2em;margin-bottom: 20px;list-style-position: outside}
.content-body .content-body-content li {margin-bottom: 10px}
.content-body .content-body-content img {height: auto}
.content-body .content-body-content audio, .content-body .content-body-content embed, .content-body .content-body-content iframe, .content-body .content-body-content img, .content-body .content-body-content video {max-width: 100%;vertical-align: top}
@media (max-width: 767px) {
    .content-body .content-body-content video {display: block;width: 100%;height: auto}
    .content-body .content-body-content .plyr__video-wrapper--fixed-ratio video {height: 100%}
}
.content-body .content-body-content video::-internal-media-controls-download-button {display: none}
.content-body .content-body-content video::-webkit-media-controls-enclosure {overflow: hidden}
.content-body .content-body-content video::-webkit-media-controls-panel {width: calc(100% + 30px)}
@media (max-width: 767px) {
    .content-body .content-body-content audio {display: block;width: 100%}
}
@media (max-width: 767px) {
    .content-body .content-body-content embed, .content-body .content-body-content iframe {display: block;width: 100%;min-height: 200px;max-height: 500px}
}
.content-body .content-body-content .aligncenter {display: block;margin-left: auto;margin-right: auto;text-align: center}
.content-body .content-body-content .alignleft {float: left;margin-right: 10px;margin-bottom: 10px}
.content-body .content-body-content .alignright {float: right;margin-left: 10px;margin-bottom: 10px}
.content-body .content-body-content .wp-caption, .content-body .content-body-content button, .content-body .content-body-content input, .content-body .content-body-content select, .content-body .content-body-content textarea {max-width: 100%}
.content-body .content-body-content .wp-caption-text {padding-top: 5px;text-align: center;text-indent: 0;font-size: 14px;line-height: 1.5;color: #666}
.content-body .content-body-content table {width: 100%;border-collapse: collapse}
.content-body .content-body-content table td, .content-body .content-body-content table th {padding: .5em;border: 1px solid #efefef}
.content-body .content-body-content .has-theme-light-color {color: #f5f5f5}
.content-body .content-body-content .has-theme-light-background-color {background-color: #f5f5f5}
.content-body .content-body-content .has-theme-gray-color {color: #999}
.content-body .content-body-content .has-theme-gray-background-color {background-color: #999}
.content-body .content-body-content .has-theme-black-color {color: #333}
.content-body .content-body-content .has-theme-black-background-color {background-color: #333}
.content-body .content-body-content .has-theme-hover-color, .content-body-bar .info-item a:hover {color: var(--theme-hover)}
.content-body .content-body-content .has-theme-hover-background-color {background-color: var(--theme-hover)}
.content-body .content-body-content .has-theme-color-color {color: var(--theme-color)}
.content-body .content-body-content .has-theme-color-background-color {background-color: var(--theme-color)}
.content-body .content-body-content .gallery {margin: 0 -10px;font-size: 0}
.content-body .content-body-content .gallery .gallery-item {display: inline-block;padding: 10px;text-align: center;vertical-align: top}
.content-body .content-body-content .gallery .gallery-item img {max-width: 100%;height: auto;vertical-align: top;border: 0}
.content-body .content-body-content .gallery-columns-1 .gallery-item {width: 100%}
.content-body .content-body-content .gallery-columns-2 .gallery-item {width: 50%}
.content-body .content-body-content .gallery-columns-3 .gallery-item {width: 33.33333%}
.content-body .content-body-content .gallery-columns-4 .gallery-item {width: 25%}
.content-body .content-body-content .gallery-columns-5 .gallery-item {width: 20%}
.content-body .content-body-content .gallery-columns-6 .gallery-item {width: 16.66667%}
.content-body .content-body-content .gallery-columns-7 .gallery-item {width: 14.28571%}
.content-body .content-body-content .gallery-columns-8 .gallery-item {width: 12.5%}
.content-body .content-body-content .gallery-columns-9 .gallery-item {width: 11.11111%}
.content-body .content-body-content .wp-video {width: auto !important}
.content-body .content-body-content .plyr--video {margin-bottom: 20px}
.content-body .content-body-content .j-hotnews-video {display: block;max-width: 100%}
.content-body .content-body-content.text-indent p {text-indent: 2em}
.content-body .content-body-content .wp-block-quote, .content-body .content-body-content blockquote {position: relative;padding: 30px 30px 30px 85px;background: #f3f3f3;border-left: 0;font-size: 16px;border-radius: 3px}
@media (max-width: 1239px) {
    .content-body .content-body-content .wp-block-quote, .content-body .content-body-content blockquote {padding: 20px 20px 20px 70px}
}
@media (max-width: 767px) {
    .content-body .content-body-content .wp-block-quote, .content-body .content-body-content blockquote {padding: 15px 15px 15px 60px}
}
.content-body .content-body-content .wp-block-quote:before, .content-body .content-body-content blockquote:before {position: absolute;width: 46px;height: 46px;left: 20px;top: 15px;background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg class='icon' viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='180' height='180'%3E%3Cpath d='M195.541 739.03C151.595 692.351 128 640 128 555.135c0-149.333 104.832-283.179 257.28-349.355l38.101 58.795c-142.293 76.97-170.112 176.853-181.205 239.83 22.912-11.862 52.907-16 82.304-13.27 76.97 7.125 137.643 70.315 137.643 148.864a149.333 149.333 0 01-149.334 149.333 165.163 165.163 0 01-117.248-50.304zm426.667 0c-43.947-46.678-67.541-99.03-67.541-183.894 0-149.333 104.832-283.179 257.28-349.355l38.101 58.795c-142.293 76.97-170.112 176.853-181.205 239.83 22.912-11.862 52.906-16 82.304-13.27 76.97 7.125 137.642 70.315 137.642 148.864a149.333 149.333 0 01-149.333 149.333 165.163 165.163 0 01-117.248-50.304z'/%3E%3C/svg%3E");background-repeat: no-repeat;background-position: 50%;background-size: 100% auto;content: "";opacity: .15}
@media (max-width: 1239px) {
    .content-body .content-body-content .wp-block-quote:before, .content-body .content-body-content blockquote:before {width: 42px;height: 42px;left: 15px}
}
@media (max-width: 767px) {
    .content-body .content-body-content .wp-block-quote:before, .content-body .content-body-content blockquote:before {width: 36px;height: 36px;left: 10px}
}
.content-body .content-body-content .wp-block-code, .content-body .content-body-content pre {padding: 10px;}
@media (max-width: 1239px) {
    .content-body .content-body-content .wp-block-code, .content-body .content-body-content pre {padding: 10px}
}
@media (max-width: 767px) {
    .content-body .content-body-content .wp-block-code, .content-body .content-body-content pre {padding: 10px}
}
.content-body .content-body-page p {margin: 0}
.page .content-body .content-body-head {margin-bottom: 45px}
.page .content-body .content-body-title {position: relative;margin-bottom: 0;text-align: center}
.page .content-body .content-body-title:after {position: absolute;left: 50%;bottom: -15px;width: 20px;height: 2px;margin-left: -10px;background: #999;content: "";border-radius: 2px}
.content-body-footer {margin: 30px 0}
.content-body-copyright {margin-top: 30px;padding: 15px;font-size: 14px;line-height: 1.6;color: #828282;border: 1px solid #efefef;background: rgba(239, 239, 239, .4);border-radius: 4px}
.content-body-copyright a, .content-body-copyright span {color: #5a5a5a}
.content-body-copyright p {margin-bottom: 8px !important;text-indent: 0 !important}
.content-body-copyright p:last-child {margin-bottom: 0 !important}
.content-body-tag {font-size: 0}
.content-body-tag a {display: inline-block;margin: 0 10px 5px 0;padding: 4px 15px;font-size: 12px;line-height: 1.2;color: #5a5a5a;border: 1px solid #efefef;border-radius: 3px}
.content-body-tag a:focus, .content-body-tag a:hover {color: #fff;background: var(--theme-hover);border-color: var(--theme-hover);text-decoration: none}
.content-body-bar {height: 60px;margin: 30px 0}
.content-body-bar .info-item.act .wi, .content-body-bar .info-item.share .wi {font-size: 20px}
@media (max-width: 1239px) {
    .content-body-bar .info-item.share .mobile {display: none}
}
@media (max-width: 767px) {
    .content-body-bar .author, .content-body-bar .info-item.act, .content-body-bar .info-item.share .meta-item, .content-body-bar .info-item.share:before {display: none}
    .content-body-bar .info {float: none !important}
    .content-body-bar .info-item.meta {margin: 0;float: right;line-height: 30px}
    .content-body-bar .info-item.share {float: left;margin-right: 0;padding-left: 0;vertical-align: top}
    .content-body-bar .info-item.share .mobile {display: block;padding-left: 12px;padding-right: 12px;color: var(--theme-color);border: 1px solid var(--theme-color);line-height: 28px;border-radius: 30px}
    .content-body-bar .info-item.share .mobile .wi {font-size: 16px}
}
.content-body-bar .info-item {position: relative;display: inline-block;margin-right: 20px;padding-left: 20px;color: #828282;line-height: 24px}
.content-body-bar .info-item:before {position: absolute;left: 0;top: 50%;width: 1px;height: 18px;margin-top: -9px;background: #999;content: "";opacity: .6}
.content-body-bar .info-item a {color: #828282;text-decoration: none}
.content-body-bar .info-item .stared {color: #ff5e5e}
.content-body-bar .info-item .stared:hover {color: #f33}
.content-body-bar .meta {padding-left: 0}
.content-body-bar .meta:before {display: none}
.content-body-bar .act {margin-right: 0}
.content-body-bar .meta-item {position: relative;display: inline-block;padding: 0 8px;overflow: hidden;vertical-align: top;-webkit-transition: all .3s ease-out 0s;-o-transition: all .3s ease-out 0s;transition: all .3s ease-out 0s}
.content-body-bar .meta-item:first-child {padding-left: 0}
.content-body-bar .meta-item:last-child {padding-right: 0}
.content-body-bar .meta-item:hover {overflow: visible}
.content-body-bar .meta-item:hover .share-wx-wrap {opacity: 1;filter: alpha(opacity=100);bottom: 40px}
.dashang-img, .content-body-bar .meta-item:hover, .content-body-bar .meta-item:hover .dashang-img, .content-body-bar .meta-item:hover .share-wx-wrap, .content-body-bar.fixed .content-body-bar-inner {-webkit-transition: all .3s ease-out 0s;-o-transition: all .3s ease-out 0s;transition: all .3s ease-out 0s}
.content-body-bar .meta-item:hover .dashang-img {opacity: 1;filter: alpha(opacity=100);bottom: 40px;pointer-events: auto}
.content-body-bar .meta-item.wechat:hover {color: #44b549}
.content-body-bar .meta-item.weibo:hover {color: #e05244}
.content-body-bar .meta-item.qq:hover {color: #22a4ff}
.content-body-bar .meta-item.douban:hover {color: #33b045}
.content-body-bar .meta-item.douban:hover svg {fill: #33b045}
.content-body-bar .meta-item.qzone:hover {color: #fdbf2f}
.content-body-bar .meta-item.qzone:hover svg {fill: #fdbf2f}
.content-body-bar .meta-item.linkedin:hover {color: #0077b5}
.content-body-bar .meta-item.facebook:hover {color: #44619d}
.content-body-bar .meta-item.twitter:hover {color: #55acee}
.content-body-bar .meta-item.dashang .wi {font-size: 18px}
.dashang-img span, .content-body-bar .wi {font-size: 16px;vertical-align: top}
@media (max-width: 767px) {
    .content-body-bar .dashang-img {left: auto;right: 0}
    .content-body-bar .dashang-img:after, .content-body-bar .dashang-img:before {left: 100%;margin-left: -26px}
}
.dashang-img, .content-body-bar.fixed .content-body-bar-inner {background: #fff}
.content-body-bar.fixed .content-body-bar-inner {position: fixed;bottom: 0;padding: 18px 30px;border-top: 1px solid #ddd;background: rgba(255, 255, 255, .92)}
@media (max-width: 991px) {
    .content-body-bar.fixed .content-body-bar-inner {position: relative}
}
.dashang-img {opacity: 0;filter: alpha(opacity=0);position: absolute;z-index: 9;left: 50%;bottom: 50px;width: 160px;margin-left: -81px;line-height: 1;text-align: center;border: 1px solid #efefef;-webkit-box-shadow: 0 0 3px 1px rgba(0, 0, 0, .05);box-shadow: 0 0 3px 1px rgba(0, 0, 0, .05);-webkit-box-sizing: content-box;box-sizing: content-box;pointer-events: none}
.dashang-img:after, .dashang-img:before {position: absolute;left: 50%;width: 0;height: 0;margin-left: -8px;line-height: 0;border: 8px solid transparent;content: ""}
.dashang-img:before {bottom: -16px;border-top-color: #efefef}
.dashang-img:after {z-index: 1;bottom: -15px;border-top-color: #fff}
.dashang-img canvas, .dashang-img img {display: block;width: 100%;height: auto;padding: 10px 10px 0;vertical-align: top}
.dashang-img span {display: inline-block;width: 160px;padding-bottom: 10px;color: #999;font-size: 12px;line-height: 1}
.dashang-img span img {margin-bottom: 2px}
.dashang-img2 {width: 330px;font-size: 0;margin-left: -166px}
@media (max-width: 767px) {
    .dashang-img2 {width: 300px;left: auto;right: 0;margin-left: 0}
    .dashang-img2:after, .dashang-img2:before {right: 76px;margin-right: -8px;left: auto;margin-left: 0}
    .dashang-img2 span {max-width: 50%}
}
.content-body-bar-inner {position: relative;z-index: 99;padding: 18px 0;border-top: 1px solid transparent;-webkit-transition: all .3s ease-out 0s;-o-transition: all .3s ease-out 0s;transition: all .3s ease-out 0s}
@media (max-width: 767px) {
    .content-body-bar-inner {padding: 15px 0}
}
.content-body-bar-inner .author a {display: inline-block;text-decoration: none}
.content-body-bar-inner .author img {display: inline-block;width: 24px;height: 24px;margin-right: 5px;border-radius: 50%;vertical-align: top}
.content-body-bar-inner .author-name {max-width: 120px;line-height: 24px;display: inline-block;-o-text-overflow: ellipsis;text-overflow: ellipsis;white-space: nowrap;overflow: hidden;vertical-align: top}
.content-body-bar-inner .user-group {position: relative;z-index: 0;display: inline-block;padding: 4px 8px;margin-left: 8px;color: var(--theme-color);font-size: 12px;font-weight: 400;line-height: 1;vertical-align: top;border-radius: 3px;border: 1px solid var(--theme-color);overflow: hidden;margin-top: 1px}
.content-body-bar-inner .user-group:before {position: absolute;z-index: -1;left: 0;top: 0;width: 100%;height: 100%;background-color: #fff;content: ""}
.content-body-bar-inner .user-group:after {position: absolute;z-index: 0;left: -1px;top: -1px;right: -1px;bottom: -1px;content: "";background: var(--theme-color);opacity: .148}
.body-box .content-body .content-body-video {margin: -30px -30px 20px}
@media (max-width: 1239px) {
    .body-box .content-body .content-body-video {margin: -20px -20px 20px}
}
@media (max-width: 767px) {
    .body-box .content-body .content-body-video {margin: -20px -15px 20px}
}
.body-box .content-body-bar {margin: 30px -30px}
@media (max-width: 1239px) {
    .body-box .content-body-bar {margin: 30px -20px}
}
@media (max-width: 767px) {
    .body-box .content-body-bar {margin: 30px 0}
}
.body-box .content-body-bar-inner {padding: 18px 30px;background: #fff;background: rgba(255, 255, 255, .92)}
@media (max-width: 767px) {
    .body-box .content-body-bar-inner {padding: 15px 0}
}
.content-body-action {margin-top: 30px;margin-bottom: 30px;text-align: center}
.content-body-action .btn-dashang, .content-body-action .btn-zan {display: inline-block;padding: 4px 30px;font-size: 18px;line-height: 30px;color: var(--theme-color);border: 1px solid var(--theme-color);border-radius: 4px;vertical-align: top}
@media (max-width: 767px) {
    .content-body-action .btn-dashang, .content-body-action .btn-zan {padding: 4px 18px;font-size: 16px;line-height: 26px}
}
.content-body-action .btn-dashang.liked, .content-body-action .btn-dashang:hover, .content-body-action .btn-zan.liked, .content-body-action .btn-zan:hover {color: #fff;background-color: var(--theme-hover);cursor: pointer;border-color: var(--theme-hover)}
.content-body-action .btn-dashang i, .content-body-action .btn-zan i {position: relative;vertical-align: top}
.content-body-action .btn-dashang {position: relative;z-index: 99;margin-left: 10px;overflow: hidden}
.content-body-action .btn-dashang:hover, .content-body-action .btn-dashang:hover .dashang-img {-webkit-transition: all .3s ease-out 0s;-o-transition: all .3s ease-out 0s;transition: all .3s ease-out 0s}
.content-body-action .btn-dashang:hover {overflow: visible}
.content-body-action .btn-dashang:hover .dashang-img {opacity: 1;filter: alpha(opacity=100);bottom: 40px;pointer-events: auto}
.comment-reply-title, .comments-title, .content-body-related-title {position: relative;padding: 0 0 0 12px}
.comment-reply-title:before, .comments-title:before, .content-body-related-title:before {position: absolute;left: 0;top: 1px;width: 3px;height: calc(100% - 2px);content: "";background-color: var(--theme-color);background-image: -o-linear-gradient(top, rgba(255, 255, 255, .15), transparent);background-image: linear-gradient(180deg, rgba(255, 255, 255, .15), transparent);border-radius: 3px}

.page-no-sidebar.member-lostpassword {padding: 0}
.page-no-sidebar.member-lostpassword .member-lp-main {min-height: 280px}
@media (max-width: 767px) {
    .page-no-sidebar.member-lostpassword .lostpassword-form {width: auto;margin: 30px 10px}
}
.page-no-sidebar.member-account .main-content {padding: 0;background: 0 0}
.page-no-sidebar.member-account .banner, .page-no-sidebar.member-account .breadcrumb {display: none}
.page-no-sidebar.member-login, .page-no-sidebar.member-register {background-size: cover;background-position: 50%;background-repeat: no-repeat;background-color: #fff}
.page-no-sidebar.member-login #wrap, .page-no-sidebar.member-register #wrap {width: 100%;padding: 0;background: 0 0;-webkit-box-sizing: border-box;box-sizing: border-box;display: -webkit-box;display: -webkit-flex;display: -ms-flexbox;display: flex;-webkit-box-align: center;-webkit-align-items: center;-ms-flex-align: center;align-items: center}
@media (max-width: 767px) {
    .page-no-sidebar.member-login #wrap, .page-no-sidebar.member-register #wrap {background: #fff !important;position: relative;height: auto}
}
.page-no-sidebar.member-login .wrap, .page-no-sidebar.member-register .wrap {width: 100%;margin: 0;padding: 0}
.page-no-sidebar.member-login .action, .page-no-sidebar.member-login .footer .footer-col-logo, .page-no-sidebar.member-login .footer .footer-col-sns, .page-no-sidebar.member-login header.header, .page-no-sidebar.member-register .action, .page-no-sidebar.member-register .footer .footer-col-logo, .page-no-sidebar.member-register .footer .footer-col-sns, .page-no-sidebar.member-register header.header {display: none}
.page-no-sidebar.member-login .footer, .page-no-sidebar.member-register .footer {width: 100%;color: #fff;text-align: center;background: -o-linear-gradient(top, transparent 0, rgba(0, 0, 0, .3) 100%);background: linear-gradient(180deg, transparent, rgba(0, 0, 0, .3))}
@media (max-width: 767px) {
    .page-no-sidebar.member-login .footer, .page-no-sidebar.member-register .footer {position: relative;font-size: 12px;color: #999;background: #fff}
}
.page-no-sidebar.member-login .footer a, .page-no-sidebar.member-login .footer a:hover, .page-no-sidebar.member-register .footer a, .page-no-sidebar.member-register .footer a:hover {color: #fff}
@media (max-width: 767px) {
    .page-no-sidebar.member-login .footer a, .page-no-sidebar.member-register .footer a {color: #999}
}
@media (max-width: 767px) {
    .page-no-sidebar.member-login .footer a:hover, .page-no-sidebar.member-register .footer a:hover {color: #999}
}
.page-no-sidebar.member-login .main-content, .page-no-sidebar.member-register .main-content {padding: 0;margin: 0;border: 0;background: 0 0}
.page-no-sidebar.member-login .member-form-wrap, .page-no-sidebar.member-register .member-form-wrap {margin: 30px auto}
.page-no-sidebar.member-lostpassword #wrap {background: #fff}
.page-no-sidebar.member-lostpassword .wrap {width: 100%;margin: 0;padding: 0}
.page-no-sidebar.member-lostpassword .main-content {padding: 0;margin: 0;border: 0;background: 0 0}
.page-no-sidebar.member-lostpassword .member-lp-process {width: 1140px;margin: 0 auto !important}
@media (max-width: 1239px) {
    .page-no-sidebar.member-lostpassword .member-lp-process {width: 970px}
}
@media (max-width: 991px) {
    .page-no-sidebar.member-lostpassword .member-lp-process {width: 750px}
}
@media (max-width: 767px) {
    .page-no-sidebar.member-lostpassword .member-lp-process {width: 100%;padding: 0 15px}
}
.hotnews-member.member-account .main-content, .hotnews-member.member-profile .main-content {padding: 0}
.hotnews-member.member-account .breadcrumb, .hotnews-member.member-account .content-body-head, .hotnews-member.member-profile .breadcrumb, .hotnews-member.member-profile .content-body-head {display: none}
@media (max-width: 767px) {
    .hotnews-member.member-profile .wrap {margin-top: 0;padding: 0}
}
.hotnews-member.member-social .breadcrumb, .hotnews-member.member-social .content-body-head {display: none}

.wp-block-hotnews-hidden-content:not(:last-child) {margin-bottom: 30px}
.hidden-content-wrap {position: relative;z-index: 0;width: 100%;margin: 0;text-align: center !important;-webkit-box-shadow: 0 0 1px 0 rgba(0, 0, 0, .15);box-shadow: 0 0 1px 0 rgba(0, 0, 0, .15)}
.hidden-content-wrap:before {position: absolute;z-index: -1;left: 0;top: 0;right: 0;bottom: 0;background-color: var(--theme-color);content: "";opacity: .05}
.hidden-content-wrap.loading:after {position: absolute;left: 0;top: 0;width: 100%;height: 100%;background: rgba(255, 255, 255, .6);content: ""}
.hidden-content-wrap.loading .hidden-content-loading {display: block;position: absolute;left: 50%;top: 50%;z-index: 9;width: 32px;height: 32px;margin-left: -16px;margin-top: -16px;line-height: 32px;color: #828282;font-size: 24px;text-align: center;-webkit-animation: rotating 2s linear infinite;animation: rotating 2s linear infinite;opacity: .8}
.hidden-content-wrap .hidden-content-bg {position: absolute;z-index: -1;left: 0;top: 0;width: 100%;height: 100%;-o-object-fit: cover;object-fit: cover}
.hidden-content-wrap .pay {margin-bottom: 20px;position: relative;line-height: 1;width: 100%}
.hidden-content-wrap .pay:before {content: "";position: absolute;top: 10px;left: 0;width: 100%;height: 1px;background-color: rgba(0, 0, 0, .08)}
.hidden-content-wrap .pay:after {position: absolute;top: -180px;left: 0;content: "";width: 100%;height: 160px;background: -o-linear-gradient(top, rgba(238, 238, 238, 0) 0, #fff 100%);background: linear-gradient(180deg, rgba(238, 238, 238, 0), #fff)}
.hidden-content-wrap .pay .pay-top-text {padding: 0 20px;position: relative;display: inline-block;font-size: 14px;line-height: 1;color: #666;z-index: 2;background-color: #fff}
.hidden-content-wrap .hidden-content {position: relative;padding: 24px 12px}
.hidden-content-wrap .hidden-content-loading {display: none}
.hidden-content-wrap .hidden-content-icon {display: block;width: 42px;height: 42px;margin: 0 auto;font-size: 42px;line-height: 1;color: var(--theme-color);-webkit-mask-image: linear-gradient(270deg, rgba(0, 0, 0, .4) 30%, #000 90%);mask-image: linear-gradient(270deg, rgba(0, 0, 0, .4) 30%, #000 90%)}
.hidden-content-wrap .hidden-content-btn {margin-top: 16px}
.hidden-content-wrap .hidden-content-desc {margin: 10px 0 0 !important;font-size: 14px;line-height: 1.4;color: #5a5a5a;text-indent: 0 !important;text-align: center !important}
.hidden-content-wrap .hidden-content-groupname {margin-top: 8px}
.hidden-content-wrap .hidden-content-groupname b {display: inline-block;margin: 0 5px;padding: 4px 8px;color: var(--theme-color);font-size: 12px;font-weight: 400;line-height: 1;border: 1px solid var(--theme-color);border-radius: 2px}
.hidden-content-wrap .hidden-content-forbidden {color: #ffa400;font-size: 14px;margin: 16px 0 0 !important;text-indent: 0 !important;text-align: center !important}
.hidden-content-wrap .hidden-content-refresh {position: absolute;right: 8px;top: 8px;font-size: 12px;line-height: 14px;color: #828282}
.hidden-content-wrap .hidden-content-refresh .hotnews-icon {display: inline-block;margin-left: 2px;font-size: 14px;line-height: inherit;vertical-align: top}
.hidden-content-wrap .hidden-content-refresh .refresh-url {display: inline-block;color: #5a5a5a;vertical-align: top}
.comments-list .comment-children .comment-text-reply a:hover, .hidden-content-wrap .hidden-content-refresh .refresh-url:hover {color: var(--theme-hover)}
.hidden-content-wrap .hidden-content-refresh .refresh-url.loading .wi {-webkit-animation: rotating 2s linear infinite;animation: rotating 2s linear infinite}
.comment-reply-title, .comments-title {font-size: 18px;margin: 30px 0 20px;font-weight: 500}
.comment-notes, .comments-list .comment .comment-reply-title:before, .comments-list .comment-children .reply span {display: none}
.comments-list {padding: 0;margin: 0 0 20px;list-style: none}
.comments-list .comment {position: relative;margin-bottom: 10px;padding-bottom: 10px;border-bottom: 1px solid rgba(239, 239, 239, .8);-webkit-transition: all .3s ease-out 0s;-o-transition: all .3s ease-out 0s;transition: all .3s ease-out 0s}
@media (max-width: 767px) {
    .comments-list .comment {margin-bottom: 15px;padding-bottom: 15px}
}
.comments-list .comment .comment-respond {position: relative;margin-top: 20px;padding-left: 55px}
@media (max-width: 767px) {
    .comments-list .comment .comment-respond {margin-top: 15px;padding-left: 40px}
}
.comments-list .comment .comment-reply-title {position: static;padding-left: 0;margin: 0 0 15px;font-size: 16px;font-weight: 500;max-width: 75%;white-space: nowrap;overflow: hidden;-o-text-overflow: ellipsis;text-overflow: ellipsis}
.comments-list .comment-children {position: relative;margin: 15px 0 0 55px;padding: 20px;list-style: none;background: #f8f8f8;border-radius: 4px}
@media (max-width: 767px) {
    .comments-list .comment-children {margin-left: 40px;padding: 15px 10px}
}
.comments-list .comment-children:before {position: absolute;left: 15px;top: -17px;border: 9px solid transparent;border-bottom: 10px solid #f8f8f8;content: ""}
.comments-list .comment-children .comment {margin-bottom: 10px;padding-bottom: 5px}
.comments-list .comment-children .comment .comment-respond {margin-top: 15px;padding-left: 34px}
.comments-list .comment-children .comment:last-child {border-bottom: 0;padding-bottom: 0;margin-bottom: 0}
.comments-list .comment-children .comment-author {width: 24px;margin-right: 10px}
.comments-list .comment-children .comment-author img {width: 24px;height: 24px}
.comments-list .comment-children .comment-body .nickname, .comments-list .comment-children .reply {line-height: 24px}
.comments-list .comment-children .comment-body .comment-time {display: inline-block;margin-left: 10px;vertical-align: top}
@media (max-width: 767px) {
    .comments-list .comment-children .comment-body .comment-time {line-height: 24px;margin-top: 0}
}
.comments-list .comment-children .comment-body .comment-text {padding-left: 34px;padding-right: 34px;color: #5a5a5a}
.comments-list .comment-children .comment-text-reply a {font-weight: 500}
.comments-list .comment-children .reply .wi {margin-right: 0;font-size: 16px}
.comments-list .reply {position: absolute;right: 0;top: 0;font-size: 12px;line-height: 40px;visibility: hidden}
@media (max-width: 767px) {
    .comments-list .reply {line-height: 30px}
}
.comments-list .reply .wi {margin-right: 3px;font-size: 14px;vertical-align: top}
.comments-list .reply a {display: block;color: #828282}
#cancel-comment-reply-link:hover, .comment-body .nickname a, .comments-list .reply a:hover {color: var(--theme-hover)}
.comment-body .nickname .comment-text-reply a,.comment-body .nickname .comment-text-reply a:hover{
    color: #b40707;
}
.comments-list .comment > .comment-inner:hover > .reply {visibility: visible}
.comment-author {float: left;width: 40px;margin-right: 15px}
@media (max-width: 767px) {
    .comment-author {width: 30px;margin-right: 10px}
}
.comment-author img {width: 40px;height: 40px;border-radius: 50%}
@media (max-width: 767px) {
    .comment-author img {width: 30px;height: 30px}
}
.comment-body .nickname {margin-bottom: 10px;font-size: 14px;font-weight: 500;line-height: 20px;color: #262626}
@media (max-width: 767px) {
    .comment-body .nickname {line-height: 16px}
}
.comment-body .comment-time {display: block;margin-top: 4px;font-size: 12px;line-height: 16px;font-weight: 400;color: #828282}
@media (max-width: 767px) {
    .comment-body .comment-time {margin-top: 2px;line-height: 14px}
}
.comment-body .comment-awaiting-moderation {padding-left: 55px;margin-bottom: 10px;color: #262626;font-weight: 500}
.comment-text {padding-left: 55px;padding-right: 55px;font-size: 14px;line-height: 1.6;color: #262626}
.comment-text-content{padding: 5px;background: #f1f1f1; border-radius: 5px;}
.comment-text img {height: auto}
.comment-text audio, .comment-text img, .comment-text video {max-width: 100%;vertical-align: top}
@media (max-width: 767px) {
    .comment-text audio, .comment-text video {display: block;width: 100%;height: auto}
    .comment-text {padding-left: 40px}
}
.comment-text p:last-child {margin-bottom: 0}
.comment-respond {margin-top: 30px}
.comment-respond span.required {color: red}
#cancel-comment-reply-link {position: absolute;right: 0;top: 0;font-size: 18px;color: #5a5a5a}
.logged-in-as {margin-bottom: 10px}
.reply-left {width: 260px;float: left}
.comment-form {margin-top: 15px;border: 1px solid #efefef;background: #fbfbfb}
.comment-form-comment {position: relative}
.comment-form-comment textarea {width: 100%;height: 100px;padding: 8px 12px;line-height: 22px;border: 0;border-bottom: 1px solid #f5f5f5;outline: 0;vertical-align: top;-webkit-box-sizing: border-box;box-sizing: border-box;background: #fff;resize: none}
.comment-form-comment .error {border: 1px solid #a94442}
.comment-form-smile, .modal-message-smile {position: absolute;right: 12px;bottom: 8px;z-index: 9;width: 28px;text-align: center;font-size: 26px;line-height: 28px;color: rgba(130, 130, 130, .7);-webkit-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none}
.comment-form-smile:hover, .modal-message-smile:hover {cursor: pointer;color: #828282}
.comment-form-smile:active, .modal-message-smile:active {color: #5a5a5a}
.comment-form-smile .smilies-box, .modal-message-smile .smilies-box {display: none;position: absolute;right: 0;top: 100%;-webkit-flex-wrap: wrap;-ms-flex-wrap: wrap;flex-wrap: wrap;width: 360px;min-height: 140px;padding: 8px;-webkit-box-shadow: 0 0 5px 3px rgba(0, 0, 0, .1);box-shadow: 0 0 5px 3px rgba(0, 0, 0, .1);border-radius: 3px;overflow: hidden;cursor: default;background: #fff}
.comment-form-smile .smilies-box.active, .modal-message-smile .smilies-box.active {display: -webkit-box;display: -webkit-flex;display: -ms-flexbox;display: flex}
.comment-form-smile .smilies-box-loading, .modal-message-smile .smilies-box-loading {position: absolute;left: 50%;top: 50%;width: 32px;height: 32px;margin-left: -16px;margin-top: -16px;line-height: 32px;color: #828282;font-size: 24px;text-align: center;-webkit-animation: rotating 2s linear infinite;animation: rotating 2s linear infinite;opacity: .8}
.comment-form-smile .smilies-item, .modal-message-smile .smilies-item {width: 12.5%;padding: 8px;border-radius: 2px;-webkit-box-sizing: border-box;box-sizing: border-box;-webkit-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none}
.comment-form-smile .smilies-item:hover, .modal-message-smile .smilies-item:hover {cursor: pointer;background: rgba(0, 0, 0, .06)}
.comment-form-smile .smilies-item img, .modal-message-smile .smilies-item img {width: 100%;height: auto;display: block}
.comment-form-author, .comment-form-email, .comment-form-url {position: relative;float: left;width: 33.33%;padding: 15px 15px 0;margin: 0;-webkit-box-sizing: border-box;box-sizing: border-box}
@media (max-width: 767px) {
    .comment-form-author, .comment-form-email, .comment-form-url {width: 100%;float: none}
}
.comment-form-author label, .comment-form-email label, .comment-form-url label {position: absolute;left: 15px;top: 15px;line-height: 34px;width: 62px;color: #666;font-weight: 400;text-align: center}
.comment-form-author input, .comment-form-email input, .comment-form-url input {display: block;height: 34px;width: 100%;line-height: 22px;padding: 6px 10px 6px 60px;border: 1px solid #efefef;outline: 0}
.comment-form-author input.error, .comment-form-email input.error, .comment-form-url input.error {border-color: #a94442}
.comment-form-author {border-right: 0}
.comment-form-email {border-left: 0}
.comment-form-cookies-consent {float: left;padding: 10px 15px;margin: 0;font-size: 13px;color: #666}
@media (max-width: 767px) {
    .comment-form-cookies-consent {width: 100%;float: none}
}
.form-submit {padding: 8px 12px;margin: 0;border-bottom-left-radius: 2px;border-bottom-right-radius: 2px;overflow: hidden}
.form-submit .avatar {width: 28px;height: 28px;margin-right: 8px;vertical-align: top;border-radius: 50%}
.form-submit .submit {float: right}
@media (max-width: 767px) {
    .logged-in .form-submit .submit {display: inline-block;width: auto;float: right}
}
.comment-must-login {height: 100px;padding: 8px 12px;font-size: 14px;color: #999;border: 1px solid transparent;border-bottom-color: #efefef;-webkit-box-sizing: border-box;box-sizing: border-box}
.form-submit-text, .articles-list-list .item a {overflow: hidden;-o-text-overflow: ellipsis;text-overflow: ellipsis;white-space: nowrap}
.form-submit-text {font-size: 12px;line-height: 28px;color: #666;max-width: 60%}
.form-submit-text span {color: var(--theme-color)}
.multi-filter + .sec-panel .sec-panel-head {display: none}
.articles-list, .articles-list-list {list-style: none;padding: 0;margin: 0}
.archive .main > .sec-panel > .sec-panel-body, .search .main > .sec-panel > .sec-panel-body {padding-top: 0;padding-left: 0;padding-right: 0}
.archive.category .breadcrumb {margin-top: -20px}
@media (max-width: 1239px) {
    .archive.category .breadcrumb {margin-top: -10px}
}
.body-box .archive .main > .sec-panel .sec-panel-head + .sec-panel-body, .body-box .search .main > .sec-panel .sec-panel-head + .sec-panel-body {padding-top: 20px}
.body-box.archive.category .breadcrumb {margin-top: 0}
.articles-list-list {padding: 0 0 10px}
.articles-list-list .item {position: relative;margin: 0;padding: 15px 0 15px 18px;color: #828282;line-height: 32px;border-bottom: 1px solid rgba(239, 239, 239, .8)}
.articles-list-list .item:before {position: absolute;left: 1px;top: 27px;width: 0;height: 0;border: 4px solid transparent;border-left: 6px solid #ccc;content: ""}
.articles-list-list .item .date {position: absolute;right: 0;top: 10px;font-size: 14px}
@media (max-width: 767px) {
    .articles-list-list .item .date {display: none}
}
.articles-list-list .item a {display: block;font-size: 16px;padding-right: 110px;vertical-align: top;color: #262626}
.articles-list-list .item a:hover {color: var(--theme-hover);text-decoration: none}
@media (max-width: 767px) {
    .articles-list-list .item a {width: 100%;padding-right: 0}
}
.body-box .articles-list-list {padding: 0 16px 20px}
@media (max-width: 767px) {
    .body-box .articles-list-list {padding: 0 0 20px}
}
.articles-list-default {padding: 0;margin: 0 0 20px}
.articles-list-default .item, .articles-list-default .item:hover {-webkit-transition: all .3s ease 0s;-o-transition: all .3s ease 0s;transition: all .3s ease 0s}
.articles-list-default .item {display: -webkit-box;display: -webkit-flex;display: -ms-flexbox;display: flex;padding: 20px 0;border-bottom: 1px solid rgba(239, 239, 239, .8);overflow: hidden}
@media (max-width: 767px) {
    .articles-list-default .item {padding: 15px 0}
}
.articles-list-default .item:hover {margin: 0 -20px;padding: 20px;background: #fafafa}
.articles-list-default .item3, .articles-list-default .item:hover .edit-link {display: block}
@media (max-width: 767px) {
    .articles-list-default .item:hover {margin: 0;padding: 15px 0}
    .articles-list-default .item:hover .edit-link {display: none}
}
.articles-list-default .item3 .item-content {padding: 0}
.articles-list-default .item3 .item-images {display: -webkit-box;display: -webkit-flex;display: -ms-flexbox;display: flex;-webkit-box-pack: justify;-webkit-justify-content: space-between;-ms-flex-pack: justify;justify-content: space-between;margin-bottom: 10px}
.articles-list-default .item3 .item-images span {position: relative;width: 24.25%;overflow: hidden;border-radius: 3px;-webkit-transition: all .3s ease-out 0s;-o-transition: all .3s ease-out 0s;transition: all .3s ease-out 0s}
.articles-list-default .item3 .item-images span:before {position: relative;z-index: 1;display: block;content: "";padding-top: 62.5%}
@media (max-width: 420px) {
    .articles-list-default .item3 .item-images span {width: 32.5%}
    .articles-list-default .item3 .item-images span:last-child {display: none}
}
.articles-list-default .item3 .item-images span:hover {-webkit-box-shadow: 0 0 1px 0 rgba(0, 0, 0, .3);box-shadow: 0 0 1px 0 rgba(0, 0, 0, .3);-webkit-transition: all .3s ease-out 0s;-o-transition: all .3s ease-out 0s;transition: all .3s ease-out 0s}
.articles-list-default .item-img:hover img, .articles-list-default .item3 .item-images span:hover .item-images-el {-webkit-transform: scale(1.03);-ms-transform: scale(1.03);transform: scale(1.03);-webkit-transition: all .3s ease-out 0s;-o-transition: all .3s ease-out 0s;transition: all .3s ease-out 0s}
.articles-list-default .item3 .item-images span:last-child {margin-right: 0}
.articles-list-default .item-img img, .articles-list-default .item3 .item-images .item-images-el {width: 100%;height: 100%;-webkit-transition: all .3s ease-out 0s;-o-transition: all .3s ease-out 0s;transition: all .3s ease-out 0s}
.articles-list-default .item3 .item-images .item-images-el {position: absolute;left: 0;top: 0;background-size: cover;background-position: 50%;background-repeat: no-repeat}
.articles-list-default .item3 .item-meta {position: relative;padding-right: 0}
@media (max-width: 767px) {
    .articles-list-default .item3 .item-meta {padding: 0}
}
.articles-list-default .item3 .item-excerpt {height: auto}
.articles-list-default .item-excerpt p, .articles-list-default .item3 .item-excerpt p {margin-bottom: 0}
@media screen and (-webkit-min-device-pixel-ratio: 0) {
    .articles-list-default .item-sticky .item-title a {display: block}
    .articles-list-default .item-sticky .item-title a .sticky-post {-webkit-text-fill-color: #fff}
}
.articles-list-default .item-img, .articles-list-default .item-img:hover {-webkit-transition: -webkit-box-shadow .3s ease 0s;-o-transition: box-shadow .3s ease 0s;transition: box-shadow .3s ease 0s;transition: box-shadow .3s ease 0s, -webkit-box-shadow .3s ease 0s}
.articles-list-default .item-img {position: relative;width: 20%;overflow: hidden;border-radius: 4px;-webkit-flex-shrink: 0;-ms-flex-negative: 0;flex-shrink: 0;-webkit-box-flex: 0;-webkit-flex-grow: 0;-ms-flex-positive: 0;flex-grow: 0}
@media (max-width: 767px) {
    .articles-list-default .item-img {width: 25%}
}
.articles-list-default .item-img:before {content: "";display: block;padding-top: 62.5%}
.articles-list-default .item-img:hover {-webkit-box-shadow: 0 0 1px 0 rgba(0, 0, 0, .3);box-shadow: 0 0 1px 0 rgba(0, 0, 0, .3)}
.articles-list-default .item-img .item-img-inner {position: absolute;left: 0;top: 0;width: 100%;height: 100%}
.articles-list-default .item-img img {-o-object-fit: cover;object-fit: cover}
.articles-list-default .item-img.item-video:hover .item-img-inner:after, .articles-list-default .item-img.item-video:hover .item-img-inner:before, .widget_lastest_products .p-item-wrap .thumb.thumb-video:hover:after, .widget_lastest_products .p-item-wrap .thumb.thumb-video:hover:before, .widget_post_tabs .item-img.item-video:hover .item-img-inner:after, .widget_post_tabs .item-img.item-video:hover .item-img-inner:before, .widget_post_thumb .item-img.item-video:hover .item-img-inner:after, .widget_post_thumb .item-img.item-video:hover .item-img-inner:before {opacity: 1;-webkit-transition: all .3s ease-out 0s;-o-transition: all .3s ease-out 0s;transition: all .3s ease-out 0s}
.articles-list-default .item-img.item-video .item-img-inner:after, .articles-list-default .item-img.item-video .item-img-inner:before {position: absolute;left: 50%;top: 50%;width: 44px;margin-left: -22px;margin-top: -22px;-webkit-box-sizing: border-box;box-sizing: border-box;border-radius: 50%;-webkit-transition: all .3s ease-out 0s;-o-transition: all .3s ease-out 0s;transition: all .3s ease-out 0s}
.articles-list-default .item-img.item-video .item-img-inner:after {opacity: .9}
.articles-list-default .item-img.item-video .item-img-inner:before {height: 44px}
@media (max-width: 767px) {
    .articles-list-default .item-img.item-video .item-img-inner:after, .articles-list-default .item-img.item-video .item-img-inner:before {width: 36px;height: 36px;margin-left: -18px;margin-top: -18px}
}
.articles-list-default .item-img.item-video .item-img-inner:before {z-index: 1;content: "";background: rgba(0, 0, 0, .6);opacity: .8}
.articles-list-default .item-img.item-video .item-img-inner:after {z-index: 1;height: 44px;content: "";background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='300' height='300'%3E%3Cpath d='M341.333 290.987v442.026c0 33.707 37.12 54.187 65.707 35.84L754.347 547.84a42.667 42.667 0 000-72.107L407.04 255.147a42.581 42.581 0 00-65.707 35.84z' fill='%23fff'/%3E%3C/svg%3E");background-position: 50%;background-repeat: no-repeat;background-size: 32px}
@media (max-width: 767px) {
    .articles-list-default .item-img.item-video .item-img-inner:after {height: 36px;background-size: 26px}
}
.articles-list-default .item-ad {display: block}
.articles-list-default .item-ad:hover {background: #fff}
.articles-list-default .item-ad .hotnews_ad_wrap, .special-item-bottom li, .widget-kx-list .kx-content p {margin: 0}
.articles-list-default .item-category {position: absolute;left: 10px;top: 15px;padding: 4px 8px;font-size: 12px;line-height: 14px;color: #fff;background-color: #000;filter: alpha(opacity=60);background: rgba(0, 0, 0, .6);border-radius: 3px;text-decoration: none}
@media (max-width: 767px) {
    .articles-list-default .item-category {display: none}
}
.articles-list-default .item-category:hover {background: var(--theme-hover)}
.articles-list-default .item-content {position: relative;width: 100%;padding-left: 20px}
@media (max-width: 767px) {
    .articles-list-default .item-content {padding-left: 10px}
}
.articles-list-default .item-content .edit-link {display: none;position: absolute;right: 0;top: 0;z-index: 1}
.articles-list-default .item-no-thumb .item-content {padding: 0 !important;min-height: auto}
.articles-list-default .item-no-thumb .item-excerpt {max-height: 44px;height: auto;font-size: 14px}
@media (max-width: 767px) {
    .articles-list-default .item-no-thumb .item-excerpt {display: block}
}
.articles-list-default .item-no-thumb .item-meta {position: relative;bottom: 0}
@media (max-width: 767px) {
    .articles-list-default .item-no-thumb .item-meta {padding-left: 0}
}
.articles-list-default .item-title {margin: 0 0 5px;font-size: 16px;line-height: 1.5;font-weight: 500}
@media (max-width: 767px) {
    .articles-list-default .item-title {font-size: 16px;line-height: 1.4;font-weight: 400}
}
.articles-list-default .item-excerpt, .articles-list-default .item-title a {overflow: hidden;-o-text-overflow: ellipsis;text-overflow: ellipsis;-webkit-box-orient: vertical;display: block;display: -webkit-box;-webkit-line-clamp: 2}
.articles-list-default .item-title a {color: #262626;max-height: 56px;text-decoration: none}
@media (max-width: 1239px) and (min-width: 768px) {
    .articles-list-default .item-title a {-webkit-line-clamp: 1;max-height: 28px}
}
@media (max-width: 767px) {
    .articles-list-default .item-title a {color: #000;max-height: 50px}
}
.articles-list-default .item-title a span {color: #e0b228}
.articles-list-default .item-title a .sticky-post {display: inline-block;padding: 0 5px;margin-top: 5px;font-size: 12px;font-weight: 400;line-height: 20px;color: #fff;background: var(--theme-color);border-radius: 2px;vertical-align: top}
@media (max-width: 767px) {
    .articles-list-default .item-title a .sticky-post {margin-top: 3px;font-size: 11px;line-height: 19px}
}
.articles-list-default .item-meta a:hover, .articles-list-default .item-title a:hover {color: var(--theme-hover)}
.articles-list-default .item-excerpt {height: 40px;margin-bottom: 5px;line-height: 1.5;color: #5a5a5a;font-size: 14px;overflow: hidden;}
@media (max-width: 767px) {
    .articles-list-default .item-excerpt {display: none}
}
.articles-list-default .item-meta {width: 100%;padding-right: 20px;color: #828282;font-size: 12px}
.articles-list-default .item-meta .item-meta-right {float: right}
.articles-list-default .item-meta .item-meta-li {display: inline-block;margin-right: 15px;vertical-align: top;line-height: 24px}
.articles-list-default .item-meta .item-meta-li:last-child {margin-right: 0}
@media (min-width: 992px) and (max-width: 1239px) {
    .articles-list-default .item-meta .item-meta-li {margin-right: 10px}
}
@media (max-width: 767px) {
    .articles-list-default .item-meta .item-meta-li {line-height: 16px;margin-right: 10px}
}
.articles-list-default .item-meta .item-meta-li.dot {margin-left: -7px;margin-right: 8px;font-size: 20px;opacity: .8}
.articles-list-default .item-meta .item-meta-li .wi {font-size: 14px;vertical-align: top}
.articles-list-default .item-meta a {color: #828282;text-decoration: none}
.articles-list-default .item-meta .author {margin-right: 15px;display: inline-block}
@media (min-width: 992px) and (max-width: 1239px) {
    .articles-list-default .item-meta .author {display: none}
}
.articles-list-default .item-meta a.avatar {height: 24px;line-height: 24px;display: inline-block;vertical-align: top}
.articles-list-default .item-meta a.avatar img {display: inline-block;width: 24px;height: 24px;margin-right: 3px;border-radius: 50%;background: #f5f5f5;vertical-align: top}
.articles-list-default .item-meta a.avatar img:hover {opacity: .9;filter: alpha(opacity=90)}
@media (max-width: 767px) {
    .articles-list-default .item-meta .author, .articles-list-default .item-meta .comments, .articles-list-default .item-meta .likes, .articles-list-default .item-meta .stars {display: none}
    .articles-list-default .item-meta .views {margin: 0;float: right}
}
.articles-list-default .item2 .item-img {-webkit-box-ordinal-group: 2;-webkit-order: 1;-ms-flex-order: 1;order: 1}
.articles-list-default .item2 .item-content {padding-left: 0;padding-right: 20px}
@media (max-width: 767px) {
    .articles-list-default .item2 .item-content {padding-right: 10px}
}
.articles-list-default .item2 .item-category {left: auto;right: 10px}
.articles-list-default .item2:hover .edit-link {right: 20px}
.articles-list-default .item4 .item-content {padding: 0}
.articles-list-default .item4 .item-image, .articles-list-default .item4 .item-image:hover {-webkit-transition: all .3s ease-out 0s;-o-transition: all .3s ease-out 0s;transition: all .3s ease-out 0s}
.articles-list-default .item4 .item-image {position: relative;display: block;margin-bottom: 10px;border-radius: 3px;overflow: hidden}
.articles-list-default .item4 .item-image:hover {opacity: .9}
.articles-list-default .item4 .item-image:before {display: block;padding-bottom: 37%;content: ""}
.articles-list-default .item4 .item-image .item-image-el, .articles-list-default .item4 .item-image .item-slider {position: absolute;top: 0;left: 0;width: 100%;height: 100%}
.articles-list-default .item4 .item-image .item-image-el {background-size: cover;background-position: 50%;background-repeat: no-repeat}
.articles-list-default .item4 .item-image img {display: block;width: 100%;height: auto}
.articles-list-default .item4 .item-excerpt {height: auto}
.articles-list-default .item4 .item-meta {position: relative;padding: 0}
.body-box .articles-list-default {padding: 0 20px}
@media (max-width: 767px) {
    .body-box .articles-list-default {padding: 0}
}
.sec-panel-head + .sec-panel-body .articles-list-card, .sec-panel-head + .sec-panel-body > .articles-list-image {margin-top: 0}
.articles-list-card, .articles-list-image {display: -webkit-box;display: -webkit-flex;display: -ms-flexbox;display: flex;margin: -10px -10px 10px;-webkit-flex-wrap: wrap;-ms-flex-wrap: wrap;flex-wrap: wrap}
@media (max-width: 767px) {
    .articles-list-card, .articles-list-image {margin: -6px -6px 10px}
}
.articles-list-card .item, .articles-list-image .item {width: 33.33%;padding: 10px;-webkit-flex-shrink: 0;-ms-flex-negative: 0;flex-shrink: 0;-webkit-box-sizing: border-box;box-sizing: border-box}
@media (max-width: 767px) {
    .articles-list-card .item, .articles-list-image .item {padding: 6px}
}
.articles-list-card .item-inner, .articles-list-image .item-inner {background: #fff;border-radius: 4px;overflow: hidden;-webkit-box-shadow: 0 0 1px 0 rgba(0, 0, 0, .3);box-shadow: 0 0 1px 0 rgba(0, 0, 0, .3);-webkit-transition: all .2s ease-out 0s;-o-transition: all .2s ease-out 0s;transition: all .2s ease-out 0s}
.articles-list-card .item-inner:hover, .articles-list-image .item-inner:hover {-webkit-box-shadow: 0 2px 10px 3px rgba(0, 0, 0, .15);box-shadow: 0 2px 10px 3px rgba(0, 0, 0, .15);-webkit-transform: translateY(-3px);-ms-transform: translateY(-3px);transform: translateY(-3px);-webkit-transition: all .2s ease-in 0s;-o-transition: all .2s ease-in 0s;transition: all .2s ease-in 0s}
.articles-list-card .item-inner:hover .item-video:after, .articles-list-card .item-inner:hover .item-video:before, .articles-list-image .item-inner:hover .item-video:after, .articles-list-image .item-inner:hover .item-video:before {opacity: 1;-webkit-transition: all .2s ease-in 0s;-o-transition: all .2s ease-in 0s;transition: all .2s ease-in 0s}
.articles-list-card .item-inner:hover .item-thumb img, .articles-list-image .item-inner:hover .item-thumb img {opacity: .9;-webkit-transition: all .2s ease-in 0s;-o-transition: all .2s ease-in 0s;transition: all .2s ease-in 0s}
.articles-list-card.cols-2 .item, .articles-list-image.cols-2 .item {width: 50%}
.articles-list-card.cols-4 .item, .articles-list-image.cols-4 .item {width: 25%}
.articles-list-card.cols-5 .item, .articles-list-image.cols-5 .item {width: 20%}
@media (max-width: 991px) {
    .articles-list-card.cols-2 .item, .articles-list-card.cols-3 .item, .articles-list-card.cols-4 .item, .articles-list-card.cols-5 .item, .articles-list-image.cols-2 .item, .articles-list-image.cols-3 .item, .articles-list-image.cols-4 .item, .articles-list-image.cols-5 .item {width: 50%}
}
@media screen and (-webkit-min-device-pixel-ratio: 0) {
    .articles-list-card .item-sticky .item-title .sticky-post, .articles-list-image .item-sticky .item-title .sticky-post {-webkit-text-fill-color: #fff}
}
.articles-list-image .item-img {position: relative}
.articles-list-card .item-img .item-category, .articles-list-image .item-img .item-category {position: absolute;left: 10px;top: 15px;padding: 4px 8px;font-size: 12px;line-height: 14px;color: #fff;background: rgba(0, 0, 0, .6);border-radius: 3px}
.articles-list-card .item-img .item-category:hover, .articles-list-image .item-img .item-category:hover {color: #fff;text-decoration: none;background: var(--theme-hover)}
.articles-list-card .item-thumb, .articles-list-image .item-thumb {display: block}
.articles-list-card .item-thumb img, .articles-list-image .item-thumb img {width: 100%;height: auto;display: block;-webkit-transition: all .2s ease-out 0s;-o-transition: all .2s ease-out 0s;transition: all .2s ease-out 0s}
.articles-list-image .item-thumb img {height: 100%;-o-object-fit: cover;object-fit: cover}
.articles-list-card .item-video, .articles-list-image .item-video {position: relative;display: block}
.articles-list-card .item-video:after, .articles-list-card .item-video:before, .articles-list-image .item-video:after, .articles-list-image .item-video:before {position: absolute;-webkit-box-sizing: border-box;box-sizing: border-box;border-radius: 50%;-webkit-transition: all .2s ease-out 0s;-o-transition: all .2s ease-out 0s;transition: all .2s ease-out 0s}
.articles-list-image .item-video:after, .articles-list-image .item-video:before {left: 50%;top: 50%;width: 44px;margin-left: -22px;margin-top: -22px}
.articles-list-image .item-video:before {height: 44px}
@media (max-width: 767px) {
    .articles-list-card .item-video:after, .articles-list-card .item-video:before, .articles-list-image .item-video:after, .articles-list-image .item-video:before {width: 36px;height: 36px;margin-left: -18px;margin-top: -18px}
}
.articles-list-card .item-video:before, .articles-list-image .item-video:before {z-index: 1;content: "";background: rgba(0, 0, 0, .6)}
.articles-list-image .item-video:before {opacity: .8}
.articles-list-card .item-video:after, .articles-list-image .item-video:after {z-index: 1;content: "";background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='300' height='300'%3E%3Cpath d='M341.333 290.987v442.026c0 33.707 37.12 54.187 65.707 35.84L754.347 547.84a42.667 42.667 0 000-72.107L407.04 255.147a42.581 42.581 0 00-65.707 35.84z' fill='%23fff'/%3E%3C/svg%3E");background-position: 50%;background-repeat: no-repeat;background-size: 32px}
.articles-list-image .item-video:after {height: 44px}
@media (max-width: 767px) {
    .articles-list-card .item-video:after, .articles-list-image .item-video:after {height: 36px;background-size: 26px}
}
.articles-list-card .item-title {margin: 0}
.articles-list-image .item-title {margin: 0;padding: 15px;font-size: 16px;font-weight: 500}
@media (max-width: 767px) {
    .articles-list-card .item-title, .articles-list-image .item-title {font-weight: 400;padding: 10px}
}
.articles-list-card .item-title a, .articles-list-image .item-title a {line-height: 1.4;height: 44.8px;color: #262626;overflow: hidden;-o-text-overflow: ellipsis;text-overflow: ellipsis;-webkit-box-orient: vertical;display: block;display: -webkit-box;-webkit-line-clamp: 2;text-decoration: none}
.articles-list-card .item-title a:hover, .articles-list-image .item-title a:hover {color: var(--theme-hover)}
.articles-list-card .item-title .sticky-post, .articles-list-image .item-title .sticky-post {display: inline-block;padding: 0 5px;margin-top: 1px;font-size: 12px;font-weight: 400;line-height: 20px;color: #fff;background: var(--theme-color);border-radius: 3px;vertical-align: top}
@media (max-width: 500px) {
    .articles-list-card .item-title .sticky-post, .articles-list-image .item-title .sticky-post {margin-top: 2px;line-height: 18px}
}
.articles-list-card .item-meta, .articles-list-image .item-meta {display: -webkit-box;display: -webkit-flex;display: -ms-flexbox;display: flex;-webkit-box-pack: justify;-webkit-justify-content: space-between;-ms-flex-pack: justify;justify-content: space-between;line-height: 14px;font-size: 12px}
.articles-list-image .item-meta {padding: 0 15px 15px;color: #828282;overflow: hidden}
@media (max-width: 767px) {
    .articles-list-card .item-meta, .articles-list-image .item-meta {font-size: 10px}
}
.articles-list-card .item-meta-left, .articles-list-image .item-meta-left {white-space: nowrap}
.articles-list-card .item-meta-right, .articles-list-image .item-meta-right {-webkit-flex-shrink: 0;-ms-flex-negative: 0;flex-shrink: 0}
.articles-list-card .item-meta-li {margin-left: 5px}
.articles-list-image .item-meta-li {margin-left: 5px;color: #828282}
@media (max-width: 767px) {
    .articles-list-card .item-meta-li:nth-child(2), .articles-list-card .item-meta-li:nth-child(3), .articles-list-card .item-meta-li:nth-child(4), .articles-list-image .item-meta-li:nth-child(2), .articles-list-image .item-meta-li:nth-child(3), .articles-list-image .item-meta-li:nth-child(4) {display: none}
}
@media (min-width: 992px) and (max-width: 1239px) {
    .articles-list-card .item-meta-li:nth-child(3), .articles-list-card .item-meta-li:nth-child(4), .articles-list-image .item-meta-li:nth-child(3), .articles-list-image .item-meta-li:nth-child(4) {display: none}
}
.articles-list-card .item-meta-li .wi, .articles-list-image .item-meta-li .wi {font-size: 14px;vertical-align: top}
.articles-list-card .item-meta-li.a, .articles-list-image .item-meta-li.a {text-decoration: none}
.articles-list-image .item-img {overflow: hidden}
.articles-list-image .item-img:before {padding-top: 62.5%;content: "";display: block}
.articles-list-image .item-thumb {position: absolute;left: 0;top: 0;width: 100%;height: 100%}
.articles-list-image .item-meta-items {padding: 0 15px;margin-bottom: 15px;font-size: 12px;line-height: 14px}
@media (max-width: 767px) {
    .articles-list-image .item-meta-items {padding: 0 10px;margin-bottom: 10px}
}
.articles-list-image .item-meta-items .item-meta-li {margin: 0 20px 0 0;display: inline-block !important}
@media (max-width: 991px) {
    .articles-list-image .item-meta-items .item-meta-li {margin: 0 10px 0 0}
}
.articles-list-image .item-meta-items .item-meta-li .wi {line-height: inherit}
.articles-list-image .item-meta-items .item-meta-li:last-child {margin-right: 0}
.articles-list-image .item-meta-author {display: -webkit-box;display: -webkit-flex;display: -ms-flexbox;display: flex;-webkit-box-pack: justify;-webkit-justify-content: space-between;-ms-flex-pack: justify;justify-content: space-between;padding: 15px;line-height: 20px;border-top: 1px solid rgba(239, 239, 239, .9);color: #5a5a5a;font-size: 12px}
@media (max-width: 767px) {
    .articles-list-image .item-meta-author {padding: 10px}
}
.articles-list-image .item-meta-author .avatar {line-height: inherit;color: inherit}
.articles-list-image .item-meta-author .avatar:hover {color: var(--theme-hover)}
.articles-list-image .item-meta-author .avatar img {width: 20px;height: 20px;margin-right: 3px;border-radius: 50%;display: inline-block;vertical-align: top}
.articles-list-card .item-inner {position: relative}
.articles-list-card .item-inner:before {display: block;padding-top: 62.5%;content: ""}
.articles-list-card.cols-5 .item-wrap {padding: 12px}
.articles-list-card.cols-5 .item-video:after, .articles-list-card.cols-5 .item-video:before {right: 12px;bottom: 37px}
.articles-list-card.cols-5 .item-video .item-title {padding-right: 64px}
.articles-list-card.cols-5 .item-title {font-size: 15px;padding: 0 12px;bottom: 35px}
.articles-list-card.cols-5 .item-meta {padding: 0 12px;bottom: 12px}
.articles-list-card .item-img {position: absolute;z-index: 1;left: 0;top: 0;width: 100%;height: 100%;background-position: 50%;background-size: cover;background-repeat: no-repeat}
.articles-list-card .item-img:before {position: absolute;left: 0;bottom: 0;width: 100%;height: 60%;background: -o-linear-gradient(top, transparent, rgba(0, 0, 0, .5));background: linear-gradient(180deg, transparent, rgba(0, 0, 0, .5));content: "";-webkit-transition: all .3s ease 0s;-o-transition: all .3s ease 0s;transition: all .3s ease 0s}
.articles-list-card .item-wrap {position: absolute;display: block;left: 0;bottom: 0;width: 100%;height: 100%;padding: 15px;color: #fff;-webkit-box-sizing: border-box;box-sizing: border-box}
@media (max-width: 767px) {
    .articles-list-card .item-wrap {padding: 8px}
}
.articles-list-card .item-wrap:hover {text-decoration: none}
.articles-list-card .item-video:after, .articles-list-card .item-video:before {left: auto;top: auto;right: 15px;bottom: 44px;width: 36px;height: 36px;margin: 0}
.articles-list-card .item-video:before {opacity: .8}
@media (max-width: 767px) {
    .articles-list-card .item-video:after, .articles-list-card .item-video:before {bottom: 30px;right: 8px;width: 30px;height: 30px}
}
.articles-list-card .item-video:after {line-height: 36px;font-size: 26px;opacity: .8}
@media (max-width: 767px) {
    .articles-list-card .item-video:after {font-size: 14px;line-height: 30px}
}
.articles-list-card .item-video .item-title {padding-right: 66px}
@media (max-width: 767px) {
    .articles-list-card .item-video .item-title {padding-right: 46px}
}
.articles-list-card .item-title {position: absolute;bottom: 40px;left: 0;max-height: 44px;overflow: hidden;padding: 0 15px;width: 100%;font-weight: 500;font-size: 16px;line-height: 1.4;-o-text-overflow: ellipsis;text-overflow: ellipsis;-webkit-box-orient: vertical;display: block;display: -webkit-box;-webkit-line-clamp: 2}
@media (max-width: 767px) {
    .articles-list-card .item-title {bottom: 26px;padding: 0 8px;font-size: 14px;max-height: 38px}
    .articles-list-card .item-title .sticky-post {margin-top: 1px;font-size: 9px;padding: 0 4px;line-height: 16px}
}
.articles-list-card .item-meta {position: absolute;left: 0;bottom: 15px;width: 100%;padding: 0 15px;margin-top: 10px;color: #fff;overflow: hidden;opacity: .95}
@media (max-width: 767px) {
    .articles-list-card .item-meta {bottom: 8px;margin-top: 8px;padding: 0 8px;line-height: 12px}
}
.articles-list-card .item-meta-li, .articles-list-card .item-meta-li .fa, .sidebar .widget_nav_menu ul li li li a:hover:after, .sidebar .widget_nav_menu ul li.current-menu-item .sub-menu a:hover:after, .sidebar .widget_nav_menu ul li.current-menu-item a:after, .sidebar .widget_nav_menu ul li.current-post-parent .sub-menu a:hover:after, .sidebar .widget_nav_menu ul li.current-post-parent a:after {color: #fff}
.articles-list-embed {margin-bottom: 0;padding: 20px 15px !important}
@media (max-width: 767px) {
    .articles-list-embed {padding: 0 !important}
    .articles-list-embed:hover {background: #fff}
}
.articles-list-embed .item {padding: 0 !important;margin: 0 !important;border-bottom: 0}
.articles-list-embed .item:hover, .sidebar .widget_nav_menu ul li.current-menu-item .sub-menu a, .sidebar .widget_nav_menu ul li.current-post-parent .sub-menu a {background: 0 0}
.articles-list-embed .item-title {margin-bottom: 10px}
.articles-list-embed .item-title a {max-height: 60px}
.articles-list-embed .item-meta {bottom: 0}
.articles-list-empty {padding: 80px 0;text-align: center;color: #828282}
.articles-list-empty img {display: block;margin: 20px auto;width: 240px;height: auto;max-width: 60%}
.articles-list-empty p {margin: 20px 0;padding: 0 30px;line-height: 1.67}
@media (max-width: 767px) {
    .hotnews-profile-main .profile-posts-list .item-meta {padding-left: 0;font-size: 12px}
}
.special-wrap {margin-bottom: 20px}
.special-list {margin: 0 -10px;font-size: 0}
.special-item-wrap {vertical-align: top;float: none;display: inline-block;padding: 0 10px !important;font-size: 14px}
.special-item {padding: 20px;margin-bottom: 20px;background: #fff;-webkit-transition: all .3s ease 0s;-o-transition: all .3s ease 0s;transition: all .3s ease 0s}
@media (max-width: 767px) {
    .special-item {padding: 5px}
}
.special-item:hover {-webkit-box-shadow: 0 2px 10px rgba(0, 0, 0, .15);box-shadow: 0 2px 10px rgba(0, 0, 0, .15)}
.special-item-top {position: relative;overflow: hidden}
.special-item-thumb {width: 220px;float: left;border-radius: 3px;overflow: hidden;max-height: 140px;}
@media (max-width: 520px) {
    .special-item-thumb {width: 140px}
}
.feature-post li:hover img, .special-item-thumb img, .special-item-thumb:hover img {-webkit-transition: all .3s ease-out 0s;-o-transition: all .3s ease-out 0s;transition: all .3s ease-out 0s}
.special-item-thumb img {width: 100%;height: auto}
.feature-post li:hover img, .special-item-thumb:hover img {-webkit-transform: scale(1.05);-ms-transform: scale(1.05);transform: scale(1.05)}
.special-item-title {padding-left: 240px}
@media (max-width: 520px) {
    .special-item-title {padding-left: 155px}
}
.special-item-title h2 {font-size: 20px;font-weight: 500;line-height: 1.4;margin: 0 0 15px}
@media (max-width: 520px) {
    .special-item-title h2 {font-size: 16px;margin-bottom: 8px}
}
.special-item-bottom a:hover, .special-item-title a, .special-item-title a:hover, .special-item:hover {-webkit-transition: all .3s ease 0s;-o-transition: all .3s ease 0s;transition: all .3s ease 0s}
.special-item-title a {color: #262626;text-decoration: none}
.special-item-bottom a:hover, .special-item-title a:hover {color: var(--theme-hover)}
.special-item-bottom a, .special-item-title p {overflow: hidden;-o-text-overflow: ellipsis;text-overflow: ellipsis}
.special-item-title p {height: 66px;color: #828282;line-height: 22px;-webkit-box-orient: vertical;display: block;display: -webkit-box;-webkit-line-clamp: 3}
@media (max-width: 520px) {
    .special-item-title p {height: 38px;line-height: 1.4}
}
.special-item-bottom {margin: 20px 0 0;padding: 0;list-style: none}
.special-item-bottom a {position: relative;display: inline-block;padding-left: 15px;line-height: 32px;color: #5a5a5a;text-decoration: none;max-width: 100%;white-space: nowrap;vertical-align: top}
.special-item-bottom a, .special-item-bottom a:before, .special-item-bottom a:hover:before {-webkit-transition: all .3s ease 0s;-o-transition: all .3s ease 0s;transition: all .3s ease 0s}
.special-item-bottom a:before {position: absolute;left: 4px;top: 12px;width: 0;height: 0;line-height: 0;border: 4px solid transparent;border-left: 5px solid #ccc;content: ""}
.special-item-bottom a:hover:before {border-left-color: var(--theme-hover)}
.special-item-more {position: absolute;bottom: 0;right: 0;font-size: 12px;line-height: 20px;color: #828282}
.special-item-more .wi {margin-left: 2px;font-size: 14px;vertical-align: top}
.special-item-more:hover {color: var(--theme-hover)}
.special-item-more:focus, .special-item-more:hover {text-decoration: none}
@media (max-width: 767px) {
    .special-item-more {display: none}
}
.widget {margin-bottom: 40px}
@media (max-width: 1239px) {
    .widget {margin-bottom: 30px}
}
.widget ol, .widget ul {list-style: none;margin: 0;padding: 0}
.widget ol li, .widget ul li {position: relative;padding: 5px 0 10px 12px;line-height: 1.46}
.widget ol li li, .widget ul li li {padding-left: 20px;border-bottom: 0}
.widget ol li:before, .widget ul li:before {position: absolute;left: 0;top: 11px;content: "";width: 0;height: 0;line-height: 0;border: 4px solid transparent;border-left: 5px solid #ccc}
.widget ol a, .widget ul a {color: #5a5a5a}
.widget ol a:hover, .widget ul a:hover {color: var(--theme-hover);text-decoration: none}
.widget .tagcloud {font-size: 0}
.widget .tagcloud a {display: inline-block;width: 32.39%;margin: 0 .7% 1.4%;padding: 5px 12px;font-size: 12px !important;line-height: 1.2;text-align: center;color: #5a5a5a;border: 1px solid rgba(100, 100, 100, .3);border-radius: 2px;text-decoration: none;background: rgba(255, 255, 255, .2);overflow: hidden;-o-text-overflow: ellipsis;text-overflow: ellipsis;white-space: nowrap}
.widget .tagcloud a:nth-child(3n+1) {margin-left: 0}
.widget .tagcloud a:nth-child(3n+3) {margin-right: 0}
.widget .tagcloud a:hover {color: #fff;background: var(--theme-hover);border-color: var(--theme-hover)}
.widget-title {margin: 2px 0 15px;padding: 0 0 0 12px;font-size: 16px;font-weight: 500;line-height: 1}
.widget-title:before {position: absolute;left: 0;top: 0;width: 3px;height: 100%;content: "";background-color: var(--theme-color);background-image: -o-linear-gradient(top, rgba(255, 255, 255, .15), transparent);background-image: linear-gradient(180deg, rgba(255, 255, 255, .15), transparent);border-radius: 3px}
.widget_calendar #wp-calendar {width: 100%;line-height: 2.2}
.widget_calendar #wp-calendar caption {padding: 3px;font-size: 16px;color: #262626;text-align: center}
.widget_calendar #wp-calendar th {vertical-align: bottom;border-bottom: 2px solid #ddd}
.widget_calendar #wp-calendar td {vertical-align: bottom;border-bottom: 1px solid #ddd;text-align: center}
.widget_tag_cloud a {margin: 0 2px}
.widget_lastest_products .p-list {display: -webkit-box;display: -webkit-flex;display: -ms-flexbox;display: flex;-webkit-flex-wrap: wrap;-ms-flex-wrap: wrap;flex-wrap: wrap;margin: 0 -7px !important;padding-top: 7px}
.widget_lastest_products .p-item {padding: 0 7px;margin: 0 0 15px;border: 0}
.sidebar .widget_nav_menu ul li:before, .widget_comments ul li:before, .widget_lastest_products .p-item:before {display: none}
.widget_lastest_products .p-item-wrap .thumb {position: relative;display: block;border-radius: 4px;background: #f8f9fa;overflow: hidden}
.search-form, .widget-title, .widget_lastest_products .p-item-wrap .thumb.thumb-video {position: relative}
.widget_lastest_products .p-item-wrap .thumb.thumb-video:after, .widget_lastest_products .p-item-wrap .thumb.thumb-video:before {position: absolute;z-index: 1;left: 50%;top: 50%;width: 32px;margin-left: -16px;margin-top: -16px;text-align: center;opacity: .9;border-radius: 50%;-webkit-transition: all .3s ease-out 0s;-o-transition: all .3s ease-out 0s;transition: all .3s ease-out 0s}
.widget_lastest_products .p-item-wrap .thumb.thumb-video:before {height: 32px;content: "";background: rgba(0, 0, 0, .6);opacity: .8}
.widget_lastest_products .p-item-wrap .thumb.thumb-video:after {height: 32px;content: "";background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='300' height='300'%3E%3Cpath d='M341.333 290.987v442.026c0 33.707 37.12 54.187 65.707 35.84L754.347 547.84a42.667 42.667 0 000-72.107L407.04 255.147a42.581 42.581 0 00-65.707 35.84z' fill='%23fff'/%3E%3C/svg%3E");background-position: 50%;background-repeat: no-repeat;background-size: 22px}
.widget_lastest_products .p-item-wrap .thumb img {display: block;width: 100%;height: auto;-webkit-transition: all .3s ease-out 0s;-o-transition: all .3s ease-out 0s;transition: all .3s ease-out 0s}
.widget_lastest_products .p-item-wrap .thumb:hover img {opacity: .9;-webkit-transform: scale(1.02);-ms-transform: scale(1.02);transform: scale(1.02);-webkit-transition: all .3s ease-out 0s;-o-transition: all .3s ease-out 0s;transition: all .3s ease-out 0s}
.widget_lastest_products .p-item-wrap .title {margin: 0;bottom: 0;opacity: 1;padding: 6px 0 0}
.widget_lastest_products .p-item-wrap .title a {height: 36px;font-size: 14px;font-weight: 400;overflow: hidden;-o-text-overflow: ellipsis;text-overflow: ellipsis;-webkit-box-orient: vertical;display: block;display: -webkit-box;-webkit-line-clamp: 2;line-height: 18px}
.widget_lastest_products:hover .title {bottom: 0}
.sidebar .widget_nav_menu {padding: 0;background: #fff;overflow: hidden}
.sidebar .widget_nav_menu .widget-title {padding-left: 30px;margin: 0;font-size: 18px;line-height: 40px;border-bottom: 1px solid #efefef}
.sidebar .widget_nav_menu .widget-title span {margin: 0;padding: 0;color: #262626;border: 0}
.sidebar .widget_nav_menu ul li {position: relative;padding: 0;border-bottom: 1px solid rgba(239, 239, 239, .8)}
.sidebar .widget_nav_menu ul li li {padding-left: 0;border-top: 1px solid rgba(239, 239, 239, .8);border-bottom: 0}
.sidebar .widget_nav_menu ul li li a {padding-left: 45px}
.sidebar .widget_nav_menu ul li li li a {padding-left: 60px}
.sidebar .widget_nav_menu ul li li li a:after {left: 50px}
.sidebar .widget_nav_menu ul li.current-menu-item a, .sidebar .widget_nav_menu ul li.current-post-parent a {color: #fff;background: var(--theme-color)}
.sidebar .widget_nav_menu ul li.current-menu-item .sub-menu a:after, .sidebar .widget_nav_menu ul li.current-post-parent .sub-menu a:after {color: #666}
.sidebar .widget_nav_menu ul li.current-menu-item li a, .sidebar .widget_nav_menu ul li.current-post-parent li a {color: #262626}
.sidebar .widget_nav_menu ul li a {display: block;padding: 15px 15px 15px 30px;line-height: 16px;font-size: 14px;color: #262626}
.sidebar .widget_nav_menu ul li a:hover {color: #fff;background: var(--theme-hover)}
.sidebar .widget_nav_menu ul li .material-icons {margin-right: 2px;vertical-align: top;font-size: 16px;line-height: inherit}
.search-form {overflow: hidden;background: #fff}
.search-form input {background: 0 0;border: 0;line-height: 22px;display: block;float: left;outline: 0}
.search-form input.keyword {width: 100%;height: 34px;padding: 5px 52px 5px 10px;font-size: 14px;color: #262626;border: 1px solid #efefef}
.search-form input.keyword:focus {border-color: var(--theme-color)}
.search-form .submit {position: absolute;right: 0;top: 0;z-index: 9;width: 42px;height: 34px;padding: 0;font-size: 18px;line-height: 34px;text-align: center;color: #fff;background-color: var(--theme-color);cursor: pointer;border: 0;outline: 0}
.search-form .submit:hover {background-color: var(--theme-hover)}
.widget_kuaixun .widget-title-more {float: right;font-size: 13px;font-weight: 400;color: #828282;line-height: 19px}
.widget_kuaixun .widget-title-more:hover {color: var(--theme-hover);text-decoration: none}
.widget-kx-list {margin-left: 1px !important;line-height: 1.6;border-left: 1px dashed #f1eded}
.widget-kx-list .kx-item {margin-bottom: 15px;padding: 5px 0 5px 18px;line-height: 1.6}
.widget-kx-list .kx-item:after, .widget-kx-list .kx-item:before {position: absolute;margin-left: -6px;left: 0;top: 10px;width: 4px;height: 4px;border-radius: 6px;border: 4px solid var(--theme-color);background: #fff;-webkit-box-sizing: content-box;box-sizing: content-box;opacity: .3;content: ""}
.widget-kx-list .kx-item:before {border: 4px solid #fff;opacity: 1}
.widget-kx-list .kx-item:hover .kx-share {display: block}
.widget-kx-list .kx-item.active:before, .widget-kx-list .kx-item:hover:before {border-color: var(--theme-hover);opacity: .6}
.widget-kx-list .kx-title {color: #262626;font-size: 14px;display: block}
.widget-kx-list .kx-content {display: none;margin: 10px 0;font-size: 14px;color: #5a5a5a}
.widget-kx-list .kx-content img {display: block;max-width: 100%;height: auto;margin-top: 5px}
.widget-kx-list .kx-meta {margin-top: 10px}
.widget-kx-list .kx-meta span {margin-right: 4px;font-size: 12px;vertical-align: top}
.widget-kx-list .kx-share {display: none;float: right;font-size: 0}
.widget-kx-list .kx-share span {vertical-align: top}
.widget-kx-list .kx-share .share-icon {font-size: 16px;margin-right: 9px}
.widget-kx-list .kx-share .share-icon:nth-child(10), .widget-kx-list .kx-share .share-icon:nth-child(6), .widget-kx-list .kx-share .share-icon:nth-child(7), .widget-kx-list .kx-share .share-icon:nth-child(8), .widget-kx-list .kx-share .share-icon:nth-child(9) {display: none}
.widget-kx-list .kx-share .share-icon:hover {color: #fff}
@media (max-width: 1239px) {
    .widget-kx-list .kx-share .share-icon {display: none}
    .widget-kx-list .kx-share .share-icon:nth-child(2), .widget-kx-list .kx-share .share-icon:nth-child(3) {display: inline-block}
}
.widget-kx-list .kx-share .share-icon:last-child {margin-right: 0}
.widget_profile {padding: 0;border: 1px solid #efefef;overflow: hidden}
.widget_profile .profile-cover {position: relative;width: 100%;height: 0 !important;padding-bottom: 37%;overflow: hidden}
.widget_profile .profile-cover img {position: absolute;left: 0;top: 0;width: 100%;height: 100% !important;-o-object-fit: cover;object-fit: cover}
.widget_profile .cover_photo {height: 110px;background: #eee}
.widget_profile img {width: 100%;height: auto;vertical-align: top}
.widget_profile p {margin: 0}
.widget_profile .avatar-wrap {margin-top: -43px;text-align: center}
.widget_profile .avatar-link {position: relative;display: block;width: 82px;height: 82px;padding: 2px;margin: 0 auto;border-radius: 50%;background: #fff;-webkit-box-sizing: content-box;box-sizing: content-box}
.widget_comments .comment-info img:hover, .widget_profile .avatar {-webkit-transition: all .3s ease-out 0s;-o-transition: all .3s ease-out 0s;transition: all .3s ease-out 0s}
.widget_profile .avatar {width: 100%;height: auto;border-radius: 50%;background: #fff}
.widget_profile .profile-info {padding: 10px 15px 20px;text-align: center;line-height: 1.4}
.widget_profile .profile-stats {margin-left: -12px;margin-right: -12px;margin-bottom: 20px;font-size: 12px}
.widget_profile .profile-stats-inner {display: -webkit-box;display: -webkit-flex;display: -ms-flexbox;display: flex;-webkit-box-pack: cneter;-webkit-justify-content: cneter;-ms-flex-pack: cneter;justify-content: cneter}
.widget_profile .profile-stats-inner .user-stats-item {width: 100%;padding: 6px 0}
.widget_profile .profile-stats-inner .user-stats-item b {font-weight: 400}
.widget_profile .profile-name {color: #262626}
.kx-list .kx-content h2 a:hover, .widget_profile .profile-name:hover {color: var(--theme-hover);text-decoration: none}
.widget_profile .author-name {display: inline-block;font-size: 16px;line-height: 22px;font-weight: 500;overflow: hidden;max-width: 180px;-o-text-overflow: ellipsis;text-overflow: ellipsis;white-space: nowrap;vertical-align: top}
.widget_profile .user-group {position: relative;z-index: 0;display: inline-block;padding: 4px 8px;margin-left: 8px;color: var(--theme-color);font-size: 12px;font-weight: 400;line-height: 1;border-radius: 3px;border: 1px solid var(--theme-color);overflow: hidden;vertical-align: top}
.widget_profile .user-group:before {position: absolute;z-index: -1;left: 0;top: 0;width: 100%;height: 100%;background-color: #fff;content: ""}
.widget_profile .user-group:after {position: absolute;z-index: 0;left: -1px;top: -1px;right: -1px;bottom: -1px;content: "";background: var(--theme-color);opacity: .148}
.widget_profile .author-description {margin-top: 10px;margin-bottom: 20px;font-size: 14px;color: #5a5a5a}
.widget_profile .profile-posts {padding: 20px 15px 15px;border-top: 1px dashed #efefef}
.widget_profile .profile-posts .widget-title {font-size: 14px}
.widget_comments ul li {padding: 15px 0;border-bottom: 1px solid rgba(239, 239, 239, .8)}
.widget_comments .comment-info {margin-bottom: 10px}
.widget_comments .comment-info img {width: 30px;height: 30px;border-radius: 50%;border: 1px solid #f3f3f3;-webkit-transition: all .2s ease-out 0s;-o-transition: all .2s ease-out 0s;transition: all .2s ease-out 0s;vertical-align: top}
.widget_comments .comment-info img:hover {opacity: .8;filter: alpha(opacify=80)}
.widget_comments .comment-info a {display: inline-block;vertical-align: top;line-height: 30px;color: #828282}
.widget_comments .comment-info .comment-author {display: inline-block;float: none;width: auto;max-width: 120px;white-space: nowrap;word-wrap: normal;vertical-align: top}
.widget_comments .comment-info span {float: right;line-height: 30px;color: #828282}
.widget_comments .comment-excerpt {position: relative;padding: 10px;margin-bottom: 10px;background: rgba(0, 0, 0, .03);border-radius: 1px;color: #666}
.widget_comments .comment-excerpt:before {position: absolute;left: 10px;top: -10px;content: "";width: 0;height: 0;line-height: 0;border: 5px solid transparent;border-bottom-color: rgba(0, 0, 0, .03)}
.widget_comments .comment-excerpt p, .widget_comments .comment-info .comment-author, .widget_comments .comment-post {margin: 0;overflow: hidden;-o-text-overflow: ellipsis;text-overflow: ellipsis}
.widget_comments .comment-excerpt p {line-height: 1.6;-webkit-box-orient: vertical;display: block;display: -webkit-box;-webkit-line-clamp: 3}
.widget_comments .comment-post {color: #828282;font-size: 13px;line-height: 1.2;white-space: nowrap;word-wrap: normal}
.widget_comments .comment-post a, .widget_post_tabs .post-tabs-hd .post-tabs-item.active .hotnews-icon {color: inherit}
.widget_image_ad, .widget_media_image {padding: 0;background: 0 0}
.widget_image_ad img, .widget_media_image img {width: 100%;height: auto;vertical-align: top;border-radius: 4px}
.widget_image_ad a, .widget_media_image a {display: block;-webkit-transition: all .3s ease;-o-transition: all .3s ease;transition: all .3s ease}
.widget_image_ad a:hover, .widget_media_image a:hover {opacity: .9;-webkit-transition: all .3s ease;-o-transition: all .3s ease;transition: all .3s ease}
.widget_html_ad {padding: 0;overflow: hidden}
.widget_html_ad img {vertical-align: top;max-width: 100%;height: auto}
.widget_post_tabs .item, .widget_post_thumb .item {margin: 0;padding: 15px 0;display: -webkit-box;display: -webkit-flex;display: -ms-flexbox;display: flex;width: 100%;overflow: hidden;border-bottom: 1px solid rgba(239, 239, 239, .8)}
.widget_post_tabs .item:hover img, .widget_post_thumb .item:hover img, .widget_special .speial-item:hover img {-webkit-transform: scale(1.03);-ms-transform: scale(1.03);transform: scale(1.03);-webkit-transition: all .3s ease-out 0s;-o-transition: all .3s ease-out 0s;transition: all .3s ease-out 0s}
.widget_post_tabs .item:last-child, .widget_post_thumb .item:last-child {border-bottom: 0;padding-bottom: 0}
.widget_post_tabs li:before, .widget_post_thumb li:before {display: none}
.widget_post_tabs .item-img, .widget_post_thumb .item-img {width: 100px;-webkit-flex-shrink: 0;-ms-flex-negative: 0;flex-shrink: 0;overflow: hidden;border-radius: 6px}
.widget_post_tabs .item-img img, .widget_post_thumb .item-img img {display: block;width: 100%;height: auto;-webkit-transition: all .3s ease-out 0s;-o-transition: all .3s ease-out 0s;transition: all .3s ease-out 0s}
.widget_post_tabs .item-img.item-video .item-img-inner, .widget_post_thumb .item-img.item-video .item-img-inner {position: relative;display: block}
.widget_post_tabs .item-img.item-video .item-img-inner:before, .widget_post_thumb .item-img.item-video .item-img-inner:before {position: absolute;z-index: 1;left: 50%;top: 50%;width: 32px;height: 32px;margin-left: -16px;margin-top: -16px;border-radius: 50%;content: "";-webkit-box-sizing: border-box;box-sizing: border-box;background: rgba(0, 0, 0, .6);opacity: .8;-webkit-transition: all .3s ease-out 0s;-o-transition: all .3s ease-out 0s;transition: all .3s ease-out 0s}
.widget_post_tabs .item-img.item-video .item-img-inner:after, .widget_post_thumb .item-img.item-video .item-img-inner:after {position: absolute;z-index: 1;left: 50%;top: 50%;width: 32px;height: 32px;margin-left: -16px;margin-top: -16px;content: "";opacity: .9;background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='300' height='300'%3E%3Cpath d='M341.333 290.987v442.026c0 33.707 37.12 54.187 65.707 35.84L754.347 547.84a42.667 42.667 0 000-72.107L407.04 255.147a42.581 42.581 0 00-65.707 35.84z' fill='%23fff'/%3E%3C/svg%3E");background-position: 50%;background-repeat: no-repeat;background-size: 22px;-webkit-transition: all .3s ease-out 0s;-o-transition: all .3s ease-out 0s;transition: all .3s ease-out 0s}
.widget_post_tabs .item-img-inner, .widget_post_thumb .item-img-inner {display: block}
.widget_post_tabs .item-title, .widget_post_thumb .item-title {height: 38px;margin-bottom: 10px;overflow: hidden;-o-text-overflow: ellipsis;text-overflow: ellipsis;-webkit-box-orient: vertical;display: block;display: -webkit-box;-webkit-line-clamp: 2;line-height: 1.4}
.widget_post_tabs .item-title a, .widget_post_thumb .item-title a {color: #262626}
.widget_post_tabs .item-date, .widget_post_thumb .item-date {position: absolute;bottom: 0;margin: 0;font-size: 12px;color: #828282;line-height: 14px;right: 0;}
.widget_post_tabs .item-content, .widget_post_thumb .item-content {position: relative;margin-left: 10px;width: calc(100% - 110px);-webkit-flex-shrink: 1;-ms-flex-negative: 1;flex-shrink: 1;flex-grow: 1;}
.widget_post_tabs .item-no-thumb, .widget_post_thumb .item-no-thumb {margin-left: 0;width: 100%}
.widget_post_tabs .item-no-thumb .item-title, .widget_post_thumb .item-no-thumb .item-title {display: block;height: auto;-webkit-line-clamp: unset}
.widget_post_tabs .item-no-thumb .item-date, .widget_post_thumb .item-no-thumb .item-date {position: relative}
.widget_icon_url {padding: 0 !important}
.widget_icon_url .icon-list {display: -webkit-box;display: -webkit-flex;display: -ms-flexbox;display: flex;-webkit-flex-wrap: wrap;-ms-flex-wrap: wrap;flex-wrap: wrap}
.widget_icon_url .icon-list-item {position: relative;width: 33.33%;padding: 20px 10px;text-align: center;border: 0}
.widget_icon_url .icon-list-item:before {position: absolute;top: 0;left: 0;width: 100%;height: 100%;background-color: var(--theme-color);content: "";opacity: 0}
.widget_icon_url .icon-list-item:hover:before {opacity: .2}
.widget_icon_url .hotnews-icon {font-size: 32px;line-height: 32px;height: 32px;color: var(--theme-color);vertical-align: top}
.widget_icon_url .hotnews-icon img {width: 32px;height: auto;vertical-align: top}
.widget_icon_url .list-item-title {display: block;font-size: 12px;margin-top: 5px;line-height: 20px;color: #262626;white-space: nowrap;-o-text-overflow: ellipsis;text-overflow: ellipsis;overflow: hidden}
.widget_user_list .user-list-item {padding: 16px 0;display: -webkit-box;display: -webkit-flex;display: -ms-flexbox;display: flex;border-bottom: 1px solid #efefef}
.widget_special .speial-item:before, .widget_user_list .user-list-item:before {content: none}
.widget_user_list .user-list-item:first-child {padding-top: 0}
.widget_user_list .user-list-item:last-child {border-bottom: 0;padding-bottom: 0}
.widget_user_list .user-list-item .avatar {display: block;margin-right: 8px;width: 48px;height: 48px;border-radius: 50%;-o-object-fit: cover;object-fit: cover}
.widget_user_list .user-list-content {-webkit-box-flex: 1;-webkit-flex: 1;-ms-flex: 1;flex: 1}
.widget_user_list .user-list-hd {display: -webkit-box;display: -webkit-flex;display: -ms-flexbox;display: flex;-webkit-box-pack: justify;-webkit-justify-content: space-between;-ms-flex-pack: justify;justify-content: space-between}
.widget_user_list .user-list-btn {margin: 0;padding: 0;font-size: 13px;line-height: 24px;color: var(--theme-color);background-color: transparent;border: 0;outline: 0;cursor: pointer}
.widget_user_list .user-list-btn:focus {background-color: transparent;border: 0}
.widget_user_list .user-list-btn:hover {border: 0;background-color: transparent}
.widget_user_list .user-list-btn.followed {color: #5a5a5a}
.widget_user_list .user-list-btn .hotnews-icon {font-size: 11px}
.widget_user_list .user-list-name {max-width: 150px;-webkit-box-flex: 1;-webkit-flex: 1;-ms-flex: 1;flex: 1;margin: 0;font-size: 16px;font-weight: 500;color: #262626;line-height: 24px;overflow: hidden;-o-text-overflow: ellipsis;text-overflow: ellipsis;white-space: nowrap}
@media (max-width: 1239px) {
    .widget_user_list .user-list-name {max-width: 110px}
}
.widget_user_list .user-list-desc {margin: 4px 0 0;font-size: 14px;max-height: 40px;line-height: 20px;color: #828282;overflow: hidden;-o-text-overflow: ellipsis;text-overflow: ellipsis;-webkit-box-orient: vertical;display: -webkit-box;-webkit-line-clamp: 2}
.widget_special .speial-item {padding: 15px 0;display: -webkit-box;display: -webkit-flex;display: -ms-flexbox;display: flex;border-bottom: 1px solid #f2f2f2}
.widget_special .speial-item:first-child {padding-top: 5px}
.widget_special .speial-item:last-child {border-bottom: 0;padding-bottom: 5px}
.widget_special .speial-item-img {margin-right: 10px;width: 64px;height: 64px;border-radius: 4px;overflow: hidden;-webkit-flex-shrink: 0;-ms-flex-negative: 0;flex-shrink: 0}
.widget_special .speial-item-img img {width: 100%;height: 100%;border-radius: 4px;-o-object-fit: cover;object-fit: cover;-webkit-transition: all .3s ease-out 0s;-o-transition: all .3s ease-out 0s;transition: all .3s ease-out 0s}
.widget_special .speial-item-text {width: calc(100% - 74px)}
.widget_special .speial-item-title {display: block;font-size: 16px;font-weight: 500;line-height: 20px;margin: 0;color: #262626;overflow: hidden;-o-text-overflow: ellipsis;text-overflow: ellipsis;white-space: nowrap}
.widget_special .speial-item-title:hover {color: var(--theme-hover)}
.widget_post_tabs .widget-title span, .widget_special .speial-item-desc {overflow: hidden;-o-text-overflow: ellipsis;text-overflow: ellipsis;-webkit-box-orient: vertical;display: -webkit-box}
.widget_special .speial-item-desc {margin-bottom: 0;margin-top: 8px;font-size: 14px;line-height: 18px;max-height: 40px;color: #5a5a5a;-webkit-line-clamp: 2}
.widget_special .speial-item-last {color: #828282}
.widget_post_tabs {padding-top: 0}
.widget_post_tabs .widget-title {margin-bottom: 2px;white-space: nowrap}
.widget_post_tabs .widget-title span {width: 130px;display: inline-block;line-height: 1.1;-webkit-line-clamp: 1}
@media (max-width: 1239px) {
    .widget_post_tabs .widget-title span {width: 82px}
}
.widget_post_tabs .post-tabs-hd-inner {position: relative;width: 100%}
.widget_post_tabs .post-tabs-hd-inner:before {position: absolute;left: 0;right: 0;bottom: -2px;height: 2px;background-color: #efefef;content: ""}
.widget_post_tabs .post-tabs-hd, .widget_post_tabs .post-tabs-hd-inner {display: -webkit-box;display: -webkit-flex;display: -ms-flexbox;display: flex;-webkit-justify-content: space-around;-ms-flex-pack: distribute;justify-content: space-around}
.widget_post_tabs .post-tabs-hd .post-tabs-item {padding: 0 2px;line-height: 52px;font-size: 16px;color: #5a5a5a;cursor: pointer;-webkit-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none}
.widget_post_tabs .post-tabs-hd .post-tabs-item.active {position: relative;color: var(--theme-color)}
.widget_post_tabs .post-tabs-hd .post-tabs-item.active:after {position: absolute;bottom: -2px;left: 0;width: 100%;height: 2px;background-color: var(--theme-color);content: ""}
.body-box .sec-panel-head h1 span a:hover, .body-box .sec-panel-head h2 span a:hover, .body-box .sec-panel-head h3 span a:hover, .widget_post_tabs .post-tabs-hd .post-tabs-item:hover {color: var(--theme-hover)}
.tagHandler ul.tagHandlerContainer li.tagItem:hover, .widget_post_tabs .post-tabs-hd .post-tabs-item:hover:after, .hotnews-slider .swiper-pagination span:hover {background-color: var(--theme-hover)}

.textwidget img {max-width: 100%}
.body-box .widget {margin-bottom: 20px;padding: 15px;background: #fff}
.body-box .sidebar .widget_nav_menu, .body-box .widget_html_ad, .body-box .widget_image_ad, .body-box .widget_media_image, .body-box .widget_media_video, .body-box .widget_profile, .body-box .widget_search {padding: 0}
.body-box .widget_profile {background: #fff;border: 0}
.body-box .widget_image_ad, .body-box .widget_media_image {background: 0 0}
.body-box .sec-panel-head h1 span:before, .body-box .sec-panel-head h2 span:before, .body-box .sec-panel-head h3 span:before, .body-box .widget_search .widget-title {display: none}
.body-box .widget_post_tabs .post-tabs-hd {margin: -15px 0 0}
.body-box .widget_post_tabs .tab-has-title {margin: 0}
.body-box .widget_post_tabs .post-tabs-hd-inner:before {left: -15px;right: -15px}
.footer {padding: 50px 0;color: rgba(255, 255, 255, .7);background: #2d3236}
@media (max-width: 991px) {
    .footer {padding: 25px 10px;text-align: center}
}
@media (max-width: 767px) {
    .footer {padding: 20px 0}
}
.body-box .sec-panel-head h1 span a, .body-box .sec-panel-head h2 span a, .body-box .sec-panel-head h3 span a, .footer a {color: inherit}
.footer a:hover {color: rgba(255, 255, 255, .9);text-decoration: none}
.footer .copyright {font-size: 14px}
@media (max-width: 767px) {
    .footer .copyright {font-size: 12px}
}
.footer .copyright p {margin: 0 0 5px}
.footer .copyright p:last-child {margin-bottom: 0}
.footer .footer-nav, .footer .menu ul, .footer ul.menu {padding: 0;margin: 0 0 12px;font-size: 14px;list-style: none}
.footer .footer-nav li, .footer .menu ul li, .footer ul.menu li {display: inline-block;margin-right: 10px;line-height: 18px}
.footer .footer-nav li:after, .footer .menu ul li:after, .footer ul.menu li:after {display: inline-block;font-size: 12px;line-height: 18px;opacity: .8;content: "|";vertical-align: top}
.footer .footer-nav li:last-child:after, .footer .menu ul li:last-child:after, .footer ul.menu li:last-child:after {display: none}
.footer .footer-nav a, .footer .menu ul a, .footer ul.menu a {display: inline-block;padding-right: 10px;vertical-align: top}
.footer-col {display: inline-block;font-size: 0;vertical-align: middle;margin-right: 32px}
@media (max-width: 767px) {
    .footer-col {margin-right: 0}
}
@media (max-width: 1239px) {
    .footer-col-logo {display: none}
}
.action {
    position: fixed;
    z-index: 999;
    bottom: 20%;
    right: 20px;
    width: 40px;
    text-align: center;
    background: #e1e1e1;
    border-radius: 3px;
}
.go-top{
    cursor: pointer;
}
.hide-gotop{
    display: none;
}
:root {--theme-color: #1d1d1d;--theme-hover: #1162e8;--action-color: #206be7;--theme-font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Ubuntu, Helvetica Neue, Helvetica, Arial, PingFang SC, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei, Source Han Sans CN, sans-serif}

html {font-size: 12px}
body {padding-bottom: env(safe-area-inset-bottom);background: #f5f5f5;word-wrap: break-word}
@media (max-width: 991px) {
    body {-webkit-transition: all .3s ease-out 0s;-o-transition: all .3s ease-out 0s;transition: all .3s ease-out 0s}
}
@media (max-width: 767px) {
    body {background: #fff !important}
    .container, .page-no-sidebar.member-lostpassword .member-lp-process {padding-left: 15px;padding-right: 15px}
}
#wrap {padding-top: 64px}
@media (max-width: 767px) {
    #wrap {padding-top: 50px;overflow: hidden}
}
.wrap {position: relative;margin-top: 40px;margin-bottom: 20px}
@media (max-width: 1239px) {
    .wrap {margin-top: 30px}
}
.home .wrap {margin-bottom: 0}
@media (max-width: 767px) {
    .home .wrap {margin-top: 15px;margin-bottom: 15px}
}
.main, .main-archive {-webkit-transition: all .3s ease-out 0s;-o-transition: all .3s ease-out 0s;transition: all .3s ease-out 0s}
.main, .main-archive {width: 840px;float: left}
@media (max-width: 1239px) {
    .main, .main-archive {width: 670px}
}
@media (max-width: 991px) {
    .main, .main-archive {width: 100%;float: none !important}
}
.main-full {width: 100% !important;float: none}
.main-woo {padding: 15px;margin-bottom: 20px;background: #fff}
@media (max-width: 767px) {
    .main-woo {background: 0 0;padding: 5px}
}
.single-product .content-woo {padding: 20px}
.sec-panel {margin-bottom: 40px}
@media (max-width: 1239px) {
    .sec-panel {margin-bottom: 30px}
}
.sec-panel-head {position: relative;margin-bottom: 20px;overflow: hidden}
.sec-panel-head h1, .sec-panel-head h2, .sec-panel-head h3 {margin: 0;padding: 0;font-size: 18px;line-height: 18px;font-weight: 500;color: #262626}
.sec-panel-head h1 a, .sec-panel-head h2 a, .sec-panel-head h3 a {text-decoration: none}
.sec-panel-head h1 span, .sec-panel-head h2 span, .sec-panel-head h3 span {position: relative;display: inline-block;padding-left: 12px}
.sec-panel-head h1 span:before, .sec-panel-head h2 span:before, .sec-panel-head h3 span:before {position: absolute;left: 0;top: 0;height: 18px;width: 3px;border-radius: 4px;background-color: var(--theme-color);background-image: -o-linear-gradient(top, rgba(255, 255, 255, .15), transparent);background-image: linear-gradient(180deg, rgba(255, 255, 255, .15), transparent);content: ""}
.sec-panel-head small {display: inline-block;font-size: 14px;margin-left: 10px;color: #5a5a5a;font-weight: 400;line-height: inherit;vertical-align: top}
@media (max-width: 991px) {
    .sec-panel-head small {display: none}
}
.sec-panel-head .more {float: right;font-size: 14px;font-weight: 400;color: #828282}
.sec-panel-head .more, .sec-panel-head .more:hover, .topic-list span {-webkit-transition: all .3s ease-out;-o-transition: all .3s ease-out;transition: all .3s ease-out}
.sec-panel-head .more:hover {color: var(--theme-hover);text-decoration: none}
.body-box .wrap {margin-top: 20px}
.body-box .main, .body-box .main-archive {width: 860px;}
@media (max-width: 1239px) {
    .body-box .main, .body-box .main-archive {width: 684px}
}
@media (max-width: 991px) {
    .body-box .main, .body-box .main-archive {width: auto}
}
.body-box .slider-wrap {margin-bottom: 20px}
.body-box .main-slider {width: 620px}
@media (max-width: 1239px) {
    .body-box .main-slider {width: 100%}
}
.body-box .sec-panel {margin-bottom: 20px;background: #fff}
.body-box .sec-panel-head {padding: 20px 20px 0;margin-bottom: 0}
@media (max-width: 767px) {
    .body-box .sec-panel-head {padding: 10px 0 0}
}
.body-box .sec-panel-head h1, .body-box .sec-panel-head h2, .body-box .sec-panel-head h3 {border-bottom: 0;line-height: 26px}
.body-box .sec-panel-head h1 span, .body-box .sec-panel-head h2 span, .body-box .sec-panel-head h3 span {padding-bottom: 0;padding-left: 0;color: #262626}
.body-box .sec-panel-body {padding: 20px}
@media (max-width: 767px) {
    .body-box .sec-panel-body {padding: 10px 0}
}
.body-box .sec-panel-card, .body-box .sec-panel-image {background: 0 0}
.body-box .sec-panel-card .sec-panel-head, .body-box .sec-panel-image .sec-panel-head {margin-bottom: 10px;padding-bottom: 20px;background: #fff}
.body-box .sec-panel-card .sec-panel-head h1, .body-box .sec-panel-card .sec-panel-head h2, .body-box .sec-panel-card .sec-panel-head h3, .body-box .sec-panel-image .sec-panel-head h1, .body-box .sec-panel-image .sec-panel-head h2, .body-box .sec-panel-image .sec-panel-head h3 {border-bottom: 0}
.body-box .sec-panel-default .sec-panel-head, .body-box .sec-panel-list .sec-panel-head {padding-bottom: 15px}
.body-box .sec-panel-default .sec-panel-head h1, .body-box .sec-panel-default .sec-panel-head h2, .body-box .sec-panel-default .sec-panel-head h3 {border-bottom: 0;font-size: 22px;line-height: 1}
.body-box .list.tabs {border-bottom: 1px solid rgba(99, 99, 99, .1)}
.body-box .list.tabs .tab a {line-height: 58px;padding-bottom: 0}
.body-box .main-list .sec-panel-head {padding-top: 0;border-bottom: 0}
.body-box .main-list .list.tabs {height: 60px;margin-left: -20px;margin-right: -20px;padding-left: 20px;padding-right: 20px}
@media (max-width: 767px) {
    .body-box .main-list .list.tabs {margin-left: 0;margin-right: 0;padding-left: 0;padding-right: 0}
}
.body-box .main-list-card .sec-panel-head, .body-box .main-list-image .sec-panel-head {padding: 0 20px}
@media (max-width: 767px) {
    .body-box .main-list-card .sec-panel-head, .body-box .main-list-image .sec-panel-head {padding: 0 10px}
}
.body-box .main-list-card .articles-list, .body-box .main-list-image .articles-list {padding: 20px}
@media (max-width: 767px) {
    .body-box .main-list-card .articles-list, .body-box .main-list-image .articles-list {padding: 10px}
}
.list {margin: 0;padding: 0;list-style-type: none}

.sidebar {width: 300px;float: right}
.sidebar.fixed {position: fixed;left: 50%;bottom: 0;margin-left: 290px}
@media (min-width: 992px) and (max-width: 1239px) {
    .sidebar.fixed {margin-left: 230px}
}
.sidebar.fixed.sidebar-on-left {right: 50%;left: auto;margin-right: 290px}
@media (min-width: 992px) and (max-width: 1239px) {
    .sidebar.fixed.sidebar-on-left {margin-right: 230px}
}
.sidebar.abs {position: absolute;bottom: 0;right: 10px}
.sidebar.abs.sidebar-on-left {left: 10px;right: auto}
@media (max-width: 1239px) {
    .sidebar {width: 250px}
}
@media screen and (max-width: 991px) {
    .sidebar {display: none}
}
.slider-wrap {margin-bottom: 40px}
@media (max-width: 1239px) {
    .slider-wrap {margin-bottom: 30px}
}
@media (max-width: 767px) {
    .slider-wrap {margin: -20px -15px 30px}
}
.hotnews-slider .swiper-wrapper {padding: 0 !important;margin: 0 !important;-webkit-flex-wrap: nowrap;-ms-flex-wrap: nowrap;flex-wrap: nowrap}
.hotnews-slider .swiper-button-next, .hotnews-slider .swiper-button-prev {width: 30px;height: 40px;margin-top: -20px;opacity: 0;background: 0 0;-webkit-transition: all .3s;-o-transition: all .3s;transition: all .3s;-webkit-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none}
@media (max-width: 991px) {
    .hotnews-slider .swiper-button-next, .hotnews-slider .swiper-button-prev {display: none}
}
.hotnews-slider .swiper-button-prev {left: -20px}
.hotnews-slider .swiper-button-next {left: auto;right: -20px}
.hotnews-slider:hover .swiper-button-next, .hotnews-slider:hover .swiper-button-prev {opacity: .7}
.hotnews-slider:hover .swiper-button-next:hover, .hotnews-slider:hover .swiper-button-prev:hover {opacity: 1}
.hotnews-slider:hover .swiper-button-prev {left: 0}
.hotnews-slider:hover .swiper-button-next {right: 0}
.hotnews-slider .swiper-pagination {z-index: 2;height: 6px;font-size: 0}
.hotnews-slider .swiper-pagination span {height: 6px;width: 8px;margin: 0 3px !important;border-radius: 6px !important;background-color: rgba(255, 255, 255, .8);vertical-align: top}

.modules-feature-posts .articles-list-card .hotnews-slider .item, .hotnews-slider .modules-feature-posts .articles-list-card .item, .hotnews-slider .swiper-slide {position: relative}
.modules-feature-posts .articles-list-card .hotnews-slider .item > a, .hotnews-slider .modules-feature-posts .articles-list-card .item > a, .hotnews-slider .swiper-slide > a {display: block;height: 100%}
.modules-feature-posts .articles-list-card .hotnews-slider .item img, .hotnews-slider .modules-feature-posts .articles-list-card .item img, .hotnews-slider .swiper-slide img {width: 100%;height: 100%;-o-object-fit: cover;object-fit: cover;-webkit-transition: all .3s ease-out 0s;-o-transition: all .3s ease-out 0s;transition: all .3s ease-out 0s}
.modules-feature-posts .articles-list-card .hotnews-slider .item:hover img, .hotnews-slider .modules-feature-posts .articles-list-card .item:hover img, .hotnews-slider .swiper-slide:hover img {-webkit-transform: scale(1.02);-ms-transform: scale(1.02);transform: scale(1.02);-webkit-transition: all .3s ease-out 0s;-o-transition: all .3s ease-out 0s;transition: all .3s ease-out 0s}
.hotnews-slider .slide-title {position: absolute;z-index: 1;width: 100%;bottom: 0;left: 0;padding: 40px 20px 16px;margin: 0;font-size: 18px;line-height: 26px;font-weight: 500;color: #fff;-webkit-box-sizing: border-box;box-sizing: border-box;background: #262626;background: -o-linear-gradient(top, transparent, rgba(0, 0, 0, .7));background: linear-gradient(180deg, transparent, rgba(0, 0, 0, .7))}
.hotnews-slider .slide-title a {display: block;color: #fff;width: 80%;-o-text-overflow: ellipsis;text-overflow: ellipsis;-webkit-box-orient: vertical;display: -webkit-box;-webkit-line-clamp: 2;overflow: hidden}
.hotnews-slider .slide-title a:focus, .hotnews-slider .slide-title a:hover {text-decoration: none}
.feature-post li, .main-slider {border-radius: 4px;overflow: hidden;background: rgba(255, 255, 255, .15)}
.main-slider {width: 600px;height: 320px;direction: ltr}
@media (max-width: 1239px) {
    .main-slider {width: 100%;height: auto;float: none !important}
}
@media (max-width: 767px) {
    .main-slider {border-radius: 0}
}
.container > .slider-wrap .main-slider.slider-full, .main-slider.slider-full, .page-no-sidebar.member-lostpassword .member-lp-process > .slider-wrap .main-slider.slider-full {width: 100%}
.main-slider .swiper-wrapper {height: 100%}
.main-slider .swiper-pagination {width: auto;right: 20px;left: auto;bottom: 26px}
.feature-post {width: 226px;padding: 0;margin: 0;list-style: none}
@media (max-width: 1239px) {
    .feature-post {display: -webkit-box;display: -webkit-flex;display: -ms-flexbox;display: flex;width: 100%;margin-top: 15px;float: none !important;-webkit-box-align: start;-webkit-align-items: flex-start;-ms-flex-align: start;align-items: flex-start}
}
@media (max-width: 767px) {
    .feature-post {padding: 0 15px}
}
.feature-post li {position: relative;height: 153px;margin-bottom: 14px}
@media (max-width: 1239px) {
    .feature-post li {width: 50%;height: auto;-webkit-flex-shrink: 1;-ms-flex-negative: 1;flex-shrink: 1}
    .feature-post li:first-child {margin-right: 15px;margin-bottom: 0}
}
.feature-post li:last-child {margin-bottom: 0}
.feature-post a {display: block}
.feature-post img {width: 100%;height: 100%;vertical-align: top;-webkit-transition: all .3s ease-out 0s;-o-transition: all .3s ease-out 0s;transition: all .3s ease-out 0s}
@media (max-width: 1239px) {
    .feature-post img {height: auto}
}
.feature-post span {position: absolute;display: block;left: 0;bottom: 0;width: 100%;padding: 10px 15px;line-height: 24px;color: #fff;font-size: 16px;font-weight: 500;background: -o-linear-gradient(top, transparent, rgba(0, 0, 0, .7));background: linear-gradient(180deg, transparent, rgba(0, 0, 0, .7));-webkit-box-sizing: border-box;box-sizing: border-box;overflow: hidden;-o-text-overflow: ellipsis;text-overflow: ellipsis;white-space: nowrap;word-wrap: normal}

.list.tabs {font-size: 0;white-space: nowrap;overflow: hidden}
@media (max-width: 991px) {
    .list.tabs {-webkit-overflow-scrolling: touch;overflow-x: auto}
    .list.tabs::-webkit-scrollbar {display: none;width: 0;height: 0}
}
.list.tabs .tab {position: relative;display: inline-block;margin-right: 31px;vertical-align: middle}
.list.tabs .tab:last-child {margin-right: 0}
.list.tabs .tab.active a {position: relative;color: var(--theme-color);font-weight: 500}
.list.tabs .tab a {display: block;font-size: 16px;padding-bottom: 12px;color: #5a5a5a;line-height: 20px;font-weight: 400;text-decoration: none}
.list.tabs .tab a:hover {color: var(--theme-hover)}
.list.tabs .tab-underscore {position: absolute;left: 0;bottom: -1px;width: 100%;height: 2px;background: var(--theme-color);border-radius: 2px;content: "";-webkit-transition: all .3s ease-out 0s;-o-transition: all .3s ease-out 0s;transition: all .3s ease-out 0s}
.main-list .tab-wrap {display: none;min-height: 200px}
.main-list .tab-wrap.active {display: block}
.main-list .tab-wrap.active.articles-list-card, .main-list .tab-wrap.active.articles-list-image {display: -webkit-box;display: -webkit-flex;display: -ms-flexbox;display: flex}
.main-list .tab-wrap.loading {padding: 40px 0;background: url(cms/default/images/loading.gif) 50% no-repeat}
.list-partner {margin: -10px;font-size: 0}
@media (max-width: 767px) {
    .list-partner {margin: -5px}
}
.list-partner li {display: inline-block;margin: 0;-webkit-box-sizing: border-box;box-sizing: border-box;padding: 10px}
@media (max-width: 767px) {
    .list-partner li {width: 25% !important;padding: 5px}
}
.list-partner img {display: block;width: 100%;height: auto;border: 1px solid #efefef;vertical-align: top;border-radius: 2px}
.list-links {margin-right: -20px;margin-bottom: -7px}
.list-links a {display: inline-block;margin: 0 20px 12px 0;color: #828282;line-height: 1}
.list-links a:focus, .list-links a:hover {color: var(--theme-hover);text-decoration: none}

.load-more-wrap {text-align: center;padding: 20px 0;width: 100%;margin: 0 !important}
.load-more {padding-left: 90px;padding-right: 90px;margin: 0;font-weight: 400;color: var(--theme-color);border-color: var(--theme-color)}
@media (max-width: 767px) {
    .load-more {padding-left: 60px;padding-right: 60px}
}
.load-more:focus, .load-more:hover {color: #fff !important;border-color: var(--theme-hover);background: var(--theme-hover)}
.load-more.disabled {color: #828282;background: 0 0;border: 1px solid #e5e5e5;cursor: default}
.load-more.disabled:active, .load-more.disabled:focus, .load-more.disabled:hover {color: #828282 !important;background: 0 0;border: 1px solid #e5e5e5;cursor: default;-webkit-box-shadow: inset 0 0 5px 30px transparent;box-shadow: inset 0 0 5px 30px transparent}
.load-more.disabled:active:after, .load-more.disabled:active:before, .load-more.disabled:focus:after, .load-more.disabled:focus:before, .load-more.disabled:hover:after, .load-more.disabled:hover:before {display: none}
.banner {display: -webkit-box;display: -webkit-flex;display: -ms-flexbox;display: flex;width: 100%;background-position: 50%;background-size: cover;background-repeat: no-repeat;-webkit-box-align: center;-webkit-align-items: center;-ms-flex-align: center;align-items: center}
@media (max-width: 767px) {
    .banner {height: auto !important;padding: 30px 15px}
}
.banner-2 {background-size: cover;background-repeat: no-repeat; height: 180px;position: relative;}
@media (max-width: 767px) {
    .banner-2 {padding-top: 30px;padding-bottom: 30px}
}
.banner-inner {text-align: center;display:flex;flex-direction: column;align-items: center;justify-content: center;}
.banner-inner h1 {margin: 0;padding: 0;font-size: 26px;color: #262626}
@media (max-width: 767px) {
    .banner-inner h1 {font-size: 22px}
}
.banner-inner .page-description, .banner-inner .term-description {margin: 20px 0 0;color: #5a5a5a}
@media (max-width: 767px) {
    .banner-inner .page-description, .banner-inner .term-description {margin-top: 12px}
}
.banner-inner p {margin: 0 auto}
.banner-white .banner-inner h1 {color: #fff}
.banner-white .banner-inner .page-description, .banner-white .banner-inner .term-description {color: #f9f9f9}

.main-list .sec-panel-head {margin-bottom: 0;border-bottom: 1px solid rgba(100, 100, 100, .1)}
.main-list .list.tabs {height: 34px;margin-bottom: -1px}
.main-list .articles-list {margin-top: 1px;padding-bottom: 0;margin-bottom: 0}
.main-list-card, .main-list-image {background: 0 0}
.main-list-card .sec-panel-head, .main-list-image .sec-panel-head {padding: 0}
.main-list-card .articles-list, .main-list-image .articles-list {padding: 15px 0 0}
.main-list-card .load-more-wrap, .main-list-image .load-more-wrap {padding-bottom: 0;margin-top: -8px !important}

.container-fluid .modules-feature-posts {margin-left: -15px;margin-right: -15px}
.container-fluid .modules-feature-posts .feature-posts-wrap {border-radius: 0}
.content-body .member-account-content .woocommerce h2 {border-bottom: 0;padding-left: 10px;padding-bottom: 0;font-size: 18px;font-weight: 500;line-height: 1;border-left: 4px solid var(--theme-color)}
.content-body .member-account-content .woocommerce .woocommerce-order-details__title {margin-top: 0}
.e404-page {padding: 80px 0;text-align: center;color: #5a5a5a}
.e404-page-icon {margin-bottom: 30px}
.e404-page-icon svg {width: 250px;max-width: 100%;height: auto}
.e404-page-text .content-body-title {font-size: 28px;text-shadow: 3px 5px 2px rgba(0, 0, 0, .3)}
:root {}
.page-no-sidebar.member-login, .page-no-sidebar.member-register {background-image: url('/cms/default/images/login_bg.jpg')}
body > header.header {background-color: #fff;}
body > header.header .logo img {max-height: 40px}
@media (max-width: 767px) {
    body > header.header .logo img {max-height: 36px}
}
.content-body .content-body-video {height: 484px}
@media (max-width: 1219px) {
    .content-body .content-body-video {height: 388.10256410256px}
}
@media (max-width: 991px) {
    .content-body .content-body-video {height: 451.28205128205px}
}
@media (max-width: 767px) {
    .content-body .content-body-video {height: 345.71428571429px}
}
@media (max-width: 500px) {
    .content-body .content-body-video {height: 242px}
}
.articles-list-default .item-img:before, .hotnews-profile-main .profile-posts-list .item-img:before {padding-top: 62.5%}
.articles-list-image .item-img:before, .articles-list-card .item-inner:before {padding-top: 62.5%}
@media screen and (-webkit-min-device-pixel-ratio: 0) {
    .articles-list .item-sticky .item-title a {-webkit-background-clip: text;-webkit-text-fill-color: transparent}
    .articles-list .item-sticky .item-title a, .articles-list .item-sticky .item-title a .sticky-post, .articles-list-card .item-sticky .item-title .sticky-post {background-color: #3ca5f6;background-image: -webkit-linear-gradient(0, #3ca5f6 0%, #a86af9 100%);background-image: -o-linear-gradient(0, #3ca5f6 0%, #a86af9 100%);background-image: -moz-linear-gradient(0, #3ca5f6 0%, #a86af9 100%);background-image: linear-gradient(90deg, #3ca5f6 0%, #a86af9 100%)}
}
.no-more-item,.waiting-more {
    height: 40px;
    border-top: 1px dotted #cccccc;
    text-align: center;
    visibility: hidden;
    line-height: 2rem;
}
.article-category-item{
    font-size: 16px;
    color: #0a53be;
    display: inline!important;
}
.article-item{
    display: inline!important;
    font-size: 16px;
}
.ace-code .ace_static_highlight{
    overflow: auto;
}
.ace-code .ace_static_highlight .ace_line{
    white-space: pre;
}
.container .breadcrumb li {
    font-size: 14px;
}
.container .error-container {
    min-height: 200px;
    font-size: 16px;
    padding: 40px;
    text-align: center;
}
.hidden{
    display: none;
}
.article-modules-data{

}
.article-modules-data .article-modules-data-field{
    display: flex;
}
.article-modules-data .article-modules-data-field .article-modules-data-field-name{
    flex: 0 0 100px;
}
.article-modules-data .article-modules-data-field .article-modules-data-field-value{
    flex: 1 1 auto;
    text-align: left;
}
.article-modules-data-images{
    display: flex;
}
.article-modules-data-image-container{
    padding: 5px;
    border: 1px solid #f0f0f0;
    margin-right: 20px;
    border-radius: 0px;
}
.article-modules-data-image-container .article-modules-data-image-image{
    text-align: center;
    max-width: 200px;
    max-height: 200px;
}
.article-modules-data-image-container .article-modules-data-image-title{
    text-align: center;
    line-height: 2;
}

.list-more-none{
    display: none;
}
.hotnews-profile-nav-container{
    width: 800px;
    background: #FFFFFF;
}
ul.pagination > li.total{
    background: none;
}
ul.pagination > li.total:hover{
    color: #5a5a5a;
    cursor: unset;
}
.follow-btn{
    width: 80px;
    background: #FFFFFF;
    border-radius: 3px;
    border:1px solid #cccccc;
    display: inline-block;
    text-align: center;
    float: right;
}
.empty-content-empty{
    background: url("/static/images/empty.png") no-repeat 100% center;
    font-size:12px;
    color:#666666;
    min-height:300px;
    line-height: 300px;
    text-align: center;
}
.message-tab-content .tab-pane{
    display: none;
}
.message-tab-content .tab-pane.active{
    display: block;
}
.banner .banner-inner{
    position: relative;
}
.banner .banner-inner .banner-img{
    position: absolute;
    top: 0;
    left: 0;
    width:100%;
    height: 100%;
}