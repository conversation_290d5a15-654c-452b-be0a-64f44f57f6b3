import{d as v,X as F,ad as B,ag as r,aq as c,c as f,o as s,P as e,H as t,L as a,a as l,I as _,G as V}from"./@vue-C21YZbHS.js";import{a as C}from"./index-BGmsw1a8.js";import"./pinia-DwJf82dV.js";import"./vue-router-BDT9dCQ7.js";import"./js-cookie-Cz0CWeBA.js";import"./@element-plus-_Cc-TEQX.js";import"./nprogress-Scw6VIZr.js";import"./@intlify-D8kAWvSi.js";import"./axios-B62VicFi.js";import"./qs-D41HS0uK.js";import"./side-channel-DytTmCZg.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-DbsE5JW_.js";import"./crypto-js-oM26lpET.js";import"./side-channel-list-C_7Uk4RP.js";import"./side-channel-map-BUokNT8A.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-bSkmVQJL.js";import"./element-plus-CUmVNDWO.js";import"./lodash-es-S0Y0Up6J.js";import"./@vueuse-D_5IXmcI.js";import"./@popperjs-D_chPuIy.js";import"./@ctrl-r5W6hzzQ.js";import"./dayjs-CepoAVPu.js";import"./async-validator-9PlIezaS.js";import"./memoize-one-BdPwpGay.js";import"./normalize-wheel-es-BQoi3Ox2.js";import"./@floating-ui-DwceP2Gb.js";import"./vue-i18n-BkLk9Ti1.js";import"./vue-iNFchrNl.js";import"./vue-clipboard3-Dr8V5xR7.js";import"./clipboard-DzJOpNE6.js";import"./mitt-DJ65BbbF.js";import"./vue-grid-layout-CIPjP0XN.js";import"./vue-simple-uploader-j6sX1TKo.js";import"./vue-ueditor-wrap-CJ5gjsd3.js";const E=v({name:"pagesDrag",setup(){const i=F({dialogVisible:!1});return{...B(i)}}}),b={class:"drag-container"},A={class:"drag-dom"},$={class:"drag-header"},k={class:"dialog-footer"};function w(i,o,y,z,N,P){const p=r("ele-Pointer"),n=r("el-icon"),u=r("el-button"),m=r("el-card"),D=r("el-dialog"),g=c("drag");return s(),f("div",b,[e(m,{shadow:"hover",header:"拖动指令效果（v-drag）作用于 Dialog 对话框"},{default:t(()=>[e(u,{type:"primary",onClick:o[0]||(o[0]=d=>i.dialogVisible=!0),size:"default"},{default:t(()=>[e(n,null,{default:t(()=>[e(p)]),_:1}),o[4]||(o[4]=a(" 点击打开 Dialog "))]),_:1})]),_:1}),e(m,{shadow:"hover",header:"自定义div",class:"mt15"},{default:t(()=>[l("div",A,[l("div",$,[_((s(),V(u,{type:"success",size:"default"},{default:t(()=>[e(n,null,{default:t(()=>[e(p)]),_:1}),o[5]||(o[5]=a(" 按住进行拖动测试 "))]),_:1})),[[g,[".drag-container .drag-dom",".drag-container .drag-header"]]])])])]),_:1}),e(D,{modelValue:i.dialogVisible,"onUpdate:modelValue":o[3]||(o[3]=d=>i.dialogVisible=d),width:"769px"},{header:t(()=>[_((s(),f("div",null,o[6]||(o[6]=[a("拖动指令效果（v-drag）")]))),[[g,[".drag-container .el-dialog",".drag-container .el-dialog__header"]]])]),footer:t(()=>[l("span",k,[e(u,{onClick:o[1]||(o[1]=d=>i.dialogVisible=!1),size:"default"},{default:t(()=>o[7]||(o[7]=[a("取 消")])),_:1}),e(u,{type:"primary",onClick:o[2]||(o[2]=d=>i.dialogVisible=!1),size:"default"},{default:t(()=>o[8]||(o[8]=[a("确 定")])),_:1})])]),default:t(()=>[o[9]||(o[9]=l("p",null,"鼠标放标题头进行 Dialog 对话框拖动",-1))]),_:1},8,["modelValue"])])}const ko=C(E,[["render",w],["__scopeId","data-v-e69c296b"]]);export{ko as default};
