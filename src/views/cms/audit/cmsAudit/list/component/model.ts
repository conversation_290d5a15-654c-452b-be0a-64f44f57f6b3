export interface CmsAuditTableColumns {    
    id:number;  // ID    
    articleId:number;  // 文章ID    
    categoryId:number;  // 栏目ID    
    userId:number;  // 审核管理员ID    
    stepId:number;  // 步骤ID    
    nextStepId:number;  // 下个步骤ID    
    status:string;  // 状态    
    content:string;  // 审核意见    
    action:string;  // 动作    
    auditedAt:string;  // 审核时间    
    createdAt:string;  // 创建时间    
}


export interface CmsAuditInfoData {    
    id:number|undefined;        // ID    
    articleId:number|undefined; // 文章ID    
    categoryId:number|undefined; // 栏目ID    
    userId:number|undefined; // 审核管理员ID    
    stepId:number|undefined; // 步骤ID    
    nextStepId:number|undefined; // 下个步骤ID    
    status:string|undefined; // 状态    
    content:string|undefined; // 审核意见    
    action:string|undefined; // 动作    
    auditedAt:string|undefined; // 审核时间    
    createdAt:string|undefined; // 创建时间    
    updatedAt:string|undefined; // 更新时间    
}


export interface CmsAuditTableDataState {
    ids:any[];
    tableData: {
        data: Array<CmsAuditTableColumns>;
        total: number;
        loading: boolean;
        param: {
            pageNum: number;
            pageSize: number;            
            id: number|undefined;            
            articleId: number|undefined;            
            categoryId: number|undefined;            
            userId: number|undefined;            
            stepId: number|undefined;            
            nextStepId: number|undefined;            
            status: string|undefined;            
            content: string|undefined;            
            action: string|undefined;            
            auditedAt: string|undefined;            
            createdAt: string|undefined;            
            dateRange: string[];
        };
    };
}


export interface CmsAuditEditState{
    loading:boolean;
    isShowDialog: boolean;
    formData:CmsAuditInfoData;
    rules: object;
}