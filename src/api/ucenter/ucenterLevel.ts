import request from '/@/utils/request'
// 查询用户等级列表
export function listUcenterLevel(query:object) {
  return request({
    url: '/api/v1/ucenter/admin/ucenterLevel/list',
    method: 'get',
    params: query
  })
}
// 查询用户等级详细
export function getUcenterLevel(id:number) {
  return request({
    url: '/api/v1/ucenter/admin/ucenterLevel/get',
    method: 'get',
    params: {
      id: id.toString()
    }
  })
}
// 新增用户等级
export function addUcenterLevel(data:object) {
  return request({
    url: '/api/v1/ucenter/admin/ucenterLevel/add',
    method: 'post',
    data: data
  })
}
// 修改用户等级
export function updateUcenterLevel(data:object) {
  return request({
    url: '/api/v1/ucenter/admin/ucenterLevel/edit',
    method: 'put',
    data: data
  })
}
// 删除用户等级
export function delUcenterLevel(ids:number[]) {
  return request({
    url: '/api/v1/ucenter/admin/ucenterLevel/delete',
    method: 'delete',
    data:{
      ids:ids
    }
  })
}
