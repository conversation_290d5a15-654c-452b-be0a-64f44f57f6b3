<template>
  <div class="right-content">
    <div class="audit-cmsAuditCategory-actions mb15">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="info"
            @click="cmsAuditCategoryList"
          ><el-icon><ele-Refresh /></el-icon>刷新</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="primary"
            @click="handleAdd"
            v-auth="'api/v1/audit/cmsAuditCategory/add'"
          ><el-icon><ele-Plus /></el-icon>新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            :disabled="multiple"
            @click="handleDelete(null)"
            v-auth="'api/v1/audit/cmsAuditCategory/delete'"
          ><el-icon><ele-Delete /></el-icon>删除</el-button>
        </el-col>
      </el-row>
    </div>
    <el-table v-loading="loading" :data="tableData.data" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" min-width="150px" />
      <el-table-column label="栏目" align="center" prop="categoryInfo.name" min-width="150px" />
      <el-table-column label="审核人员" align="center" prop="userNames" min-width="150px" />
      <el-table-column label="审核步骤" align="center" prop="stepInfo.title" min-width="150px" />
      <el-table-column label="操作" align="center" class-name="small-padding" min-width="160px" fixed="right">
        <template #default="scope">
          <el-button
            type="primary"
            link
            @click="handleUpdate(scope.row)"
            v-auth="'api/v1/audit/cmsAuditCategory/edit'"
          ><el-icon><ele-EditPen /></el-icon>修改</el-button>
          <el-button
            type="primary"
            link
            @click="handleDelete(scope.row)"
            v-auth="'api/v1/audit/cmsAuditCategory/delete'"
          ><el-icon><ele-DeleteFilled /></el-icon>删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="tableData.total>0"
      :total="tableData.total"
      v-model:page="tableData.param.pageNum"
      v-model:limit="tableData.param.pageSize"
      @pagination="cmsAuditCategoryList"
    />

    <!-- 编辑和详情组件 -->
    <ApiV1CmsAuditCmsAuditCategoryEdit
      ref="editRef"
      @cmsAuditCategoryList="cmsAuditCategoryList"
    ></ApiV1CmsAuditCmsAuditCategoryEdit>
    <ApiV1CmsAuditCmsAuditCategoryDetail
      ref="detailRef"
      @cmsAuditCategoryList="cmsAuditCategoryList"
    ></ApiV1CmsAuditCmsAuditCategoryDetail>
  </div>
</template>

<script setup lang="ts">
import { toRefs, reactive, onMounted, ref, getCurrentInstance, toRaw, watch } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import {
  listCmsAuditCategory,
  getCmsAuditCategory,
  delCmsAuditCategory,
  addCmsAuditCategory,
  updateCmsAuditCategory,
} from "/@/api/cms/audit/cmsAuditCategory";
import {
  CmsAuditCategoryTableColumns,
  CmsAuditCategoryInfoData,
  CmsAuditCategoryTableDataState,
} from "/@/views/cms/audit/cmsAuditCategory/list/component/model";
import ApiV1CmsAuditCmsAuditCategoryEdit from "/@/views/cms/audit/cmsAuditCategory/list/component/edit.vue";
import ApiV1CmsAuditCmsAuditCategoryDetail from "/@/views/cms/audit/cmsAuditCategory/list/component/detail.vue";

defineOptions({ name: "RightList" });

// 定义props和emits
const props = defineProps({
  selectedCategoryId: {
    type: Number,
    default: undefined
  }
});

const emit = defineEmits(['update:selectedCategoryId']);

const { proxy } = <any>getCurrentInstance();
const loading = ref(false);
const editRef = ref();
const detailRef = ref();

// 非多个禁用
const multiple = ref(true);

// 字典选项数据
const {} = proxy.useDict();

const state = reactive<CmsAuditCategoryTableDataState>({
  ids: [],
  tableData: {
    data: [],
    total: 0,
    loading: false,
    param: {
      pageNum: 1,
      pageSize: 10,
      id: undefined,
      userIds: undefined,
      categoryId: undefined,
      dateRange: []
    },
  },
});

const { tableData } = toRefs(state);

// 监听props变化
watch(() => props.selectedCategoryId, (newVal) => {
  if (newVal !== state.tableData.param.categoryId) {
    state.tableData.param.categoryId = newVal;
    state.tableData.param.pageNum = 1; // 重置到第一页
    cmsAuditCategoryList();
  }
}, { immediate: true });

// 页面加载时
onMounted(() => {
  initTableData();
});

// 初始化表格数据
const initTableData = () => {
  cmsAuditCategoryList();
};

// 获取列表数据
const cmsAuditCategoryList = () => {
  loading.value = true;
  listCmsAuditCategory(state.tableData.param).then((res: any) => {
    let list = res.data.list ?? [];
    state.tableData.data = list;
    state.tableData.total = res.data.total;
    loading.value = false;
  });
};

// 多选框选中数据
const handleSelectionChange = (selection: Array<CmsAuditCategoryInfoData>) => {
  state.ids = selection.map(item => item.id);
  multiple.value = !selection.length;
};

const handleAdd = () => {
  // 将当前选中的分类ID传递给编辑组件作为默认值
  editRef.value.openDialog(undefined, props.selectedCategoryId);
};

const handleUpdate = (row: CmsAuditCategoryTableColumns | null) => {
  if (!row) {
    row = state.tableData.data.find((item: CmsAuditCategoryTableColumns) => {
      return item.id === state.ids[0];
    }) as CmsAuditCategoryTableColumns;
  }
  editRef.value.openDialog(toRaw(row));
};

const handleDelete = (row: CmsAuditCategoryTableColumns | null) => {
  let msg = '你确定要删除所选数据？';
  let id: number[] = [];
  if (row) {
    msg = `此操作将永久删除数据，是否继续?`;
    id = [row.id];
  } else {
    id = state.ids;
  }
  if (id.length === 0) {
    ElMessage.error('请选择要删除的数据。');
    return;
  }
  ElMessageBox.confirm(msg, '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      delCmsAuditCategory(id).then(() => {
        ElMessage.success('删除成功');
        cmsAuditCategoryList();
      });
    })
    .catch(() => {});
};

const handleView = (row: CmsAuditCategoryTableColumns) => {
  detailRef.value.openDialog(toRaw(row));
};
</script>

<style lang="scss" scoped>
.right-content {
  flex: 1;
  padding: 10px;
  overflow: auto;
}

.ml-2 {
  margin: 3px;
}
</style>
