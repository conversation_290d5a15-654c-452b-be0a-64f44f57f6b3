package service

import (
	"context"
	"github.com/tiger1103/gfast/v3/api/v1/home"
	"github.com/tiger1103/gfast/v3/internal/app/home/<USER>"
)

type ICategory interface {
	//
	// List
	//  @Description: 获取文章分类列表
	//  @param ctx
	//  @param req
	//  @return listRes
	//  @return err
	//
	List(ctx context.Context, req *home.CategorySearchReq) (listRes *home.CategorySearchRes, err error)
	//
	// GetById
	//  @Description: 通过ID获取文章分类信息
	//  @param ctx
	//  @param id
	//  @return res
	//  @return err
	//
	GetById(ctx context.Context, id uint) (res *model.CategoryInfoRes, err error)
}

var localCategory ICategory

func Category() ICategory {
	if localCategory == nil {
		panic("implement not found for interface ICategory, forgot register?")
	}
	return localCategory
}

func RegisterCategory(i ICategory) {
	localCategory = i
}
