/**
 * @Company: 云南奇讯科技有限公司
 * @Author: yxf
 * @Description:
 * @Version: 1.0.0
 * @Date: 2021/11/3 16:14
 */

package service

import (
	"context"
	"errors"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/text/gregex"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/plugins/sms/dao"
	"github.com/tiger1103/gfast/v3/internal/app/plugins/sms/driver"
	"github.com/tiger1103/gfast/v3/internal/app/plugins/sms/model"
	"time"
)

type demoConfig struct {
	Host         string // 短信的发送地址
	AccessKey    string
	TemplateCode g.Map
	SignName     g.Map
}
type demoService struct {
	DemoConfig *demoConfig
}

var Demo = new(demoService)

// SendTemplateSms 发送模板短信
func (s *demoService) SendTemplateSms(args *driver.SendSmsArgs) (err error) {
	g.Log().Info(args.Ctx, "发送成功,这里是测试")
	return
}

// SendVerifyCode 发送短信验证码
func (s *demoService) SendVerifyCode(args *driver.SendSmsArgs) (err error) {
	if args == nil {
		return errors.New("args cannot be nil")
	}
	err = s.setConfig(args.Ctx)
	if err != nil {
		return
	}
	template := ""
	err = s.setConfig(args.Ctx)
	if err != nil {
		err = errors.New("请配置短信参数")
		return
	}

	if v, ok := s.DemoConfig.TemplateCode["verifyCode"]; ok {
		template = gconv.String(v)
	}
	if len(args.Mobiles) < 1 {
		err = errors.New("手机号码不能为空")
		return
	}
	mobile, _ := gregex.ReplaceString("\\s|-", "", args.Mobiles[0])
	verifyCode, err := Sms.CreateVerifyCode(args.Ctx, mobile)
	if err != nil {
		return
	}
	if s.DemoConfig.Host == "" {
		err = errors.New("请配置验证码短信发送地址")
		return
	}
	return s.sendSms(args.Ctx, s.DemoConfig.Host, g.Map{
		"apikey":      s.DemoConfig.AccessKey,
		"tpl_id":      gconv.Int(template),
		"mobile":      mobile,
		"tpl_value":   ("#code#") + "=" + gconv.String(verifyCode),
		"verify_code": verifyCode,
	}, "SendVerifyCode")

	g.Log().Info(args.Ctx, "发送成功,测试验证码为："+gconv.String(verifyCode))
	return
}

// CheckVerifyCode 验证短信验证码
func (s *demoService) CheckVerifyCode(ctx context.Context, mobile string, code string) (err error) {
	return Sms.CheckVerifyCode(ctx, mobile, code)
}

// 设置配置
func (s *demoService) setConfig(ctx context.Context) (err error) {
	if s.DemoConfig == nil {
		s.DemoConfig, err = s.GetConfig(ctx)
	}
	return
}

// GetConfig 获取短信的配置
func (s *demoService) GetConfig(ctx context.Context) (demoConfig *demoConfig, err error) {
	smsConfig, err := dao.PluginSmsConfig.Ctx(nil).Where("sms_type", "demo").One()
	if err != nil {
		g.Log().Error(ctx, err)
		return
	}
	if smsConfig.IsEmpty() {
		err = errors.New("短信配置信息不存在")
		g.Log().Error(ctx, err)
		return
	}

	if v, ok := smsConfig["config"]; ok {
		err = gconv.Struct(v, &demoConfig)
		if err != nil {
			g.Log().Error(ctx, err)
			return
		}
	}
	if demoConfig.AccessKey == "" || demoConfig.Host == "" {
		err = errors.New("短信配置有错误")
		g.Log().Error(ctx, err)
		return
	}
	return
}

// 发送短信
func (s *demoService) sendSms(ctx context.Context, url string, data g.Map, smsType string) (err error) {
	result := g.Client().Timeout(5*time.Second).Header(map[string]string{
		"Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
	}).PostContent(ctx, url, data)
	res := gconv.Map(result)
	if statusCode, ok := res["code"]; ok {
		//写入发送日志
		mobile := make([]string, 0)
		params := make([]string, 0)
		if m, ok := data["mobile"]; ok {
			mobile = append(mobile, gconv.String(m))
		} else if m, ok := data["mobiles"]; ok {
			mobile = gconv.SliceStr(m)
		}
		if p, ok := data["params"]; ok {
			params = gconv.SliceStr(p)
		} else if p, ok := data["tpl_value"]; ok {
			params = append(params, gconv.String(p))
		}
		err = Sms.WriteLog(ctx, &model.ClientArg{
			SmsType:    "yunpian",
			MsgType:    smsType,
			Result:     gconv.String(res),
			TemplateId: gconv.String(data["tpl_id"]),
			Mobiles:    mobile,
			Params:     params,
		})
		if err != nil {
			return
		}
		if gconv.Int(statusCode) == 0 {
			return
		} else {
			if msg, ok2 := res["msg"]; ok2 {
				return errors.New("发送失败：" + gconv.String(msg))
			}
		}
	}
	return errors.New("未知错误,发送短信失败")
}
