// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-05-06 17:48:32
// 生成路径: internal/app/cms/audit/service/cms_audit_step.go
// 生成人：zhanghui
// desc:审核步骤
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/cms/audit/model"
)

type ICmsAuditStep interface {
	List(ctx context.Context, req *model.CmsAuditStepSearchReq) (res *model.CmsAuditStepSearchRes, err error)
	GetById(ctx context.Context, Id uint) (res *model.CmsAuditStepInfoRes, err error)
	Add(ctx context.Context, req *model.CmsAuditStepAddReq) (err error)
	Edit(ctx context.Context, req *model.CmsAuditStepEditReq) (err error)
	Delete(ctx context.Context, Id []uint) (err error)
	GetCmsAuditStepMap(ctx context.Context) (cmsAuditStepMap map[string]string, err error)
}

var localCmsAuditStep ICmsAuditStep

func CmsAuditStep() ICmsAuditStep {
	if localCmsAuditStep == nil {
		panic("implement not found for interface ICmsAuditStep, forgot register?")
	}
	return localCmsAuditStep
}

func RegisterCmsAuditStep(i ICmsAuditStep) {
	localCmsAuditStep = i
}
