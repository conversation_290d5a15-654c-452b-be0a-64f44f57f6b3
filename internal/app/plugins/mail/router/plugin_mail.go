// ==========================================================================
// GFast自动生成router操作代码。
// 生成日期：2023-11-08 10:11:31
// 生成路径: internal/app/plugin/mail/router/plugin_mail.go
// 生成人：gfast
// desc:邮件发送
// company:云南奇讯科技有限公司
// ==========================================================================

package router

import (
	"context"

	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/internal/app/plugins/mail/controller"
)

func (router *Router) BindPluginsMailController(ctx context.Context, group *ghttp.RouterGroup) {
	group.Group("/mail", func(group *ghttp.RouterGroup) {
		group.Bind(
			controller.PluginMail,
		)
	})
}
