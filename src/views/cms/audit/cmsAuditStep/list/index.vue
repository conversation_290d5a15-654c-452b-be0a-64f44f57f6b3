<template>
  <div class="audit-cmsAuditStep-container">
    <el-card shadow="hover">
        <div class="audit-cmsAuditStep-search mb15">
            <el-form :model="tableData.param" ref="queryRef" :inline="true" label-width="100px">
            <el-row>                
                <el-col :span="6" >
                  <el-form-item label="步骤名称" prop="title">
                    <el-input
                        v-model="tableData.param.title"
                        placeholder="请输入步骤名称"
                        clearable                        
                        @keyup.enter.native="cmsAuditStepList"
                    />                    
                  </el-form-item>
                </el-col>                
                <el-col :span="6" >
                  <el-form-item label="步骤" prop="step">
                    <el-input
                        v-model="tableData.param.step"
                        placeholder="请输入步骤"
                        clearable                        
                        @keyup.enter.native="cmsAuditStepList"
                    />                    
                  </el-form-item>
                </el-col>                
                <el-col :span="6" >
                  <el-form-item label="类别" prop="type">
                    <el-select filterable v-model="tableData.param.type" placeholder="请选择类别" clearable style="width:200px;">
                        <el-option
                            v-for="dict in cms_audit_type"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                        />
                    </el-select>
                  </el-form-item>
                </el-col>                
                <el-col :span="6" >
                  <el-form-item>
                    <el-button type="primary"  @click="cmsAuditStepList"><el-icon><ele-Search /></el-icon>搜索</el-button>
                    <el-button  @click="resetQuery(queryRef)"><el-icon><ele-Refresh /></el-icon>重置</el-button>
                  </el-form-item>
                </el-col>            
              </el-row>
            </el-form>
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button
                  type="primary"
                  @click="handleAdd"
                  v-auth="'api/v1/audit/cmsAuditStep/add'"
                ><el-icon><ele-Plus /></el-icon>新增</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                  type="success"
                  :disabled="single"
                  @click="handleUpdate(null)"
                  v-auth="'api/v1/audit/cmsAuditStep/edit'"
                ><el-icon><ele-Edit /></el-icon>修改</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                  type="danger"
                  :disabled="multiple"
                  @click="handleDelete(null)"
                  v-auth="'api/v1/audit/cmsAuditStep/delete'"
                ><el-icon><ele-Delete /></el-icon>删除</el-button>
              </el-col>            
            </el-row>
        </div>
        <el-table v-loading="loading" :data="tableData.data" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />          
          <el-table-column label="" align="center" prop="id"
            min-width="150px"            
             />          
          <el-table-column label="步骤名称" align="center" prop="title"
            min-width="150px"            
             />          
          <el-table-column label="步骤" align="center" prop="step"
            min-width="150px"            
             />          
          <el-table-column label="类别" align="center" prop="type" :formatter="typeFormat"
            min-width="150px"            
             />          
          <el-table-column label="创建时间" align="center" prop="createdAt"
            min-width="150px"            
            >
            <template #default="scope">
                <span>{{ proxy.parseTime(scope.row.createdAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </template>
          </el-table-column>        
          <el-table-column label="操作" align="center" class-name="small-padding" min-width="160px" fixed="right">
            <template #default="scope">            
              <el-button
                type="primary"
                link
                @click="handleUpdate(scope.row)"
                v-auth="'api/v1/audit/cmsAuditStep/edit'"
              ><el-icon><ele-EditPen /></el-icon>修改</el-button>
              <el-button
                type="primary"
                link
                @click="handleDelete(scope.row)"
                v-auth="'api/v1/audit/cmsAuditStep/delete'"
              ><el-icon><ele-DeleteFilled /></el-icon>删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
            v-show="tableData.total>0"
            :total="tableData.total"
            v-model:page="tableData.param.pageNum"
            v-model:limit="tableData.param.pageSize"
            @pagination="cmsAuditStepList"
        />
    </el-card>
    <ApiV1CmsAuditCmsAuditStepEdit
       ref="editRef"       
       :typeOptions="cms_audit_type"       
       @cmsAuditStepList="cmsAuditStepList"
    ></ApiV1CmsAuditCmsAuditStepEdit>
    <ApiV1CmsAuditCmsAuditStepDetail
      ref="detailRef"      
      :typeOptions="cms_audit_type"      
      @cmsAuditStepList="cmsAuditStepList"
    ></ApiV1CmsAuditCmsAuditStepDetail>    
  </div>
</template>
<script setup lang="ts">
import {toRefs, reactive, onMounted, ref, defineComponent, computed,getCurrentInstance,toRaw} from 'vue';
import {ElMessageBox, ElMessage, FormInstance} from 'element-plus';
import {
    listCmsAuditStep,
    getCmsAuditStep,
    delCmsAuditStep,
    addCmsAuditStep,
    updateCmsAuditStep,
} from "/@/api/cms/audit/cmsAuditStep";
import {
    CmsAuditStepTableColumns,
    CmsAuditStepInfoData,
    CmsAuditStepTableDataState,    
} from "/@/views/cms/audit/cmsAuditStep/list/component/model"
import ApiV1CmsAuditCmsAuditStepEdit from "/@/views/cms/audit/cmsAuditStep/list/component/edit.vue"
import ApiV1CmsAuditCmsAuditStepDetail from "/@/views/cms/audit/cmsAuditStep/list/component/detail.vue"
defineOptions({ name: "apiV1CmsAuditCmsAuditStepList"})
const {proxy} = <any>getCurrentInstance()
const loading = ref(false)
const queryRef = ref()
const editRef = ref();
const detailRef = ref();
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple =ref(true)

// 字典选项数据
const {    
    cms_audit_type,    
} = proxy.useDict(    
    'cms_audit_type',    
)
const state = reactive<CmsAuditStepTableDataState>({
    ids:[],
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,            
            id: undefined,            
            title: undefined,            
            step: undefined,            
            type: undefined,            
            createdAt: undefined,            
            dateRange: []
        },
    },
});
const { tableData } = toRefs(state);
// 页面加载时
onMounted(() => {
    initTableData();
});
// 初始化表格数据
const initTableData = () => {    
    cmsAuditStepList()
};
/** 重置按钮操作 */
const resetQuery = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.resetFields()
    cmsAuditStepList()
};
// 获取列表数据
const cmsAuditStepList = ()=>{
  loading.value = true
  listCmsAuditStep(state.tableData.param).then((res:any)=>{
    let list = res.data.list??[];    
    state.tableData.data = list;
    state.tableData.total = res.data.total;
    loading.value = false
  })
};

// 类别字典翻译
const typeFormat = (row:CmsAuditStepTableColumns) => {
    return proxy.selectDictLabel(cms_audit_type.value, row.type);
}
// 多选框选中数据
const handleSelectionChange = (selection:Array<CmsAuditStepInfoData>) => {
    state.ids = selection.map(item => item.id)
    single.value = selection.length!=1
    multiple.value = !selection.length
}
const handleAdd =  ()=>{
    editRef.value.openDialog()
}
const handleUpdate = (row: CmsAuditStepTableColumns|null) => {
    if(!row){
        row = state.tableData.data.find((item:CmsAuditStepTableColumns)=>{
            return item.id ===state.ids[0]
        }) as CmsAuditStepTableColumns
    }
    editRef.value.openDialog(toRaw(row));
};
const handleDelete = (row: CmsAuditStepTableColumns|null) => {
    let msg = '你确定要删除所选数据？';
    let id:number[] = [] ;
    if(row){
    msg = `此操作将永久删除数据，是否继续?`
    id = [row.id]
    }else{
    id = state.ids
    }
    if(id.length===0){
        ElMessage.error('请选择要删除的数据。');
        return
    }
    ElMessageBox.confirm(msg, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            delCmsAuditStep(id).then(()=>{
                ElMessage.success('删除成功');
                cmsAuditStepList();
            })
        })
        .catch(() => {});
}

</script>
<style lang="scss" scoped>
    .colBlock {
        display: block;
    }
    .colNone {
        display: none;
    }
    .ml-2{margin: 3px;}
</style>