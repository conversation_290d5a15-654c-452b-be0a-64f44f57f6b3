/**
 * @Company: 云南奇讯科技有限公司
 * @Author: yxf
 * @Description:
 * @Version: 1.0.0
 * @Date: 2023/4/4 16:30
 */

package home

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/cms/model"
)

// CommentAddReq 添加操作请求参数
type CommentAddReq struct {
	g.Meta        `path:"/add" tags:"cms前台接口/文章评论" method:"post" summary:"文章评论增加"`
	ArticleId     uint64 `p:"articleId"`
	Content       string `p:"content"`
	MemberId      uint64
	ReplyMemberId uint64 `p:"replyMemberId"`
	Pid           uint64 `p:"pid"`
}

// CommentAddRes 文章评论增加返回
type CommentAddRes struct {
	commonApi.EmptyRes
}

// CommentDeleteReq 删除数据请求
type CommentDeleteReq struct {
	g.Meta   `path:"/delete" tags:"cms前台接口/文章评论" method:"delete" summary:"文章评论删除"`
	Id       uint64 `p:"id" v:"required#主键必须"` //通过主键删除
	MemberId uint64
}

// CommentDeleteRes 文章评论删除返回
type CommentDeleteRes struct {
	commonApi.EmptyRes
}

// CommentSearchReq 分页请求参数
type CommentSearchListReq struct {
	g.Meta    `path:"/list" tags:"cms前台接口/文章评论" method:"get" summary:"文章评论列表"`
	ArticleId string `p:"articleId"`
	MemberId  string `p:"memberId"`
	commonApi.PageReq
}

// CommentSearchListRes 文章评论分页请求返回
type CommentSearchListRes struct {
	g.Meta `mime:"application/json"`
	commonApi.ListRes
	List []*model.CmsCommentListRes `json:"list"`
}
