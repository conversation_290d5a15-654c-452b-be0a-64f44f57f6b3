// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2023-03-09 16:49:02
// 生成路径: internal/app/ucenter/model/ucenter_members.go
// 生成人：yxf
// desc:用户中心
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// UCenterMembersInfoRes is the golang structure for table ucenter_members.
type UCenterMembersInfoRes struct {
	gmeta.Meta  `orm:"table:ucenter_members"`
	Id          uint64                            `orm:"id,primary" json:"id"`     // ID
	Username    string                            `orm:"username" json:"username"` // 用户名
	Nickname    string                            `orm:"nickname" json:"nickname"` // 昵称
	Sex         int                               `orm:"sex" json:"sex"`           // 性别
	DeptId      int                               `orm:"dept_id" json:"deptId"  //部门ID`
	LinkedDept  *LinkedUCenterMembersSysDept      `orm:"with:dept_id=dept_id" json:"linkedDept"`
	Email       string                            `orm:"email" json:"email"`               // 邮箱
	Mobile      string                            `orm:"mobile" json:"mobile"`             // 手机号
	Uuid        string                            `orm:"uuid" json:"uuid"`                 // UUID
	RegType     string                            `orm:"reg_type" json:"regType"`          // 注册类型
	Avatar      string                            `orm:"avatar" json:"avatar"`             // 头像
	CreatedAt   *gtime.Time                       `orm:"created_at" json:"createdAt"`      // 创建时间
	WxOpenid    string                            `orm:"wx_openid" json:"wxOpenid"`        // 微信openid
	IsOpen      int                               `orm:"is_open" json:"isOpen"`            // 是否激活
	LastLoginIp string                            `orm:"last_login_ip" json:"lastLoginIp"` // 最后登陆IP
	LastLoginAt *gtime.Time                       `orm:"last_login_at" json:"lastLoginAt"` // 最后登录日期
	Qq          string                            `orm:"qq" json:"qq"`                     // QQ号
	Score       int                               `orm:"score" json:"score"`               // 积分
	Level       uint                              `orm:"level" json:"level"`               // 用户等级
	LinkedLevel *LinkedUCenterMembersUCenterLevel `orm:"with:id=level" json:"linkedLevel"`
	Follower    int                               `orm:"follower" json:"follower"`
	IsSecret    int                               `orm:"is_secret" json:"isSecret"`      // 是否保密
	Cover       string                            `orm:"cover" json:"cover"`             // 用户封面
	Description string                            `orm:"description" json:"description"` // 个人说明
	Status      int                               `orm:"status" json:"status"`           // 状态
}

type LinkedUCenterMembersUCenterLevel struct {
	gmeta.Meta `orm:"table:ucenter_level"`
	Id         uint   `orm:"id" json:"id"`     // 等级ID
	Name       string `orm:"name" json:"name"` // 等级名称
}

type LinkedUCenterMembersSysDept struct {
	gmeta.Meta `orm:"table:sys_dept"`
	DeptId     uint   `orm:"dept_id" json:"deptId"`     // 部门ID
	DeptName   string `orm:"dept_name" json:"deptName"` // 部门名称
}

type LinkedUCenterMembers struct {
	gmeta.Meta  `orm:"table:ucenter_members"`
	Id          uint   `orm:"id" json:"id"`                   // 等级ID
	Username    string `orm:"username" json:"username"`       // 登录名
	Nickname    string `orm:"nickname" json:"nickname"`       // 昵称
	Sex         string `orm:"sex" json:"sex"`                 // 性别
	DeptId      string `orm:"dept_id" json:"deptId"`          //部门ID
	Avatar      string `orm:"avatar" json:"avatar"`           // 头像
	Description string `orm:"description" json:"description"` // 简介
	Level       uint   `orm:"level" json:"level"`             // 用户等级
}

type UCenterMembersListRes struct {
	Id          uint64                            `json:"id"`
	Nickname    string                            `json:"nickname"`
	Sex         int                               `json:"sex"`
	DeptId      int                               `json:"deptId"`
	LinkedDept  *LinkedUCenterMembersSysDept      `orm:"with:dept_id=dept_id" json:"linkedDept"`
	Mobile      string                            `json:"mobile"`
	Username    string                            `json:"username"`
	Description string                            `json:"description"` // 简介
	Uuid        string                            `json:"uuid"`
	RegType     string                            `json:"regType"`
	Avatar      string                            `json:"avatar"`
	CreatedAt   *gtime.Time                       `json:"createdAt"`
	WxOpenid    string                            `json:"wxOpenid"`
	IsOpen      int                               `json:"isOpen"`
	Status      int                               `json:"status"`
	LastLoginIp string                            `json:"lastLoginIp"`
	LastLoginAt *gtime.Time                       `json:"lastLoginAt"`
	Qq          string                            `json:"qq"`
	Score       int                               `json:"score"`
	Level       uint                              `json:"level"`
	LinkedLevel *LinkedUCenterMembersUCenterLevel `orm:"with:id=level" json:"linkedLevel"`
}

type LinkedUCenterMembersInfoRes struct {
	gmeta.Meta `orm:"table:ucenter_members"`
	Username   string `orm:"username" json:"username"` // 用户名
	Nickname   string `orm:"nickname" json:"nickname"` // 昵称
	Sex        int    `orm:"sex" json:"sex"`           // 性别
	DeptId     int    `orm:"dept_id" json:"deptId"`    //部门ID
	Email      string `orm:"email" json:"email"`       // 邮箱
	Mobile     string `orm:"mobile" json:"mobile"`     // 手机号类型
	Avatar     string `orm:"avatar" json:"avatar"`     // 头像
	Referrer   int    `orm:"referrer" json:"referrer"` // 推荐人
}
