/**
 * @Company: 云南奇讯科技有限公司
 * @Author: yxf
 * @Description:
 * @Version: 1.0.0
 * @Date: 2023/2/20 16:31
 */

package logic

import (
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gview"
	"github.com/tiger1103/gfast/v3/internal/app/home/<USER>"
	"github.com/tiger1103/gfast/v3/library/libResponse"
)

func init() {
	service.RegisterHomeCommon(NewHomeCommonService())
}

type homeCommonService struct{}

func NewHomeCommonService() *homeCommonService {
	return &homeCommonService{}
}

func (c *homeCommonService) Success(r *ghttp.Request, view *gview.View, templateName string, layoutTemplate ...string) {
	layoutTemplateName := "common/layout.html"
	if len(layoutTemplate) > 0 {
		layoutTemplateName = layoutTemplate[0]
	}
	err := libResponse.WriteTpl(r, layoutTemplateName, view, gview.Params{
		"mainTpl": templateName,
	})
	if err != nil {
		c.Error(r, view, err.Error())
	}
	r.Exit()
}

func (c *homeCommonService) Error(r *ghttp.Request, view *gview.View, errMsg string, layoutTemplate ...string) {
	layoutTemplateName := "common/layout.html"
	if len(layoutTemplate) > 0 {
		layoutTemplateName = layoutTemplate[0]
	}
	view.Assign("errMsg", errMsg)
	err := libResponse.WriteTpl(r, layoutTemplateName, view, gview.Params{
		"mainTpl": "common/error.html",
	})
	if err != nil {
		r.Response.WriteExit(err.Error())
	}
	r.Exit()
}
