import{l as b}from"./cmsCategory-MP-V5q2B.js";import{d as A,r as u,i as D,h as E,ag as n,c as B,o as g,a as w,P as a,H as l,a4 as R,L as T,G as N,M as S}from"./@vue-C21YZbHS.js";import{a as V}from"./index-BGmsw1a8.js";import"./pinia-DwJf82dV.js";import"./vue-router-BDT9dCQ7.js";import"./js-cookie-Cz0CWeBA.js";import"./@element-plus-_Cc-TEQX.js";import"./nprogress-Scw6VIZr.js";import"./@intlify-D8kAWvSi.js";import"./axios-B62VicFi.js";import"./qs-D41HS0uK.js";import"./side-channel-DytTmCZg.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-DbsE5JW_.js";import"./crypto-js-oM26lpET.js";import"./side-channel-list-C_7Uk4RP.js";import"./side-channel-map-BUokNT8A.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-bSkmVQJL.js";import"./element-plus-CUmVNDWO.js";import"./lodash-es-S0Y0Up6J.js";import"./@vueuse-D_5IXmcI.js";import"./@popperjs-D_chPuIy.js";import"./@ctrl-r5W6hzzQ.js";import"./dayjs-CepoAVPu.js";import"./async-validator-9PlIezaS.js";import"./memoize-one-BdPwpGay.js";import"./normalize-wheel-es-BQoi3Ox2.js";import"./@floating-ui-DwceP2Gb.js";import"./vue-i18n-BkLk9Ti1.js";import"./vue-iNFchrNl.js";import"./vue-clipboard3-Dr8V5xR7.js";import"./clipboard-DzJOpNE6.js";import"./mitt-DJ65BbbF.js";import"./vue-grid-layout-CIPjP0XN.js";import"./vue-simple-uploader-j6sX1TKo.js";import"./vue-ueditor-wrap-CJ5gjsd3.js";const x=A({name:"CmsCategoryLeftTree",emits:["node-click"],setup(t,{emit:i}){const p=u(""),r=u(),v=u([]),s=u(null),d=u(!1);D(p,e=>{r.value&&r.value.filter(e)});const c=(e,o)=>e?o.name.toLowerCase().includes(e.toLowerCase()):!0,m=()=>{r.value&&r.value.filter(p.value)},f=async()=>{try{const e=await b({});if(e.code===0){let o=[];e.data&&Array.isArray(e.data)?o=e.data:e.data&&e.data.list&&Array.isArray(e.data.list)&&(o=e.data.list),v.value=h(o,0)}}catch(e){console.error("Failed to load category tree:",e)}},h=(e,o)=>e.filter(_=>_.parentId===o).map(_=>({..._,children:h(e,_.id)})),C=e=>{s.value=e,i("node-click",e)},k=async()=>{try{p.value="",await f(),s.value&&(r.value.getNode(s.value.id)?r.value.setCurrentKey(s.value.id):s.value=null)}catch(e){console.error("Failed to refresh tree:",e)}},y=()=>{if(!r.value)return;const e=r.value.store.nodesMap;d.value=!d.value;for(const o in e)e[o].childNodes.length>0&&(d.value?e[o].expand():e[o].collapse())};return E(()=>{f()}),{filterText:p,treeRef:r,treeData:v,isExpanded:d,handleTreeSearch:m,filterNode:c,handleNodeClick:C,refreshTree:k,toggleExpand:y}}}),F={class:"left-tree"},K={class:"tree-header"},L={class:"search-box"},$={class:"tree-buttons"};function M(t,i,p,r,v,s){const d=n("ele-Search"),c=n("el-icon"),m=n("el-button"),f=n("el-input"),h=n("ele-Refresh"),C=n("ele-ArrowDown"),k=n("ele-ArrowRight"),y=n("el-tree");return g(),B("div",F,[w("div",K,[w("div",L,[a(f,{modelValue:t.filterText,"onUpdate:modelValue":i[0]||(i[0]=e=>t.filterText=e),placeholder:"输入关键字搜索",clearable:"",class:"tree-filter",onKeyup:R(t.handleTreeSearch,["enter"])},{append:l(()=>[a(m,{onClick:t.handleTreeSearch},{default:l(()=>[a(c,null,{default:l(()=>[a(d)]),_:1})]),_:1},8,["onClick"])]),_:1},8,["modelValue","onKeyup"])]),w("div",$,[a(m,{link:"",onClick:t.refreshTree},{default:l(()=>[a(c,null,{default:l(()=>[a(h)]),_:1}),i[1]||(i[1]=T(" 刷新 "))]),_:1},8,["onClick"]),a(m,{link:"",onClick:t.toggleExpand},{default:l(()=>[a(c,null,{default:l(()=>[t.isExpanded?(g(),N(C,{key:0})):(g(),N(k,{key:1}))]),_:1}),T(" "+S(t.isExpanded?"收缩":"展开"),1)]),_:1},8,["onClick"])])]),a(y,{ref:"treeRef",data:t.treeData,props:{label:"name",children:"children",disabled:e=>e.type==="channel"},"node-key":"id","highlight-current":"",onNodeClick:t.handleNodeClick,"expand-on-click-node":!1,"filter-node-method":t.filterNode},null,8,["data","props","onNodeClick","filter-node-method"])])}const xe=V(x,[["render",M],["__scopeId","data-v-bf79c1fd"]]);export{xe as default};
