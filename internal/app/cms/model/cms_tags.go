// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2023-10-17 14:50:36
// 生成路径: internal/app/cms/model/cms_tags.go
// 生成人：yxf
// desc:CMS标签
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// CmsTagsInfoRes is the golang structure for table cms_tags.
type CmsTagsInfoRes struct {
	gmeta.Meta `orm:"table:cms_tags"`
	Id         uint        `orm:"id,primary" json:"id" dc:"ID"`          // ID
	Name       string      `orm:"name" json:"name" dc:"标签名称"`            // 标签名称
	CreatedAt  *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建日期"` // 创建日期
	UpdatedAt  *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新日期"` // 更新日期
	Sort       int         `orm:"sort" json:"sort" dc:"排序"`              // 排序
}

type CmsTagsListRes struct {
	Id        uint        `json:"id" dc:"ID"`
	Name      string      `json:"name" dc:"标签名称"`
	CreatedAt *gtime.Time `json:"createdAt" dc:"创建日期"`
	Sort      int         `json:"sort" dc:"排序"`
}

// CmsTagsSearchReq 分页请求参数
type CmsTagsSearchReq struct {
	comModel.PageReq
	Name   string `p:"name" dc:"标签名称"` //标签名称
	TagIds []int
}

// CmsTagsSearchRes 列表返回结果
type CmsTagsSearchRes struct {
	comModel.ListRes
	List []*CmsTagsListRes `json:"list"`
}

// CmsTagsAddReq 添加操作请求参数
type CmsTagsAddReq struct {
	Name string `p:"name" v:"required#标签名称不能为空" dc:"标签名称"`
	Sort int    `p:"sort"  dc:"排序"`
}

// CmsTagsEditReq 修改操作请求参数
type CmsTagsEditReq struct {
	Id   uint   `p:"id" v:"required#主键ID不能为空" dc:"ID"`
	Name string `p:"name" v:"required#标签名称不能为空" dc:"标签名称"`
	Sort int    `p:"sort"  dc:"排序"`
}
