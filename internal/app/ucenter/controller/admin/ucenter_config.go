// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2023-03-09 16:48:33
// 生成路径: internal/app/ucenter/controller/ucenter_config.go
// 生成人：yxf
// desc:用户中心配置
// company:云南奇讯科技有限公司
// ==========================================================================

package admin

import (
	"context"
	apiAdmin "github.com/tiger1103/gfast/v3/api/v1/ucenter/admin"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
	"github.com/tiger1103/gfast/v3/internal/app/ucenter/service"
)

type ucenterConfigController struct {
	systemController.BaseController
}

var UCenterConfig = new(ucenterConfigController)

// List 列表
func (c *ucenterConfigController) List(ctx context.Context, req *apiAdmin.UCenterConfigSearchReq) (res *apiAdmin.UCenterConfigSearchRes, err error) {
	res, err = service.UCenterConfig().List(ctx, req)
	return
}

// Get 获取用户中心配置
func (c *ucenterConfigController) Get(ctx context.Context, req *apiAdmin.UCenterConfigGetReq) (res *apiAdmin.UCenterConfigGetRes, err error) {
	res = new(apiAdmin.UCenterConfigGetRes)
	res.UCenterConfigInfoRes, err = service.UCenterConfig().GetById(ctx, req.Id)
	return
}

// Add 添加用户中心配置
func (c *ucenterConfigController) Add(ctx context.Context, req *apiAdmin.UCenterConfigAddReq) (res *apiAdmin.UCenterConfigAddRes, err error) {
	err = service.UCenterConfig().Add(ctx, req)
	return
}

// Edit 修改用户中心配置
func (c *ucenterConfigController) Edit(ctx context.Context, req *apiAdmin.UCenterConfigEditReq) (res *apiAdmin.UCenterConfigEditRes, err error) {
	err = service.UCenterConfig().Edit(ctx, req)
	return
}

// Delete 删除用户中心配置
func (c *ucenterConfigController) Delete(ctx context.Context, req *apiAdmin.UCenterConfigDeleteReq) (res *apiAdmin.UCenterConfigDeleteRes, err error) {
	err = service.UCenterConfig().Delete(ctx, req.Ids)
	return
}
