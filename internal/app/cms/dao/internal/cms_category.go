// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2023-01-31 09:33:48
// 生成路径: internal/app/cms/dao/internal/cms_category.go
// 生成人：yxf
// desc:文章分类
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// CmsCategoryDao is the manager for logic model data accessing and custom defined data operations functions management.
type CmsCategoryDao struct {
	table   string             // Table is the underlying table name of the DAO.
	group   string             // Group is the database configuration group name of current DAO.
	columns CmsCategoryColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// CmsCategoryColumns defines and stores column names for table cms_category.
type CmsCategoryColumns struct {
	Id              string // ID
	Name            string // 名称
	Type            string // 类型
	SeoTitle        string // SEO标题
	Keywords        string // SEO关键字
	Description     string // SEO描述
	CreatedAt       string // 创建日期
	UpdatedAt       string // 更新日期
	DeletedAt       string // 删除日期
	Content         string // 内容
	ParentId        string // 父ID
	ModuleId        string // 模型ID
	Sort            string // 排序
	Template        string // 模板
	ContentTemplate string // 内容模板
	IsInherit       string // 继承模板
	Thumb           string // 缩略图
	Banner          string // 顶部图片
	Status          string // 状态
	PageTemplate    string // 单页模板
	ChannelTemplate string // 单页模板
	JumpUrl         string // 跳转链接
	Alias           string // 别名
	Nav             string //导航
}

var cmsCategoryColumns = CmsCategoryColumns{
	Id:              "id",
	Name:            "name",
	Type:            "type",
	SeoTitle:        "seo_title",
	Keywords:        "keywords",
	Description:     "description",
	CreatedAt:       "created_at",
	UpdatedAt:       "updated_at",
	DeletedAt:       "deleted_at",
	Content:         "content",
	ParentId:        "parent_id",
	ModuleId:        "module_id",
	Sort:            "sort",
	Template:        "template",
	ContentTemplate: "content_template",
	IsInherit:       "is_inherit",
	Thumb:           "thumb",
	Banner:          "banner",
	Status:          "status",
	Alias:           "alias",
}

// NewCmsCategoryDao creates and returns a new DAO object for table data access.
func NewCmsCategoryDao() *CmsCategoryDao {
	return &CmsCategoryDao{
		group:   "default",
		table:   "cms_category",
		columns: cmsCategoryColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *CmsCategoryDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *CmsCategoryDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *CmsCategoryDao) Columns() CmsCategoryColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *CmsCategoryDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *CmsCategoryDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *CmsCategoryDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
