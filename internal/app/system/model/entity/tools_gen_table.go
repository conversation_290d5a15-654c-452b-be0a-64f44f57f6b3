// =================================================================================
// Code generated by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// ToolsGenTable is the golang structure for table tools_gen_table.
type ToolsGenTable struct {
	TableId        int64            `json:"tableId"        description:"编号"`
	TableName      string           `json:"tableName"      description:"表名称"`
	TableComment   string           `json:"tableComment"   description:"表描述"`
	ClassName      string           `json:"className"      description:"实体类名称"`
	TplCategory    string           `json:"tplCategory"    description:"使用的模板（crud单表操作 tree树表操作）"`
	PackageName    string           `json:"packageName"    description:"生成包路径"`
	ModuleName     string           `json:"moduleName"     description:"生成模块名"`
	BusinessName   string           `json:"businessName"   description:"生成业务名"`
	FunctionName   string           `json:"functionName"   description:"生成功能名"`
	FunctionAuthor string           `json:"functionAuthor" description:"生成功能作者"`
	Options        string           `json:"options"        description:"其它生成选项"`
	CreateTime     *gtime.Time      `json:"createTime"     description:"创建时间"`
	UpdateTime     *gtime.Time      `json:"updateTime"     description:"更新时间"`
	Remark         string           `json:"remark"         description:"备注"`
	Overwrite      bool             `json:"overwrite"      description:"是否覆盖原有文件"`
	SortColumn     string           `json:"sortColumn"     description:"排序字段名"`
	SortType       string           `json:"sortType"       description:"排序方式 (asc顺序 desc倒序)"`
	ShowDetail     bool             `json:"showDetail"     description:"是否有查看详情功能"`
	ExcelPort      bool             `json:"excelPort" description:"是否有excel导出功能"`
	ExcelImp       bool             `json:"excelImp" description:"是否有excel导入功能"`
	UseSnowId      bool             `json:"useSnowId" description:"主键是否使用雪花ID"`
	UseVirtual     bool             `json:"useVirtual" orm:"use_virtual" description:"树表是否使用虚拟表"`
	OverwriteInfo  []*OverwriteInfo `json:"overwriteInfo" orm:"overwrite_info" description:"覆盖文件信息"`
	MenuPid        uint             `json:"menuPid" orm:"menu_pid" description:"父级菜单ID"`
}
