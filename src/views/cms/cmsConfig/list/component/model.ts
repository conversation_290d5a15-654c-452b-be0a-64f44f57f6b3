export interface CmsConfigTableColumns {
    id: number;  // ID
    title: string;  // 标签
    name: string;  // 名称
    value: string;  // 内容
    description: string;  // 说明
    type: string;  // 类型
}


export interface CmsConfigInfoData {
    id: number | undefined;        // ID
    title: string | undefined; // 标签
    name: string | undefined; // 名称
    value: string | undefined; // 内容
    description: string | undefined; // 说明
    type: string | undefined; // 类型
    setting: string | undefined; // 设置
}


export interface CmsConfigTableDataState {
    ids: any[];
    tableData: {
        data: Array<CmsConfigTableColumns>;
        total: number;
        loading: boolean;
        param: {
            pageNum: number;
            pageSize: number;
        };
    };
}

export interface CmsConfigEditState {
    loading: boolean;
    isShowDialog: boolean;
    formData: CmsConfigInfoData;
    rules: object;
}
