package cmsVisit

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/api/v1/cms"
	"github.com/tiger1103/gfast/v3/internal/app/cms/dao"
	"github.com/tiger1103/gfast/v3/internal/app/cms/model"
	"github.com/tiger1103/gfast/v3/internal/app/cms/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/cms/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterCmsVisit(New())
}

func New() *sCmsVisit {
	return &sCmsVisit{}
}

type sCmsVisit struct{}

func (s *sCmsVisit) List(ctx context.Context) (res *cms.CmsVisitRes) {
	totalCount, _ := dao.CmsVisit.Ctx(ctx).Fields("SUM(count)").Value()
	homeCount, _ := dao.CmsVisit.Ctx(ctx).Fields("SUM(count)").Where("type", "home").Value()
	categoryCount, _ := dao.CmsVisit.Ctx(ctx).Fields("SUM(count)").Where("type", "category").Value()
	articleCount, _ := dao.CmsVisit.Ctx(ctx).Fields("SUM(count)").Where("type", "article").Value()
	res = &cms.CmsVisitRes{
		TotalCount:    gconv.Int(totalCount),
		HomeCount:     gconv.Int(homeCount),
		CategoryCount: gconv.Int(categoryCount),
		ArticleCount:  gconv.Int(articleCount),
	}
	return
}

func (s *sCmsVisit) Category(ctx context.Context) (resList *cms.CmsVisitCategoryRes, err error) {
	// 初始化返回结果
	resList = new(cms.CmsVisitCategoryRes)
	//先读取分类信息
	err = g.Try(ctx, func(ctx context.Context) {
		categoryList, err := dao.CmsCategory.Ctx(ctx).WithAll().Where("status", "1").OrderDesc("sort").All()
		liberr.ErrIsNil(ctx, err, "获取分类失败")
		//读取分类统计信息
		categoryVisit, err := dao.CmsVisit.Ctx(ctx).Fields("category_id,SUM(count) as count").Where("type", "category").Group("category_id").All()
		liberr.ErrIsNil(ctx, err, "获取分类统计失败")
		categoryVisitMap := make(map[uint]uint)
		for _, v := range categoryVisit.List() {
			categoryVisitMap[gconv.Uint(v["category_id"])] = gconv.Uint(v["count"])
		}

		// 初始化列表
		resList.List = make([]*cms.CmsVisitCategoryListRes, len(categoryList.List()))

		for k, v := range categoryList.List() {
			// v 已经是 map[string]interface{} 类型
			resList.List[k] = &cms.CmsVisitCategoryListRes{
				Id:       gconv.Uint(v["id"]),
				Name:     gconv.String(v["name"]),
				ParentId: gconv.Uint(v["parent_id"]),
				Type:     gconv.String(v["type"]),
				Status:   gconv.String(v["status"]),
				Count:    categoryVisitMap[gconv.Uint(v["id"])],
			}
		}
	})
	return
}

func (s *sCmsVisit) Article(ctx context.Context, req *cms.CmsVistiArticleReq) (listRes *cms.CmsVistiArticleRes, err error) {
	listRes = new(cms.CmsVistiArticleRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.CmsVisit.Ctx(ctx).Where("type", "article")
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "count desc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.CmsVisitArticleListRes
		err = m.WithAll().Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = res
	})
	return
}

func (s *sCmsVisit) Add(ctx context.Context, req *model.CmsVisitAddReq) (err error) {
	res, _ := dao.CmsVisit.Ctx(ctx).Where("type", req.Type).Where("category_id", req.CategoryId).Where("article_id", req.ArticleId).One()
	if res != nil {

		_, err = dao.CmsVisit.Ctx(ctx).Where("id", res["id"]).Update(do.CmsVisit{
			Count: gconv.Int(res["count"]) + 1,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
		return
	} else {
		err = g.Try(ctx, func(ctx context.Context) {
			_, err = dao.CmsVisit.Ctx(ctx).Insert(do.CmsVisit{
				Type:       req.Type,
				CategoryId: req.CategoryId,
				ArticleId:  req.ArticleId,
				Count:      1,
			})
			liberr.ErrIsNil(ctx, err, "添加失败")
		})
	}
	return
}
