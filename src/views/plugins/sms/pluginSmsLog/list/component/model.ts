export interface PluginSmsLogTableColumns {    
    id:number;  // ID    
    smsType:string;  // 短信平台的类型    
    msgType:string;  // 短信的类型    
    templateid:string;  // 模板id    
    mobiles:string;  // 电话号码    
    params:string;  // 模板参数    
    createdAt:string;  // 发送日期    
}


export interface PluginSmsLogInfoData {    
    id:number|undefined;        // ID    
    smsType:string|undefined; // 短信平台的类型    
    msgType:string|undefined; // 短信的类型    
    templateid:string|undefined; // 模板id    
    mobiles:string|undefined; // 电话号码    
    params:string|undefined; // 模板参数    
    result:string|undefined; // 发送结果    
    createdAt:string|undefined; // 发送日期    
    updatedAt:string|undefined; //    
    deletedAt:string|undefined; //    
}


export interface PluginSmsLogTableDataState {
    ids:any[];
    tableData: {
        data: Array<PluginSmsLogTableColumns>;
        total: number;
        loading: boolean;
        param: {
            pageNum: number;
            pageSize: number;            
            smsType: string|undefined;            
            msgType: string|undefined;            
            templateid: string|undefined;            
            mobiles: string|undefined;            
            createdAt: string|undefined;            
            dateRange: string[];
        };
    };
}


export interface PluginSmsLogEditState{
    loading:boolean;
    isShowDialog: boolean;
    formData:PluginSmsLogInfoData;
    rules: object;
}