export interface CmsArticleTableColumns {
    id: number;  // ID
    title: string;  // 标题
    categoryId: number;  // 分类
    moduleId: number;  // 模型
    hits: number;  // 点击数
    flag: string;  // 推荐属性
    commentCount: number;  // 评论数
    status: string;  // 状态
    userId: number;  // 用户ID
    readPermissions: string;  // 阅读权限
    originTitle: string;  // 来源名称
    createdAt: string;  // 创建日期
    isJump: string; // 是否跳转链接
}

export interface CmsArticleInfoData {
    id: number | undefined;        // ID
    title: string | undefined; // 标题
    subTitle: string | undefined; // 副标题
    writer: string | undefined; // 作者
    summary: string | undefined; // 标题
    seoTitle: string | undefined; // SEO标题
    categoryId: number | undefined; // 分类
    moduleId: number | undefined; // 模型
    keywords: string | undefined; // 关键字
    description: string | undefined; // 描述
    hits: number | undefined; // 点击数
    flag: any[]; // 推荐属性
    commentCount: number | undefined; // 评论数
    thumb: string | undefined; // 缩略图
    template: string | undefined; // 模板
    status: string | undefined; // 状态
    userId: number | undefined; // 用户ID
    readPermissions: string | undefined; // 阅读权限
    originUrl: string | undefined; // 来源地址
    originTitle: string | undefined; // 来源名称
    createdAt: string | undefined; // 创建日期
    updatedAt: string | undefined; // 更新日期
    deletedAt: string | undefined; // 删除日期
    publishedAt: string | undefined; // 发布日期
    content: string | undefined; // 文章内容
    isJump: string | undefined;
    allowComment: string | undefined; // 允许评论
    jumpUrl: string | undefined;
    modulesData: any; // 模型数据
    tags:any[];//文章标签
    weigh: number | undefined; // 权重
    otherCategoryId: number[] | undefined;
}

export interface CmsArticleTableDataState {
    ids: any[];
    modulesList: any[];
    tableData: {
        data: Array<CmsArticleTableColumns>;
        total: number;
        loading: boolean;
        param: {
            pageNum: number;
            pageSize: number;
            title: string | undefined;
            categoryId: number | undefined;
            moduleId: number | undefined;
            flag: string | undefined;
            status: string | undefined;
        };
    };
}

export interface CmsArticleEditState {
    loading: boolean;
    isShowDialog: boolean;
    formData: CmsArticleInfoData;
    rules: object;
}
