import{G as a}from"./index-BSsI7aMT.js";import{d as u,r as i,Y as d,a3 as l,ag as c,c as f,o as m,G as g,J as B,a as p}from"./@vue-C21YZbHS.js";import{a as v}from"./index-BGmsw1a8.js";import"./pinia-DwJf82dV.js";import"./vue-router-BDT9dCQ7.js";import"./js-cookie-Cz0CWeBA.js";import"./@element-plus-_Cc-TEQX.js";import"./nprogress-Scw6VIZr.js";import"./@intlify-D8kAWvSi.js";import"./axios-B62VicFi.js";import"./qs-D41HS0uK.js";import"./side-channel-DytTmCZg.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-DbsE5JW_.js";import"./crypto-js-oM26lpET.js";import"./side-channel-list-C_7Uk4RP.js";import"./side-channel-map-BUokNT8A.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-bSkmVQJL.js";import"./element-plus-CUmVNDWO.js";import"./lodash-es-S0Y0Up6J.js";import"./@vueuse-D_5IXmcI.js";import"./@popperjs-D_chPuIy.js";import"./@ctrl-r5W6hzzQ.js";import"./dayjs-CepoAVPu.js";import"./async-validator-9PlIezaS.js";import"./memoize-one-BdPwpGay.js";import"./normalize-wheel-es-BQoi3Ox2.js";import"./@floating-ui-DwceP2Gb.js";import"./vue-i18n-BkLk9Ti1.js";import"./vue-iNFchrNl.js";import"./vue-clipboard3-Dr8V5xR7.js";import"./clipboard-DzJOpNE6.js";import"./mitt-DJ65BbbF.js";import"./vue-grid-layout-CIPjP0XN.js";import"./vue-simple-uploader-j6sX1TKo.js";import"./vue-ueditor-wrap-CJ5gjsd3.js";const y=u({name:"demoUeditor",components:{GfUeditor:a},setup(){const t=i(!0),o=i(`<P>这是有一个测试内容</P>
    <p><img src="https://yxh-1301841944.cos.ap-chongqing.myqcloud.com/gfast/2022-06-08/timg.jpg"/></p>
    <p style="text-indent: 2em;">在软件工程中，有些主题和写程序并没有直接的关联，但它们为你提供了工具和基础设施 支持，使得软件对每个人都变得更易用。这些主题包括：
文档：通过附带的 rustdoc 生成库文档给用户。
测试：为库创建测试套件，确保库准确地实现了你想要的功能。
基准测试（benchmark）：对功能进行基准测试，保证其运行速度足够快。</p>
    <p style="text-indent: 2em;">Rust 是一门注重安全（safety）、速度（speed）和并发（concurrency）的现代系统编程语言。Rust 通过内存安全来实现以上目标，但不使用垃圾回收机制（garbage collection, GC）。
《通过例子学 Rust》（Rust By Example, RBE）内容由一系列可运行的实例组成，通过这些例子阐明了各种 Rust 的概念和基本库。想获取这些例子外的更多内容，不要忘了安装 Rust 到本地并查阅官方标准库文档。另外为了满足您的好奇心，您还可以查阅本网站的源代码。</p>`),e=r=>{o.value=r};return d(()=>{t.value||(t.value=!0)}),l(()=>{t.value=!1}),{show:t,content:o,setEditContent:e}}}),R=["innerHTML"];function C(t,o,e,r,E,h){const n=c("gf-ueditor");return m(),f("div",null,[t.show?(m(),g(n,{key:0,editorId:"demoEdit01",modelValue:t.content,"onUpdate:modelValue":o[0]||(o[0]=s=>t.content=s)},null,8,["modelValue"])):B("",!0),o[1]||(o[1]=p("h3",null,"同步获取编辑器内容如下：",-1)),p("div",{innerHTML:t.content},null,8,R)])}const vt=v(y,[["render",C]]);export{vt as default};
