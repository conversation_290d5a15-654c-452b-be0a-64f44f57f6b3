<template>
  <!-- 用户中心配置详情抽屉 -->  
  <div class="ucenter-ucenterConfig-detail">
    <el-drawer v-model="isShowDialog" size="80%" direction="ltr">
      <template #header>
        <h4>用户中心配置详情</h4>
      </template>
      <el-form ref="formRef" :model="formData" label-width="100px">          
        <el-row>        
          <el-col :span="12">          
            <el-form-item label="ID">{{ formData.id }}</el-form-item>          
          </el-col>        
          <el-col :span="12">          
            <el-form-item label="名称">{{ formData.name }}</el-form-item>          
          </el-col>        
          <el-col :span="12">          
            <el-form-item label="值">{{ formData.value }}</el-form-item>          
          </el-col>        
          <el-col :span="12">          
            <el-form-item label="值类型">{{ formData.type }}</el-form-item>          
          </el-col>        
          <el-col :span="12">          
            <el-form-item label="提示信息">{{ formData.tips }}</el-form-item>          
          </el-col>      
        </el-row>      
      </el-form>
    </el-drawer>
  </div>
</template>
<script lang="ts">
  import { reactive, toRefs, defineComponent,ref,unref,getCurrentInstance,computed } from 'vue';
  import {ElMessageBox, ElMessage, FormInstance,UploadProps} from 'element-plus';  
  import {
    listUcenterConfig,
    getUcenterConfig,
    delUcenterConfig,
    addUcenterConfig,
    updateUcenterConfig,    
  } from "/@/api/ucenter/ucenterConfig";  
  import {
    UcenterConfigTableColumns,
    UcenterConfigInfoData,
    UcenterConfigTableDataState,
    UcenterConfigEditState,    
  } from "/@/views/ucenter/ucenterConfig/list/component/model"
  export default defineComponent({
    name:"apiV1UcenterUcenterConfigDetail",
    components:{      
    },
    props:{      
    },
    setup(props,{emit}) {      
      const {proxy} = <any>getCurrentInstance()
      const formRef = ref<HTMLElement | null>(null);
      const menuRef = ref();      
      const state = reactive<UcenterConfigEditState>({
        loading:false,
        isShowDialog: false,
        formData: {          
          id: undefined,          
          name: undefined,          
          value: undefined,          
          type: undefined,          
          tips: undefined,          
        },
        // 表单校验
        rules: {          
          name : [
              { required: true, message: "名称不能为空", trigger: "blur" }
          ],          
        }
      });
        // 打开弹窗
        const openDialog = (row?: UcenterConfigInfoData) => {
          resetForm();
          if(row) {
            getUcenterConfig(row.id!).then((res:any)=>{
              const data = res.data;              
              state.formData = data;              
            })
          }
          state.isShowDialog = true;
        };
        // 关闭弹窗
        const closeDialog = () => {
          state.isShowDialog = false;
        };
        // 取消
        const onCancel = () => {
          closeDialog();
        };
        const resetForm = ()=>{
          state.formData = {            
            id: undefined,            
            name: undefined,            
            value: undefined,            
            type: undefined,            
            tips: undefined,            
          }
        };        
        return {
          proxy,
          openDialog,
          closeDialog,
          onCancel,
          menuRef,
          formRef,          
          ...toRefs(state),
        };
      }
  })
</script>
<style scoped>  
  .ucenter-ucenterConfig-detail :deep(.el-form-item--large .el-form-item__label){
    font-weight: bolder;
  }
  .pic-block{
    margin-right: 8px;
  }
  .file-block{
    width: 100%;
    border: 1px solid var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
    margin-bottom: 5px;
    padding: 3px 6px;
  }
  .ml-2{margin-right: 5px;}
</style>