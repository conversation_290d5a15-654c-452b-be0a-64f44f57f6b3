{"name": "vue-next-admin", "version": "2.4.33", "description": "vue3 vite next admin template", "author": "lyt_20201208", "license": "MIT", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint-fix": "eslint --fix --ext .js --ext .jsx --ext .vue src/"}, "dependencies": {"@codemirror/lang-javascript": "^6.1.1", "@codemirror/theme-one-dark": "^6.1.0", "@element-plus/icons-vue": "^2.3.1", "axios": "1.7.4", "codemirror": "^6.0.1", "countup.js": "^2.8.0", "cropperjs": "^1.6.0", "crypto-js": "^4.2.0", "echarts": "^5.5.0", "echarts-gl": "^2.0.9", "echarts-wordcloud": "^2.1.0", "element-plus": "^2.8.7", "element-plus-table-dragable": "^1.0.0", "js-cookie": "^3.0.5", "jsplumb": "^2.15.6", "lodash": "^4.17.21", "mitt": "^3.0.1", "nprogress": "^0.2.0", "pinia": "^2.1.7", "print-js": "^1.6.0", "qrcodejs2-fixes": "^0.0.2", "qs": "^6.12.0", "screenfull": "^6.0.2", "sortablejs": "^1.15.2", "spark-md5": "^3.0.2", "splitpanes": "^3.1.5", "vue": "^3.4.21", "vue-clipboard3": "^2.0.0", "vue-codemirror": "^6.1.1", "vue-demi": "^0.14.7", "vue-grid-layout": "^3.0.0-beta1", "vue-i18n": "^9.10.2", "vue-router": "^4.3.0", "vue-simple-uploader": "^1.0.0-beta.5", "vue-ueditor-wrap": "^3.0.8"}, "devDependencies": {"@types/node": "^20.11.28", "@types/nprogress": "^0.2.3", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-vue": "^5.0.4", "@vue/compiler-sfc": "^3.4.21", "dotenv": "^16.0.1", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.23.0", "prettier": "^3.2.5", "sass": "^1.80.7", "sass-loader": "^16.0.3", "typescript": "^5.4.2", "vite": "5.4.6", "vite-plugin-cdn-import": "^0.3.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-vue-setup-extend-plus": "^0.1.0", "vue-eslint-parser": "^9.4.1"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "bugs": {"url": "https://gitee.com/lyt-top/vue-next-admin/issues"}, "engines": {"node": ">=16.0.0", "npm": ">=7.0.0"}, "keywords": ["vue", "vue3", "vuejs/vue-next", "element-ui", "element-plus", "vue-next-admin", "next-admin"], "repository": {"type": "git", "url": "https://gitee.com/lyt-top/vue-next-admin.git"}}