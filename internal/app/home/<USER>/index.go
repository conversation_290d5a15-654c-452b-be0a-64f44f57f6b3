/**
 * @Company: 云南奇讯科技有限公司
 * @Author: yxf
 * @Description:
 * @Version: 1.0.0
 * @Date: 2023/2/16 17:07
 */

package controller

import (
	"github.com/gogf/gf/v2/crypto/gmd5"
	"github.com/gogf/gf/v2/encoding/ghtml"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/api/v1/home"
	model2 "github.com/tiger1103/gfast/v3/internal/app/cms/model"
	cmsService "github.com/tiger1103/gfast/v3/internal/app/cms/service"
	"github.com/tiger1103/gfast/v3/internal/app/home/<USER>"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	ucenterService "github.com/tiger1103/gfast/v3/internal/app/ucenter/service"
	"github.com/tiger1103/gfast/v3/library/libCms"
)

type homeIndexController struct{}

var HomeIndexController = new(homeIndexController)

func (c *homeIndexController) Index(r *ghttp.Request) {
	//访问统计
	libCms.RecordVisitLog(r.GetCtx(), "home", 0, 0)
	view := service.CmsTemplate().GetCmsView(r)
	service.HomeCommon().Success(r, view, "index.html")
}

func (c *homeIndexController) List(r *ghttp.Request) {
	var req *home.ArticleSearchReq
	templateFile := "list/list.html"
	view := service.CmsTemplate().GetCmsView(r)
	err := r.Parse(&req)
	if err != nil {
		service.HomeCommon().Error(r, view, err.Error())
	}
	cmsConfigMap, _ := cmsService.CmsConfig().GetCmsConfigMap(r.GetCtx())
	if v, ok := cmsConfigMap["list_template"]; ok {
		templateFile = v
	}
	var cmsCategoryInfo *model2.CmsCategoryInfoRes
	if req.CateId != "" {
		cmsCategoryInfo, _ = cmsService.CmsCategory().GetById(r.GetCtx(), gconv.Uint(req.CateId))
		if cmsCategoryInfo == nil {
			service.HomeCommon().Error(r, view, "栏目不存在")
		}
		switch cmsCategoryInfo.Type {
		case "channel":
			c.Channel(r)
			break
		case "page":
			c.Page(r)
			break
		case "jump":
			r.Response.RedirectTo(cmsCategoryInfo.JumpUrl)
			break
		}
		if cmsCategoryInfo.Template != "" {
			templateFile = "list/" + cmsCategoryInfo.Template
		}
	}
	//访问统计
	libCms.RecordVisitLog(r.GetCtx(), "category", gconv.Int(req.CateId), 0)
	//默认分页参数为page,转换给list
	pageNUm := r.Get("page").Int()
	if pageNUm > 0 {
		req.PageNum = pageNUm
	}
	listRes, _ := service.Article().List(r.GetCtx(), req)
	//分页
	if req.PageSize == 0 {
		req.PageSize = consts.PageSize
	}
	page := r.GetPage(gconv.Int(listRes.Total), req.PageSize)
	page.UrlTemplate = cmsConfigMap["site_url"] + "/list/" + gconv.String(req.CateId) + "/{.page}.html"
	view.Assigns(g.Map{
		"total":        listRes.Total,
		"articleList":  listRes.List,
		"currentPage":  listRes.CurrentPage,
		"keyword":      req.Keywords,
		"categoryInfo": cmsCategoryInfo,
		"flag":         req.Flag,
		"seoTitle":     cmsCategoryInfo.Name,
		"page":         page,
	})
	if cmsCategoryInfo.SeoTitle != "" {
		view.Assign("seoTitle", cmsCategoryInfo.SeoTitle)
	}
	if cmsCategoryInfo.Keywords != "" {
		view.Assign("seoKeywords", cmsCategoryInfo.Keywords)
	}
	if cmsCategoryInfo.Description != "" {
		view.Assign("seoDescription", cmsCategoryInfo.Description)
	}
	service.HomeCommon().Success(r, view, templateFile)
}

func (c *homeIndexController) Show(r *ghttp.Request) {
	id := r.Get("id").Uint64()
	//预览key
	key := r.Get("key").String()
	key2, _ := gmd5.EncryptString("preview_url_" + gconv.String(id))
	id2, _ := g.Redis().Get(r.GetCtx(), "preview:"+key)
	preview := false
	if key == key2 && id == gconv.Uint64(id2) {
		preview = true
	}

	view := service.CmsTemplate().GetCmsView(r)
	templateFile := "content/content.html"
	articleInfo, _ := service.Article().GetById(r.GetCtx(), id, preview)
	if articleInfo == nil {
		service.HomeCommon().Error(r, view, "信息不存在")
	}

	//分类IP访问限制
	ok := libCms.CheckCategoryIpAccess(r.GetCtx(), gconv.Uint(articleInfo.CategoryId))
	if !ok {
		service.HomeCommon().Error(r, service.CmsTemplate().GetCmsView(r), "您所在的IP地址不允许访问")
	}

	cmsConfigMap, _ := cmsService.CmsConfig().GetCmsConfigMap(r.GetCtx())
	if v, ok := cmsConfigMap["content_template"]; ok {
		templateFile = v
	}
	if articleInfo.Category != nil && articleInfo.Category.ContentTemplate != "" {
		templateFile = "content/" + articleInfo.Category.ContentTemplate
	}
	view.Assigns(g.Map{
		"article":      articleInfo,
		"categoryInfo": articleInfo.Category,
		"seoTitle":     articleInfo.Title,
	})
	if articleInfo.SeoTitle != "" {
		view.Assign("seoTitle", articleInfo.SeoTitle)
	}
	if articleInfo.Keywords != "" {
		view.Assign("seoKeywords", articleInfo.Keywords)
	}
	if articleInfo.Description != "" {
		view.Assign("seoDescription", articleInfo.Description)
	}
	// 增加浏览历史
	memberId := ucenterService.Context().GetMemberId(r.GetCtx())
	if memberId > 0 {
		service.History().Add(r.GetCtx(), &home.HistoryAddReq{
			MemberId:  memberId,
			ArticleId: id,
		})
	}
	//访问统计
	libCms.RecordVisitLog(r.GetCtx(), "article", gconv.Int(articleInfo.CategoryId), gconv.Int(id))
	service.HomeCommon().Success(r, view, templateFile)
}

func (c *homeIndexController) Channel(r *ghttp.Request) {
	cateId := r.Get("cateId").Uint()
	view := service.CmsTemplate().GetCmsView(r)
	templateFile := "channel/channel.html"
	cmsConfigMap, _ := cmsService.CmsConfig().GetCmsConfigMap(r.GetCtx())
	cmsCategoryInfo, _ := cmsService.CmsCategory().GetById(r.GetCtx(), cateId)
	if cmsCategoryInfo == nil {
		service.HomeCommon().Error(r, view, "信息不存在")
	}
	if v, ok := cmsConfigMap["channel_template"]; ok {
		templateFile = v
	}
	if cmsCategoryInfo.Template != "" {
		templateFile = "channel/" + cmsCategoryInfo.ChannelTemplate
	}
	view.Assign("categoryInfo", cmsCategoryInfo)
	if cmsCategoryInfo.SeoTitle != "" {
		view.Assign("seoTitle", cmsCategoryInfo.SeoTitle)
	}
	if cmsCategoryInfo.Keywords != "" {
		view.Assign("seoKeywords", cmsCategoryInfo.Keywords)
	}
	if cmsCategoryInfo.Description != "" {
		view.Assign("seoDescription", cmsCategoryInfo.Description)
	}
	service.HomeCommon().Success(r, view, templateFile)
}

func (c *homeIndexController) Page(r *ghttp.Request) {
	cateId := r.Get("cateId").Uint()
	view := service.CmsTemplate().GetCmsView(r)
	templateFile := "page/page.html"
	cmsConfigMap, _ := cmsService.CmsConfig().GetCmsConfigMap(r.GetCtx())
	cmsCategoryInfo, _ := cmsService.CmsCategory().GetById(r.GetCtx(), cateId)
	if cmsCategoryInfo == nil {
		service.HomeCommon().Error(r, view, "信息不存在")
	}
	if v, ok := cmsConfigMap["page_template"]; ok {
		templateFile = v
	}
	if cmsCategoryInfo.Template != "" {
		templateFile = "page/" + cmsCategoryInfo.PageTemplate
	}
	if cmsCategoryInfo.SeoTitle != "" {
		view.Assign("seoTitle", cmsCategoryInfo.SeoTitle)
	}
	if cmsCategoryInfo.Keywords != "" {
		view.Assign("seoKeywords", cmsCategoryInfo.Keywords)
	}
	if cmsCategoryInfo.Description != "" {
		view.Assign("seoDescription", cmsCategoryInfo.Description)
	}
	view.Assign("categoryInfo", cmsCategoryInfo)
	service.HomeCommon().Success(r, view, templateFile)
}

func (c *homeIndexController) Search(r *ghttp.Request) {
	var req *home.ArticleSearchReq
	view := service.CmsTemplate().GetCmsView(r)
	err := r.Parse(&req)
	if err != nil {
		service.HomeCommon().Error(r, view, err.Error())
	}
	listRes, _ := service.Article().List(r.GetCtx(), req)
	view.Assigns(g.Map{
		"total":       listRes.Total,
		"articleList": listRes.List,
		"currentPage": listRes.CurrentPage,
		"keywords":    ghtml.SpecialChars(req.Keywords),
		"flag":        req.Flag,
	})
	templateFile := "search.html"
	service.HomeCommon().Success(r, view, templateFile)
}
