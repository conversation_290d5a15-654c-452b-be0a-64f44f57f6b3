<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
    "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
    <title></title>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8"/>
    <script type="text/javascript" src="../internal.js?aea0c61c"></script>
    <style type="text/css">
        html, body {
            overflow: hidden;
        }

        #specharsTab {
            width: 97%;
            margin: 10px auto;
            zoom: 1;
            position: relative
        }

        .tabbody {
            height: 447px;
        }

        .tabbody span {
            margin: 5px 3px;
            text-align: center;
            display: inline-block;
            width: 40px;
            height: 16px;
            line-height: 16px;
            cursor: pointer;
        }
    </style>
</head>
<body>
<div id="specharsTab">
    <div id="tabHeads" class="tabhead"></div>
    <div id="tabBodys" class="tabbody"></div>
</div>
<script type="text/javascript" src="spechars.js?f474d0a8"></script>
</body>
</html>
