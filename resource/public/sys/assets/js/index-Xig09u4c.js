import{g as S,a as U}from"./index-BGmsw1a8.js";import{_ as L}from"./lodash-BPJNOONf.js";import{a as v,E as P}from"./element-plus-CUmVNDWO.js";import{d as V,f as _,r as x,X as C,k as R,ag as s,G as T,o as k,H as p,P as y,a as c,L as z,M as D}from"./@vue-C21YZbHS.js";const N=V({name:"uploadFile",props:{action:{type:String,default:""},name:{type:String,default:"file"},method:{type:String,default:"post"},multiple:{type:Boolean,default:!0},showFileList:{type:Boolean,default:!0},drag:{type:Boolean,default:!0},accept:{type:String,default:".txt,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.zip,.rar"},disabled:{type:Boolean,default:!1},listType:{type:String,default:"picture-card"},uploadSize:{type:Number,default:200},limit:{type:Number,default:5},modelValue:{type:Array,default:function(){return[]}}},emits:["update:modelValue"],setup(a,{emit:o}){let t=[];const{proxy:f}=_(),r=x(),m=C({token:S()}),n=R({get:()=>{let e=a.modelValue||[];return e.map(l=>(l.url&&(l.url=f.getUpFileUrl(l.url)),l)),t=L.cloneDeep(e),e},set:e=>{o("update:modelValue",e)}}),d=()=>!0,u=()=>{},i=()=>{v.error("最多可上传"+a.limit+"个文件,已超出最大限制数。")},B=(e,l)=>{t=t.filter(E=>{var F,h;return((F=E.raw)==null?void 0:F.uid)!=((h=l.raw)==null?void 0:h.uid)}),e.code===0?t.push({name:e.data.name,url:e.data.path,fullUrl:e.data.fullPath,fileType:e.data.type,size:e.data.size}):v.error(e.message),g()},b=e=>P.confirm(`您确定要删除 ${e.name} ?`,"提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(()=>!0,()=>!1),w=e=>{t.splice(t.findIndex(l=>l.name===e.name),1),g()},g=()=>{n.value=t};return{dataFileList:n,handleSuccess:B,beforeRemove:b,handleRemove:w,beforeUpload:d,handleChange:u,handleExceed:i,handlePreview:e=>{window.open(e.url)},upFileRef:r,stopUpFile:()=>{r.value.abort()},dataParam:m}}}),$={class:"el-upload__tip"};function M(a,o,t,f,r,m){const n=s("ele-UploadFilled"),d=s("el-icon"),u=s("el-upload");return k(),T(u,{"file-list":a.dataFileList,"onUpdate:fileList":o[0]||(o[0]=i=>a.dataFileList=i),class:"upload-demo",action:a.action,multiple:a.multiple,drag:a.drag,"on-remove":a.handleRemove,"before-remove":a.beforeRemove,limit:a.limit,"on-exceed":a.handleExceed,"before-upload":a.beforeUpload,"on-change":a.handleChange,"on-success":a.handleSuccess,data:a.dataParam,"on-preview":a.handlePreview,ref:"upFileRef"},{tip:p(()=>[c("div",$," 请上传"+D(a.uploadSize)+" M 以内 ",1)]),default:p(()=>[y(d,{class:"el-icon--upload"},{default:p(()=>[y(n)]),_:1}),o[1]||(o[1]=c("div",{class:"el-upload__text"},[z(" 拖拽文件至此 或"),c("em",null,"点击上传")],-1))]),_:1},8,["file-list","action","multiple","drag","on-remove","before-remove","limit","on-exceed","before-upload","on-change","on-success","data","on-preview"])}const q=U(N,[["render",M]]);export{q as u};
