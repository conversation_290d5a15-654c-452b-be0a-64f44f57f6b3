import{l as P}from"./cmsAudit-DTT0fkIX.js";import{v as K,o as Q,k as j,T as G,R as H,U as X,B as J,M as W}from"./@element-plus-_Cc-TEQX.js";import Y from"./audit-BmxY1NZ_.js";import Z from"./detail-8TJllOm1.js";import{a as V}from"./element-plus-CUmVNDWO.js";import{d as x,f as tt,r as f,X as et,i as at,h as lt,ad as ot,ag as i,aq as T,c as q,o as _,a as L,I as O,P as t,H as a,a4 as nt,O as it,a6 as st,G as b,D as ut,L as g,M as w,Q as rt}from"./@vue-C21YZbHS.js";import{a as pt}from"./index-BGmsw1a8.js";import"./cmsAuditStep-w5STeaQC.js";import"./lodash-es-S0Y0Up6J.js";import"./@vueuse-D_5IXmcI.js";import"./@popperjs-D_chPuIy.js";import"./@ctrl-r5W6hzzQ.js";import"./dayjs-CepoAVPu.js";import"./@intlify-D8kAWvSi.js";import"./async-validator-9PlIezaS.js";import"./memoize-one-BdPwpGay.js";import"./normalize-wheel-es-BQoi3Ox2.js";import"./@floating-ui-DwceP2Gb.js";import"./pinia-DwJf82dV.js";import"./vue-router-BDT9dCQ7.js";import"./js-cookie-Cz0CWeBA.js";import"./nprogress-Scw6VIZr.js";import"./axios-B62VicFi.js";import"./qs-D41HS0uK.js";import"./side-channel-DytTmCZg.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-DbsE5JW_.js";import"./crypto-js-oM26lpET.js";import"./side-channel-list-C_7Uk4RP.js";import"./side-channel-map-BUokNT8A.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-bSkmVQJL.js";import"./vue-i18n-BkLk9Ti1.js";import"./vue-iNFchrNl.js";import"./vue-clipboard3-Dr8V5xR7.js";import"./clipboard-DzJOpNE6.js";import"./mitt-DJ65BbbF.js";import"./vue-grid-layout-CIPjP0XN.js";import"./vue-simple-uploader-j6sX1TKo.js";import"./vue-ueditor-wrap-CJ5gjsd3.js";const dt=x({name:"RightList",components:{"ele-Search":W,"ele-Plus":J,"ele-Edit":X,"ele-Refresh":H,"ele-DeleteFilled":G,"ele-ArrowDown":j,"ele-ArrowUp":Q,"ele-View":K,ApiV1CmsAuditCmsAuditAudit:Y,ApiV1CmsAuditCmsAuditDetail:Z},props:{currentNode:{type:Object,default:null}},setup(e){const{proxy:n}=tt(),h=f(!1),D=f(!1),E=f("更多"),C=f(),y=f(),v=f(),A=f(!0),B=f(!0),{cms_audit_status:k}=n.useDict("cms_audit_status"),u=et({ids:[],tableData:{data:[],total:0,loading:!1,param:{pageNum:1,pageSize:10}}});at(()=>e.currentNode,l=>{l&&(u.tableData.param.categoryId=l.id.toString(),u.tableData.param.pageNum=1,c())});const d=()=>{c()},m=l=>{l&&(l.resetFields(),e.currentNode&&(u.tableData.param.categoryId=e.currentNode.id.toString()),c())},c=()=>{h.value=!0,P(u.tableData.param).then(l=>{let p=l.data.list??[];p.forEach(r=>{r.nextStepId===0||r.nextStepId==="0"?r._disableAudit=!0:r._disableAudit=!!(r.stepInfo&&r.stepInfo.type==="end"&&r.status==="1")}),u.tableData.data=p,u.tableData.total=l.data.total,h.value=!1})},I=()=>{c(),V.success("刷新成功")},R=()=>{D.value=!D.value,E.value=D.value?"收起":"更多"},s=l=>n.selectDictLabel(k.value,l.status),F=l=>{u.ids=l.map(p=>p.id),A.value=l.length!=1,B.value=!l.length},N=()=>{C.value.openDialog()},S=l=>l?l.nextStepId===0||l.nextStepId==="0"?!0:!!(l.stepInfo&&l.stepInfo.type==="end"&&l.status==="1"):!1,U=l=>{if(l)C.value.openDialog(l);else if(u.ids.length>0){const p=u.tableData.data.find(r=>r.id===u.ids[0]);p?C.value.openDialog(p):V.warning("请选择要审核的记录")}else V.warning("请选择要审核的记录")},$=l=>{l&&l.id?y.value.openDialog(l):V.warning("无法查看详情，数据不完整")};return lt(()=>{d()}),{loading:h,showAll:D,word:E,auditRef:C,detailRef:y,queryRef:v,single:A,multiple:B,cms_audit_status:k,statusFormat:s,cmsAuditList:c,toggleSearch:R,handleSelectionChange:F,handleAdd:N,handleUpdate:U,handleView:$,isAuditDisabled:S,resetQuery:m,refreshList:I,...ot(u)}}}),mt={class:"right-content"},ct={class:"audit-cmsAudit-search mb15"},ft=["href"];function _t(e,n,h,D,E,C){const y=i("el-input"),v=i("el-form-item"),A=i("el-col"),B=i("el-option"),k=i("el-select"),u=i("ele-Search"),d=i("el-icon"),m=i("el-button"),c=i("ele-Refresh"),I=i("el-row"),R=i("el-form"),s=i("el-table-column"),F=i("el-tag"),N=i("ele-View"),S=i("ele-Edit"),U=i("el-table"),$=i("pagination"),l=i("ApiV1CmsAuditCmsAuditAudit"),p=i("ApiV1CmsAuditCmsAuditDetail"),r=T("auth"),z=T("loading");return _(),q("div",mt,[L("div",ct,[t(R,{model:e.tableData.param,ref:"queryRef",inline:!0,"label-width":"100px"},{default:a(()=>[t(I,null,{default:a(()=>[t(A,{span:8,class:"colBlock"},{default:a(()=>[t(v,{label:"文章ID",prop:"articleId"},{default:a(()=>[t(y,{modelValue:e.tableData.param.articleId,"onUpdate:modelValue":n[0]||(n[0]=o=>e.tableData.param.articleId=o),placeholder:"请输入文章ID",clearable:"",onKeyup:nt(e.cmsAuditList,["enter","native"])},null,8,["modelValue","onKeyup"])]),_:1})]),_:1}),t(A,{span:8,class:"colBlock"},{default:a(()=>[t(v,{label:"状态",prop:"status"},{default:a(()=>[t(k,{filterable:"",modelValue:e.tableData.param.status,"onUpdate:modelValue":n[1]||(n[1]=o=>e.tableData.param.status=o),placeholder:"请选择状态",clearable:"",style:{width:"200px"}},{default:a(()=>[(_(!0),q(it,null,st(e.cms_audit_status,o=>(_(),b(B,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(A,{span:8,class:ut(e.showAll?"colNone":"colBlock")},{default:a(()=>[t(v,null,{default:a(()=>[t(m,{type:"primary",onClick:e.cmsAuditList},{default:a(()=>[t(d,null,{default:a(()=>[t(u)]),_:1}),n[5]||(n[5]=g("搜索"))]),_:1},8,["onClick"]),t(m,{onClick:n[2]||(n[2]=o=>e.resetQuery(e.queryRef))},{default:a(()=>[t(d,null,{default:a(()=>[t(c)]),_:1}),n[6]||(n[6]=g("重置"))]),_:1})]),_:1})]),_:1},8,["class"])]),_:1})]),_:1},8,["model"]),t(I,{gutter:10,class:"mb8"},{default:a(()=>[t(A,{span:1.5},{default:a(()=>[t(m,{type:"primary",onClick:e.refreshList},{default:a(()=>[t(d,null,{default:a(()=>[t(c)]),_:1}),n[7]||(n[7]=g("刷新 "))]),_:1},8,["onClick"])]),_:1})]),_:1})]),O((_(),b(U,{data:e.tableData.data,onSelectionChange:e.handleSelectionChange},{default:a(()=>[t(s,{type:"selection",width:"20",align:"center"}),t(s,{label:"ID",align:"center",prop:"id"}),t(s,{label:"上步",align:"center",prop:"stepTitle"}),t(s,{label:"审核员",align:"center",prop:"userInfo.userNickname"}),t(s,{label:"当前",align:"center",prop:"nextStepTitle"}),t(s,{label:"文章",align:"center",prop:"articleInfo.title"},{default:a(o=>[L("a",{href:o.row.previewUrl,target:"_blank"},w(o.row.articleInfo.title),9,ft)]),_:1}),t(s,{label:"栏目",align:"center",prop:"categoryInfo.name"}),t(s,{label:"状态",align:"center",prop:"status"},{default:a(o=>[o.row.status==="1"?(_(),b(F,{key:0,type:"success"},{default:a(()=>[g(w(e.statusFormat(o.row)),1)]),_:2},1024)):(_(),b(F,{key:1,type:"warning"},{default:a(()=>[g(w(e.statusFormat(o.row)),1)]),_:2},1024))]),_:1}),t(s,{label:"审核意见",align:"center",prop:"content"}),t(s,{label:"审核时间",align:"center",prop:"auditedAt",width:"180px"},{default:a(o=>[L("span",null,w(o.row.auditedAt),1)]),_:1}),t(s,{label:"创建时间",align:"center",prop:"createdAt",width:"180px"},{default:a(o=>[L("span",null,w(o.row.createdAt),1)]),_:1}),t(s,{label:"操作",align:"center","class-name":"small-padding",fixed:"right",width:"150px"},{default:a(o=>[t(m,{type:"primary",link:"",onClick:M=>e.handleView(o.row)},{default:a(()=>[t(d,null,{default:a(()=>[t(N)]),_:1}),n[8]||(n[8]=g("详情"))]),_:2},1032,["onClick"]),o.row._disableAudit?(_(),b(m,{key:0,type:"info",link:"",disabled:"",title:"已完成审核流程，无法再次审核"},{default:a(()=>[t(d,null,{default:a(()=>[t(S)]),_:1}),n[9]||(n[9]=g("审核"))]),_:1})):O((_(),b(m,{key:1,type:"primary",link:"",onClick:M=>e.handleUpdate(o.row)},{default:a(()=>[t(d,null,{default:a(()=>[t(S)]),_:1}),n[10]||(n[10]=g("审核"))]),_:2},1032,["onClick"])),[[r,"api/v1/cms/audit/cmsAudit/edit"]])]),_:1})]),_:1},8,["data","onSelectionChange"])),[[z,e.loading]]),O(t($,{total:e.tableData.total,page:e.tableData.param.pageNum,"onUpdate:page":n[3]||(n[3]=o=>e.tableData.param.pageNum=o),limit:e.tableData.param.pageSize,"onUpdate:limit":n[4]||(n[4]=o=>e.tableData.param.pageSize=o),onPagination:e.cmsAuditList},null,8,["total","page","limit","onPagination"]),[[rt,e.tableData.total>0]]),t(l,{ref:"auditRef",statusOptions:e.cms_audit_status,onCmsAuditList:e.cmsAuditList},null,8,["statusOptions","onCmsAuditList"]),t(p,{ref:"detailRef",statusOptions:e.cms_audit_status,onCmsAuditList:e.cmsAuditList},null,8,["statusOptions","onCmsAuditList"])])}const ce=pt(dt,[["render",_t],["__scopeId","data-v-24666f46"]]);export{ce as default};
