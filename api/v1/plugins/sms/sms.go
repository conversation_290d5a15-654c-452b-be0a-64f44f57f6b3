/*
* @desc:短信发送测试
* @company:云南奇讯科技有限公司
* @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><<EMAIL>>
* @Date:   2023/12/5 17:44
 */

package sms

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
)

type SendVerifyReq struct {
	g.Meta `path:"/sendVerify" tags:"插件接口/短信" method:"post" summary:"短信验证码发送"`
	Mobile string `p:"mobile" v:"required#手机号码不能为空"`
}

type SendVerifyRes struct {
	commonApi.EmptyRes
}

type SendContentReq struct {
	g.Meta `path:"/sendContent" tags:"插件接口/短信" method:"post" summary:"短信模板内容发送"`
	commonApi.Author
	Mobile  []string `p:"mobile" v:"required#手机号不能为空"`
	Params  []string `p:"params"`  // 用于替换内容或模板里的参数
	Content string   `p:"content"` // 发送内容
}

type SendContentRes struct {
	commonApi.EmptyRes
}

type CheckVerifyReq struct {
	g.<PERSON>a `path:"/checkVerify" tags:"插件接口/短信" method:"post" summary:"验证码验证"`
	commonApi.Author
	Mobile string `p:"mobile" v:"required#手机号不能为空"`
	Code   string `p:"code" v:"required#验证码不能为空"`
}

type CheckVerifyRes struct {
	commonApi.EmptyRes
}
