<template>
  <div class="cms-article-push">
    <el-dialog v-model="isShowDialog" :close-on-click-modal="false" :destroy-on-close="true" width="600px">
      <template #header>
        <div>文章推送</div>
      </template>
      <div class="push-category-container">
        <div class="push-title">选择目标栏目</div>
        <el-table
          ref="treeTableRef"
          :data="cmsCategoryOptions"
          row-key="id"
          border
          default-expand-all
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          highlight-current-row
          @row-click="handleCategorySelect"
        >

          <el-table-column prop="name" label="栏目名称">
            <template #default="{row}">
              <span :class="{'disabled-row': row.id === formData.sourceCategoryId}">
                {{ row.name }}
                <span v-if="row.id === formData.sourceCategoryId" class="disabled-text">(当前栏目)</span>
              </span>
            </template>
          </el-table-column>
          <el-table-column label="选择" width="100" align="center">
            <template #default="{row}">
              <div v-if="row.type === 'list'" @click.stop="handleCheckboxClick(row)">
                <el-checkbox
                  :model-value="isRowSelected(row)"
                  :disabled="row.id === formData.sourceCategoryId"
                />
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" :loading="loading" @click="onSubmit">确 定</el-button>
          <el-button @click="onCancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { defineComponent, getCurrentInstance, reactive, ref, toRefs, unref } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { listCmsPerCategorys } from "/src/api/cms/cmsCategory";
import { pushCmsArticle } from "/src/api/cms/cmsArticle";

interface PushFormData {
  ids: number[];
  categoryIds: number[];
  sourceCategoryId?: number; // 源栏目 ID
}

interface PushDialogState {
  loading: boolean;
  isShowDialog: boolean;
  formData: PushFormData;
  rules: Record<string, any[]>;
}

export default defineComponent({
  name: "PushDialog",
  setup() {
    const { proxy } = <any>getCurrentInstance();
    const treeTableRef = ref();
    const cmsCategoryOptions = ref<any[]>([]);

    // 当前选中的栏目
    const selectedCategory = ref<any>(null);
    // 选中的栏目 ID 列表
    const selectedCategoryIds = ref<number[]>([]);

    const state = reactive<PushDialogState>({
      loading: false,
      isShowDialog: false,
      formData: {
        ids: [],
        categoryIds: [],
        sourceCategoryId: undefined
      },
      rules: {
        categoryIds: [
          { required: true, message: "请选择目标栏目", trigger: "change" }
        ]
      }
    });

    // 获取栏目选项
    const getCategoryOptions = () => {
      if (!state.formData.sourceCategoryId) {
        // 如果没有源栏目 ID，则不进行查询
        cmsCategoryOptions.value = [];
        return;
      }

      listCmsPerCategorys({
        categoryId: state.formData.sourceCategoryId,
        permission: "push"
      }).then((res: any) => {
        // 处理数据，添加isSelected属性
        const list = res.data.list || [];
        list.forEach((item: any) => {
          item.isSelected = false;
          // 标记与当前分类ID相同的项
          if (item.id === state.formData.sourceCategoryId) {
            item.disabled = true;
          }
        });
        cmsCategoryOptions.value = proxy.handleTree(list, "id", "parentId");
      });
    };



    // 打开弹窗
    const openDialog = (ids: number[], sourceCategoryId?: number) => {
      resetForm();
      state.formData.ids = ids;

      // 设置源栏目 ID
      if (sourceCategoryId) {
        state.formData.sourceCategoryId = sourceCategoryId;
        // 获取栏目选项
        getCategoryOptions();
      } else {
        ElMessage.warning('缺少源栏目 ID，无法获取目标栏目');
      }

      state.isShowDialog = true;
    };

    // 关闭弹窗
    const closeDialog = () => {
      state.isShowDialog = false;
    };

    // 取消
    const onCancel = () => {
      closeDialog();
    };

    // 重置表单
    const resetForm = () => {
      state.formData = {
        ids: [],
        categoryIds: [],
        sourceCategoryId: undefined
      };
      // 清空选中的栏目 ID 列表
      selectedCategoryIds.value = [];
    };

    // 检查行是否被选中
    const isRowSelected = (row: any): boolean => {
      return row && row.type === 'list' && selectedCategoryIds.value.includes(row.id);
    };

    // 切换行选中状态
    const toggleRowSelection = (val: boolean, row: any) => {
      if (!row || row.type !== 'list') return;

      // 如果是当前分类，不允许选择
      if (row.id === state.formData.sourceCategoryId) {
        return;
      }

      if (val) {
        // 添加到选中列表
        if (!selectedCategoryIds.value.includes(row.id)) {
          selectedCategoryIds.value.push(row.id);
          // 设置当前行高亮
          treeTableRef.value?.setCurrentRow(row);
        }
      } else {
        // 从选中列表移除
        const index = selectedCategoryIds.value.indexOf(row.id);
        if (index !== -1) {
          selectedCategoryIds.value.splice(index, 1);
          // 取消当前行高亮
          if (treeTableRef.value?.getCurrentRow()?.id === row.id) {
            treeTableRef.value?.setCurrentRow(null);
          }
        }
      }

      // 更新表单数据
      state.formData.categoryIds = [...selectedCategoryIds.value];
    };

    // 处理栏目选择
    const handleCategorySelect = (row: any) => {
      if (row && row.type === 'list') {
        // 切换选中状态
        const currentlySelected = isRowSelected(row);
        toggleRowSelection(!currentlySelected, row);

        // 调试信息
        console.log(`点击行: ${row.name}, ID: ${row.id}, 当前状态: ${currentlySelected}, 切换为: ${!currentlySelected}`);
        console.log('选中的 ID 列表:', selectedCategoryIds.value);
      }
    };

    // 处理复选框点击
    const handleCheckboxClick = (row: any) => {
      if (row && row.type === 'list') {
        // 切换选中状态
        const currentlySelected = isRowSelected(row);
        toggleRowSelection(!currentlySelected, row);

        // 调试信息
        console.log(`点击复选框: ${row.name}, ID: ${row.id}, 当前状态: ${currentlySelected}, 切换为: ${!currentlySelected}`);
        console.log('选中的 ID 列表:', selectedCategoryIds.value);
      }
    };

    // 提交表单
    const onSubmit = () => {
      state.loading = true;

      // 验证必填字段
      if (!state.formData.categoryIds || state.formData.categoryIds.length === 0) {
        ElMessage.error('请选择目标栏目');
        state.loading = false;
        return;
      }

      // 调用推送API
      const pushData = {
        ids: state.formData.ids,
        categoryIds: state.formData.categoryIds,
        sourceCategoryId: state.formData.sourceCategoryId
      };

      pushCmsArticle(pushData).then(() => {
        ElMessage.success('推送成功');
        closeDialog();
      }).catch(() => {
        ElMessage.error('推送失败');
      }).finally(() => {
        state.loading = false;
      });
    };

    return {
      treeTableRef,
      cmsCategoryOptions,
      selectedCategory,
      selectedCategoryIds,
      isRowSelected,
      toggleRowSelection,
      handleCategorySelect,
      handleCheckboxClick,
      openDialog,
      closeDialog,
      onCancel,
      onSubmit,
      ...toRefs(state)
    };
  }
});
</script>

<style lang="scss" scoped>
.cms-article-push {
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
  }

  .push-category-container {
    padding: 0 20px;

    .push-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
    }

    .el-table {
      margin-bottom: 20px;
      max-height: 400px;
      overflow-y: auto;

      .el-checkbox {
        margin-right: 0;
      }
    }

    .disabled-row {
      color: #999;
      cursor: not-allowed;
    }

    .disabled-text {
      color: #999;
      font-size: 12px;
      margin-left: 5px;
    }
  }
}
</style>
