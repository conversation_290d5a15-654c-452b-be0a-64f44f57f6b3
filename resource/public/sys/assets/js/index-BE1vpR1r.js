import{L as B}from"./cmsVisit-Hye8gtb9.js";import{d as H,f as L,r as f,X as h,i as N,h as k,ag as r,c as I,o as P,P as o,H as a,a as t,M as m}from"./@vue-C21YZbHS.js";import{a as q}from"./index-BGmsw1a8.js";import"./pinia-DwJf82dV.js";import"./vue-router-BDT9dCQ7.js";import"./js-cookie-Cz0CWeBA.js";import"./@element-plus-_Cc-TEQX.js";import"./nprogress-Scw6VIZr.js";import"./@intlify-D8kAWvSi.js";import"./axios-B62VicFi.js";import"./qs-D41HS0uK.js";import"./side-channel-DytTmCZg.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-DbsE5JW_.js";import"./crypto-js-oM26lpET.js";import"./side-channel-list-C_7Uk4RP.js";import"./side-channel-map-BUokNT8A.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-bSkmVQJL.js";import"./element-plus-CUmVNDWO.js";import"./lodash-es-S0Y0Up6J.js";import"./@vueuse-D_5IXmcI.js";import"./@popperjs-D_chPuIy.js";import"./@ctrl-r5W6hzzQ.js";import"./dayjs-CepoAVPu.js";import"./async-validator-9PlIezaS.js";import"./memoize-one-BdPwpGay.js";import"./normalize-wheel-es-BQoi3Ox2.js";import"./@floating-ui-DwceP2Gb.js";import"./vue-i18n-BkLk9Ti1.js";import"./vue-iNFchrNl.js";import"./vue-clipboard3-Dr8V5xR7.js";import"./clipboard-DzJOpNE6.js";import"./mitt-DJ65BbbF.js";import"./vue-grid-layout-CIPjP0XN.js";import"./vue-simple-uploader-j6sX1TKo.js";import"./vue-ueditor-wrap-CJ5gjsd3.js";const E={class:"cms-visit-container"},X={class:"statistics-cards"},j={class:"card-body"},z={class:"card-value"},A={class:"card-icon"},F={class:"card-body"},G={class:"card-value"},J={class:"card-icon"},K={class:"card-body"},O={class:"card-value"},Q={class:"card-icon"},R={class:"card-body"},T={class:"card-value"},U={class:"card-icon"},W=H({name:"cmsVisitStats",__name:"index",props:{type:{type:String,default:""}},emits:["update:loading"],setup(y,{expose:g,emit:w}){const c=y,_=w,{proxy:Y}=L(),u=f(!1),x=f(0),V=f([]),l=h({}),i=h({total:0,home:0,category:0,article:0}),v=()=>{u.value=!0,_("update:loading",!0),c.type?l.type=c.type:l.type=void 0,B(l).then(e=>{V.value=e.data.list||[],x.value=e.data.total||0,e.data&&(i.total=e.data.total||0,i.home=e.data.home||0,i.category=e.data.category||0,i.article=e.data.article||0),u.value=!1,_("update:loading",!1)}).catch(()=>{u.value=!1,_("update:loading",!1)})};return N(()=>c.type,(e,s)=>{e!==s&&(l.pageNum=1,v())},{immediate:!1}),k(()=>{c.type===""&&v()}),g({getList:v}),(e,s)=>{const b=r("ele-View"),n=r("el-icon"),d=r("el-card"),p=r("el-col"),C=r("ele-House"),S=r("ele-Menu"),D=r("ele-Document"),M=r("el-row");return P(),I("div",E,[o(d,{shadow:"hover",class:"mb20"},{default:a(()=>[t("div",X,[o(M,{gutter:20},{default:a(()=>[o(p,{span:6},{default:a(()=>[o(d,{shadow:"hover",class:"statistics-card"},{header:a(()=>s[0]||(s[0]=[t("div",{class:"card-header"},[t("span",null,"总访问量")],-1)])),default:a(()=>[t("div",j,[t("div",z,m(i.total||0),1),t("div",A,[o(n,null,{default:a(()=>[o(b)]),_:1})])])]),_:1})]),_:1}),o(p,{span:6},{default:a(()=>[o(d,{shadow:"hover",class:"statistics-card"},{header:a(()=>s[1]||(s[1]=[t("div",{class:"card-header"},[t("span",null,"首页访问量")],-1)])),default:a(()=>[t("div",F,[t("div",G,m(i.home||0),1),t("div",J,[o(n,null,{default:a(()=>[o(C)]),_:1})])])]),_:1})]),_:1}),o(p,{span:6},{default:a(()=>[o(d,{shadow:"hover",class:"statistics-card"},{header:a(()=>s[2]||(s[2]=[t("div",{class:"card-header"},[t("span",null,"分类访问量")],-1)])),default:a(()=>[t("div",K,[t("div",O,m(i.category||0),1),t("div",Q,[o(n,null,{default:a(()=>[o(S)]),_:1})])])]),_:1})]),_:1}),o(p,{span:6},{default:a(()=>[o(d,{shadow:"hover",class:"statistics-card"},{header:a(()=>s[3]||(s[3]=[t("div",{class:"card-header"},[t("span",null,"文章访问量")],-1)])),default:a(()=>[t("div",R,[t("div",T,m(i.article||0),1),t("div",U,[o(n,null,{default:a(()=>[o(D)]),_:1})])])]),_:1})]),_:1})]),_:1})])]),_:1})])}}}),Qt=q(W,[["__scopeId","data-v-321fe7dd"]]);export{Qt as default};
