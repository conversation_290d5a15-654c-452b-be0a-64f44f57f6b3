// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-04-24 10:40:09
// 生成路径: internal/app/cms/model/cms_flags.go
// 生成人：zhanghui
// desc:推荐属性
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// CmsFlagsInfoRes is the golang structure for table cms_flags.
type CmsFlagsInfoRes struct {
	gmeta.Meta  `orm:"table:cms_flags"`
	Id          uint        `orm:"id,primary" json:"id" dc:"ID"`               // ID
	Name        string      `orm:"name" json:"name" dc:"属性名称"`                 // 属性名称
	Sort        uint        `orm:"sort" json:"sort" dc:"排序"`                   // 排序
	CreatedAt   *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`      // 创建时间
	CategoryIds string      `orm:"category_ids" json:"categoryIds" dc:"栏目ids"` // 栏目ids
	UpdatedAt   *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"`      // 更新时间
	DeletedAt   *gtime.Time `orm:"deleted_at" json:"deletedAt" dc:"删除时间"`      // 删除时间
}

type CmsFlagsListRes struct {
	Id          uint        `json:"id" dc:"ID"`
	Name        string      `json:"name" dc:"属性名称"`
	Sort        uint        `json:"sort" dc:"排序"`
	CreatedAt   *gtime.Time `json:"createdAt" dc:"创建时间"`
	CategoryIds string      `json:"categoryIds" dc:"栏目ids"`
}

// CmsFlagsSearchReq 分页请求参数
type CmsFlagsSearchReq struct {
	comModel.PageReq
	Id          string `p:"id" dc:"ID"`                                                             //ID
	Name        string `p:"name" dc:"属性名称"`                                                         //属性名称
	Sort        string `p:"sort" v:"sort@integer#排序需为整数" dc:"排序"`                                   //排序
	CreatedAt   string `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"` //创建时间
	CategoryIds string `p:"categoryIds" dc:"栏目ids"`                                                 //栏目ids
}

// CmsFlagsSearchRes 列表返回结果
type CmsFlagsSearchRes struct {
	comModel.ListRes
	List []*CmsFlagsListRes `json:"list"`
}

// CmsFlagsAddReq 添加操作请求参数
type CmsFlagsAddReq struct {
	Name        string `p:"name" v:"required#属性名称不能为空" dc:"属性名称"`
	Sort        uint   `p:"sort"  dc:"排序"`
	CategoryIds string `p:"categoryIds"  dc:"栏目ids"`
}

// CmsFlagsEditReq 修改操作请求参数
type CmsFlagsEditReq struct {
	Id          uint   `p:"id" v:"required#主键ID不能为空" dc:"ID"`
	Name        string `p:"name" v:"required#属性名称不能为空" dc:"属性名称"`
	Sort        uint   `p:"sort"  dc:"排序"`
	CategoryIds string `p:"categoryIds"  dc:"栏目ids"`
}
