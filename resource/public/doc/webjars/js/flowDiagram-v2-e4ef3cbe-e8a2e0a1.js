import{p as e,f as o}from"./flowDb-01624e90-32c0e086.js";import{f as t,g as a}from"./styles-4fcf332f-12f03159.js";import{an as s}from"./doc-30bb18f4.js";import"./layout-a867abc3.js";import"./index-892ad7fb-8cfb25e5.js";import"./edges-c959041a-5364e2cd.js";import"./createText-b670c180-011fa2f3.js";import"./svgDraw-b48a99d5-5bec0454.js";import"./line-d2bd5b46.js";import"./array-9f3ba611.js";import"./path-53f90ab3.js";const n={parser:e,db:o,renderer:t,styles:a,init:r=>{r.flowchart||(r.flowchart={}),r.flowchart.arrowMarkerAbsolute=r.arrowMarkerAbsolute,s({flowchart:{arrowMarkerAbsolute:r.arrowMarkerAbsolute}}),t.setConf(r.flowchart),o.clear(),o.setGen("gen-2")}};export{n as diagram};
