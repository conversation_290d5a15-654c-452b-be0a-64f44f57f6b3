import{i as a}from"./countup.js-C1c0q9Jw.js";import{d as x,X as k,h as v,ad as w,n as F,ag as e,c as m,o as p,P as i,H as s,a as t,O as R,a6 as I,G as B,C as u,M as d,D as E,L as A}from"./@vue-C21YZbHS.js";import{a as $}from"./index-BGmsw1a8.js";import"./pinia-DwJf82dV.js";import"./vue-router-BDT9dCQ7.js";import"./js-cookie-Cz0CWeBA.js";import"./@element-plus-_Cc-TEQX.js";import"./nprogress-Scw6VIZr.js";import"./@intlify-D8kAWvSi.js";import"./axios-B62VicFi.js";import"./qs-D41HS0uK.js";import"./side-channel-DytTmCZg.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-DbsE5JW_.js";import"./crypto-js-oM26lpET.js";import"./side-channel-list-C_7Uk4RP.js";import"./side-channel-map-BUokNT8A.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-bSkmVQJL.js";import"./element-plus-CUmVNDWO.js";import"./lodash-es-S0Y0Up6J.js";import"./@vueuse-D_5IXmcI.js";import"./@popperjs-D_chPuIy.js";import"./@ctrl-r5W6hzzQ.js";import"./dayjs-CepoAVPu.js";import"./async-validator-9PlIezaS.js";import"./memoize-one-BdPwpGay.js";import"./normalize-wheel-es-BQoi3Ox2.js";import"./@floating-ui-DwceP2Gb.js";import"./vue-i18n-BkLk9Ti1.js";import"./vue-iNFchrNl.js";import"./vue-clipboard3-Dr8V5xR7.js";import"./clipboard-DzJOpNE6.js";import"./mitt-DJ65BbbF.js";import"./vue-grid-layout-CIPjP0XN.js";import"./vue-simple-uploader-j6sX1TKo.js";import"./vue-ueditor-wrap-CJ5gjsd3.js";const D=x({name:"funCountup",setup(){const r=k({topCardItemRefs:null,topCardItemList:[{title:"今日访问人数",titleNum:"123",tip:"在场人数",tipNum:"911",color:"--el-color-primary",iconColor:"#ffcb47",icon:"iconfont icon-jinridaiban"},{title:"实验室总数",titleNum:"123",tip:"使用中",tipNum:"611",color:"--el-color-success",iconColor:"#70cf41",icon:"iconfont icon-AIshiyanshi"},{title:"申请人数（月）",titleNum:"123",tip:"通过人数",tipNum:"911",color:"--el-color-warning",iconColor:"#dfae64",icon:"iconfont icon-shenqingkaiban"},{title:"销售情况",titleNum:"123",tip:"销售数",tipNum:"911",color:"--el-color-danger",iconColor:"#e56565",icon:"iconfont icon-ditu"}]}),o=()=>{F(()=>{r.topCardItemRefs.forEach(c=>{new a(c.querySelector(".countup-card-item-title-num"),Math.random()*1e4).start(),new a(c.querySelector(".countup-card-item-tip-num"),Math.random()*1e3).start()})})},l=()=>{o()};return v(()=>{o()}),{refreshCurrent:l,...w(r)}}}),L={class:"countup-card-item-title pb3"},M={class:"countup-card-item-tip pb3"},S={class:"flex-warp"},j={class:"flex-warp-item"},q={class:"flex-warp-item-box"};function z(r,o,l,c,V,T){const f=e("el-alert"),_=e("el-col"),C=e("el-row"),h=e("ele-RefreshRight"),b=e("el-icon"),g=e("el-button"),y=e("el-card");return p(),m("div",null,[i(y,{shadow:"hover",header:"数字滚动演示"},{default:s(()=>[i(f,{title:"感谢优秀的 `countup.js`，项目地址：https://github.com/inorganik/countUp.js",type:"success",closable:!1,class:"mb15"}),i(C,{gutter:20},{default:s(()=>[(p(!0),m(R,null,I(r.topCardItemList,(n,N)=>(p(),B(_,{sm:6,class:"mb15",key:N},{default:s(()=>[t("div",{class:"countup-card-item countup-card-item-box",style:u({background:`var(${n.color})`})},[t("div",{class:"countup-card-item-flex",ref_for:!0,ref:"topCardItemRefs"},[t("div",L,d(n.title),1),o[0]||(o[0]=t("div",{class:"countup-card-item-title-num pb6"},null,-1)),t("div",M,d(n.tip),1),o[1]||(o[1]=t("div",{class:"countup-card-item-tip-num"},null,-1))],512),t("i",{class:E(n.icon),style:u({color:n.iconColor})},null,6)],4)]),_:2},1024))),128))]),_:1}),t("div",S,[t("div",j,[t("div",q,[i(g,{type:"primary",size:"default",onClick:r.refreshCurrent},{default:s(()=>[i(b,null,{default:s(()=>[i(h)]),_:1}),o[2]||(o[2]=A(" 重置/刷新数值 "))]),_:1},8,["onClick"])])])])]),_:1})])}const St=$(D,[["render",z],["__scopeId","data-v-cfddb9c3"]]);export{St as default};
