import{g as q,b as I,u as _}from"./ucenterMembers-DT9eS8-f.js";import{a as M}from"./element-plus-CUmVNDWO.js";import{d as L,f as N,r as E,X as h,h as Q,ad as R,u as $,ag as m,aq as T,c as f,o as i,P as a,H as t,a as A,J as P,M as v,O as V,a6 as F,G as y,L as D,I as G}from"./@vue-C21YZbHS.js";import{a as H}from"./index-BGmsw1a8.js";import"./lodash-es-S0Y0Up6J.js";import"./@vueuse-D_5IXmcI.js";import"./@element-plus-_Cc-TEQX.js";import"./@popperjs-D_chPuIy.js";import"./@ctrl-r5W6hzzQ.js";import"./dayjs-CepoAVPu.js";import"./@intlify-D8kAWvSi.js";import"./async-validator-9PlIezaS.js";import"./memoize-one-BdPwpGay.js";import"./normalize-wheel-es-BQoi3Ox2.js";import"./@floating-ui-DwceP2Gb.js";import"./pinia-DwJf82dV.js";import"./vue-router-BDT9dCQ7.js";import"./js-cookie-Cz0CWeBA.js";import"./nprogress-Scw6VIZr.js";import"./axios-B62VicFi.js";import"./qs-D41HS0uK.js";import"./side-channel-DytTmCZg.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-DbsE5JW_.js";import"./crypto-js-oM26lpET.js";import"./side-channel-list-C_7Uk4RP.js";import"./side-channel-map-BUokNT8A.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-bSkmVQJL.js";import"./vue-i18n-BkLk9Ti1.js";import"./vue-iNFchrNl.js";import"./vue-clipboard3-Dr8V5xR7.js";import"./clipboard-DzJOpNE6.js";import"./mitt-DJ65BbbF.js";import"./vue-grid-layout-CIPjP0XN.js";import"./vue-simple-uploader-j6sX1TKo.js";import"./vue-ueditor-wrap-CJ5gjsd3.js";const J=L({name:"apiV1UcenterUcenterMembersEdit",components:{},props:{sexOptions:{type:Array,default:()=>[]},isOpenOptions:{type:Array,default:()=>[]},levelOptions:{type:Array,default:()=>[]},isSecretOptions:{type:Array,default:()=>[]}},setup(o,{emit:l}){const{proxy:S}=N(),k=E(null),C=E(),d=h({loading:!1,isShowDialog:!1,formData:{id:void 0,nickname:void 0,sex:"0",deptId:0,email:void 0,mobile:void 0,username:void 0,password:void 0,wxOpenid:void 0,isOpen:!1,level:void 0,isSecret:!1},rules:{id:[{required:!0,message:"ID不能为空",trigger:"blur"}],username:[{required:!0,message:"用户名不能为空",trigger:"blur"}],mobile:[{required:!0,message:"手机号不能为空",trigger:"blur"}],nickname:[{required:!0,message:"昵称不能为空",trigger:"blur"}],status:[{required:!0,message:"状态不能为空",trigger:"blur"}]}}),n=E([]),u=async()=>{try{const r=await q();Array.isArray(r.data.deps)?n.value=r.data.deps:(console.warn("获取到的部门数据不是数组，初始化为空数组"),n.value=[])}catch(r){console.error("获取部门树失败",r),n.value=[]}},B=r=>{U(),r&&(c(),I(r.id).then(g=>{const s=g.data;s.sex=""+s.sex,s.level=""+s.level,s.isOpen=""+s.isOpen,s.isSecret=""+s.isSecret,d.formData=s})),d.isShowDialog=!0},p=()=>{d.isShowDialog=!1},b=()=>{p()},O=()=>{const r=$(k);r&&r.validate(g=>{g&&(d.loading=!0,_(d.formData).then(()=>{M.success("修改成功"),p(),l("ucenterMembersList")}).finally(()=>{d.loading=!1}))})},U=()=>{d.formData={id:void 0,nickname:void 0,sex:"0",deptId:0,email:void 0,mobile:void 0,username:void 0,password:void 0,wxOpenid:void 0,isOpen:!1,level:void 0,isSecret:!1}},c=()=>{l("getUcenterLevelItemsLevel")};return Q(()=>{u()}),{proxy:S,openDialog:B,closeDialog:p,onCancel:b,onSubmit:O,menuRef:C,formRef:k,getUcenterLevelItemsLevel:c,deptData:n,...R(d)}}}),W={class:"ucenter-ucenterMembers-edit"},X={key:0},j={class:"dialog-footer"};function z(o,l,S,k,C,d){const n=m("el-input"),u=m("el-form-item"),B=m("el-cascader"),p=m("el-radio"),b=m("el-radio-group"),O=m("el-option"),U=m("el-select"),c=m("el-form"),r=m("el-button"),g=m("el-dialog"),s=T("drag");return i(),f("div",W,[a(g,{modelValue:o.isShowDialog,"onUpdate:modelValue":l[10]||(l[10]=e=>o.isShowDialog=e),"close-on-click-modal":!1,"destroy-on-close":!0,width:"769px"},{header:t(()=>[G((i(),f("div",null,[D(v((!o.formData.id||o.formData.id==0?"添加":"修改")+"用户中心"),1)])),[[s,[".ucenter-ucenterMembers-edit .el-dialog",".ucenter-ucenterMembers-edit .el-dialog__header"]]])]),footer:t(()=>[A("div",j,[a(r,{type:"primary",onClick:o.onSubmit},{default:t(()=>l[11]||(l[11]=[D("确 定")])),_:1},8,["onClick"]),a(r,{onClick:o.onCancel},{default:t(()=>l[12]||(l[12]=[D("取 消")])),_:1},8,["onClick"])])]),default:t(()=>[a(c,{ref:"formRef",model:o.formData,rules:o.rules,"label-width":"80px"},{default:t(()=>[a(u,{label:"用户名",prop:"username"},{default:t(()=>[a(n,{modelValue:o.formData.username,"onUpdate:modelValue":l[0]||(l[0]=e=>o.formData.username=e),placeholder:"请输入用户名"},null,8,["modelValue"])]),_:1}),a(u,{label:"昵称",prop:"nickname"},{default:t(()=>[a(n,{modelValue:o.formData.nickname,"onUpdate:modelValue":l[1]||(l[1]=e=>o.formData.nickname=e),placeholder:"请输入昵称"},null,8,["modelValue"])]),_:1}),a(u,{label:"部门",prop:"deptId"},{default:t(()=>[a(B,{options:o.deptData,props:{checkStrictly:!0,emitPath:!1,value:"deptId",label:"deptName"},placeholder:"请选择部门",clearable:"",class:"w100",modelValue:o.formData.deptId,"onUpdate:modelValue":l[2]||(l[2]=e=>o.formData.deptId=e)},{default:t(({node:e,data:w})=>[A("span",null,v(w.deptName),1),e.isLeaf?P("",!0):(i(),f("span",X," ("+v(w.children.length)+") ",1))]),_:1},8,["options","modelValue"])]),_:1}),a(u,{label:"性别",prop:"sex"},{default:t(()=>[a(b,{modelValue:o.formData.sex,"onUpdate:modelValue":l[3]||(l[3]=e=>o.formData.sex=e)},{default:t(()=>[(i(!0),f(V,null,F(o.sexOptions,e=>(i(),y(p,{key:e.value,label:e.value},{default:t(()=>[D(v(e.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(u,{label:"邮箱",prop:"email"},{default:t(()=>[a(n,{modelValue:o.formData.email,"onUpdate:modelValue":l[4]||(l[4]=e=>o.formData.email=e),placeholder:"请输入邮箱"},null,8,["modelValue"])]),_:1}),a(u,{label:"手机号",prop:"mobile"},{default:t(()=>[a(n,{modelValue:o.formData.mobile,"onUpdate:modelValue":l[5]||(l[5]=e=>o.formData.mobile=e),placeholder:"请输入手机号"},null,8,["modelValue"])]),_:1}),a(u,{label:"是否激活",prop:"isOpen"},{default:t(()=>[a(b,{modelValue:o.formData.isOpen,"onUpdate:modelValue":l[6]||(l[6]=e=>o.formData.isOpen=e)},{default:t(()=>[(i(!0),f(V,null,F(o.isOpenOptions,e=>(i(),y(p,{key:e.value,label:e.value},{default:t(()=>[D(v(e.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(u,{label:"是否保密",prop:"isSecret"},{default:t(()=>[a(b,{modelValue:o.formData.isSecret,"onUpdate:modelValue":l[7]||(l[7]=e=>o.formData.isSecret=e)},{default:t(()=>[(i(!0),f(V,null,F(o.isSecretOptions,e=>(i(),y(p,{key:e.value,label:e.value},{default:t(()=>[D(v(e.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(u,{label:"QQ号",prop:"qq"},{default:t(()=>[a(n,{modelValue:o.formData.qq,"onUpdate:modelValue":l[8]||(l[8]=e=>o.formData.qq=e),placeholder:"请输入QQ号"},null,8,["modelValue"])]),_:1}),a(u,{label:"用户等级",prop:"level"},{default:t(()=>[a(U,{modelValue:o.formData.level,"onUpdate:modelValue":l[9]||(l[9]=e=>o.formData.level=e),placeholder:"请选择用户等级",onClick:o.getUcenterLevelItemsLevel},{default:t(()=>[(i(!0),f(V,null,F(o.levelOptions,e=>(i(),y(O,{key:e.key,label:e.value,value:e.key},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onClick"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}const He=H(J,[["render",z]]);export{He as default};
