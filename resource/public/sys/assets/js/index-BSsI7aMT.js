import{q as d,g as n,a as c}from"./index-BGmsw1a8.js";import{d as m,X as u,k as p,ag as f,c as g,o as h,P as b}from"./@vue-C21YZbHS.js";const y=["fullscreen","source","|","undo","redo","|","bold","italic","underline","fontborder","strikethrough","superscript","subscript","removeformat","formatmatch","autotypeset","blockquote","pasteplain","|","forecolor","backcolor","insertorderedlist","insertunorderedlist","selectall","cleardoc","|","rowspacingtop","rowspacingbottom","lineheight","|","customstyle","paragraph","fontfamily","fontsize","|","directionalityltr","directionalityrtl","indent","|","justifyleft","justifycenter","justifyright","justifyjustify","|","touppercase","tolowercase","|","link","unlink","anchor","|","imagenone","imageleft","imageright","imagecenter","|","simpleupload","insertimage","emotion","scrawl","insertvideo","attachment","insertframe","insertcode","pagebreak","template","background","formula","|","horizontal","date","time","spechars","wordimage","|","inserttable","deletetable","insertparagraphbeforetable","insertrow","deleterow","insertcol","deletecol","mergecells","mergeright","mergedown","splittocells","splittorows","splittocols","|","print","preview","searchreplace","help"],E=["source","undo","redo","bold","italic","underline","strikethrough","superscript","subscript","autotypeset","blockquote","pasteplain","forecolor","backcolor","insertorderedlist","insertunorderedlist","selectall","cleardoc","rowspacingtop","rowspacingbottom","lineheight","paragraph","fontfamily","fontsize","indent","justifyleft","justifycenter","justifyright","link","unlink","imagenone","imageleft","imageright","imagecenter","simpleupload","insertimage","emotion","insertvideo","attachment","insertframe","horizontal","wordimage","inserttable","deletetable","insertparagraphbeforetable"],I=["bold","italic","underline","strikethrough","removeformat","blockquote","lineheight","fontfamily","fontsize","forecolor","justifyleft","justifycenter","link","unlink","imagenone","imageleft","imageright","imagecenter","simpleupload","horizontal"],k=m({name:"GfUeditor",props:{editorId:{type:String,default:"vueEditorId01"},modelValue:{type:String,default:""},ueditorConfig:{type:Object,default(){return{initialFrameWidth:"100%",initialFrameHeight:400,maximumWords:5e3,topOffset:80,zIndex:2020,autoHeightEnabled:!1}}},toolBars:{type:Array,default(){return y}}},setup(e,{emit:t}){const o=Object.assign({elementPathEnabled:!1,maximumWords:1e4,headers:{Authorization:"Bearer "+n()},toolbars:[e.toolBars],UEDITOR_HOME_URL:"/sys/js/ueditor/",serverUrl:d+"api/v1/system/uEditor/action?token="+encodeURIComponent(n())},e.ueditorConfig),r=u(o);return{html:p({get:()=>e.modelValue,set:i=>{t("setEditContent",i)}}),editorConfig:r}},unmounted(){var e;(e=window.document.getElementById(this.editorId))==null||e.remove()}}),w={class:"ue-content"};function j(e,t,o,r,a,i){const s=f("vue-ueditor-wrap");return h(),g("div",w,[b(s,{modelValue:e.html,"onUpdate:modelValue":t[0]||(t[0]=l=>e.html=l),config:e.editorConfig,"editor-id":e.editorId,editorDependencies:["ueditor.config.js","ueditor.all.js"]},null,8,["modelValue","config","editor-id"])])}const z=c(k,[["render",j],["__scopeId","data-v-9e745355"]]);export{z as G,E as c,I as m};
