// ==========================================================================
// GFast自动生成router操作代码。
// 生成日期：2023-03-02 08:45:44
// 生成路径: internal/app/cms/router/cms_ad_position.go
// 生成人：yxf
// desc:广告位置
// company:云南奇讯科技有限公司
// ==========================================================================

package router

import (
	"context"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/internal/app/cms/controller"
)

func (router *Router) BindCmsAdPositionController(ctx context.Context, group *ghttp.RouterGroup) {
	group.Group("/cmsAdPosition", func(group *ghttp.RouterGroup) {
		group.Bind(
			controller.CmsAdPosition,
		)
	})
}
