import{a as s}from"./element-plus-CUmVNDWO.js";import{d as $,r as x,X as N,ad as T,ag as t,G as u,o as r,H as l,P as i,c as m,O as w,a6 as y,J as c,a as d,M as z,L as h}from"./@vue-C21YZbHS.js";import{a as L}from"./index-BGmsw1a8.js";import"./lodash-es-S0Y0Up6J.js";import"./@vueuse-D_5IXmcI.js";import"./@element-plus-_Cc-TEQX.js";import"./@popperjs-D_chPuIy.js";import"./@ctrl-r5W6hzzQ.js";import"./dayjs-CepoAVPu.js";import"./@intlify-D8kAWvSi.js";import"./async-validator-9PlIezaS.js";import"./memoize-one-BdPwpGay.js";import"./normalize-wheel-es-BQoi3Ox2.js";import"./@floating-ui-DwceP2Gb.js";import"./pinia-DwJf82dV.js";import"./vue-router-BDT9dCQ7.js";import"./js-cookie-Cz0CWeBA.js";import"./nprogress-Scw6VIZr.js";import"./axios-B62VicFi.js";import"./qs-D41HS0uK.js";import"./side-channel-DytTmCZg.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-DbsE5JW_.js";import"./crypto-js-oM26lpET.js";import"./side-channel-list-C_7Uk4RP.js";import"./side-channel-map-BUokNT8A.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-bSkmVQJL.js";import"./vue-i18n-BkLk9Ti1.js";import"./vue-iNFchrNl.js";import"./vue-clipboard3-Dr8V5xR7.js";import"./clipboard-DzJOpNE6.js";import"./mitt-DJ65BbbF.js";import"./vue-grid-layout-CIPjP0XN.js";import"./vue-simple-uploader-j6sX1TKo.js";import"./vue-ueditor-wrap-CJ5gjsd3.js";const M=$({name:"pagesTableRules",setup(){const p=x(),o=N({tableData:{data:[],header:[{prop:"a1",width:"",label:"一级分类",isRequired:!0,type:"select"},{prop:"a2",width:"",label:"二级分类",isRequired:!0,type:"select"},{prop:"a3",width:"",label:"三级分类",isRequired:!0,type:"select"},{prop:"a4",width:"",label:"四级分类",isRequired:!0,type:"date"},{prop:"a5",width:"",label:"五级分类",isRequired:!0,type:"input"},{prop:"a6",width:"",label:"六级分类",isTooltip:!0,type:"dialog"},{prop:"a7",width:"",label:"演示级分类",type:"input"},{prop:"a8",width:"",label:"颜色是分类",type:"input"}],option:[{value:"选项1",label:"黄金糕"},{value:"选项2",label:"双皮奶"},{value:"选项3",label:"蚵仔煎"}]}});return{onValidate:()=>{if(o.tableData.data.length<=0)return s.warning("请先点击增加一行");p.value.validate(_=>{if(!_)return s.warning("表格项必填未填");s.success("全部验证通过")})},onAddRow:()=>{o.tableData.data.push({a1:"",a2:"",a3:"",a4:"",a5:"",a6:"",a7:"",a8:""})},tableRulesRef:p,...T(o)}}}),G={key:0,class:"color-danger"},H={class:"pl5"},J={class:"flex-margin"};function O(p,o,V,k,_,P){const g=t("el-tooltip"),R=t("el-option"),C=t("el-select"),B=t("el-date-picker"),f=t("el-input"),v=t("el-form-item"),D=t("el-table-column"),F=t("el-table"),q=t("el-form"),b=t("el-button"),E=t("el-row"),A=t("el-card");return r(),u(A,{shadow:"hover",header:"表单表格验证"},{default:l(()=>[i(q,{ref:"tableRulesRef",model:p.tableData,size:"default"},{default:l(()=>[i(F,{data:p.tableData.data,border:"",class:"module-table-uncollected"},{default:l(()=>[(r(!0),m(w,null,y(p.tableData.header,(e,U)=>(r(),u(D,{key:U,"show-overflow-tooltip":"",prop:e.prop,width:e.width,label:e.label},{header:l(()=>[e.isRequired?(r(),m("span",G,"*")):c("",!0),d("span",H,z(e.label),1),e.isTooltip?(r(),u(g,{key:1,effect:"dark",content:"这是tooltip",placement:"top"},{default:l(()=>o[0]||(o[0]=[d("i",{class:"iconfont icon-quanxian"},null,-1)])),_:1})):c("",!0)]),default:l(n=>[i(v,{prop:`data.${n.$index}.${e.prop}`,rules:[{required:e.isRequired,message:"不能为空",trigger:`${e.type}`=="input"?"blur":"change"}]},{default:l(()=>[e.type==="select"?(r(),u(C,{key:0,modelValue:n.row[e.prop],"onUpdate:modelValue":a=>n.row[e.prop]=a,placeholder:"请选择"},{default:l(()=>[(r(!0),m(w,null,y(p.tableData.option,a=>(r(),u(R,{key:a.id,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])):e.type==="date"?(r(),u(B,{key:1,modelValue:n.row[e.prop],"onUpdate:modelValue":a=>n.row[e.prop]=a,type:"date",placeholder:"选择日期",style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue"])):e.type==="input"?(r(),u(f,{key:2,modelValue:n.row[e.prop],"onUpdate:modelValue":a=>n.row[e.prop]=a,placeholder:"请输入内容"},null,8,["modelValue","onUpdate:modelValue"])):e.type==="dialog"?(r(),u(f,{key:3,modelValue:n.row[e.prop],"onUpdate:modelValue":a=>n.row[e.prop]=a,readonly:"",placeholder:"请输入内容"},{suffix:l(()=>o[1]||(o[1]=[d("i",{class:"iconfont icon-shouye_dongtaihui"},null,-1)])),_:2},1032,["modelValue","onUpdate:modelValue"])):c("",!0)]),_:2},1032,["prop","rules"])]),_:2},1032,["prop","width","label"]))),128))]),_:1},8,["data"])]),_:1},8,["model"]),i(E,{class:"flex mt15"},{default:l(()=>[d("div",J,[i(b,{size:"default",type:"success",onClick:p.onValidate},{default:l(()=>o[2]||(o[2]=[h("表格验证")])),_:1},8,["onClick"]),i(b,{size:"default",type:"primary",onClick:p.onAddRow},{default:l(()=>o[3]||(o[3]=[h("新增一行")])),_:1},8,["onClick"])])]),_:1})]),_:1})}const Le=L(M,[["render",O]]);export{Le as default};
