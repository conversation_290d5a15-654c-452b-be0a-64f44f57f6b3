import request from '/@/utils/request'
// 查询文章审核列表
export function listCmsAudit(query:object) {
  return request({
    url: '/api/v1/cms/audit/cmsAudit/list',
    method: 'get',
    params: query
  })
}
// 查询文章审核详细
export function getCmsAudit(id:number) {
  return request({
    url: '/api/v1/cms/audit/cmsAudit/get',
    method: 'get',
    params: {
      id: id.toString()
    }
  })
}

// 文章审核
export function auditCmsAudit(data:object) {
  return request({
    url: '/api/v1/cms/audit/cmsAudit/audit',
    method: 'put',
    data: data
  })
}

