<template>
  <div class="ucenter-ucenterConfig-add">
    <!-- 添加或修改用户中心配置对话框 -->
    <el-dialog v-model="isShowDialog" width="769px" :close-on-click-modal="false" :destroy-on-close="true">
      <template #header>
        <div v-drag="['.ucenter-ucenterConfig-add .el-dialog', '.ucenter-ucenterConfig-add .el-dialog__header']">添加用户中心配置</div>
      </template>
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="90px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="值" prop="value">
          <el-input type="textarea" :rows="10" v-model="formData.value" placeholder="请输入值" />
        </el-form-item>
        <el-form-item label="值类型" prop="type">
          <el-select v-model="formData.type" placeholder="请选择值类型" >
            <el-option label="请选择字典生成" value="" />
          </el-select>
        </el-form-item>
        <el-form-item label="提示信息" prop="tips">
          <el-input v-model="formData.tips" placeholder="请输入提示信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="onSubmit">确 定</el-button>
          <el-button @click="onCancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts">
import { reactive, toRefs, defineComponent,ref,unref,getCurrentInstance } from 'vue';
import {ElMessageBox, ElMessage, FormInstance,UploadProps} from 'element-plus';
import {
  listUcenterConfig,
  getUcenterConfig,
  delUcenterConfig,
  addUcenterConfig,
  updateUcenterConfig,
} from "/@/api/ucenter/ucenterConfig";
import {
  UcenterConfigTableColumns,
  UcenterConfigInfoData,
  UcenterConfigTableDataState,
  UcenterConfigEditState,
} from "/@/views/ucenter/ucenterConfig/list/component/model"
export default defineComponent({
  name:"apiV1UcenterUcenterConfigEdit",
  components:{
  },
  props:{
  },
  setup(props,{emit}) {
    const {proxy} = <any>getCurrentInstance()
    const formRef = ref<HTMLElement | null>(null);
    const menuRef = ref();
    const state = reactive<UcenterConfigEditState>({
      loading:false,
      isShowDialog: false,
      formData: {
        id: undefined,
        name: undefined,
        value: undefined,
        type: undefined,
        tips: undefined,
      },
      // 表单校验
      rules: {
        name : [
            { required: true, message: "名称不能为空", trigger: "blur" }
        ],
      }
    });
    // 打开弹窗
    const openDialog = () => {
      resetForm();
      state.isShowDialog = true;
    };
    // 关闭弹窗
    const closeDialog = () => {
      state.isShowDialog = false;
    };
    // 取消
    const onCancel = () => {
      closeDialog();
    };
    // 提交
    const onSubmit = () => {
      const formWrap = unref(formRef) as any;
      if (!formWrap) return;
      formWrap.validate((valid: boolean) => {
        if (valid) {
          state.loading = true;
          //添加
          addUcenterConfig(state.formData).then(()=>{
              ElMessage.success('添加成功');
              closeDialog(); // 关闭弹窗
              emit('ucenterConfigList')
            }).finally(()=>{
              state.loading = false;
            })
        }
      });
    };
    const resetForm = ()=>{
      state.formData = {
        id: undefined,
        name: undefined,
        value: undefined,
        type: undefined,
        tips: undefined,
      }
    };
    return {
      proxy,
      openDialog,
      closeDialog,
      onCancel,
      onSubmit,
      menuRef,
      formRef,
      ...toRefs(state),
    };
  }
})
</script>
<style scoped>
</style>
