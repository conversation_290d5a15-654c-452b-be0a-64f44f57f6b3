<template>
  <div class="system-modulesField-edit">
    <!-- 添加或修改模型字段对话框 -->
    <el-dialog v-model="isShowDialog" :close-on-click-modal="false" :destroy-on-close="true" width="769px">
      <template #header>
        <div v-drag="['.system-modulesField-edit .el-dialog', '.system-modulesField-edit .el-dialog__header']">
          {{ (!formData.id || formData.id == 0 ? '添加' : '修改') + '模型字段' }}
        </div>
      </template>
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="90px" size="default">
        <el-form-item label="模型名称">
          <el-input :value="modulesInfo?.name" disabled></el-input>
        </el-form-item>
        <el-form-item label="字段名称" prop="label">
          <el-input v-model="formData.label" placeholder="请输入字段名称"/>
        </el-form-item>
        <el-form-item label="调用名称" prop="name">
          <el-input v-model="formData.name" placeholder="只能输入字母、数字或下划线"/>
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select v-model="formData.type" placeholder="请选择类型" value-key="value">
            <el-option v-for="item in fieldOptions" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="内容长度" prop="fieldLength" v-if="['text','select','radio','checkbox'].includes(formData.type)">
          <el-input v-model="formData.fieldLength" placeholder="字段内容的最大长度"/>
        </el-form-item>
        <el-form-item v-if="['checkbox','radio','select'].includes(formData.type)" prop="options">
          <template #label>
            <el-tooltip content="选项格式：label:value,label:value,label:value" effect="dark" placement="right">
              <span>选项数据
                <el-icon>
                <ele-QuestionFilled/>
              </el-icon>
              </span>
            </el-tooltip>
          </template>
          <el-input v-model="formData.options" placeholder="请输入选项数据，数据格式：label:value,label:value,label:value" type="textarea"/>
        </el-form-item>
        <el-form-item label="默认值" prop="default">
          <el-input v-model="formData.default" placeholder="请输入默认值"/>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="formData.description" placeholder="请输入字段描述"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="onSubmit">确 定</el-button>
          <el-button @click="onCancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts">
import {defineComponent, getCurrentInstance, reactive, ref, toRefs, unref} from "vue";
import {ElMessage} from "element-plus";
import {addModulesField, getModulesField, updateModulesField} from "/@/api/system/modulesField";
import {ModulesFieldEditState, ModulesFieldInfoData, modulesInfoFieldOptions} from "/@/views/system/modulesField/list/component/model";
import {getModulesInfo} from "/@/api/system/modulesInfo";

export default defineComponent({
  name: "apiV1SystemModulesFieldEdit",
  components: {},
  props: {
    moduleId: {
      type: Number,
      default: 0
    }
  },
  setup(props, {emit}) {
    const {proxy} = <any>getCurrentInstance();
    const formRef = ref<HTMLElement | null>(null);
    const menuRef = ref();
    const modulesInfo = ref();
    const state = reactive<ModulesFieldEditState>({
      loading: false,
      isShowDialog: false,
      formData: {
        id: undefined,
        label: undefined,
        name: "",
        type: undefined,
        description: undefined,
        default: undefined,
        sort: undefined,
        options: undefined,
        fieldLength: undefined,
        moduleId: props.moduleId,
        content:""
      },
      // 表单校验
      rules: {
        label: [
          {required: true, message: "字段名称不能为空", trigger: "blur"}
        ],
        name: [
          {required: true, message: "调用名称不能为空", trigger: "blur"}
        ]
      }
    });
    // 打开弹窗
    const openDialog = (row?: ModulesFieldInfoData) => {
      resetForm();
      getModulesInfo(props.moduleId).then((res: any) => {
        modulesInfo.value = res.data;
      });
      if (row) {
        getModulesField(row.id!).then((res: any) => {
          const data = res.data;
          state.formData = data;
        });
      }
      state.isShowDialog = true;
    };
    // 关闭弹窗
    const closeDialog = () => {
      state.isShowDialog = false;
    };
    // 取消
    const onCancel = () => {
      closeDialog();
    };
    // 提交
    const onSubmit = () => {
      const formWrap = unref(formRef) as any;
      if (!formWrap) return;
      formWrap.validate((valid: boolean) => {
        if (valid) {
          state.loading = true;
          if (!state.formData.id || state.formData.id === 0) {
            //添加
            addModulesField(state.formData).then(() => {
              ElMessage.success("添加成功");
              closeDialog(); // 关闭弹窗
              emit("modulesFieldList");
            }).finally(() => {
              state.loading = false;
            });
          } else {
            //修改
            updateModulesField(state.formData).then(() => {
              ElMessage.success("修改成功");
              closeDialog(); // 关闭弹窗
              emit("modulesFieldList");
            }).finally(() => {
              state.loading = false;
            });
          }
        }
      });
    };
    const resetForm = () => {
      state.formData = {
        id: undefined,
        label: undefined,
        name: "",
        type: "text",
        description: undefined,
        default: undefined,
        sort: undefined,
        options: undefined,
        moduleId: props.moduleId,
        fieldLength:undefined,
        content:""
      };
    };
    return {
      proxy,
      fieldOptions:reactive(modulesInfoFieldOptions),
      modulesInfo,
      openDialog,
      closeDialog,
      onCancel,
      onSubmit,
      menuRef,
      formRef,
      ...toRefs(state)
    };
  }
});
</script>
<style scoped>
</style>
