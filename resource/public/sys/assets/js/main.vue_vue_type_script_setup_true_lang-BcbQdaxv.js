import{d as W,k as j,c as T,o as c,a as h,u as b,M as D,ag as V,a6 as re,O as K,G as A,H as u,P as i,L as H,X as te,ad as se,h as oe,J as U,am as Ge,V as le,t as he,r as ne,n as ge,f as we,i as _e,C as Se,D as ce,a2 as We,a5 as Ke,S as Ne,T as Xe,I as Oe,Q as Ee,ai as Ye}from"./@vue-C21YZbHS.js";import{s as F}from"./pinia-DwJf82dV.js";import{u as ae,a as X,a2 as Pe,L as ee,b as ke,s as Je,c as Ze,a4 as et,S as J,t as qe,k as tt,a3 as st,a5 as He,V as at}from"./index-BGmsw1a8.js";import{u as me,b as ye,o as $e}from"./vue-router-BDT9dCQ7.js";import{s as Ve}from"./screenfull-DPkPBHsl.js";import{v as Qe}from"./vue-i18n-BkLk9Ti1.js";import{e as nt,c as ot}from"./sysNotice-BzL5jZQk.js";import{r as it}from"./sysNoticeRead-DQgBZpSz.js";import{a as xe,f as lt,E as rt}from"./element-plus-CUmVNDWO.js";import{d as ut}from"./index-CHwKOJtz.js";import{l as je}from"./logo-mini-DKVW2eaB.js";import{S as ct}from"./sortablejs-C83syoBY.js";import mt from"./parent-BEDLVKEV.js";const dt=["src"],ft=["src"],pt=W({name:"layoutLogo",__name:"index",setup(M){const C=ae(),{themeConfig:y}=F(C),_=j(()=>{let{isCollapse:m,layout:S}=y.value;return!m||S==="classic"||document.body.clientWidth<1e3}),$=()=>{if(y.value.layout==="transverse")return!1;y.value.isCollapse=!y.value.isCollapse};return(m,S)=>_.value?(c(),T("div",{key:0,class:"layout-logo",onClick:$},[h("img",{src:b(je),class:"layout-logo-medium-img"},null,8,dt),h("span",null,D(b(y).globalTitle),1)])):(c(),T("div",{key:1,class:"layout-logo-size",onClick:$},[h("img",{src:b(je),class:"layout-logo-size-img"},null,8,ft)]))}}),ht=X(pt,[["__scopeId","data-v-5eb18eb0"]]),gt=W({name:"navMenuSubItem",props:{chil:{type:Array,default:()=>[]}},setup(M){return{chils:j(()=>M.chil)}}}),_t=["href"];function wt(M,C,y,_,$,m){const S=V("SvgIcon"),d=V("sub-item",!0),R=V("el-sub-menu"),B=V("el-menu-item");return c(!0),T(K,null,re(M.chils,w=>(c(),T(K,null,[w.children&&w.children.length>0?(c(),A(R,{index:w.path,key:w.path},{title:u(()=>[i(S,{name:w.meta.icon},null,8,["name"]),h("span",null,D(M.$t(w.meta.title)),1)]),default:u(()=>[i(d,{chil:w.children},null,8,["chil"])]),_:2},1032,["index"])):(c(),A(B,{index:w.path,key:w.path},{default:u(()=>[!w.meta.isLink||w.meta.isLink&&w.meta.isIframe?(c(),T(K,{key:0},[i(S,{name:w.meta.icon},null,8,["name"]),h("span",null,D(M.$t(w.meta.title)),1)],64)):(c(),T("a",{key:1,href:w.meta.isLink,target:"_blank",rel:"opener",class:"w100"},[i(S,{name:w.meta.icon},null,8,["name"]),H(" "+D(M.$t(w.meta.title)),1)],8,_t))]),_:2},1032,["index"]))],64))),256)}const yt=X(gt,[["render",wt]]),bt={key:0,class:"layout-navbars-breadcrumb"},Ct={key:0,class:"layout-navbars-breadcrumb-span"},Lt={key:1},vt={key:2},Vt=["onClick"],St=W({name:"layoutBreadcrumb",__name:"breadcrumb",setup(M){const C=Pe(),y=ae(),{themeConfig:_}=F(y),{routesList:$}=F(C),m=me(),S=ye(),d=te({breadcrumbList:[],routeSplit:[],routeSplitFirst:"",routeSplitIndex:1}),{breadcrumbList:R}=se(d),B=j(()=>{f(m.path);const{layout:n,isBreadcrumb:a}=_.value;return n==="classic"||n==="transverse"?!1:!!a}),w=n=>{const{redirect:a,path:I}=n;a?S.push(a):S.push(I)},L=()=>{_.value.isCollapse=!_.value.isCollapse,p()},p=()=>{ee.remove("themeConfig"),ee.set("themeConfig",_.value)},l=n=>{let a=[];n.map(I=>{a.push(...e(I,m.path,null)),a.length>0}),a.push(m),a=g(a),d.breadcrumbList=a},e=(n,a,I)=>{const z=[];if(n.path==a)return I&&z.push(I),z;if(n.children)for(let q=0;q<n.children.length;q++){const k=e(n.children[q],a,n);if(k&&k.length>0)return z.push(n,...k),z}return z},g=n=>{let a={},I=[];return n.forEach(z=>{a[z.path]||(a[z.path]=!0,I.push(z))}),I},f=n=>{if(!_.value.isBreadcrumb)return!1;d.breadcrumbList=[$.value[0]],d.routeSplit=n.split("/"),d.routeSplit.shift(),d.routeSplitFirst=`/${d.routeSplit[0]}`,d.routeSplitIndex=1,l($.value)};return oe(()=>{f(m.path)}),$e(n=>{f(n.path)}),(n,a)=>{const I=V("SvgIcon"),z=V("el-breadcrumb-item"),q=V("el-breadcrumb");return B.value?(c(),T("div",bt,[i(I,{class:"layout-navbars-breadcrumb-icon",name:b(_).isCollapse?"ele-Expand":"ele-Fold",size:16,onClick:L},null,8,["name"]),i(q,{class:"layout-navbars-breadcrumb-hide"},{default:u(()=>[i(Ge,{name:"breadcrumb"},{default:u(()=>[(c(!0),T(K,null,re(b(R),(k,Y)=>(c(),A(z,{key:k.path},{default:u(()=>[Y===b(R).length-1?(c(),T("span",Ct,[b(_).isBreadcrumbIcon?(c(),A(I,{key:0,name:k.meta.icon,class:"layout-navbars-breadcrumb-iconfont"},null,8,["name"])):U("",!0),k.meta.tagsViewName?(c(),T("div",vt,D(k.meta.tagsViewName),1)):(c(),T("div",Lt,D(n.$t(k.meta.title)),1))])):(c(),T("a",{key:1,onClick:le(E=>w(k),["prevent"])},[b(_).isBreadcrumbIcon?(c(),A(I,{key:0,name:k.meta.icon,class:"layout-navbars-breadcrumb-iconfont"},null,8,["name"])):U("",!0),H(D(n.$t(k.meta.title)),1)],8,Vt))]),_:2},1024))),128))]),_:1})]),_:1})])):U("",!0)}}}),kt=X(St,[["__scopeId","data-v-ce845d01"]]),xt={class:"layout-navbars-breadcrumb-user-news"},$t={class:"content-box"},Tt=["onClick"],It=["innerHTML"],Rt={class:"msg-box-attr"},Mt={class:"content-box-time"},Bt={key:0,class:"foot-box"},Dt={class:"layout-navbars-breadcrumb-user-news"},zt={class:"content-box"},At=["onClick"],Ht=["innerHTML"],qt={class:"msg-box-attr"},Nt={class:"content-box-time"},Ot={key:0,class:"foot-box"},Et=W({name:"layoutBreadcrumbUserNews",__name:"userNews",setup(M){oe(()=>{B(),w(1)});const C=ye(),y=te({type1Num:0,type2Num:0,noticeList:[],count:{notify:0,notice:0},tabsActive:1,activeInfo:1,isShowDialog:!1,barName:"通知"}),{tabsActive:_,activeInfo:$,isShowDialog:m,count:S,noticeList:d}=se(y),R=l=>{l===1?y.barName="通知":y.barName="公告",w(l)},B=()=>{nt().then(l=>{l.data!=null&&(y.count.notice=l.data.noticeCount||0,y.count.notify=l.data.notifyCount||0)})},w=l=>{ot({pageNum:1,pageSize:10,type:l,isTrim:!0}).then(g=>{y.noticeList=g.data.list||[]})},L=()=>{C.push("/system/sysNotice/show")},p=l=>{let e={noticeId:l.id};it(e).then(()=>{w(l.type),xe.success("已读")}),B()};return(l,e)=>{const g=V("el-badge"),f=V("el-tag"),n=V("el-empty"),a=V("el-button"),I=V("el-tab-pane"),z=V("el-tabs"),q=V("el-dialog");return c(),T("div",null,[i(z,{modelValue:b(_),"onUpdate:modelValue":e[2]||(e[2]=k=>he(_)?_.value=k:null),onTabChange:R},{default:u(()=>[i(I,{name:1},{label:u(()=>[e[5]||(e[5]=h("span",{style:{"font-size":"18px","font-weight":"500"}},"通知",-1)),i(g,{type:"warning",value:b(S).notify,style:{"margin-left":"10px"}},null,8,["value"])]),default:u(()=>[h("div",xt,[h("div",$t,[b(d).length>0?(c(!0),T(K,{key:0},re(b(d),(k,Y)=>(c(),T("div",{class:"content-box-item",key:Y},[h("div",{onClick:E=>p(k)},[h("div",null,D(k.title),1),h("div",{class:"content-box-msg",innerHTML:k.content},null,8,It),h("div",Rt,[h("span",Mt,D(k.createdAt),1),h("span",null,[k.isRead==!0?(c(),A(f,{key:0,type:"success",effect:"plain"},{default:u(()=>e[6]||(e[6]=[H("已读")])),_:1})):(c(),A(f,{key:1,type:"danger",effect:"plain"},{default:u(()=>e[7]||(e[7]=[H("未读")])),_:1}))])])],8,Tt)]))),128)):(c(),A(n,{key:1,description:l.$t("message.user.newDesc")},null,8,["description"]))]),b(d).length>0?(c(),T("div",Bt,[i(a,{onClick:e[0]||(e[0]=k=>L()),size:"small"},{default:u(()=>e[8]||(e[8]=[H("查看更多")])),_:1})])):U("",!0)])]),_:1}),i(I,{name:2},{label:u(()=>[e[9]||(e[9]=h("span",{style:{"font-size":"18px","font-weight":"500"}}," 私信",-1)),i(g,{type:"danger",value:b(S).notice,style:{"margin-left":"10px"}},null,8,["value"])]),default:u(()=>[h("div",Dt,[h("div",zt,[b(d).length>0?(c(!0),T(K,{key:0},re(b(d),(k,Y)=>(c(),T("div",{class:"content-box-item",key:Y},[h("div",{onClick:E=>p(k)},[h("div",null,D(k.title),1),h("div",{class:"content-box-msg",innerHTML:k.content},null,8,Ht),h("div",qt,[h("span",Nt,D(k.createdAt),1),h("span",null,[k.isRead==!0?(c(),A(f,{key:0,type:"success",effect:"plain"},{default:u(()=>e[10]||(e[10]=[H("已读")])),_:1})):(c(),A(f,{key:1,type:"danger",effect:"plain"},{default:u(()=>e[11]||(e[11]=[H("未读")])),_:1}))])])],8,At)]))),128)):(c(),A(n,{key:1,description:l.$t("message.user.newDesc")},null,8,["description"]))]),b(d).length>0?(c(),T("div",Ot,[i(a,{onClick:e[1]||(e[1]=k=>L()),size:"small"},{default:u(()=>e[12]||(e[12]=[H("查看更多")])),_:1})])):U("",!0)])]),_:1})]),_:1},8,["modelValue"]),i(q,{modelValue:b(m),"onUpdate:modelValue":e[4]||(e[4]=k=>he(m)?m.value=k:null),width:"1200px","close-on-click-modal":!1,"destroy-on-close":!0},{default:u(()=>[i(z,{modelValue:b($),"onUpdate:modelValue":e[3]||(e[3]=k=>he($)?$.value=k:null),type:"border-card"},{default:u(()=>[i(I,{name:1},{label:u(()=>e[13]||(e[13]=[h("span",{style:{"font-weight":"600","font-size":"18px",color:"#6c6c6c"}},"通知",-1)])),_:1}),i(I,{label:"Config",name:2},{label:u(()=>e[14]||(e[14]=[h("span",{style:{"font-weight":"600","font-size":"18px",color:"#6c6c6c"}},"私信",-1)])),_:1})]),_:1},8,["modelValue"])]),_:1},8,["modelValue"])])}}}),Pt=X(Et,[["__scopeId","data-v-e86cabc0"]]),Ft={class:"layout-search-dialog"},Ut=W({name:"layoutBreadcrumbSearch",__name:"search",setup(M,{expose:C}){const y=ke(),{tagsViewRoutes:_}=F(y),$=ne(),{t:m}=Qe.useI18n(),S=ye(),d=te({isShowSearch:!1,menuQuery:"",tagsViewList:[]}),{isShowSearch:R,menuQuery:B}=se(d),w=()=>{d.menuQuery="",d.isShowSearch=!0,e(),ge(()=>{setTimeout(()=>{$.value.focus()})})},L=()=>{d.isShowSearch=!1},p=(n,a)=>{let I=n?d.tagsViewList.filter(l(n)):d.tagsViewList;a(I)},l=n=>a=>a.path.toLowerCase().indexOf(n.toLowerCase())>-1||a.meta.title.toLowerCase().indexOf(n.toLowerCase())>-1||m(a.meta.title).indexOf(n.toLowerCase())>-1,e=()=>{if(d.tagsViewList.length>0)return!1;_.value.map(n=>{n.meta.isHide||d.tagsViewList.push({...n})})},g=n=>{let{path:a,redirect:I}=n;n.meta.isLink&&!n.meta.isIframe?window.open(n.meta.isLink):I?S.push(I):S.push(a),L()},f=()=>{L()};return C({openSearch:w}),(n,a)=>{const I=V("ele-Search"),z=V("el-icon"),q=V("SvgIcon"),k=V("el-autocomplete"),Y=V("el-dialog");return c(),T("div",Ft,[i(Y,{modelValue:b(R),"onUpdate:modelValue":a[1]||(a[1]=E=>he(R)?R.value=E:null),width:"300px","destroy-on-close":"",modal:!1,fullscreen:"","show-close":!1},{default:u(()=>[i(k,{modelValue:b(B),"onUpdate:modelValue":a[0]||(a[0]=E=>he(B)?B.value=E:null),"fetch-suggestions":p,placeholder:n.$t("message.user.searchPlaceholder"),ref_key:"layoutMenuAutocompleteRef",ref:$,onSelect:g,onBlur:f},{prefix:u(()=>[i(z,{class:"el-input__icon"},{default:u(()=>[i(I)]),_:1})]),default:u(({item:E})=>[h("div",null,[i(q,{name:E.meta.icon,class:"mr5"},null,8,["name"]),H(" "+D(n.$t(E.meta.title)),1)])]),_:1},8,["modelValue","placeholder"])]),_:1},8,["modelValue"])])}}}),jt=X(Ut,[["__scopeId","data-v-b9a41043"]]);function Wt(){return Je({url:"/api/v1/system/cache/remove",method:"delete"})}const Qt={class:"layout-navbars-breadcrumb-user-icon"},Gt=["title"],Kt={class:"layout-navbars-breadcrumb-user-icon"},Xt=["title"],Yt=["title"],Jt={class:"layout-navbars-breadcrumb-user-icon"},Zt=["title"],es={class:"layout-navbars-breadcrumb-user-link"},ts=["src"],ss=W({name:"layoutBreadcrumbUser",__name:"user",setup(M){const{t:C}=Qe.useI18n(),{proxy:y}=we(),_=ye(),$=me(),m=Ze(),S=ae(),{userInfos:d}=F(m),{themeConfig:R}=F(S),B=ne(),w=ne(),L=te({isScreenfull:!1,disabledI18n:"zh-cn",disabledSize:"large"}),{isScreenfull:p,disabledI18n:l,disabledSize:e}=se(L),g=j(()=>{let x="";const{layout:N,isClassicSplitMenu:O}=R.value;return["defaults","columns"].includes(N)||N==="classic"&&!O?x="1":x="",x}),f=()=>{if(!Ve.isEnabled)return xe.warning("暂不不支持全屏"),!1;Ve.toggle(),Ve.on("change",()=>{Ve.isFullscreen?L.isScreenfull=!0:L.isScreenfull=!1})},n=()=>{y.mittBus.emit("openSetingsDrawer")},a=()=>{J.remove("userMenu"),localStorage.clear(),Wt().then(()=>{xe.success("缓存清除成功"),window.location.reload()})},I=x=>{x==="logOut"?rt({closeOnClickModal:!1,closeOnPressEscape:!1,title:C("message.user.logOutTitle"),message:C("message.user.logOutMessage"),showCancelButton:!0,confirmButtonText:C("message.user.logOutConfirm"),cancelButtonText:C("message.user.logOutCancel"),buttonSize:"default",beforeClose:(N,O,Z)=>{N==="confirm"?ut().then(()=>{O.confirmButtonLoading=!0,O.confirmButtonText=C("message.user.logOutExit"),setTimeout(()=>{Z(),setTimeout(()=>{O.confirmButtonLoading=!1},300)},500)}):Z()}}).then(async()=>{J.clear(),window.location.reload()}).catch(()=>{}):x==="wareHouse"?window.open("https://gitee.com/tiger1103/gfast"):_.push(x)},z=()=>{B.value.openSearch()},q=()=>{debugger;w.value.hide()},k=x=>{ee.remove("themeConfig"),R.value.globalComponentSize=x,ee.set("themeConfig",R.value),ue(),window.location.reload()},Y=x=>{ee.remove("themeConfig"),R.value.globalI18n=x,ee.set("themeConfig",R.value),y.$i18n.locale=x,de(),qe.useTitle()},E=x=>{y.mittBus.emit("getI18nConfig",y.i18n.global.messages.value[x])},de=()=>{switch(ee.get("themeConfig").globalI18n){case"zh-cn":L.disabledI18n="zh-cn",E("zh-cn");break;case"en":L.disabledI18n="en",E("en");break;case"zh-tw":L.disabledI18n="zh-tw",E("zh-tw");break}},ue=()=>{switch(ee.get("themeConfig").globalComponentSize){case"large":L.disabledSize="large";break;case"default":L.disabledSize="default";break;case"small":L.disabledSize="small";break}};oe(()=>{ee.get("themeConfig")&&(de(),ue())});const be=et(),fe=j(()=>be.message);_e(fe,(x,N)=>{!x||!x.id||Te(x)},{immediate:!0,deep:!0});const Te=x=>{const N=lt({title:"新消息",message:`您有一条新消息：【${x.title}】，请点击查看详情。`,type:"warning",duration:36e5,onClick(){$.fullPath=="/system/sysNotice/show?type="+x.type?_.go(0):_.push("/system/sysNotice/show?type="+x.type),N.close()}})};return(x,N)=>{const O=V("el-dropdown-item"),Z=V("el-dropdown-menu"),pe=V("el-dropdown"),Ie=V("ele-Search"),Q=V("el-icon"),Re=V("ele-Bell"),Me=V("el-badge"),Be=V("el-popover"),Ce=V("ele-ArrowDown");return c(),T("div",{class:"layout-navbars-breadcrumb-user pr15",style:Se({flex:g.value})},[i(pe,{"show-timeout":70,"hide-timeout":50,trigger:"click",onCommand:k},{dropdown:u(()=>[i(Z,null,{default:u(()=>[i(O,{command:"large",disabled:b(e)==="large"},{default:u(()=>[H(D(x.$t("message.user.dropdownLarge")),1)]),_:1},8,["disabled"]),i(O,{command:"default",disabled:b(e)==="default"},{default:u(()=>[H(D(x.$t("message.user.dropdownDefault")),1)]),_:1},8,["disabled"]),i(O,{command:"small",disabled:b(e)==="small"},{default:u(()=>[H(D(x.$t("message.user.dropdownSmall")),1)]),_:1},8,["disabled"])]),_:1})]),default:u(()=>[h("div",Qt,[h("i",{class:"iconfont icon-ziti",title:x.$t("message.user.title0")},null,8,Gt)])]),_:1}),i(pe,{"show-timeout":70,"hide-timeout":50,trigger:"click",onCommand:Y},{dropdown:u(()=>[i(Z,null,{default:u(()=>[i(O,{command:"zh-cn",disabled:b(l)==="zh-cn"},{default:u(()=>N[0]||(N[0]=[H("简体中文")])),_:1},8,["disabled"]),i(O,{command:"en",disabled:b(l)==="en"},{default:u(()=>N[1]||(N[1]=[H("English")])),_:1},8,["disabled"]),i(O,{command:"zh-tw",disabled:b(l)==="zh-tw"},{default:u(()=>N[2]||(N[2]=[H("繁體中文")])),_:1},8,["disabled"])]),_:1})]),default:u(()=>[h("div",Kt,[h("i",{class:ce(["iconfont",b(l)==="en"?"icon-fuhao-yingwen":"icon-fuhao-zhongwen"]),title:x.$t("message.user.title1")},null,10,Xt)])]),_:1}),h("div",{class:"layout-navbars-breadcrumb-user-icon",onClick:z},[i(Q,{title:x.$t("message.user.title2")},{default:u(()=>[i(Ie)]),_:1},8,["title"])]),h("div",{class:"layout-navbars-breadcrumb-user-icon",onClick:n},[h("i",{class:"icon-skin iconfont",title:x.$t("message.user.title3")},null,8,Yt)]),h("div",{class:"layout-navbars-breadcrumb-user-icon",onClick:a},N[3]||(N[3]=[h("i",{class:"fa-trash fa",title:"清除缓存"},null,-1)])),h("div",Jt,[i(Be,{ref_key:"newPopoverRef",ref:w,placement:"bottom",trigger:"click",transition:"el-zoom-in-top",width:500,persistent:!1},{reference:u(()=>[i(Me,{"is-dot":!0},{default:u(()=>[i(Q,{title:x.$t("message.user.title4")},{default:u(()=>[i(Re)]),_:1},8,["title"])]),_:1})]),default:u(()=>[i(Pt,{onHideNews:q})]),_:1},512)]),h("div",{class:"layout-navbars-breadcrumb-user-icon mr10",onClick:f},[h("i",{class:ce(["iconfont",b(p)?"icon-tuichuquanping":"icon-fullscreen"]),title:b(p)?x.$t("message.user.title6"):x.$t("message.user.title5")},null,10,Zt)]),i(pe,{"show-timeout":70,"hide-timeout":50,onCommand:I},{dropdown:u(()=>[i(Z,null,{default:u(()=>[i(O,{command:"/home"},{default:u(()=>[H(D(x.$t("message.user.dropdown1")),1)]),_:1}),i(O,{command:"/personal"},{default:u(()=>[H(D(x.$t("message.user.dropdown2")),1)]),_:1}),i(O,{divided:"",command:"logOut"},{default:u(()=>[H(D(x.$t("message.user.dropdown5")),1)]),_:1})]),_:1})]),default:u(()=>[h("span",es,[h("img",{src:b(d).avatar,class:"layout-navbars-breadcrumb-user-link-photo mr5"},null,8,ts),H(" "+D(b(d).userName===""?"common":b(d).userName)+" ",1),i(Q,{class:"el-icon--right"},{default:u(()=>[i(Ce)]),_:1})])]),_:1}),i(jt,{ref_key:"searchRef",ref:B},null,512)],4)}}}),as=X(ss,[["__scopeId","data-v-97a98c14"]]),ns=W({name:"navMenuHorizontal",components:{SubItem:yt},props:{menuList:{type:Array,default:()=>[]}},setup(M){const{proxy:C}=we(),y=Pe(),_=ae(),{routesList:$}=F(y),{themeConfig:m}=F(_),S=me(),d=te({defaultActive:null}),R=j(()=>M.menuList),B=e=>{const g=e.wheelDelta||-e.deltaY*40;C.$refs.elMenuHorizontalScrollRef.$refs.wrap$.scrollLeft=C.$refs.elMenuHorizontalScrollRef.$refs.wrap$.scrollLeft+g/4},w=()=>{ge(()=>{let e=document.querySelector(".el-menu.el-menu--horizontal li.is-active");if(!e)return!1;C.$refs.elMenuHorizontalScrollRef.$refs.wrap$.scrollLeft=e.offsetLeft})},L=e=>e.filter(g=>!g.meta.isHide).map(g=>(g=Object.assign({},g),g.children&&(g.children=L(g.children)),g)),p=e=>{const g=e.split("/");let f={};return L($.value).map((n,a)=>{n.path===`/${g[1]}`&&(n.k=a,f.item=[{...n}],f.children=[{...n}],n.children&&(f.children=n.children))}),f},l=e=>{const{path:g,meta:f}=e;if(m.value.layout==="classic")d.defaultActive=`/${g.split("/")[1]}`;else{const n=f.isDynamic?f.isDynamicPath.split("/"):g.split("/");n.length>=4&&f.isHide?d.defaultActive=n.splice(0,3).join("/"):d.defaultActive=g}};return We(()=>{l(S)}),oe(()=>{w()}),$e(e=>{l(e);let{layout:g,isClassicSplitMenu:f}=m.value;g==="classic"&&f&&C.mittBus.emit("setSendClassicChildren",p(e.path))}),{menuLists:R,onElMenuHorizontalScroll:B,...se(d)}}}),os={class:"el-menu-horizontal-warp"},is=["href"];function ls(M,C,y,_,$,m){const S=V("SvgIcon"),d=V("SubItem"),R=V("el-sub-menu"),B=V("el-menu-item"),w=V("el-menu"),L=V("el-scrollbar");return c(),T("div",os,[i(L,{onWheel:le(M.onElMenuHorizontalScroll,["prevent"]),ref:"elMenuHorizontalScrollRef"},{default:u(()=>[i(w,{router:"","default-active":M.defaultActive,ellipsis:!1,"background-color":"transparent",mode:"horizontal"},{default:u(()=>[(c(!0),T(K,null,re(M.menuLists,p=>(c(),T(K,null,[p.children&&p.children.length>0?(c(),A(R,{index:p.path,key:p.path},{title:u(()=>[i(S,{name:p.meta.icon},null,8,["name"]),h("span",null,D(M.$t(p.meta.title)),1)]),default:u(()=>[i(d,{chil:p.children},null,8,["chil"])]),_:2},1032,["index"])):(c(),A(B,{index:p.path,key:p.path},Ke({_:2},[!p.meta.isLink||p.meta.isLink&&p.meta.isIframe?{name:"title",fn:u(()=>[i(S,{name:p.meta.icon},null,8,["name"]),H(" "+D(M.$t(p.meta.title)),1)]),key:"0"}:{name:"title",fn:u(()=>[h("a",{href:p.meta.isLink,target:"_blank",rel:"opener",class:"w100"},[i(S,{name:p.meta.icon},null,8,["name"]),H(" "+D(M.$t(p.meta.title)),1)],8,is)]),key:"1"}]),1032,["index"]))],64))),256))]),_:1},8,["default-active"])]),_:1},8,["onWheel"])])}const rs=X(ns,[["render",ls],["__scopeId","data-v-0e8aec25"]]),us={class:"layout-navbars-breadcrumb-index"},cs=W({name:"layoutBreadcrumbIndex",__name:"index",setup(M){const{proxy:C}=we(),y=Pe(),_=ae(),{themeConfig:$}=F(_),{routesList:m}=F(y),S=me(),d=te({menuList:[]}),{menuList:R}=se(d),B=j(()=>{let{isShowLogo:g,layout:f}=$.value;return g&&f==="classic"||g&&f==="transverse"}),w=j(()=>{let{layout:g,isClassicSplitMenu:f}=$.value;return g==="transverse"||f&&g==="classic"}),L=()=>{let{layout:g,isClassicSplitMenu:f}=$.value;if(g==="classic"&&f){d.menuList=p(l(m.value));const n=e(S.path);C.mittBus.emit("setSendClassicChildren",n)}else d.menuList=l(m.value)},p=g=>(g.map(f=>{f.children&&delete f.children}),g),l=g=>g.filter(f=>!f.meta.isHide).map(f=>(f=Object.assign({},f),f.children&&(f.children=l(f.children)),f)),e=g=>{const f=g.split("/");let n={};return l(m.value).map((a,I)=>{a.path===`/${f[1]}`&&(a.k=I,n.item=[{...a}],n.children=[{...a}],a.children&&(n.children=a.children))}),n};return oe(()=>{L(),C.mittBus.on("getBreadcrumbIndexSetFilterRoutes",()=>{L()})}),Ne(()=>{C.mittBus.off("getBreadcrumbIndexSetFilterRoutes",()=>{})}),(g,f)=>(c(),T("div",us,[B.value?(c(),A(ht,{key:0})):U("",!0),i(kt),w.value?(c(),A(rs,{key:1,menuList:b(R)},null,8,["menuList"])):U("",!0),i(as)]))}}),ms=X(cs,[["__scopeId","data-v-5259b5fc"]]),ds={class:"el-dropdown-menu"},fs=["onClick"],ps=W({name:"layoutTagsViewContextmenu",__name:"contextmenu",props:{dropdown:{type:Object,default:()=>({x:0,y:0})}},emits:["currentContextmenuClick"],setup(M,{expose:C,emit:y}){const _=M,$=y,m=te({isShow:!1,dropdownList:[{contextMenuClickId:0,txt:"message.tagsView.refresh",affix:!1,icon:"ele-RefreshRight"},{contextMenuClickId:1,txt:"message.tagsView.close",affix:!1,icon:"ele-Close"},{contextMenuClickId:2,txt:"message.tagsView.closeOther",affix:!1,icon:"ele-CircleClose"},{contextMenuClickId:3,txt:"message.tagsView.closeAll",affix:!1,icon:"ele-FolderDelete"},{contextMenuClickId:4,txt:"message.tagsView.fullscreen",affix:!1,icon:"iconfont icon-fullscreen"}],item:{},arrowLeft:10}),{isShow:S,dropdownList:d,arrowLeft:R}=se(m),B=j(()=>_.dropdown.x+117>document.documentElement.clientWidth?{x:document.documentElement.clientWidth-117-5,y:_.dropdown.y}:_.dropdown),w=l=>{$("currentContextmenuClick",Object.assign({},{contextMenuClickId:l},m.item))},L=l=>{m.item=l,l.meta.isAffix?m.dropdownList[1].affix=!0:m.dropdownList[1].affix=!1,p(),setTimeout(()=>{m.isShow=!0},10)},p=()=>{m.isShow=!1};return oe(()=>{document.body.addEventListener("click",p)}),Ne(()=>{document.body.removeEventListener("click",p)}),_e(()=>_.dropdown,({x:l})=>{l+117>document.documentElement.clientWidth?m.arrowLeft=117-(document.documentElement.clientWidth-l):m.arrowLeft=10},{deep:!0}),C({openContextmenu:L}),(l,e)=>{const g=V("SvgIcon");return c(),A(Xe,{name:"el-zoom-in-center"},{default:u(()=>[Oe((c(),T("div",{"aria-hidden":"true",class:"el-dropdown__popper el-popper is-light is-pure custom-contextmenu",role:"tooltip","data-popper-placement":"bottom",style:Se(`top: ${B.value.y+5}px;left: ${B.value.x}px;`),key:Math.random()},[h("ul",ds,[(c(!0),T(K,null,re(b(d),(f,n)=>(c(),T(K,null,[f.affix?U("",!0):(c(),T("li",{class:"el-dropdown-menu__item","aria-disabled":"false",tabindex:"-1",key:n,onClick:a=>w(f.contextMenuClickId)},[i(g,{name:f.icon},null,8,["name"]),h("span",null,D(l.$t(f.txt)),1)],8,fs))],64))),256))]),h("div",{class:"el-popper__arrow",style:Se({left:`${b(R)}px`})},null,4)],4)),[[Ee,b(S)]])]),_:1})}}}),hs=X(ps,[["__scopeId","data-v-65ea1e21"]]),gs=["data-url","onContextmenu","onClick"],_s={key:0,class:"iconfont icon-webicon318 layout-navbars-tagsview-ul-li-iconfont"},ws=W({name:"layoutTagsView",__name:"tagsView",setup(M){const{proxy:C}=we(),y=ne([]),_=ne(),$=ne(),m=ne(),S=ke(),d=ae(),R=ke(),{themeConfig:B}=F(d),{tagsViewRoutes:w}=F(R),L=tt(),p=me(),l=ye(),e=te({routeActive:"",routePath:p.path,dropdown:{x:"",y:""},sortable:"",tagsRefsIndex:0,tagsViewList:[],tagsViewRoutesList:[]}),{tagsViewList:g,dropdown:f}=se(e),n=j(()=>B.value.tagsStyle),a=j(()=>B.value),I=j(()=>s=>qe.setTagsViewNameI18n(s)),z=s=>a.value.isShareTagsView?s.path===e.routePath:s.query&&Object.keys(s.query).length||s.params&&Object.keys(s.params).length?s.url?s.url===e.routeActive:s.path===e.routeActive:s.path===e.routePath,q=s=>{J.set("tagsViewList",s)},k=async()=>{e.routeActive=await Q(p),e.routePath=await p.meta.isDynamic?p.meta.isDynamicPath:p.path,e.tagsViewList=[],e.tagsViewRoutesList=w.value,Y()},Y=async()=>{J.get("tagsViewList")&&a.value.isCacheTagsView?e.tagsViewList=await J.get("tagsViewList"):(await e.tagsViewRoutesList.map(s=>{s.meta.isAffix&&!s.meta.isHide&&(s.url=Q(s),e.tagsViewList.push({...s}),L.addCachedView(s))}),await ue(p.path,p)),Ce(a.value.isShareTagsView?e.routePath:e.routeActive)},E=async(s,t)=>{let r=t.meta.isDynamic?t.meta.isDynamicPath:s;if(e.tagsViewList.filter(v=>v.path===r&&He(t.meta.isDynamic?v.params?v.params:null:v.query?v.query:null,t.meta.isDynamic?t!=null&&t.params?t==null?void 0:t.params:null:t!=null&&t.query?t==null?void 0:t.query:null)).length<=0){let v=e.tagsViewRoutesList.find(P=>P.path===r);if(!v||v.meta.isAffix||v.meta.isLink&&!v.meta.isIframe)return!1;t.meta.isDynamic?v.params=t.params:v.query=t.query,v.url=Q(v),e.tagsViewList.push({...v}),await L.addCachedView(v),q(e.tagsViewList)}},de=(s,t)=>{let r=t.meta.isDynamic?t.meta.isDynamicPath:s;e.tagsViewList.forEach(o=>{o.path===r&&!He(t.meta.isDynamic?o.params?o.params:null:o.query?o.query:null,t.meta.isDynamic?t!=null&&t.params?t==null?void 0:t.params:null:t!=null&&t.query?t==null?void 0:t.query:null)&&(t.meta.isDynamic?o.params=t.params:o.query=t.query,o.url=Q(o),q(e.tagsViewList))})},ue=(s,t)=>{ge(async()=>{let r="";if(t&&t.meta.isDynamic){if(a.value.isShareTagsView?await de(s,t):await E(s,t),e.tagsViewList.some(o=>o.path===t.meta.isDynamicPath))return!1;r=e.tagsViewRoutesList.find(o=>o.path===t.meta.isDynamicPath)}else{if(a.value.isShareTagsView?await de(s,t):await E(s,t),e.tagsViewList.some(o=>o.path===s))return!1;r=e.tagsViewRoutesList.find(o=>o.path===s)}if(!r||r.meta.isLink&&!r.meta.isIframe)return!1;t&&t.meta.isDynamic?r.params=t!=null&&t.params?t==null?void 0:t.params:p.params:r.query=t!=null&&t.query?t==null?void 0:t.query:p.query,r.url=Q(r),await L.addCachedView(r),await e.tagsViewList.push({...r}),await q(e.tagsViewList)})},be=async s=>{const t=e.tagsViewList.find(r=>a.value.isShareTagsView?r.path===s:r.url===s);t!=null&&(await L.delCachedView(t),C.mittBus.emit("onTagsViewRefreshRouterView",s),t.meta.isKeepAlive&&L.addCachedView(t))},fe=s=>{e.tagsViewList.map((t,r,o)=>{t.meta.isAffix||(a.value.isShareTagsView?t.path===s:t.url===s)&&(L.delCachedView(t),e.tagsViewList.splice(r,1),setTimeout(()=>{(e.tagsViewList.length===r&&a.value.isShareTagsView?e.routePath===s:e.routeActive===s)?o[o.length-1].meta.isDynamic?r!==o.length?l.push({name:o[r].name,params:o[r].params}):l.push({name:o[o.length-1].name,params:o[o.length-1].params}):r!==o.length?l.push({path:o[r].path,query:o[r].query}):l.push({path:o[o.length-1].path,query:o[o.length-1].query}):(e.tagsViewList.length!==r&&a.value.isShareTagsView?e.routePath===s:e.routeActive===s)&&(o[r].meta.isDynamic?l.push({name:o[r].name,params:o[r].params}):l.push({path:o[r].path,query:o[r].query}))},0))}),q(e.tagsViewList)},Te=s=>{J.get("tagsViewList")&&(e.tagsViewList=[],J.get("tagsViewList").map(t=>{t.meta.isAffix&&!t.meta.isHide&&(t.url=Q(t),L.delOthersCachedViews(t),e.tagsViewList.push({...t}))}),ue(s,p),q(e.tagsViewList))},x=()=>{J.get("tagsViewList")&&(L.delAllCachedViews(),e.tagsViewList=[],J.get("tagsViewList").map(s=>{s.meta.isAffix&&!s.meta.isHide&&(s.url=Q(s),e.tagsViewList.push({...s}),l.push({path:e.tagsViewList[e.tagsViewList.length-1].path}))}),q(e.tagsViewList))},N=async s=>{const t=e.tagsViewList.find(r=>a.value.isShareTagsView?r.path===s:r.url===s);t.meta.isDynamic?await l.push({name:t.name,params:t.params}):await l.push({name:t.name,query:t.query}),S.setCurrenFullscreen(!0)},O=(s,t)=>(J.get("tagsViewList")?J.get("tagsViewList"):e.tagsViewList).find(o=>{if(o.path===s&&He(o.meta.isDynamic?o.params?o.params:null:o.query?o.query:null,t&&Object.keys(t||{}).length>0?t:null))return o;if(o.path===s&&Object.keys(t||{}).length<=0)return o}),Z=async s=>{const t=s.meta.isDynamic?s.params:s.query;if(!O(s.path,t))return xe({type:"warning",message:"请正确输入路径及完整参数（query、params）"});const{path:r,name:o,params:v,query:P,meta:G,url:ie}=O(s.path,t);switch(s.contextMenuClickId){case 0:G.isDynamic?await l.push({name:o,params:v}):await l.push({path:r,query:P}),be(a.value.isShareTagsView?r:ie);break;case 1:fe(a.value.isShareTagsView?r:ie);break;case 2:G.isDynamic?await l.push({name:o,params:v}):await l.push({path:r,query:P}),Te(r);break;case 3:x();break;case 4:N(a.value.isShareTagsView?r:ie);break}},pe=(s,t)=>{const{clientX:r,clientY:o}=t;e.dropdown.x=r,e.dropdown.y=o,$.value.openContextmenu(s)},Ie=(s,t)=>{e.tagsRefsIndex=t,l.push(s)},Q=s=>{let t=s.query&&Object.keys(s.query).length>0?s.query:s.params;if(!t||Object.keys(t).length<=0)return s.path;let r="";for(let o in t)r+=t[o];return`${s.meta.isDynamic?s.meta.isDynamicPath:s.path}-${r}`},Re=()=>{_.value.update()},Me=s=>{_.value.$refs.wrapRef.scrollLeft+=s.wheelDelta/4},Be=()=>{ge(()=>{if(y.value.length<=0)return!1;let s=y.value[e.tagsRefsIndex],t=e.tagsRefsIndex,r=y.value.length,o=y.value[0],v=y.value[y.value.length-1],P=_.value.$refs.wrapRef,G=P.scrollWidth,ie=P.offsetWidth,Fe=P.scrollLeft,Ae=y.value[e.tagsRefsIndex-1],Ue=y.value[e.tagsRefsIndex+1],Le="",ve="";s===o?P.scrollLeft=0:s===v?P.scrollLeft=G-ie:(t===0?Le=o.offsetLeft-5:Le=(Ae==null?void 0:Ae.offsetLeft)-5,t===r?ve=v.offsetLeft+v.offsetWidth+5:ve=Ue.offsetLeft+Ue.offsetWidth+5,ve>Fe+ie?P.scrollLeft=ve-ie:Le<Fe&&(P.scrollLeft=Le)),Re()})},Ce=s=>{ge(async()=>{let t=await e.tagsViewList;e.tagsRefsIndex=t.findIndex(r=>a.value.isShareTagsView?r.path===s:r.url===s),Be()})},De=async()=>{const s=document.querySelector(".layout-navbars-tagsview-ul");if(!s)return!1;e.sortable.el&&e.sortable.destroy(),e.sortable=ct.create(s,{animation:300,dataIdAttr:"data-url",disabled:!a.value.isSortableTagsView,onEnd:()=>{const t=[];e.sortable.toArray().map(r=>{e.tagsViewList.map(o=>{o.url===r&&t.push({...o})})}),q(t)}})},ze=async()=>{await De(),qe.isMobile()&&e.sortable.el&&e.sortable.destroy()};return We(()=>{ze(),window.addEventListener("resize",ze),C.mittBus.on("onCurrentContextmenuClick",s=>{Z(s)}),C.mittBus.on("openOrCloseSortable",()=>{De()}),C.mittBus.on("openShareTagsView",()=>{a.value.isShareTagsView&&(l.push("/home"),e.tagsViewList=[],e.tagsViewRoutesList.map(s=>{s.meta.isAffix&&!s.meta.isHide&&(s.url=Q(s),e.tagsViewList.push({...s}))}))})}),Ne(()=>{C.mittBus.off("onCurrentContextmenuClick",()=>{}),C.mittBus.off("openOrCloseSortable",()=>{}),C.mittBus.off("openShareTagsView",()=>{}),window.removeEventListener("resize",ze)}),Ye(()=>{y.value=[]}),oe(()=>{k(),De()}),$e(async s=>{e.routeActive=Q(s),e.routePath=s.meta.isDynamic?s.meta.isDynamicPath:s.path,await ue(s.path,s),Ce(a.value.isShareTagsView?e.routePath:e.routeActive)}),_e(st.state,s=>{if(s.tagsViewRoutes.tagsViewRoutes.length===e.tagsViewRoutesList.length)return!1;k()},{deep:!0}),(s,t)=>{const r=V("SvgIcon"),o=V("el-scrollbar");return c(),T("div",{class:ce(["layout-navbars-tagsview",{"layout-navbars-tagsview-shadow":a.value.layout==="classic"}])},[i(o,{ref_key:"scrollbarRef",ref:_,onWheel:le(Me,["prevent"])},{default:u(()=>[h("ul",{class:ce(["layout-navbars-tagsview-ul",n.value]),ref_key:"tagsUlRef",ref:m},[(c(!0),T(K,null,re(b(g),(v,P)=>(c(),T("li",{key:P,class:ce(["layout-navbars-tagsview-ul-li",{"is-active":z(v)}]),"data-url":v.url,onContextmenu:le(G=>pe(v,G),["prevent"]),onClick:G=>Ie(v,P),ref_for:!0,ref:G=>{G&&(y.value[P]=G)}},[z(v)?(c(),T("i",_s)):U("",!0),!z(v)&&a.value.isTagsviewIcon?(c(),A(r,{key:1,name:v.meta.icon,class:"pr5"},null,8,["name"])):U("",!0),h("span",null,D(I.value(v)),1),z(v)?(c(),T(K,{key:2},[i(r,{name:"ele-RefreshRight",class:"ml5 layout-navbars-tagsview-ul-li-refresh",onClick:le(G=>be(a.value.isShareTagsView?v.path:v.url),["stop"])},null,8,["onClick"]),v.meta.isAffix?U("",!0):(c(),A(r,{key:0,name:"ele-Close",class:"layout-navbars-tagsview-ul-li-icon layout-icon-active",onClick:le(G=>fe(a.value.isShareTagsView?v.path:v.url),["stop"])},null,8,["onClick"]))],64)):U("",!0),v.meta.isAffix?U("",!0):(c(),A(r,{key:3,name:"ele-Close",class:"layout-navbars-tagsview-ul-li-icon layout-icon-three",onClick:le(G=>fe(a.value.isShareTagsView?v.path:v.url),["stop"])},null,8,["onClick"]))],42,gs))),128))],2)]),_:1},512),i(hs,{dropdown:b(f),ref_key:"contextmenuRef",ref:$,onCurrentContextmenuClick:Z},null,8,["dropdown"])],2)}}}),ys=X(ws,[["__scopeId","data-v-6797adeb"]]),bs={class:"layout-navbars-container"},Cs=W({name:"layoutNavBars",__name:"index",setup(M){const C=ae(),{themeConfig:y}=F(C),_=j(()=>{let{layout:$,isTagsview:m}=y.value;return $!=="classic"&&m});return($,m)=>(c(),T("div",bs,[i(ms),_.value?(c(),A(ys,{key:0})):U("",!0)]))}}),Ls=X(Cs,[["__scopeId","data-v-89085438"]]),Ns=W({name:"layoutHeader",__name:"header",setup(M){const C=ke(),y=ae(),{themeConfig:_}=F(y),{isTagsViewCurrenFull:$}=F(C),m=j(()=>{let{isTagsview:S,layout:d}=_.value;return S&&d!=="classic"?"84px":"50px"});return(S,d)=>{const R=V("el-header");return Oe((c(),A(R,{class:"layout-header",height:m.value},{default:u(()=>[i(Ls)]),_:1},8,["height"])),[[Ee,!b($)]])}}}),vs={class:"layout-footer mt15"},Vs=W({name:"layoutFooter",__name:"index",setup(M){const C=ne(!0);return $e(()=>{setTimeout(()=>{C.value=!1,setTimeout(()=>{C.value=!0},800)},0)}),(y,_)=>Oe((c(),T("div",vs,_[0]||(_[0]=[h("div",{class:"layout-footer-warp"},[h("div",null,"Copyright © 2021-2023 g-fast.cn All Rights Reserved."),h("div",{class:"mt5"},"云南奇讯科技有限公司版权所有")],-1)]),512)),[[Ee,C.value]])}}),Ss=X(Vs,[["__scopeId","data-v-6940d17f"]]),Os=W({name:"layoutMain",__name:"main",setup(M){const{proxy:C}=we(),y=ae(),{themeConfig:_}=F(y),$=me(),m=te({headerHeight:"",currentRouteMeta:{}}),{currentRouteMeta:S}=se(m),d=j(()=>{const{layout:w}=_.value;return w==="classic"||w==="transverse"}),R=()=>{const w=m.currentRouteMeta.isLink&&m.currentRouteMeta.isIframe;let{isTagsview:L}=_.value;return L?m.headerHeight=w?"86px":"115px":m.headerHeight="80px"},B=()=>{m.currentRouteMeta=$.meta};return oe(async()=>{await B(),R(),at.done()}),_e(()=>$.path,()=>{m.currentRouteMeta=$.meta;const w=m.currentRouteMeta.isLink&&m.currentRouteMeta.isIframe;m.headerHeight=w?"86px":"115px",C.$refs.layoutScrollbarRef.update()}),_e(_,w=>{var p,l;m.currentRouteMeta=$.meta;const L=m.currentRouteMeta.isLink&&m.currentRouteMeta.isIframe;m.headerHeight=w.isTagsview?L?"86px":"115px":"51px",(l=(p=C.$refs)==null?void 0:p.layoutScrollbarRef)==null||l.update()},{deep:!0}),(w,L)=>{const p=V("el-scrollbar"),l=V("el-main");return c(),A(l,{class:"layout-main"},{default:u(()=>[i(p,{ref:"layoutScrollbarRef",class:ce({"layout-scrollbar":!d.value&&!b(S).isLink&&!b(S).isIframe||!d.value&&b(S).isLink&&!b(S).isIframe})},{default:u(()=>[i(mt,{style:Se({padding:!d.value||b(S).isLink&&b(S).isIframe?"0":"15px",transition:"padding 0.3s ease-in-out"})},null,8,["style"]),b(_).isFooter?(c(),A(Ss,{key:0})):U("",!0)]),_:1},8,["class"])]),_:1})}}});export{ht as L,yt as S,ys as T,Ns as _,Os as a};
