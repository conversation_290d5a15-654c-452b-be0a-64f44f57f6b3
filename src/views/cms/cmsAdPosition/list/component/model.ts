export interface CmsAdPositionTableColumns {    
    id:number;  // ID    
    name:string;  // 位置名称    
}


export interface CmsAdPositionInfoData {    
    id:number|undefined;        // ID    
    name:string|undefined; // 位置名称    
}


export interface CmsAdPositionTableDataState {
    ids:any[];
    tableData: {
        data: Array<CmsAdPositionTableColumns>;
        total: number;
        loading: boolean;
        param: {
            pageNum: number;
            pageSize: number;            
            dateRange: string[];
        };
    };
}


export interface CmsAdPositionEditState{
    loading:boolean;
    isShowDialog: boolean;
    formData:CmsAdPositionInfoData;
    rules: object;
}