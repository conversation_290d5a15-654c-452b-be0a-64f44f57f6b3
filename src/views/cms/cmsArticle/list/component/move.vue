<template>
  <div class="cms-article-move">
    <el-dialog v-model="isShowDialog" :close-on-click-modal="false" :destroy-on-close="true" width="600px">
      <template #header>
        <div>文章移动</div>
      </template>
      <div class="move-category-container">
        <div class="move-title">选择目标栏目</div>
        <el-table
          ref="treeTableRef"
          :data="cmsCategoryOptions"
          row-key="id"
          border
          default-expand-all
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          highlight-current-row
          @row-click="handleCategorySelect"
        >
          <el-table-column prop="name" label="栏目名称">
            <template #default="{row}">
              <span :class="{'disabled-row': row.id === formData.sourceCategoryId}">
                {{ row.name }}
                <span v-if="row.id === formData.sourceCategoryId" class="disabled-text">(当前栏目)</span>
              </span>
            </template>
          </el-table-column>
          <el-table-column label="选择" width="100" align="center">
            <template #default="{row}">
              <div v-if="row.type === 'list'" @click.stop="handleRadioClick(row)">
                <el-radio
                  :model-value="selectedCategoryId"
                  :label="row.id"
                  class="custom-radio"
                  :disabled="row.id === formData.sourceCategoryId"
                >&nbsp;</el-radio>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" :loading="loading" @click="onSubmit">确 定</el-button>
          <el-button @click="onCancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { defineComponent, getCurrentInstance, reactive, ref, toRefs } from "vue";
import { ElMessage } from "element-plus";
import { listCmsPerCategorys } from "/src/api/cms/cmsCategory";
import { moveCmsArticle } from "/src/api/cms/cmsArticle";
import { events } from "/@/eventBus";

interface MoveFormData {
  ids: number[];
  categoryId?: number; // 目标栏目 ID
  sourceCategoryId?: number; // 源栏目 ID
}

interface MoveDialogState {
  loading: boolean;
  isShowDialog: boolean;
  formData: MoveFormData;
  rules: Record<string, any[]>;
}

export default defineComponent({
  name: "MoveDialog",
  setup() {
    const { proxy } = <any>getCurrentInstance();
    const treeTableRef = ref();
    const cmsCategoryOptions = ref<any[]>([]);

    // 选中的栏目 ID
    const selectedCategoryId = ref<number | null>(null);

    const state = reactive<MoveDialogState>({
      loading: false,
      isShowDialog: false,
      formData: {
        ids: [],
        categoryId: undefined,
        sourceCategoryId: undefined
      },
      rules: {
        categoryId: [
          { required: true, message: "请选择目标栏目", trigger: "change" }
        ]
      }
    });

    // 获取栏目选项
    const getCategoryOptions = () => {
      if (!state.formData.sourceCategoryId) {
        // 如果没有源栏目 ID，则不进行查询
        cmsCategoryOptions.value = [];
        return;
      }

      listCmsPerCategorys({
        categoryId: state.formData.sourceCategoryId,
        permission: "move"
      }).then((res: any) => {
        // 处理数据
        const list = res.data.list || [];

        // 标记与当前分类ID相同的项
        list.forEach((item: any) => {
          if (item.id === state.formData.sourceCategoryId) {
            item.disabled = true;
          }
        });

        cmsCategoryOptions.value = proxy.handleTree(list, "id", "parentId");
      });
    };

    // 打开弹窗
    const openDialog = (ids: number[], sourceCategoryId?: number) => {
      resetForm();
      state.formData.ids = ids;

      // 设置源栏目 ID
      if (sourceCategoryId) {
        state.formData.sourceCategoryId = sourceCategoryId;
        // 获取栏目选项
        getCategoryOptions();
      } else {
        ElMessage.warning('缺少源栏目 ID，无法获取目标栏目');
      }

      state.isShowDialog = true;
    };

    // 关闭弹窗
    const closeDialog = () => {
      state.isShowDialog = false;
    };

    // 取消
    const onCancel = () => {
      closeDialog();
    };

    // 重置表单
    const resetForm = () => {
      state.formData = {
        ids: [],
        categoryId: undefined,
        sourceCategoryId: undefined
      };
      // 清空选中的栏目 ID
      selectedCategoryId.value = null;
    };

    // 设置选中的栏目
    const setSelectedCategory = (row: any) => {
      if (!row || row.type !== 'list') return;

      // 如果选中的栏目与源栏目相同，则不允许选择
      if (row.id === state.formData.sourceCategoryId) {
        return;
      }

      selectedCategoryId.value = row.id;
      state.formData.categoryId = row.id;

      // 设置当前行高亮
      treeTableRef.value?.setCurrentRow(row);
    };

    // 处理栏目选择
    const handleCategorySelect = (row: any) => {
      if (row && row.type === 'list') {
        setSelectedCategory(row);
      }
    };

    // 处理单选框点击
    const handleRadioClick = (row: any) => {
      if (row && row.type === 'list') {
        setSelectedCategory(row);
      }
    };

    // 提交表单
    const onSubmit = () => {
      state.loading = true;

      // 验证必填字段
      if (!state.formData.categoryId) {
        ElMessage.error('请选择目标栏目');
        state.loading = false;
        return;
      }

      // 检查目标栏目是否与源栏目相同
      if (state.formData.categoryId === state.formData.sourceCategoryId) {
        ElMessage.error('目标栏目不能与源栏目相同');
        state.loading = false;
        return;
      }

      // 调用移动API
      const moveData = {
        ids: state.formData.ids,
        categoryId: state.formData.categoryId,
        sourceCategoryId: state.formData.sourceCategoryId
      };

      // 使用 moveCmsArticle 接口移动文章
      moveCmsArticle(moveData)

        .then(() => {
          ElMessage.success('移动成功');
          closeDialog();
          // 触发刷新列表事件
          events.emit('refreshArticleList');
        })
        .catch(() => {
          ElMessage.error('移动失败');
        })
        .finally(() => {
          state.loading = false;
        });
    };

    return {
      treeTableRef,
      cmsCategoryOptions,
      selectedCategoryId,
      setSelectedCategory,
      handleCategorySelect,
      handleRadioClick,
      openDialog,
      closeDialog,
      onCancel,
      onSubmit,
      ...toRefs(state)
    };
  }
});
</script>

<style lang="scss" scoped>
.cms-article-move {
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
  }

  .move-category-container {
    padding: 0 20px;

    .move-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
    }

    .el-table {
      margin-bottom: 20px;
      max-height: 400px;
      overflow-y: auto;
    }

    .custom-radio {
      margin-right: 0;

      :deep(.el-radio__label) {
        display: none;
      }
    }

    .disabled-row {
      color: #999;
      cursor: not-allowed;
    }

    .disabled-text {
      color: #999;
      font-size: 12px;
      margin-left: 5px;
    }
  }
}
</style>
