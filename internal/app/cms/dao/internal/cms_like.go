// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// CmsLikeDao is the data access object for table cms_like.
type CmsLikeDao struct {
	table   string         // table is the underlying table name of the DAO.
	group   string         // group is the database configuration group name of current DAO.
	columns CmsLikeColumns // columns contains all the column names of Table for convenient usage.
}

// CmsLikeColumns defines and stores column names for table cms_like.
type CmsLikeColumns struct {
	Id        string // ID
	ArticleId string // 文章ID
	MemberId  string // 会员ID
	CreatedAt string //
}

// cmsLikeColumns holds the columns for table cms_like.
var cmsLikeColumns = CmsLikeColumns{
	Id:        "id",
	ArticleId: "article_id",
	MemberId:  "member_id",
	CreatedAt: "created_at",
}

// NewCmsLikeDao creates and returns a new DAO object for table data access.
func NewCmsLikeDao() *CmsLikeDao {
	return &CmsLikeDao{
		group:   "default",
		table:   "cms_like",
		columns: cmsLikeColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *CmsLikeDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *CmsLikeDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *CmsLikeDao) Columns() CmsLikeColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *CmsLikeDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *CmsLikeDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *CmsLikeDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
