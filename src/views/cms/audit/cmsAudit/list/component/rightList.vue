<template>
  <div class="right-content">
    <div class="audit-cmsAudit-search mb15">
      <el-form :model="tableData.param" ref="queryRef" :inline="true" label-width="100px">
        <el-row>
          <el-col :span="8" class="colBlock">
            <el-form-item label="文章ID" prop="articleId">
              <el-input
                v-model="tableData.param.articleId"
                placeholder="请输入文章ID"
                clearable
                @keyup.enter.native="cmsAuditList"
              />
            </el-form-item>
          </el-col>

          <el-col :span="8" class="colBlock">
            <el-form-item label="状态" prop="status">
              <el-select filterable v-model="tableData.param.status" placeholder="请选择状态" clearable style="width:200px;">
                <el-option
                    v-for="dict in cms_audit_status"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :class="!showAll ? 'colBlock' : 'colNone'">
            <el-form-item>
              <el-button type="primary" @click="cmsAuditList"><el-icon><ele-Search /></el-icon>搜索</el-button>
              <el-button @click="resetQuery(queryRef)"><el-icon><ele-Refresh /></el-icon>重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" @click="refreshList">
            <el-icon><ele-Refresh /></el-icon>刷新
          </el-button>
        </el-col>
      </el-row>
    </div>
    <el-table v-loading="loading" :data="tableData.data" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="20" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="上步" align="center" prop="stepTitle" />
      <el-table-column label="审核员" align="center" prop="userInfo.userNickname" />
      <el-table-column label="当前" align="center" prop="nextStepTitle" />
      <el-table-column label="文章" align="center" prop="articleInfo.title">
        <template #default="scope">
          <a :href="scope.row.previewUrl" target="_blank">{{ scope.row.articleInfo.title }}</a>
        </template>
      </el-table-column>
      <el-table-column label="栏目" align="center" prop="categoryInfo.name" />
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <el-tag v-if="scope.row.status === '1'" type="success">
            {{ statusFormat(scope.row) }}
          </el-tag>
          <el-tag v-else type="warning">
            {{ statusFormat(scope.row) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="审核意见" align="center" prop="content" />
      <el-table-column label="审核时间" align="center" prop="auditedAt" width="180px">
        <template #default="scope">
          <span>{{ scope.row.auditedAt }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createdAt" width="180px">
        <template #default="scope">
          <span>{{ scope.row.createdAt }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding" fixed="right" width="150px">
        <template #default="scope">
          <el-button
            type="primary"
            link
            @click="handleView(scope.row)"
          ><el-icon><ele-View /></el-icon>详情</el-button>
          <template v-if="scope.row._disableAudit">
            <el-button
              type="info"
              link
              disabled
              title="已完成审核流程，无法再次审核"
            ><el-icon><ele-Edit /></el-icon>审核</el-button>
          </template>
          <template v-else>
            <el-button
              type="primary"
              link
              @click="handleUpdate(scope.row)"
              v-auth="'api/v1/cms/audit/cmsAudit/audit'"
            ><el-icon><ele-Edit /></el-icon>审核</el-button>
          </template>

        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="tableData.total>0"
      :total="tableData.total"
      v-model:page="tableData.param.pageNum"
      v-model:limit="tableData.param.pageSize"
      @pagination="cmsAuditList"
    />

    <!-- 审核文章对话框 -->
    <ApiV1CmsAuditCmsAuditAudit ref="auditRef" :statusOptions="cms_audit_status" @cmsAuditList="cmsAuditList"></ApiV1CmsAuditCmsAuditAudit>
    <ApiV1CmsAuditCmsAuditDetail ref="detailRef" :statusOptions="cms_audit_status" @cmsAuditList="cmsAuditList"></ApiV1CmsAuditCmsAuditDetail>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, ref, getCurrentInstance, toRefs, onMounted, watch } from "vue";
import { ElMessage, ElMessageBox, FormInstance } from "element-plus";
import { listCmsAudit } from "/@/api/cms/audit/cmsAudit";
import { CmsAuditInfoData, CmsAuditTableColumns } from "./model";
import { Search, Plus, Edit, Refresh, DeleteFilled, ArrowDown, ArrowUp, View } from '@element-plus/icons-vue';
import ApiV1CmsAuditCmsAuditAudit from "./audit.vue";
import ApiV1CmsAuditCmsAuditDetail from "./detail.vue";

interface CmsAuditTableDataState {
  ids: number[];
  tableData: {
    data: Array<CmsAuditInfoData>;
    total: number;
    loading: boolean;
    param: {
      pageNum: number;
      pageSize: number;
      id?: string;
      articleId?: string;
      categoryId?: string;
      userId?: string;
      stepId?: string;
      nextStepId?: string;
      status?: string;
      content?: string;
    };
  };
}

export default defineComponent({
  name: "RightList",
  components: {
    'ele-Search': Search,
    'ele-Plus': Plus,
    'ele-Edit': Edit,
    'ele-Refresh': Refresh,
    'ele-DeleteFilled': DeleteFilled,
    'ele-ArrowDown': ArrowDown,
    'ele-ArrowUp': ArrowUp,
    'ele-View': View,
    ApiV1CmsAuditCmsAuditAudit,
    ApiV1CmsAuditCmsAuditDetail
  },
  props: {
    currentNode: {
      type: Object,
      default: null
    }
  },
  setup(props) {
    const { proxy } = <any>getCurrentInstance();
    const loading = ref(false);
    const showAll = ref(false);
    const word = ref("更多");
    const auditRef = ref();
    const detailRef = ref();
    const queryRef = ref<FormInstance>();
    const single = ref(true);
    const multiple = ref(true);

    // 字典选项数据
    const {
      cms_audit_status
    } = proxy.useDict(
      "cms_audit_status"
    );

    const state = reactive<CmsAuditTableDataState>({
      ids: [],
      tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
          pageNum: 1,
          pageSize: 10,
        },
      },
    });

    // 监听当前节点变化
    watch(() => props.currentNode, (newNode) => {
      if (newNode) {
        state.tableData.param.categoryId = newNode.id.toString();
        state.tableData.param.pageNum = 1;
        cmsAuditList();
      }
    });

    // 初始化表格数据
    const initTableData = () => {
      cmsAuditList();
    };

    /** 重置按钮操作 */
    const resetQuery = (formEl: FormInstance | undefined) => {
      if (!formEl) return;
      formEl.resetFields();
      // 保持当前选中的分类
      if (props.currentNode) {
        state.tableData.param.categoryId = props.currentNode.id.toString();
      }
      cmsAuditList();
    };

    // 获取列表数据
    const cmsAuditList = () => {
      loading.value = true;
      listCmsAudit(state.tableData.param).then((res: any) => {
        let list = res.data.list ?? [];

        // 处理每一行数据
        list.forEach((item: any) => {
          // 添加一个标记，指示该行是否应该禁用审核按钮
          // 下一步骤为 0 时，禁用审核按钮
          if (item.nextStepId === 0 || item.nextStepId === '0') {
            item._disableAudit = true;
          } else {
            // 当前步骤类型为 end，且状态为 1（通过）时，禁用审核按钮
            item._disableAudit = Boolean(item.stepInfo && item.stepInfo.type === 'end' && item.status === '1');
          }
        });

        state.tableData.data = list;
        state.tableData.total = res.data.total;
        loading.value = false;
      });
    };

    // 刷新列表
    const refreshList = () => {
      cmsAuditList();
      ElMessage.success('刷新成功');
    };

    const toggleSearch = () => {
      showAll.value = !showAll.value;
      word.value = showAll.value ? "收起" : "更多";
    };

    // 状态字典翻译
    const statusFormat = (row: CmsAuditTableColumns) => {
      return proxy.selectDictLabel(cms_audit_status.value, row.status);
    };

    // 多选框选中数据
    const handleSelectionChange = (selection: Array<CmsAuditInfoData>) => {
      state.ids = selection.map(item => item.id);
      single.value = selection.length != 1;
      multiple.value = !selection.length;
    };

    const handleAdd = () => {
      auditRef.value.openDialog();
    };

    // 判断是否禁用审核按钮
    const isAuditDisabled = (row: any) => {
      if (!row) return false;

      // 下一步骤为 0 时，禁用审核按钮
      if (row.nextStepId === 0 || row.nextStepId === '0') return true;

      // 当前步骤类型为 end，且状态为 1（通过）时，禁用审核按钮
      return Boolean(row.stepInfo && row.stepInfo.type === 'end' && row.status === '1');
    };

    const handleUpdate = (row?: CmsAuditInfoData) => {
      if (row) {
        // 如果直接传入了行对象，直接使用
        auditRef.value.openDialog(row);
      } else if (state.ids.length > 0) {
        // 如果没有传入行对象，但有选中的行，查找选中的行对象
        const selectedRow = state.tableData.data.find(item => item.id === state.ids[0]);
        if (selectedRow) {
          auditRef.value.openDialog(selectedRow);
        } else {
          ElMessage.warning('请选择要审核的记录');
        }
      } else {
        ElMessage.warning('请选择要审核的记录');
      }
    };


    // 查看详情
    const handleView = (row: CmsAuditInfoData) => {
      // 确保传递正确的参数
      if (row && row.id) {
        detailRef.value.openDialog(row);
      } else {
        ElMessage.warning('无法查看详情，数据不完整');
      }
    };

    onMounted(() => {
      initTableData();
    });

    return {
      loading,
      showAll,
      word,
      auditRef,
      detailRef,
      queryRef,
      single,
      multiple,
      cms_audit_status,
      statusFormat,
      cmsAuditList,
      toggleSearch,
      handleSelectionChange,
      handleAdd,
      handleUpdate,
      handleView,
      isAuditDisabled,
      resetQuery,
      refreshList,
      ...toRefs(state),
    };
  },
});
</script>

<style lang="scss" scoped>
.right-content {
  flex: 1;
  padding: 10px;
  overflow: auto;

  // 去掉文章链接的下划线
  :deep(a) {
    text-decoration: none;
    color: #409EFF;

    &:hover {
      color: #66b1ff;
    }
  }
}

.colNone {
  display: none;
}

.colBlock {
  display: block;
}
</style>
