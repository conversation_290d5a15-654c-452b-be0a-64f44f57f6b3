export interface UcenterLoginLogTableColumns {    
    id:number;  // 访问ID    
    loginName:string;  // 登录账号    
    ipaddr:string;  // 登录IP地址    
    loginLocation:string;  // 登录地点    
    browser:string;  // 浏览器类型    
    os:string;  // 操作系统    
    status:number;  // 登录状态（0成功 1失败）    
    msg:string;  // 提示消息    
    loginTime:string;  // 登录时间    
}


export interface UcenterLoginLogInfoData {    
    id:number|undefined;        // 访问ID    
    loginName:string|undefined; // 登录账号    
    ipaddr:string|undefined; // 登录IP地址    
    loginLocation:string|undefined; // 登录地点    
    browser:string|undefined; // 浏览器类型    
    os:string|undefined; // 操作系统    
    status:number|undefined; // 登录状态（0成功 1失败）    
    msg:string|undefined; // 提示消息    
    loginTime:string|undefined; // 登录时间    
}


export interface UcenterLoginLogTableDataState {
    ids:any[];
    tableData: {
        data: Array<UcenterLoginLogTableColumns>;
        total: number;
        loading: boolean;
        param: {
            pageNum: number;
            pageSize: number;            
            loginName: string|undefined;            
            ipaddr: string|undefined;            
            dateRange: string[];
        };
    };
}


export interface UcenterLoginLogEditState{
    loading:boolean;
    isShowDialog: boolean;
    formData:UcenterLoginLogInfoData;
    rules: object;
}