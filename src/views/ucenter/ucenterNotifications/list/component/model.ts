export interface UcenterNotificationsTableColumns {
    id:number;  // ID
    type:number;  // 通知类型
    content:string;  // 内容
    status:number;  // 状态
    createdAt:string;  // 创建日期
    memberId:number;  // 会员ID
}


export interface UcenterNotificationsInfoData {
    id:number|undefined;        // ID
    type:number|undefined; // 通知类型
    content:string|undefined; // 内容
    status:boolean; // 状态
    createdAt:string|undefined; // 创建日期
    memberId:number|undefined; // 会员ID
}


export interface UcenterNotificationsTableDataState {
    ids:any[];
    tableData: {
        data: Array<UcenterNotificationsTableColumns>;
        total: number;
        loading: boolean;
        param: {
            username: '',
            pageNum: number;
            pageSize: number;
            dateRange: string[];
        };
    };
}


export interface UcenterNotificationsEditState{
    loading:boolean;
    isShowDialog: boolean;
    formData:UcenterNotificationsInfoData;
    rules: object;
}
