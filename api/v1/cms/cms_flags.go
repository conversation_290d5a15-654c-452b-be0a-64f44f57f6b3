// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-04-24 10:40:09
// 生成路径: api/v1/cms/cms_flags.go
// 生成人：zhanghui
// desc:推荐属性相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package cms

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/cms/model"
)

// CmsFlagsSearchReq 分页请求参数
type CmsFlagsSearchReq struct {
	g.Meta `path:"/list" tags:"cms后台/推荐属性" method:"get" summary:"推荐属性列表"`
	commonApi.Author
	model.CmsFlagsSearchReq
}

// CmsFlagsSearchRes 列表返回结果
type CmsFlagsSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.CmsFlagsSearchRes
}

// CmsFlagsAddReq 添加操作请求参数
type CmsFlagsAddReq struct {
	g.Meta `path:"/add" tags:"cms后台/推荐属性" method:"post" summary:"推荐属性添加"`
	commonApi.Author
	*model.CmsFlagsAddReq
}

// CmsFlagsAddRes 添加操作返回结果
type CmsFlagsAddRes struct {
	commonApi.EmptyRes
}

// CmsFlagsEditReq 修改操作请求参数
type CmsFlagsEditReq struct {
	g.Meta `path:"/edit" tags:"cms后台/推荐属性" method:"put" summary:"推荐属性修改"`
	commonApi.Author
	*model.CmsFlagsEditReq
}

// CmsFlagsEditRes 修改操作返回结果
type CmsFlagsEditRes struct {
	commonApi.EmptyRes
}

// CmsFlagsGetReq 获取一条数据请求
type CmsFlagsGetReq struct {
	g.Meta `path:"/get" tags:"cms后台/推荐属性" method:"get" summary:"获取推荐属性信息"`
	commonApi.Author
	Id uint `p:"id" v:"required#主键必须"` //通过主键获取
}

// CmsFlagsGetRes 获取一条数据结果
type CmsFlagsGetRes struct {
	g.Meta `mime:"application/json"`
	*model.CmsFlagsInfoRes
}

// CmsFlagsDeleteReq 删除数据请求
type CmsFlagsDeleteReq struct {
	g.Meta `path:"/delete" tags:"cms后台/推荐属性" method:"delete" summary:"删除推荐属性"`
	commonApi.Author
	Ids []uint `p:"ids" v:"required#主键必须"` //通过主键删除
}

// CmsFlagsDeleteRes 删除数据返回
type CmsFlagsDeleteRes struct {
	commonApi.EmptyRes
}

// CmsFlagsByCategoryRes 列表返回结果
type CmsFlagsByCategoryIdReq struct {
	g.Meta `path:"/listByCategoryId" tags:"cms后台/推荐属性" method:"get" summary:"根据栏目获取推荐属性列表"`
	commonApi.Author
	CategoryId uint `p:"categoryId" v:"required#栏目ID不能为空"`
}

// CmsFlagsByCategoryRes 列表返回结果
type CmsFlagsByCategoryIdRes struct {
	g.Meta `mime:"application/json"`
	List   []*model.CmsFlagsListRes `json:"list"`
}
