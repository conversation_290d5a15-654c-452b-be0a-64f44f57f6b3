<template>
  <!-- 用户消息详情抽屉 -->  
  <div class="ucenter-ucenterMessage-detail">
    <el-drawer v-model="isShowDialog" size="80%" direction="ltr">
      <template #header>
        <h4>用户消息详情</h4>
      </template>
      <el-form ref="formRef" :model="formData" label-width="100px">          
        <el-row>        
          <el-col :span="12">          
            <el-form-item label="ID">{{ formData.id }}</el-form-item>          
          </el-col>        
          <el-col :span="12">          
            <el-form-item label="发送人member_id">{{ formData.sender }}</el-form-item>          
          </el-col>        
          <el-col :span="12">          
            <el-form-item label="收信人member_id">{{ formData.receiver }}</el-form-item>          
          </el-col>        
          <el-col :span="12">          
            <el-form-item label="内容">{{ formData.content }}</el-form-item>          
          </el-col>        
          <el-col :span="12">
            <el-form-item label="创建时间">{{ proxy.parseTime(formData.createdAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-form-item>
          </el-col>        
          <el-col :span="12">          
            <el-form-item label="状态:{0:未读,1:已读,2:已回复}">{{ formData.status }}</el-form-item>          
          </el-col>        
          <el-col :span="12">          
            <el-form-item label="父级消息ID">{{ formData.pid }}</el-form-item>          
          </el-col>      
        </el-row>      
      </el-form>
    </el-drawer>
  </div>
</template>
<script lang="ts">
  import { reactive, toRefs, defineComponent,ref,unref,getCurrentInstance,computed } from 'vue';
  import {ElMessageBox, ElMessage, FormInstance,UploadProps} from 'element-plus';  
  import {
    listUcenterMessage,
    getUcenterMessage,
    delUcenterMessage,
    addUcenterMessage,
    updateUcenterMessage,    
  } from "/@/api/ucenter/ucenterMessage";  
  import {
    UcenterMessageTableColumns,
    UcenterMessageInfoData,
    UcenterMessageTableDataState,
    UcenterMessageEditState,    
  } from "/@/views/ucenter/ucenterMessage/list/component/model"
  export default defineComponent({
    name:"apiV1UcenterUcenterMessageDetail",
    components:{      
    },
    props:{      
    },
    setup(props,{emit}) {      
      const {proxy} = <any>getCurrentInstance()
      const formRef = ref<HTMLElement | null>(null);
      const menuRef = ref();      
      const state = reactive<UcenterMessageEditState>({
        loading:false,
        isShowDialog: false,
        formData: {          
          id: undefined,          
          sender: undefined,          
          receiver: undefined,          
          content: undefined,          
          createdAt: undefined,          
          updatedAt: undefined,          
          status: false ,          
          pid: undefined,          
        },
        // 表单校验
        rules: {          
          id : [
              { required: true, message: "ID不能为空", trigger: "blur" }
          ],          
          status : [
              { required: true, message: "状态:{0:未读,1:已读,2:已回复}不能为空", trigger: "blur" }
          ],          
        }
      });
        // 打开弹窗
        const openDialog = (row?: UcenterMessageInfoData) => {
          resetForm();
          if(row) {
            getUcenterMessage(row.id!).then((res:any)=>{
              const data = res.data;              
              state.formData = data;              
            })
          }
          state.isShowDialog = true;
        };
        // 关闭弹窗
        const closeDialog = () => {
          state.isShowDialog = false;
        };
        // 取消
        const onCancel = () => {
          closeDialog();
        };
        const resetForm = ()=>{
          state.formData = {            
            id: undefined,            
            sender: undefined,            
            receiver: undefined,            
            content: undefined,            
            createdAt: undefined,            
            updatedAt: undefined,            
            status: false ,            
            pid: undefined,            
          }
        };        
        return {
          proxy,
          openDialog,
          closeDialog,
          onCancel,
          menuRef,
          formRef,          
          ...toRefs(state),
        };
      }
  })
</script>
<style scoped>  
  .ucenter-ucenterMessage-detail :deep(.el-form-item--large .el-form-item__label){
    font-weight: bolder;
  }
  .pic-block{
    margin-right: 8px;
  }
  .file-block{
    width: 100%;
    border: 1px solid var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
    margin-bottom: 5px;
    padding: 3px 6px;
  }
  .ml-2{margin-right: 5px;}
</style>