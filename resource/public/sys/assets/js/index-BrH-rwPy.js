import{d as ne,r as m,f as ie,X as de,ad as pe,h as me,i as ue,ag as a,c as ce,o as D,P as e,I as _e,H as o,a as B,u as n,L as i,M as fe,G as C,J as we,t as ve,ab as N,Q as be}from"./@vue-C21YZbHS.js";import ge from"./editRole-DMqjZBD8.js";import De from"./dataScope-BaI1o2Hk.js";import{s as ye,g as Re,d as ke}from"./index-BvZSjrda.js";import{g as he,a as Ce}from"./index-BrYF22oA.js";import{_ as Se}from"./userList.vue_vue_type_script_setup_true_lang-DaI4YeBM.js";import{s as Le}from"./index-xkwPUaHp.js";import{E as xe,a as Ve,d as Ue}from"./element-plus-CUmVNDWO.js";import{a as Ae}from"./index-BGmsw1a8.js";import"./editUser.vue_vue_type_script_setup_true_lang-Dl2SkwSv.js";import"./@element-plus-_Cc-TEQX.js";import"./lodash-es-S0Y0Up6J.js";import"./@vueuse-D_5IXmcI.js";import"./@popperjs-D_chPuIy.js";import"./@ctrl-r5W6hzzQ.js";import"./dayjs-CepoAVPu.js";import"./@intlify-D8kAWvSi.js";import"./async-validator-9PlIezaS.js";import"./memoize-one-BdPwpGay.js";import"./normalize-wheel-es-BQoi3Ox2.js";import"./@floating-ui-DwceP2Gb.js";import"./pinia-DwJf82dV.js";import"./vue-router-BDT9dCQ7.js";import"./js-cookie-Cz0CWeBA.js";import"./nprogress-Scw6VIZr.js";import"./axios-B62VicFi.js";import"./qs-D41HS0uK.js";import"./side-channel-DytTmCZg.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-DbsE5JW_.js";import"./crypto-js-oM26lpET.js";import"./side-channel-list-C_7Uk4RP.js";import"./side-channel-map-BUokNT8A.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-bSkmVQJL.js";import"./vue-i18n-BkLk9Ti1.js";import"./vue-iNFchrNl.js";import"./vue-clipboard3-Dr8V5xR7.js";import"./clipboard-DzJOpNE6.js";import"./mitt-DJ65BbbF.js";import"./vue-grid-layout-CIPjP0XN.js";import"./vue-simple-uploader-j6sX1TKo.js";import"./vue-ueditor-wrap-CJ5gjsd3.js";const Ee={class:"system-role-container"},Ie={class:"system-user-search mb15"},Be={class:"el-dropdown-link"},Ne=ne({name:"apiV1SystemRoleList",__name:"index",setup(Oe){const S=m(),{proxy:y}=ie(),{sys_user_sex:O}=y.useDict("sys_user_sex");m();const L=m(),R=m(),x=m(),V=m(),v=m([]),U=m(0),d=de({isShowDialog:!1,deptData:[],userListParam:{roleId:void 0},selectRow:{},tableData:{data:[],total:0,loading:!1,param:{roleName:"",roleStatus:"",pageNum:1,pageSize:10}}}),{tableData:c,selectRow:P,deptData:T,userListParam:$,isShowDialog:b}=pe(d),z=()=>{_()},_=()=>{const l=[];Re(d.tableData.param).then(t=>{(t.data.list??[]).map(s=>{l.push({id:s.id,pid:s.pid,status:s.status,listOrder:s.listOrder,name:s.name,remark:s.remark,dataScope:s.dataScope,userCnt:s.userCnt,createdAt:s.createdAt})}),V.value=l,d.tableData.data=y.handleTree(l??[],"id","pid","children",!0)})},M=l=>{d.selectRow=l,d.userListParam.roleId=l.id,d.deptData.length==0?he().then(t=>{d.deptData=t.data.deps,d.isShowDialog=!0}):d.isShowDialog=!0},F=()=>{R.value.openDialog()},A=l=>{R.value.openDialog(N(l))},G=l=>{x.value.openDialog(N(l))},H=l=>{const t=Ue.service();U.value=l.id,Ce(l.id).then(p=>{t.close(),v.value=p.data.userList?p.data.userList.map(s=>s.id):[],S.value.openDialog()})},J=l=>{xe.confirm(`此操作将永久删除角色：“${l.name}”，是否继续?`,"提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(()=>{ke(l.id).then(()=>{Ve.success("删除成功"),y.$refs.editRoleRef.resetMenuSession(),_()})}).catch(()=>{})},Q=()=>{L.value.userList()};me(()=>{z()});const X=l=>{var s,f;let t=l.split("_"),p=(f=(s=V.value)==null?void 0:s.filter(k=>k.id==parseInt(t[1])))==null?void 0:f[0];switch(t[0]){case"resource":A(p);break;case"data":G(p);break;case"user":H(p);break}};ue(v,l=>{j(l)});const j=l=>{ye({roleId:U.value,userIds:l}).then(t=>{_()})};return(l,t)=>{const p=a("el-input"),s=a("el-form-item"),f=a("el-option"),k=a("el-select"),q=a("ele-Search"),w=a("el-icon"),g=a("el-button"),K=a("ele-FolderAdd"),W=a("el-form"),u=a("el-table-column"),Y=a("el-link"),E=a("el-tag"),Z=a("ele-EditPen"),ee=a("ele-ArrowDown"),h=a("el-dropdown-item"),te=a("el-dropdown-menu"),oe=a("el-dropdown"),le=a("ele-DeleteFilled"),ae=a("el-table"),se=a("el-card"),re=a("el-dialog");return D(),ce("div",Ee,[e(se,{shadow:"hover"},{default:o(()=>[B("div",Ie,[e(W,{inline:!0},{default:o(()=>[e(s,{label:"角色名称"},{default:o(()=>[e(p,{modelValue:n(c).param.roleName,"onUpdate:modelValue":t[0]||(t[0]=r=>n(c).param.roleName=r),placeholder:"请输入角色名称",class:"w-50 m-2",clearable:""},null,8,["modelValue"])]),_:1}),e(s,{label:"状态"},{default:o(()=>[e(k,{placeholder:"请选择状态",class:"w-50 m-2",modelValue:n(c).param.roleStatus,"onUpdate:modelValue":t[1]||(t[1]=r=>n(c).param.roleStatus=r),clearable:"",style:{width:"120px"}},{default:o(()=>[e(f,{label:"启用",value:"1"}),e(f,{label:"禁用",value:"0"})]),_:1},8,["modelValue"])]),_:1}),e(s,null,{default:o(()=>[e(g,{size:"default",type:"primary",class:"ml10",onClick:_},{default:o(()=>[e(w,null,{default:o(()=>[e(q)]),_:1}),t[4]||(t[4]=i(" 查询 "))]),_:1}),e(g,{size:"default",type:"success",class:"ml10",onClick:F},{default:o(()=>[e(w,null,{default:o(()=>[e(K)]),_:1}),t[5]||(t[5]=i(" 新增角色 "))]),_:1})]),_:1})]),_:1})]),e(ae,{data:n(c).data,style:{width:"100%"},"row-key":"id","default-expand-all":"","tree-props":{children:"children",hasChildren:"hasChildren"}},{default:o(()=>[e(u,{prop:"name",label:"角色名称","show-overflow-tooltip":""}),e(u,{prop:"listOrder",label:"排序","show-overflow-tooltip":""}),e(u,{prop:"userCnt",label:"用户数量",align:"center"},{default:o(r=>[e(Y,{type:"primary",onClick:I=>M(r.row)},{default:o(()=>[i(fe(r.row.userCnt),1)]),_:2},1032,["onClick"])]),_:1}),e(u,{prop:"status",label:"角色状态","show-overflow-tooltip":""},{default:o(r=>[r.row.status===1?(D(),C(E,{key:0,type:"success"},{default:o(()=>t[6]||(t[6]=[i("启用")])),_:1})):(D(),C(E,{key:1,type:"info"},{default:o(()=>t[7]||(t[7]=[i("禁用")])),_:1}))]),_:1}),e(u,{prop:"remark",label:"角色描述","show-overflow-tooltip":""}),e(u,{prop:"createdAt",label:"创建时间","show-overflow-tooltip":""}),e(u,{label:"操作",width:"220"},{default:o(r=>[e(g,{size:"small",text:"",type:"primary",onClick:I=>A(r.row)},{default:o(()=>[e(w,null,{default:o(()=>[e(Z)]),_:1}),t[8]||(t[8]=i("修改"))]),_:2},1032,["onClick"]),e(oe,{class:"auth-action",onCommand:X},{dropdown:o(()=>[e(te,{class:"auth-action-menu"},{default:o(()=>[e(h,{command:"resource_"+r.row.id},{default:o(()=>t[10]||(t[10]=[i("资源权限")])),_:2},1032,["command"]),e(h,{command:"data_"+r.row.id},{default:o(()=>t[11]||(t[11]=[i("数据权限")])),_:2},1032,["command"]),e(h,{command:"user_"+r.row.id},{default:o(()=>t[12]||(t[12]=[i("用户授权")])),_:2},1032,["command"])]),_:2},1024)]),default:o(()=>[B("span",Be,[t[9]||(t[9]=i(" 授权 ")),e(w,null,{default:o(()=>[e(ee)]),_:1})])]),_:2},1024),e(g,{size:"small",text:"",type:"primary",onClick:I=>J(r.row)},{default:o(()=>[e(w,null,{default:o(()=>[e(le)]),_:1}),t[13]||(t[13]=i("删除"))]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1}),e(ge,{ref_key:"editRoleRef",ref:R,onGetRoleList:_,roleData:n(c).data},null,8,["roleData"]),e(De,{ref_key:"dataScopeRef",ref:x,onGetRoleList:_},null,512),e(re,{title:n(P).name+"-用户列表",modelValue:n(b),"onUpdate:modelValue":t[2]||(t[2]=r=>ve(b)?b.value=r:null),width:"70vw"},{default:o(()=>[n(b)?(D(),C(Se,{key:0,ref_key:"userListRef",ref:L,"dept-data":n(T),"gender-data":n(O),param:n($),onGetUserList:Q},null,8,["dept-data","gender-data","param"])):we("",!0)]),_:1},8,["title","modelValue"]),_e(e(Le,{ref_key:"selectUserRef",ref:S,modelValue:v.value,"onUpdate:modelValue":t[3]||(t[3]=r=>v.value=r)},null,8,["modelValue"]),[[be,!1]])])}}}),Pt=Ae(Ne,[["__scopeId","data-v-bcb2864f"]]);export{Pt as default};
