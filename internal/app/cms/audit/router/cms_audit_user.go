// ==========================================================================
// GFast自动生成router操作代码。
// 生成日期：2025-05-07 08:32:03
// 生成路径: internal/app/cms/audit/router/cms_audit_user.go
// 生成人：张徽
// desc:管理员审核设置
// company:云南奇讯科技有限公司
// ==========================================================================

package router

import (
	"context"

	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/internal/app/cms/audit/controller"
)

func (router *Router) BindCmsAuditUserController(ctx context.Context, group *ghttp.RouterGroup) {
	group.Group("/cmsAuditUser", func(group *ghttp.RouterGroup) {
		group.Bind(
			controller.CmsAuditUser,
		)
	})
}
