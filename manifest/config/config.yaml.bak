server:
  address:     ":8808"
  serverRoot: "resource/public"
  dumpRouterMap: false
  routeOverWrite: true
  openapiPath: "/api.json"
  swaggerPath: "/swagger"
  NameToUriType: 3
  maxHeaderBytes: "20KB"
  clientMaxBodySize: "50MB"
  # Logging配置
  logPath: "resource/log/server"                 # 日志文件存储目录路径，建议使用绝对路径。默认为空，表示关闭
  logStdout: true               # 日志是否输出到终端。默认为true
  errorStack: true               # 当Server捕获到异常时是否记录堆栈信息到日志中。默认为true
  errorLogEnabled: true               # 是否记录异常日志信息到日志中。默认为true
  errorLogPattern: "error-{Ymd}.log"  # 异常错误日志文件格式。默认为"error-{Ymd}.log"
  accessLogEnabled: true              # 是否记录访问日志。默认为false
  accessLogPattern: "access-{Ymd}.log" # 访问日志文件格式。默认为"access-{Ymd}.log"

logger:
  path: "resource/log/run"
  file: "{Y-m-d}.log"
  level: "all"
  stdout: true

# Database.
database:
  logger:
    level:   "all"
    stdout:  true
    Path: "resource/log/sql"

  default:
    link:   "mysql:gfast3:gfast333@tcp(localhost:3306)/gfast-v32?charset=utf8mb4&parseTime=true&loc=Local"
    debug:  true
    charset: "utf8mb4" #数据库编码
    dryRun: false #空跑
    maxIdle: 10 #连接池最大闲置的连接数
    maxOpen: 10 #连接池最大打开的连接数
    maxLifetime: "30s" #(单位秒)连接对象可重复使用的时间长度

gfToken:
  cacheKey: "gfToken:"
  timeOut: 10800
  maxRefresh: 5400
  multiLogin: true
  encryptKey: "49c54195e750b04e74a8429b17896586"
  cacheModel: "dist"  #缓存模式 memory OR redis OR dist
  distPath: "./resource/data/distTokenDb" #使用磁盘缓存时配置数据缓存的目录
  excludePaths:
    - "/api/v1/system/login"
    - "api/v1/system/logout"


# Redis 配置示例
redis:
  # 单实例配置
  default:
    address: 127.0.0.1:6379
    db: 1
    idleTimeout: "60s" #连接最大空闲时间，使用时间字符串例如30s/1m/1d
    maxConnLifetime: "90s" #连接最长存活时间，使用时间字符串例如30s/1m/1d
    waitTimeout: "60s" #等待连接池连接的超时时间，使用时间字符串例如30s/1m/1d
    dialTimeout: "30s" #TCP连接的超时时间，使用时间字符串例如30s/1m/1d
    readTimeout: "30s" #TCP的Read操作超时时间，使用时间字符串例如30s/1m/1d
    writeTimeout: "30s" #TCP的Write操作超时时间，使用时间字符串例如30s/1m/1d
    maxActive: 100

system:
  notCheckAuthAdminIds: [1,2,31]  #无需验证后台权限的用户id
  dataDir: "./resource/data"
  cache:
    model: "dist"  #缓存模式 memory OR redis OR dist
    distPath: "./resource/data/distCacheDb" #使用磁盘缓存时配置数据缓存的目录
    prefix: "gFastV3Cache:" #缓存前缀
  verifyStatus: 1 #0关闭验证码 1图形验证码 2交互验证码

#casbin配置
casbin:
  modelFile: "./resource/casbin/rbac_model.conf"
  policyFile: "./resource/casbin/rbac_policy.csv"


# CLI.
gfcli:
  gen:
    dao:
      - link:            "mysql:gfast3:gfast333@tcp(localhost:3306)/gfast-v32"
        tables:          "tools_gen_table,tools_gen_table_column"
        removePrefix:    "gf_"
        descriptionTag:  true
        noModelComment:  true
        path: "./internal/app/system"

# 上传配置
upload:
  default: 0   # 默认上传至本地 0 - 本地 1 - 腾讯云 2 - 七牛云 3- 阿里oss
  tencentCOS:     # 腾讯云
    upPath : "/gfast/"
    rawUrl :    "https://xxx-xxxx.cos.ap-chongqing.myqcloud.com"
    secretID :  "xxxxxxxxxxxxxxxxxxxx"
    secretKey : "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
  qiniou: # 七牛云
    accessKey : "xxxxxxxxxxxxxxxxxxxxxx"
    sercetKey: "xxxxxxxxxxxxxxxxxxxxx"
    bucket : "gfast-test"
    qiniuServer : "http://xxxxxxxxxxx.hn-bkt.clouddn.com"
  oss: #阿里云oss
    accessKeyID: "xxxxxxxxxxxxxxxxxxx"
    accessKeySecret: "xxxxxxxxxxxxxxxxxxxx"
    endPoint: "xxx.xxxx.com" #自定域名
    bucketName: "xxx" #使用的存储桶
    isHttps: true #是否开启https访问
    path: "gfast" #上传文件保存路径

# 代码生成配置
gen:
  author: "gfast"
  moduleName: "system"
  apiName: "api/v1"
  packageName: "internal/app/system"
  goModName: "github.com/tiger1103/gfast/v3"
  autoRemovePre: true   #是否自动删除表前缀
  tablePrefix: "table_,qxkj_"   #表前缀
  templatePath: "./resource/template/vm"  #代码生成模板路径
  frontDir: "../../../project/webProject/p2022/gfast-v3.2-ui/gfast3.2-ui" #前端路径

# 消息队列
mqueue:
  enable: false
  closeWaitTime: 5                             # 关闭服务器时等待刷盘时间(秒),最好不要小于5
  driver: "diskQueue"                          # 驱动类型 nsq|diskQueue|rocketmq
  channel: "channel"                           # 默认频道名称
  nsq:
    address: "************"                    # nsq地址
    producer_port: 4150                        # 生产者端口
    consumer_port: 4161                        # 消费者http端口
  rocketmq:
    nameServers: "************:9876"           # 服务器地址
    retry: 2                                   # 重试次数
    logLevel: "warn"                           # 日志级别 debug|error|warn|fatal
  diskQueue:
    dataPath: "./resource/data/storage/diskqueue"            # 存储路径
    maxBytesPerFile: 2097152                   # 单个单件最大字节数(byte)，默认：2m(2097152)
    syncEvery: 2500                            # 多少次读写后刷盘
    syncTimeout: 2                             # 刷盘间隔（秒）
    maxMsgSize: 128144                         # 最大消息字节数(byte)
    minMsgSize: 0                              # 最小消息字节数
