import request from '/@/utils/request'
// 查询CMS配置列表
export function listCmsConfig(query:object) {
  return request({
    url: '/api/v1/cms/cmsConfig/list',
    method: 'get',
    params: query
  })
}
// 查询CMS配置详细
export function getCmsConfig(id:number) {
  return request({
    url: '/api/v1/cms/cmsConfig/get',
    method: 'get',
    params: {
      id: id.toString()
    }
  })
}
// 新增CMS配置
export function addCmsConfig(data:object) {
  return request({
    url: '/api/v1/cms/cmsConfig/add',
    method: 'post',
    data: data
  })
}
// 修改CMS配置
export function updateCmsConfig(data:object) {
  return request({
    url: '/api/v1/cms/cmsConfig/edit',
    method: 'put',
    data: data
  })
}
// 删除CMS配置
export function delCmsConfig(ids:number[]) {
  return request({
    url: '/api/v1/cms/cmsConfig/delete',
    method: 'delete',
    data:{
      ids:ids
    }
  })
}
