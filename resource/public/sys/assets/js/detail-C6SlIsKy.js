import{s as m,a as I}from"./index-BGmsw1a8.js";import{d as N,f as O,r as g,X as M,ad as T,ag as n,c as q,o as V,P as o,H as e,u as t,L as l,M as d,a as w,t as x}from"./@vue-C21YZbHS.js";function A(i){return m({url:"/api/v1/system/operLog/list",method:"get",params:i})}function R(i){return m({url:"/api/v1/system/operLog/get",method:"get",params:{operId:i.toString()}})}function E(i){return m({url:"/api/v1/system/operLog/delete",method:"delete",data:{operIds:i}})}function F(){return m({url:"/api/v1/system/operLog/clear",method:"delete"})}const k={class:"system-sysOperLog-detail"},P=N({name:"apiV1SystemSysOperLogDetail",__name:"detail",props:{requestMethodOptions:{type:Array,default:()=>[]}},emits:["getSysDeptItemsDeptName"],setup(i,{expose:v,emit:j}){const{proxy:c}=O(),y=g(null);g();const u=M({loading:!1,isShowDialog:!1,formData:{operId:void 0,title:void 0,businessType:void 0,method:void 0,requestMethod:void 0,operatorType:void 0,operName:void 0,deptName:void 0,operUrl:void 0,operIp:void 0,operLocation:void 0,operParam:void 0,jsonResult:void 0,status:!1,errorMsg:void 0,operTime:void 0,linkedSysOperLogSysDept:{deptId:void 0,deptName:void 0}},rules:{operId:[{required:!0,message:"日志编号不能为空",trigger:"blur"}],operName:[{required:!0,message:"操作人员不能为空",trigger:"blur"}],deptName:[{required:!0,message:"部门名称不能为空",trigger:"blur"}],status:[{required:!0,message:"操作状态（0正常 1异常）不能为空",trigger:"blur"}]}}),{isShowDialog:f,formData:r}=T(u);v({openDialog:_=>{b(),_&&R(_.operId).then(p=>{const a=p.data;u.formData=a}),u.isShowDialog=!0}});const b=()=>{u.formData={operId:void 0,title:void 0,businessType:void 0,method:void 0,requestMethod:void 0,operatorType:void 0,operName:void 0,deptName:void 0,operUrl:void 0,operIp:void 0,operLocation:void 0,operParam:void 0,jsonResult:void 0,status:!1,errorMsg:void 0,operTime:void 0,linkedSysOperLogSysDept:{deptId:void 0,deptName:void 0}}};return(_,p)=>{const a=n("el-form-item"),s=n("el-col"),h=n("el-row"),S=n("el-form"),L=n("el-drawer");return V(),q("div",k,[o(L,{modelValue:t(f),"onUpdate:modelValue":p[0]||(p[0]=D=>x(f)?f.value=D:null),size:"80%",direction:"ltr"},{header:e(()=>p[1]||(p[1]=[w("h4",null,"操作日志详情",-1)])),default:e(()=>[o(S,{ref_key:"formRef",ref:y,model:t(r),"label-width":"100px"},{default:e(()=>[o(h,null,{default:e(()=>[o(s,{span:12},{default:e(()=>[o(a,{label:"日志编号"},{default:e(()=>[l(d(t(r).operId),1)]),_:1})]),_:1}),o(s,{span:12},{default:e(()=>[o(a,{label:"系统模块"},{default:e(()=>[l(d(t(r).title),1)]),_:1})]),_:1}),o(s,{span:12},{default:e(()=>[o(a,{label:"操作方法"},{default:e(()=>[l(d(t(r).method),1)]),_:1})]),_:1}),o(s,{span:12},{default:e(()=>[o(a,{label:"请求方式"},{default:e(()=>[l(d(t(c).getOptionValue(t(r).requestMethod,i.requestMethodOptions,"value","label")),1)]),_:1})]),_:1}),o(s,{span:12},{default:e(()=>[o(a,{label:"操作人员"},{default:e(()=>[l(d(t(r).operName),1)]),_:1})]),_:1}),o(s,{span:12},{default:e(()=>[o(a,{label:"部门名称"},{default:e(()=>[l(d(t(r).deptName),1)]),_:1})]),_:1}),o(s,{span:12},{default:e(()=>[o(a,{label:"请求URL"},{default:e(()=>[l(d(t(r).operUrl),1)]),_:1})]),_:1}),o(s,{span:12},{default:e(()=>[o(a,{label:"主机地址"},{default:e(()=>[l(d(t(r).operIp),1)]),_:1})]),_:1}),o(s,{span:12},{default:e(()=>[o(a,{label:"操作地点"},{default:e(()=>[l(d(t(r).operLocation),1)]),_:1})]),_:1}),o(s,{span:12},{default:e(()=>[o(a,{label:"请求参数"},{default:e(()=>[l(d(t(r).operParam),1)]),_:1})]),_:1}),o(s,{span:12},{default:e(()=>[o(a,{label:"错误消息"},{default:e(()=>[l(d(t(r).errorMsg),1)]),_:1})]),_:1}),o(s,{span:12},{default:e(()=>[o(a,{label:"操作时间"},{default:e(()=>[l(d(t(c).parseTime(t(r).operTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),U=I(P,[["__scopeId","data-v-2ac8d866"]]),H=Object.freeze(Object.defineProperty({__proto__:null,default:U},Symbol.toStringTag,{value:"Module"}));export{U as a,H as b,F as c,E as d,A as l};
