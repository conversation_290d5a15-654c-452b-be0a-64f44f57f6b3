// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/cms/dao/internal"
)

// internalCmsFavoriteDao is internal type for wrapping internal DAO implements.
type internalCmsFavoriteDao = *internal.CmsFavoriteDao

// cmsFavoriteDao is the data access object for table cms_favorite.
// You can define custom methods on it to extend its functionality as you wish.
type cmsFavoriteDao struct {
	internalCmsFavoriteDao
}

var (
	// CmsFavorite is globally public accessible object for table cms_favorite operations.
	CmsFavorite = cmsFavoriteDao{
		internal.NewCmsFavoriteDao(),
	}
)

// Fill with you ideas below.
