// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-05-07 08:32:03
// 生成路径: internal/app/cms/audit/dao/internal/cms_audit_user.go
// 生成人：张徽
// desc:管理员审核设置
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// CmsAuditUserDao is the manager for logic model data accessing and custom defined data operations functions management.
type CmsAuditUserDao struct {
	table   string              // Table is the underlying table name of the DAO.
	group   string              // Group is the database configuration group name of current DAO.
	columns CmsAuditUserColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// CmsAuditUserColumns defines and stores column names for table cms_audit_user.
type CmsAuditUserColumns struct {
	Id     string // ID
	UserId string // 管理员ID
	Step   string // 第几审
	StepId string // 步骤ID
}

var cmsAuditUserColumns = CmsAuditUserColumns{
	Id:     "id",
	UserId: "user_id",
	Step:   "step",
	StepId: "step_id",
}

// NewCmsAuditUserDao creates and returns a new DAO object for table data access.
func NewCmsAuditUserDao() *CmsAuditUserDao {
	return &CmsAuditUserDao{
		group:   "default",
		table:   "cms_audit_user",
		columns: cmsAuditUserColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *CmsAuditUserDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *CmsAuditUserDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *CmsAuditUserDao) Columns() CmsAuditUserColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *CmsAuditUserDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *CmsAuditUserDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *CmsAuditUserDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
