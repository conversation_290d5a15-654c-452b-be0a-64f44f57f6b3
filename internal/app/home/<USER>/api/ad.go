/*
* @desc:广告功能
* @company:云南奇讯科技有限公司
* @Author: wdy<<EMAIL>>
* @Date:   2023/7/17 16:05
 */

package api

import (
	"context"
	"github.com/tiger1103/gfast/v3/api/v1/cms"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/api/v1/home"
	cmsService "github.com/tiger1103/gfast/v3/internal/app/cms/service"
	"github.com/tiger1103/gfast/v3/internal/app/common/model"
)

type adApiController struct{}

var AdApiController = new(adApiController)

func (c *adApiController) List(ctx context.Context, req *home.CmsAdSearchReq) (res *home.CmsAdSearchRes, err error) {
	adList, err := cmsService.CmsAd().List(ctx, &cms.CmsAdSearchReq{
		Name:     req.Name,
		Id:       req.Id,
		Position: req.Position,
		PageReq: commonApi.PageReq{
			PageReq: model.PageReq{
				DateRange: req.DateRange,
				PageNum:   req.PageNum,
				PageSize:  req.PageSize,
				OrderBy:   req.OrderBy,
			},
		},
	})
	res = &home.CmsAdSearchRes{
		ListRes: commonApi.ListRes{
			CurrentPage: adList.CurrentPage,
			Total:       adList.Total,
		},
		List: adList.List,
	}
	return
}

func (c *adApiController) Get(ctx context.Context, req *home.CmsAdGetReq) (res *home.CmsAdGetRes, err error) {
	adInfo, err := cmsService.CmsAd().GetById(ctx, req.Id)
	res = &home.CmsAdGetRes{
		CmsAdInfoRes: adInfo,
	}
	return
}
