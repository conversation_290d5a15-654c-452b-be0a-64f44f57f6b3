import{i as u}from"./getStyleSheets-CiOwvIk9.js";import{d as f,X as h,h as v,ad as w,ag as i,c as p,o as r,P as m,H as e,O as g,a6 as I,G as x,a as t,D as F,M as B}from"./@vue-C21YZbHS.js";import{a as L}from"./index-BGmsw1a8.js";import"./@element-plus-_Cc-TEQX.js";import"./pinia-DwJf82dV.js";import"./vue-router-BDT9dCQ7.js";import"./js-cookie-Cz0CWeBA.js";import"./nprogress-Scw6VIZr.js";import"./@intlify-D8kAWvSi.js";import"./axios-B62VicFi.js";import"./qs-D41HS0uK.js";import"./side-channel-DytTmCZg.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-DbsE5JW_.js";import"./crypto-js-oM26lpET.js";import"./side-channel-list-C_7Uk4RP.js";import"./side-channel-map-BUokNT8A.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-bSkmVQJL.js";import"./element-plus-CUmVNDWO.js";import"./lodash-es-S0Y0Up6J.js";import"./@vueuse-D_5IXmcI.js";import"./@popperjs-D_chPuIy.js";import"./@ctrl-r5W6hzzQ.js";import"./dayjs-CepoAVPu.js";import"./async-validator-9PlIezaS.js";import"./memoize-one-BdPwpGay.js";import"./normalize-wheel-es-BQoi3Ox2.js";import"./@floating-ui-DwceP2Gb.js";import"./vue-i18n-BkLk9Ti1.js";import"./vue-iNFchrNl.js";import"./vue-clipboard3-Dr8V5xR7.js";import"./clipboard-DzJOpNE6.js";import"./mitt-DJ65BbbF.js";import"./vue-grid-layout-CIPjP0XN.js";import"./vue-simple-uploader-j6sX1TKo.js";import"./vue-ueditor-wrap-CJ5gjsd3.js";const $=f({name:"pagesIocnfont",setup(){const o=h({sheetsIconList:[]}),s=()=>{u.ali().then(n=>o.sheetsIconList=n)};return v(()=>{s()}),{...w(o)}}}),k={class:"iconfont-container"},A={class:"iconfont-warp"},C={class:"flex-margin"},E={class:"iconfont-warp-value"},y={class:"iconfont-warp-label mt10"};function D(o,s,n,S,G,M){const c=i("el-col"),l=i("el-row"),d=i("el-card");return r(),p("div",k,[m(d,{shadow:"hover",header:`iconfont 字体图标(自动载入)：${o.sheetsIconList.length}个`},{default:e(()=>[m(l,{class:"iconfont-row"},{default:e(()=>[(r(!0),p(g,null,I(o.sheetsIconList,(a,_)=>(r(),x(c,{xs:12,sm:8,md:6,lg:4,xl:2,key:_},{default:e(()=>[t("div",A,[t("div",C,[t("div",E,[t("i",{class:F([a,"iconfont"])},null,2)]),t("div",y,B(a),1)])])]),_:2},1024))),128))]),_:1})]),_:1},8,["header"])])}const yo=L($,[["render",D],["__scopeId","data-v-366a6120"]]);export{yo as default};
