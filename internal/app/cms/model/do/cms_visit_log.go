// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-04-17 16:56:10
// 生成路径: internal/app/cms/model/entity/cms_visit_log.go
// 生成人：zhanghui
// desc:访问日志
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// CmsVisitLog is the golang structure for table cms_visit_log.
type CmsVisitLog struct {
	gmeta.Meta `orm:"table:cms_visit_log, do:true"`
	Id         interface{} `orm:"id,primary" json:"id"`          // ID
	Ip         interface{} `orm:"ip" json:"ip"`                  // 访问IP
	Url        interface{} `orm:"url" json:"url"`                // URL
	Agent      interface{} `orm:"agent" json:"agent"`            // AGENT
	ArticleId  interface{} `orm:"article_id" json:"articleId"`   // 文章id
	CategoryId interface{} `orm:"category_id" json:"categoryId"` // 分类id
	CreatedAt  *gtime.Time `orm:"created_at" json:"createdAt"`   // 访问时间
}
