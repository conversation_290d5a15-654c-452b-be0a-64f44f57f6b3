// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-05-06 17:48:31
// 生成路径: internal/app/cms/audit/dao/internal/cms_audit_step.go
// 生成人：zhanghui
// desc:审核步骤
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// CmsAuditStepDao is the manager for logic model data accessing and custom defined data operations functions management.
type CmsAuditStepDao struct {
	table   string              // Table is the underlying table name of the DAO.
	group   string              // Group is the database configuration group name of current DAO.
	columns CmsAuditStepColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// CmsAuditStepColumns defines and stores column names for table cms_audit_step.
type CmsAuditStepColumns struct {
	Id        string //
	Title     string // 步骤名称
	Step      string // 步骤
	Type      string // 类别
	CreatedAt string // 创建时间
	UpdatedAt string // 更新时间
}

var cmsAuditStepColumns = CmsAuditStepColumns{
	Id:        "id",
	Title:     "title",
	Step:      "step",
	Type:      "type",
	CreatedAt: "created_at",
	UpdatedAt: "updated_at",
}

// NewCmsAuditStepDao creates and returns a new DAO object for table data access.
func NewCmsAuditStepDao() *CmsAuditStepDao {
	return &CmsAuditStepDao{
		group:   "default",
		table:   "cms_audit_step",
		columns: cmsAuditStepColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *CmsAuditStepDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *CmsAuditStepDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *CmsAuditStepDao) Columns() CmsAuditStepColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *CmsAuditStepDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *CmsAuditStepDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *CmsAuditStepDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
