// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2023-03-10 16:19:11
// 生成路径: internal/app/ucenter/logic/ucenter_login_log.go
// 生成人：yxf
// desc:系统访问记录
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/mssola/user_agent"
	apiAdmin "github.com/tiger1103/gfast/v3/api/v1/ucenter/admin"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/internal/app/ucenter/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ucenter/model"
	"github.com/tiger1103/gfast/v3/internal/app/ucenter/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ucenter/service"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterUCenterLoginLog(New())
}

func New() *sUCenterLoginLog {
	return &sUCenterLoginLog{}
}

type sUCenterLoginLog struct{}

func (s *sUCenterLoginLog) List(ctx context.Context, req *apiAdmin.UCenterLoginLogSearchReq) (listRes *apiAdmin.UCenterLoginLogSearchRes, err error) {
	listRes = new(apiAdmin.UCenterLoginLogSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.UCenterLoginLog.Ctx(ctx).WithAll()
		if req.LoginName != "" {
			m = m.Where(dao.UCenterLoginLog.Columns().LoginName+" like ?", "%"+req.LoginName+"%")
		}
		if req.Ipaddr != "" {
			m = m.Where(dao.UCenterLoginLog.Columns().Ipaddr+" = ?", req.Ipaddr)
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id desc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.UCenterLoginLogInfoRes
		err = m.Fields(apiAdmin.UCenterLoginLogSearchRes{}).Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.UCenterLoginLogListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.UCenterLoginLogListRes{
				Id:            v.Id,
				LoginName:     v.LoginName,
				Ipaddr:        v.Ipaddr,
				LoginLocation: v.LoginLocation,
				Browser:       v.Browser,
				Os:            v.Os,
				Status:        v.Status,
				Msg:           v.Msg,
				LoginTime:     v.LoginTime,
			}
		}
	})
	return
}

func (s *sUCenterLoginLog) GetById(ctx context.Context, id int64) (res *model.UCenterLoginLogInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.UCenterLoginLog.Ctx(ctx).WithAll().Where(dao.UCenterLoginLog.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sUCenterLoginLog) Add(ctx context.Context, req *apiAdmin.UCenterLoginLogParams) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		ua := user_agent.New(req.UserAgent)
		browser, _ := ua.Browser()
		loginData := &do.UCenterLoginLog{
			LoginName:     req.Username,
			Ipaddr:        req.Ip,
			LoginLocation: libUtils.GetCityByIp(req.Ip),
			Browser:       browser,
			Os:            ua.OS(),
			Status:        req.Status,
			Msg:           req.Msg,
			LoginTime:     gtime.Now(),
		}
		_, err = dao.UCenterLoginLog.Ctx(ctx).Insert(loginData)
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sUCenterLoginLog) Delete(ctx context.Context, ids []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.UCenterLoginLog.Ctx(ctx).Delete(dao.UCenterLoginLog.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

func (s *sUCenterLoginLog) Clear(ctx context.Context) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.UCenterLoginLog.Ctx(ctx).Delete()
		liberr.ErrIsNil(ctx, err, "清除失败")
	})
	return
}
