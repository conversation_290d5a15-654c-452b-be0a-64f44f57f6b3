// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2023-03-09 16:48:47
// 生成路径: api/v1/ucenter/ucenter_level.go
// 生成人：yxf
// desc:用户等级相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ucenter/model"
)

// UCenterLevelSearchReq 分页请求参数
type UCenterLevelSearchReq struct {
	g.Meta `path:"/list" tags:"ucenter后台/用户等级" method:"get" summary:"用户等级列表"`
	commonApi.PageReq
	commonApi.Author
}

// UCenterLevelSearchRes 列表返回结果
type UCenterLevelSearchRes struct {
	g.Meta `mime:"application/json"`
	commonApi.ListRes
	List []*model.UCenterLevelListRes `json:"list"`
}

// UCenterLevelAddReq 添加操作请求参数
type UCenterLevelAddReq struct {
	g.Meta `path:"/add" tags:"ucenter后台/用户等级" method:"post" summary:"用户等级添加"`
	commonApi.Author
	Name        string `p:"name" v:"required#名称不能为空"`
	Description string `p:"description" `
	ListOrder   int    `p:"listOrder" `
}

// UCenterLevelAddRes 添加操作返回结果
type UCenterLevelAddRes struct {
	commonApi.EmptyRes
}

// UCenterLevelEditReq 修改操作请求参数
type UCenterLevelEditReq struct {
	g.Meta `path:"/edit" tags:"ucenter后台/用户等级" method:"put" summary:"用户等级修改"`
	commonApi.Author
	Id          uint   `p:"id" v:"required#主键ID不能为空"`
	Name        string `p:"name" v:"required#名称不能为空"`
	Description string `p:"description" `
	ListOrder   int    `p:"listOrder" `
}

// UCenterLevelEditRes 修改操作返回结果
type UCenterLevelEditRes struct {
	commonApi.EmptyRes
}

// UCenterLevelGetReq 获取一条数据请求
type UCenterLevelGetReq struct {
	g.Meta `path:"/get" tags:"ucenter后台/用户等级" method:"get" summary:"获取用户等级信息"`
	commonApi.Author
	Id uint `p:"id" v:"required#主键必须"` //通过主键获取
}

// UCenterLevelGetRes 获取一条数据结果
type UCenterLevelGetRes struct {
	g.Meta `mime:"application/json"`
	*model.UCenterLevelInfoRes
}

// UCenterLevelDeleteReq 删除数据请求
type UCenterLevelDeleteReq struct {
	g.Meta `path:"/delete" tags:"ucenter后台/用户等级" method:"delete" summary:"删除用户等级"`
	commonApi.Author
	Ids []uint `p:"ids" v:"required#主键必须"` //通过主键删除
}

// UCenterLevelDeleteRes 删除数据返回
type UCenterLevelDeleteRes struct {
	commonApi.EmptyRes
}
