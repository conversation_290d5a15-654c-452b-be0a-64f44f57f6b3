@use  './index.scss' as *;

/* 页面宽度小于992px
------------------------------- */
@media screen and (max-width: $lg) {
	.login-container {
		.login-icon-group {
			&::before {
				content: '';
				height: 70% !important;
				transition: all 0.3s ease;
			}
			&::after {
				content: '';
				width: 100px !important;
				height: 200px !important;
				transition: all 0.3s ease;
			}
		}
	}
}

/* 页面宽度小于992px
------------------------------- */
@media screen and (max-width: $md) {
	.login-content {

	}
}

/* 页面宽度小于576px
------------------------------- */
@media screen and (max-width: $xs) {
	.login-container {
		.login-icon-group {
			display: none !important;
		}
		.login-content {
			width: 100% !important;
			height: 100% !important;
			padding: 20px 0 !important;
			border-radius: 0 !important;
			box-shadow: unset !important;
			border: none !important;
		}
		.el-form-item {
			display: flex !important;
		}
	}
}

/* 页面宽度小于375px
------------------------------- */
@media screen and (max-width: $us) {
	.login-container {
		.login-content-title {
			font-size: 18px !important;
			transition: all 0.3s ease;
		}
	}
}
