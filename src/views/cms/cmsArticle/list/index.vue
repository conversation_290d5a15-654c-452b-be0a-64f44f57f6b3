<template>
  <div class="cms-cmsArticle-container">
    <el-card shadow="hover">
      <div class="cms-cmsArticle-content">
        <!-- 左侧分类树 -->
        <LeftTree @node-click="handleNodeClick" />

        <!-- 右侧文章列表 -->
        <RightList :currentNode="currentNode" :key="rightListKey" @refresh-list="handleRefreshList" />
      </div>
    </el-card>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from "vue";
import LeftTree from "./component/leftTree.vue";
import RightList from "./component/rightList.vue";

export default defineComponent({
  name: "apiV1CmsCmsArticleList",
  components: {
    LeftTree: LeftTree,
    RightList: RightList
  },
  setup() {
    // 初始化 currentNode 为一个空对象
    const currentNode = ref({});
    const rightListKey = ref(0); // 添加一个 key 属性

    // 添加节点点击处理
    const handleNodeClick = (node: any) => {
      // 确保 currentNode 是一个有效的对象
      currentNode.value = node || {};
      // 更新 key 值以强制重新渲染 RightList 组件
      rightListKey.value += 1;
    };

    // 添加刷新列表处理
    const handleRefreshList = () => {
      rightListKey.value += 1; // 更新 key 值以强制重新渲染 RightList 组件
    };

    return {
      currentNode,
      handleNodeClick,
      rightListKey, // 返回 key 属性
      handleRefreshList
    };
  }
});
</script>

<style lang="scss" scoped>
.cms-cmsArticle-container {
  height: 100%;

  .cms-cmsArticle-content {
    display: flex;
    height: calc(100vh - 200px);
  }
}
</style>