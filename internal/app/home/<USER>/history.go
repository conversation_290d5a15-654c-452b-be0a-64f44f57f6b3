// ==========================================================================
// desc: Home Article
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/tiger1103/gfast/v3/api/v1/home"
	"github.com/tiger1103/gfast/v3/internal/app/cms/dao"
	"github.com/tiger1103/gfast/v3/internal/app/cms/model"
	"github.com/tiger1103/gfast/v3/internal/app/cms/model/entity"
	"github.com/tiger1103/gfast/v3/internal/app/home/<USER>"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterHistory(NewHistory())
}

func NewHistory() *sHistory {
	return &sHistory{}
}

type sHistory struct{}

func (s *sHistory) List(ctx context.Context, req *home.HistorySearchReq) (res *home.HistorySearchRes, err error) {
	res = new(home.HistorySearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.CmsHistory.Ctx(ctx).Where("member_id=?", req.MemberId)
		res.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取收藏总数失败")
		var listRes []*model.CmsHistoryListRes

		if req.PageNum == 0 {
			req.PageNum = 1
		}
		res.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "created_at desc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		err = m.WithAll().Page(req.PageNum, req.PageSize).Order(order).Scan(&listRes)
		res.List = listRes
		liberr.ErrIsNil(ctx, err, "获取收藏列表失败")
	})
	return
}

func (s *sHistory) Add(ctx context.Context, req *home.HistoryAddReq) (err error) {
	count, err := dao.CmsHistory.Ctx(ctx).Where(g.Map{
		dao.CmsHistory.Columns().MemberId + "=?":  req.MemberId,
		dao.CmsHistory.Columns().ArticleId + "=?": req.ArticleId,
	}).Count()
	if count > 0 {
		_, err = dao.CmsHistory.Ctx(ctx).Where(g.Map{
			"article_id": req.ArticleId,
			"member_id":  req.MemberId,
		}).Update(g.Map{
			"created_at": gtime.Now(),
		})
	} else {
		_, err = dao.CmsHistory.Ctx(ctx).Insert(&entity.CmsHistory{
			ArticleId: req.ArticleId,
			MemberId:  req.MemberId,
		})
	}
	return
}

func (s *sHistory) Delete(ctx context.Context, req *home.HistoryDeleteReq) (err error) {
	_, err = dao.CmsHistory.Ctx(ctx).Where(g.Map{
		dao.CmsHistory.Columns().MemberId + "=?":  req.MemberId,
		dao.CmsHistory.Columns().ArticleId + "=?": req.ArticleId,
	}).Delete()
	return
}
