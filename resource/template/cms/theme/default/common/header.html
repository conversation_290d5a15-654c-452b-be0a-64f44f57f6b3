<header class="header">
    <div class="container clearfix">
        <div class="navbar-header">
            <button aria-label="menu" class="navbar-toggle collapsed" data-target=".navbar-collapse" data-toggle="collapse" type="button">
                <span class="icon-bar icon-bar-1"></span>
                <span class="icon-bar icon-bar-2"></span>
                <span class="icon-bar icon-bar-3"></span>
            </button>
            <h1 class="logo">
                <a href="${.cmsConfig.site_url}" rel="home">
                    <img alt="GFAST" src='${CorrectionPath .cmsConfig.site_logo}'>
                </a>
            </h1>
        </div>
        <div class="collapse navbar-collapse">
            <nav class="navbar-left primary-menu">
                <ul class="nav navbar-nav hotnews-children-menu">
                    <li class="menu-item"><a href="${.cmsConfig.site_url}">首页</a></li>
                    ${$nav := GetMenu}
                    ${range $index,$menu := $nav}
                    ${template "menuItem" $menu}
                    ${end}
                    <!-- 自定义模板，用于显示多级导航菜单 -->
                    ${define "menuItem"}
                    ${if len .Children | eq 0}
                    <li class="menu-item"><a href="${.JumpUrl}" ${if eq .Type "jump"} target="_blank" ${end}>${.Name}</a></li>
                    ${else}
                    <li class="menu-item dropdown"><a class="dropdown-toggle" href="${.JumpUrl}">${.Name} ${if gt .ParentId 0} > ${end}</a>
                        <ul class="dropdown-menu menu-item-wrap menu-item-col-4">
                            ${range $menuChild := .Children}
                            ${template "menuItem" $menuChild}
                            ${end}
                        </ul>
                    </li>
                    ${end}
                    ${end}
                </ul>
            </nav>
            <div class="navbar-action pull-right">
                <form action="${.cmsConfig.site_url}/search" class="navbar-search" onsubmit="return checkKeywords()">
                    <input type="text" name="keywords" id="keywords" autocomplete="Off" value="${.Query.keywords}" class="navbar-search-input">
                    <div class="navbar-search-icon">
                        <button type="submit" class="navbar-search-btn"><i class="iconfont icon-search" style="font-size: 20px"></i></button>
                    </div>
                </form>
                ${if .Session.memberInfo}
                <div class="header-member-wrap">
                    <ul class="profile">
                        <li class="menu-item dropdown">
                            <a class="menu-item-user" href="${.cmsConfig.site_url}/member/profile">
                                <span class="menu-item-avatar">
                                    <img class="avatar avatar-60 photo" height="60" src='${SetStrDef  (CorrectionPath .Session.memberInfo.Avatar) "/static/images/avatar.jpg"}' width="60">
                                </span>
                                <span class="menu-item-name">${.Session.memberInfo.Nickname}</span>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a href="${.cmsConfig.site_url}/member/${.Session.memberInfo.Id}">个人中心</a></li>
                                <li><a href="${.cmsConfig.site_url}/member/message/list">我的私信</a></li>
                                <li><a href="${.cmsConfig.site_url}/member/notifications/list">系统通知</a></li>
                                <li><a href="${.cmsConfig.site_url}/member/profile">帐号设置</a></li>
                                <li><a href="javascript:void(0)" onclick="logout()">退出登录</a></li>
                            </ul>
                        </li>
                    </ul>
                </div>
                ${else}
                <div class="header-member-wrap">
                    <a class="login cur" href="${.cmsConfig.site_url}/account/login?referer=${url .Request.RequestURI}">登录</a>
                    <a class="login register cur" href="${.cmsConfig.site_url}/account/register?referer=${url .Request.RequestURI}">注册</a>
                </div>
                ${end}
            </div>
        </div>
    </div>
</header>
<script lang="javascript">
    function checkKeywords() {
        if (!$("#keywords").val()) {
            layer.alert("请输入关键字", {icon: 2, title: "错误提示"})
            return false
        }
        return true
    }
</script>