// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-04-24 10:40:09
// 生成路径: internal/app/cms/controller/cms_flags.go
// 生成人：zhanghui
// desc:推荐属性
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/cms"
	"github.com/tiger1103/gfast/v3/internal/app/cms/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type cmsFlagsController struct {
	systemController.BaseController
}

var CmsFlags = new(cmsFlagsController)

// List 列表
func (c *cmsFlagsController) List(ctx context.Context, req *cms.CmsFlagsSearchReq) (res *cms.CmsFlagsSearchRes, err error) {
	res = new(cms.CmsFlagsSearchRes)
	res.CmsFlagsSearchRes, err = service.CmsFlags().List(ctx, &req.CmsFlagsSearchReq)
	return
}

// Get 获取推荐属性
func (c *cmsFlagsController) Get(ctx context.Context, req *cms.CmsFlagsGetReq) (res *cms.CmsFlagsGetRes, err error) {
	res = new(cms.CmsFlagsGetRes)
	res.CmsFlagsInfoRes, err = service.CmsFlags().GetById(ctx, req.Id)
	return
}

// Add 添加推荐属性
func (c *cmsFlagsController) Add(ctx context.Context, req *cms.CmsFlagsAddReq) (res *cms.CmsFlagsAddRes, err error) {
	err = service.CmsFlags().Add(ctx, req.CmsFlagsAddReq)
	return
}

// Edit 修改推荐属性
func (c *cmsFlagsController) Edit(ctx context.Context, req *cms.CmsFlagsEditReq) (res *cms.CmsFlagsEditRes, err error) {
	err = service.CmsFlags().Edit(ctx, req.CmsFlagsEditReq)
	return
}

// Delete 删除推荐属性
func (c *cmsFlagsController) Delete(ctx context.Context, req *cms.CmsFlagsDeleteReq) (res *cms.CmsFlagsDeleteRes, err error) {
	err = service.CmsFlags().Delete(ctx, req.Ids)
	return
}

// ListByCategoryId 根据栏目获取推荐属性列表
func (c *cmsFlagsController) ListByCategoryId(ctx context.Context, req *cms.CmsFlagsByCategoryIdReq) (res *cms.CmsFlagsByCategoryIdRes, err error) {
	res, err = service.CmsFlags().ListByCategoryId(ctx, req.CategoryId)
	return
}
