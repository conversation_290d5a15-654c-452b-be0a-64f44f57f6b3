// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2023-03-09 16:50:38
// 生成路径: internal/app/ucenter/service/ucenter_scores.go
// 生成人：yxf
// desc:用户积分
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"
	apiAdmin "github.com/tiger1103/gfast/v3/api/v1/ucenter/admin"
	"github.com/tiger1103/gfast/v3/internal/app/ucenter/model"
)

type IUCenterScores interface {
	List(ctx context.Context, req *apiAdmin.UCenterScoresSearchReq) (res *apiAdmin.UCenterScoresSearchRes, err error)
	GetById(ctx context.Context, Id uint64) (res *model.UCenterScoresInfoRes, err error)
	Add(ctx context.Context, req *apiAdmin.UCenterScoresAddReq) (err error)
	Edit(ctx context.Context, req *apiAdmin.UCenterScoresEditReq) (err error)
	Delete(ctx context.Context, Id []uint64) (err error)
}

var localUCenterScores IUCenterScores

func UCenterScores() IUCenterScores {
	if localUCenterScores == nil {
		panic("implement not found for interface IUCenterScores, forgot register?")
	}
	return localUCenterScores
}

func RegisterUCenterScores(i IUCenterScores) {
	localUCenterScores = i
}
