<template>
  <!-- 邮件发送详情抽屉 -->
  <div class="mail-mail-detail">
    <el-drawer v-model="isShowDialog" size="80%" direction="ltr">
      <template #header>
        <h4>邮件发送详情</h4>
      </template>
      <el-form ref="formRef" :model="formData" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="">{{ formData.id }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱类型">{{ formData.mType }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户名称">{{ formData.user }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="密码">{{ formData.pass }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="主机地址">{{ formData.host }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="端口">{{ formData.port }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态 : 0 - 禁用 1 - 启用">{{ formData.status }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注" prop="remark">{{ formData.remark }}</el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-drawer>
  </div>
</template>
<script setup lang="ts">
  import { reactive, toRefs, defineComponent,ref,unref,getCurrentInstance,computed } from 'vue';
  import {ElMessageBox, ElMessage, FormInstance,UploadProps} from 'element-plus';
  import {
    listPluginMail,
    getPluginMail,
    delPluginMail,
    addPluginMail,
    updatePluginMail,
  } from "/@/api/plugins/mail/mail";
  import {
    PluginMailTableColumns,
    PluginMailInfoData,
    PluginMailTableDataState,
    PluginMailEditState
  } from "/@/views/plugins/mail/mail/list/component/model"
  defineOptions({ name: "apiV1MailPluginMailDetail"})
  const props = defineProps({
    mailList:{
      type:Function,
      default:()=>{}
    }
  })
  const emit = defineEmits(['mailList'])
  const {proxy} = <any>getCurrentInstance()
  const formRef = ref<HTMLElement | null>(null);
  const menuRef = ref();
  const state = reactive<PluginMailEditState>({
    loading:false,
    isShowDialog: false,
    formData: {
      id: undefined,
      mType: undefined,
      user: undefined,
      pass: undefined,
      host: undefined,
      port: undefined,
      status: false ,
      remark: undefined,
    },
    // 表单校验
    rules: {
      id : [
          { required: true, message: "不能为空", trigger: "blur" }
      ],
      status : [
          { required: true, message: "状态 : 0 - 禁用 1 - 启用不能为空", trigger: "blur" }
      ],
    }
  });
const { isShowDialog, formData} = toRefs(state)
// 打开弹窗
const openDialog = (row?: PluginMailInfoData) => {
  resetForm();
  if(row) {
    getPluginMail(row.id!).then((res:any)=>{
      const data = res.data;
      state.formData = data;
    })
  }
  state.isShowDialog = true;
};
// 关闭弹窗
const closeDialog = () => {
  state.isShowDialog = false;
};
defineExpose({
  openDialog
})
// 取消
const onCancel = () => {
  closeDialog();
};
const resetForm = ()=>{
  state.formData = {
    id: undefined,
    mType: undefined,
    user: undefined,
    pass: undefined,
    host: undefined,
    port: undefined,
    status: false ,
    remark: undefined,
  }
};
</script>
<style scoped>
  .mail-mail-detail :deep(.el-form-item--large .el-form-item__label){
    font-weight: bolder;
  }
  .pic-block{
    margin-right: 8px;
  }
  .file-block{
    width: 100%;
    border: 1px solid var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
    margin-bottom: 5px;
    padding: 3px 6px;
  }
  .ml-2{margin-right: 5px;}
</style>
