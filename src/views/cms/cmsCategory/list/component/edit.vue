<template>
  <div class="cms-cmsCategory-edit">
    <!-- 添加或修改文章分类对话框 -->
    <el-dialog v-model="isShowDialog" :close-on-click-modal="false" :destroy-on-close="true" width="1069px">
      <template #header>
        <div v-drag="['.cms-cmsCategory-edit .el-dialog', '.cms-cmsCategory-edit .el-dialog__header']">
          {{ (!formData.id || formData.id == 0 ? '添加' : '修改') + '文章分类' }}
        </div>
      </template>
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="90px" size="default">
        <el-tabs model-value="base" type="border-card">
          <el-tab-pane label="基本信息" name="base">
            <el-form-item v-if="formData.type==='list'" label="内容模型" prop="moduleId">
              <el-select v-model="formData.moduleId" clearable>
                <el-option v-for="item in modulesList" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="上级分类" prop="parentId">
              <el-cascader v-model="formData.parentId" :options="cmsCategoryChannelOptions"
                           :props="{ label: 'name',value: 'id',checkStrictly: true,emitPath: false }" clearable filterable placeholder="请选择"/>
              <div style="margin-left: 10px;color: #409eff;">
                <el-icon>
                  <ele-Opportunity/>
                </el-icon>
                上级只能是频道栏目
              </div>
            </el-form-item>
            <el-form-item label="分类名称" prop="name">
              <el-input v-model.trim="formData.name" placeholder="请输入名称"/>
            </el-form-item>
            <el-form-item label="类型" prop="type">
              <el-radio-group v-model="formData.type" @change="cmsCategoryChange">
                <el-radio v-for="(item,index) in CmsCategoryTypeOptions" :key="index" :value="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="formData.type==='jump'" label="跳转链接" prop="jumpUrl">
              <el-input v-model.trim="formData.jumpUrl" placeholder="请输入跳转链接，以http或https开头"/>
            </el-form-item>
            <el-form-item v-if="formData.type==='channel'" label="频道模板" prop="channelTemplate">
              <el-select v-model="formData.channelTemplate" placeholder="请选择频道模板">
                <el-option v-for="(item,index) in templateFileList" :key="index" :label="item.name" :value="item.name"/>
              </el-select>
            </el-form-item>
            <el-form-item v-if="formData.type==='channel'" label="继承模版" prop="isInherit">
              <el-radio-group v-model="formData.isInherit">
                <el-radio v-for="dict in props.isInheritOptions" :key="dict.value" :value="dict.value === 'true' || dict.value === true ? true : false">{{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="formData.type==='channel' && formData.isInherit === true" label="列表模板" prop="template">
              <el-select v-model="formData.template" placeholder="请选择列表模板" style="width: 100%">
                <el-option v-for="(item,index) in templateFileList" :key="index" :label="item.name" :value="item.name"/>
              </el-select>
            </el-form-item>
            <el-form-item v-if="formData.type==='channel' && formData.isInherit === true" label="内容模板" prop="contentTemplate">
              <el-select v-model="formData.contentTemplate" placeholder="请选择内容模板" style="width: 100%">
                <el-option v-for="(item,index) in contentTemplateFileList" :key="index" :label="item.name" :value="item.name"/>
              </el-select>
            </el-form-item>
            <el-form-item v-if="formData.type==='list'" label="列表模板" prop="template">
              <el-select v-model="formData.template" placeholder="请选择列表模板" style="width: 100%">
                <el-option v-for="(item,index) in templateFileList" :key="index" :label="item.name" :value="item.name"/>
              </el-select>
            </el-form-item>
            <el-form-item v-if="formData.type==='list'" label="内容模板" prop="contentTemplate">
              <el-select v-model="formData.contentTemplate" placeholder="请选择内容模板" style="width: 100%">
                <el-option v-for="(item,index) in contentTemplateFileList" :key="index" :label="item.name" :value="item.name"/>
              </el-select>
            </el-form-item>
            <el-form-item v-if="formData.type==='page'" label="单页模板" prop="pageTemplate">
              <el-select v-model="formData.pageTemplate" placeholder="请选择单页模板">
                <el-option v-for="(item,index) in templateFileList" :key="index" :label="item.name" :value="item.name"/>
              </el-select>
            </el-form-item>
            <el-form-item v-if="formData.type==='page'" label="单页内容" prop="content" style="margin-top: 10px;">
              <gf-ueditor v-model="formData.content" :tool-bars="cmsToolbar" editorId="cmsConfigValueEditor" @setEditContent="setEditContent"></gf-ueditor>
            </el-form-item>
            <el-form-item label="导航" prop="nav">
              <el-select v-model="formData.nav" placeholder="请选择导航位置" clearable style="width: 100%">
                <el-option
                  v-for="dict in cms_nav_position"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="状态" prop="status">
              <!-- 使用条件渲染，根据状态值显示不同的选项 -->
              <div>
                <el-radio-group v-model="formData.status">
                  <el-radio label="0">禁用</el-radio>
                  <el-radio label="1">启用</el-radio>
                </el-radio-group>
              </div>
            </el-form-item>
          </el-tab-pane>
          <el-tab-pane label="权限设置" name="permission">
            <div class="flex items-center mb-2">
              <span>权限设置</span>

            </div>
            <el-table style="width: 100%"
                      :data="rolePermissionsList"
                      row-key="id"
                      default-expand-all
                      :tree-props="{
      children: 'children',
      hasChildren: 'hasChildren'
    }"
            >
              <el-table-column
                  prop="name"
                  label="角色"
                  min-width="180"
              />
              <el-table-column
                  label="权限设置"
                  min-width="400"
              >
                <template #header>
                    <div class="flex items-center justify-between">
                        <span>权限设置</span>
                        <el-button type="text" style="margin-left: 10px;margin-top: -5px;"@click="toggleAllPermissions">反选</el-button>
                    </div>
                </template>
                <template #default="{ row }">
                    <div class="flex items-center">
                        <!-- 新增行级全选/反选按钮 -->
                        <div class="mr-2" style="margin-right: 10px;">
                            <el-button type="text" @click="toggleAllForRow(row)">反选</el-button>
                        </div>
                        <el-checkbox-group v-model="row.permissions">
                            <!-- 只有 channel 和 list 类型显示权限 -->
                            <template v-if="formData.type === 'channel'">
                                <!-- 修复value绑定方式 -->
                                <el-checkbox :value="'list'">
                                  查看
                                </el-checkbox>
                            </template>
                            <template v-else-if="formData.type === 'list'">
                                <!-- list类型显示所有权限 -->
                                <el-checkbox
                                    v-for="(item, index) in permissionOptions"
                                    :key="index"
                                    :value="item.value"
                                    style="margin-right: 20px;"
                                >
                                  {{ item.label }}
                                </el-checkbox>
                            </template>
                        </el-checkbox-group>
                    </div>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="更多设置" name="more">
            <el-form-item label="分类别名" prop="alias">
              <el-input v-model.trim="formData.alias" placeholder="请输入分类别名"/>
            </el-form-item>
            <el-form-item label="SEO标题" prop="seoTitle">
              <el-input v-model.trim="formData.seoTitle" placeholder="请输入SEO标题"/>
            </el-form-item>
            <el-form-item label="SEO关键字" prop="keywords">
              <el-input v-model.trim="formData.keywords" placeholder="请输入SEO关键字"/>
            </el-form-item>
            <el-form-item label="SEO描述" prop="description">
              <el-input v-model="formData.description" placeholder="请输入SEO描述" type="textarea"/>
            </el-form-item>
            <el-form-item label="排序" prop="sort">
              <el-input v-model.number="formData.sort" placeholder="请输入排序"/>
            </el-form-item>
            <el-form-item label="缩略图" prop="thumb">
              <el-upload v-loading="upLoadingThumb" :action="baseURL+'api/v1/system/upload/singleImg'" :before-upload="beforeAvatarUploadThumb"
                         :data="setUpData()" :on-success="handleAvatarSuccessThumb" :show-file-list="false" class="avatar-uploader" name="file">
                <img v-if="!proxy.isEmpty(imageUrlThumb)" :src="imageUrlThumb" class="avatar">
                <el-icon v-else class="avatar-uploader-icon">
                  <ele-Plus/>
                </el-icon>
              </el-upload>
            </el-form-item>
            <el-form-item label="顶部图片" prop="banner">
              <upload-img v-model="formData.banner" :action="baseURL+'api/v1/system/upload/singleImg'" :limit="10"
                          @uploadData="setUpImgListBanner"></upload-img>
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="onSubmit">确 定</el-button>
          <el-button @click="onCancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts">
import {defineComponent, getCurrentInstance, reactive, ref, toRefs, unref, nextTick, computed} from "vue";
import {ElMessage, UploadProps} from "element-plus";
import {addCmsCategory, getCmsCategory, listCmsCategory, updateCmsCategory} from "/@/api/cms/cmsCategory";
import {getToken} from "/@/utils/gfast";
import uploadImg from "/@/components/uploadImg/index.vue";
import {CmsCategoryEditState, CmsCategoryInfoData, CmsCategoryTypeOptions} from "/@/views/cms/cmsCategory/list/component/model";
import {listModulesInfo} from "/@/api/system/modulesInfo";
import {ModulesInfoInfoData} from "/@/views/system/modulesInfo/list/component/model";
import GfUeditor from "/@/components/ueditor/index.vue";
import {listTemplateFiles} from "/@/api/cms/cmsTemplate";
import {CmsTemplateFileData} from "/@/views/cms/cmsTemplate/list/component/model";
import {cmsToolbar} from "/@/components/ueditor/model";
import { getRoleList } from "/@/api/system/role";

interface PermissionOption {
  value: string;
  label: string;
}
export default defineComponent({
  name: "apiV1CmsCmsCategoryEdit",
  components: {
    GfUeditor,
    uploadImg,
  },
  props: {
    parentIdOptions: {
      type: Array,
      default: () => []
    },
    moduleIdOptions: {
      type: Array,
      default: () => []
    },
    isInheritOptions: {
      type: Array,
      default: () => []
    },
    statusOptions: {
      type: Array,
      default: () => []
    },
    // 文章分类树选项
    cmsCategoryOptions: {
      type: Array,
      default: () => []
    },
    parentId: {
      type: Number,
      default: 0
    },
  },
  setup(props, {emit}) {
    const baseURL: string | undefined | boolean = import.meta.env.VITE_API_URL;
    const {proxy} = <any>getCurrentInstance();
    // 导航位置字典
    const { cms_nav_position } = proxy.useDict('cms_nav_position');

    const formRef = ref<HTMLElement | null>(null);
    const menuRef = ref();
    //图片上传地址
    const imageUrlThumb = ref("");
    const cmsCategoryChannelOptions = ref<any[]>([]);
    //上传加载
    const upLoadingThumb = ref(false);
    const modulesList = ref<ModulesInfoInfoData[]>([]);
    const templateFileList = ref<CmsTemplateFileData[]>([]);
    const contentTemplateFileList = ref<CmsTemplateFileData[]>([]);
    const rolePermissionsList = ref<any[]>([]);
    // 定义权限选项
    const permissionOptions = ref<PermissionOption[]>([
      { value: 'list', label: '查看' },
      { value: 'add', label: '新增' },
      { value: 'edit', label: '修改' },
      { value: 'delete', label: '删除' },
      { value: 'push', label: '推送' },
      { value: 'move', label: '移动' },
      { value: 'publish', label: '发布' },
      { value: 'recycle', label: '回收站' },
    ]);
    const state = reactive<CmsCategoryEditState>({
      loading: false,
      isShowDialog: false,
      formData: {
        id: undefined,
        name: undefined,
        type: "list",
        seoTitle: undefined,
        keywords: undefined,
        description: undefined,
        createdAt: undefined,
        updatedAt: undefined,
        deletedAt: undefined,
        content: undefined,
        parentId: undefined,
        moduleId: undefined,
        sort: 0,
        template: "list.html",
        contentTemplate: "content.html",
        isInherit: undefined,
        thumb: undefined,
        banner: [],
        status: "1",
        jumpUrl: undefined,
        alias: undefined,
        nav: undefined,
        pageTemplate: "page.html",
        channelTemplate: "channel.html",
        linkedCmsCategoryCmsCategory: {
          id: undefined,    // ID
          name: undefined    // 名称
        },
        linkedCmsCategoryModulesInfo: {
          id: undefined,    // ID
          name: undefined    // 模型名称
        },
        rolePermissionsList: {
          id: undefined,    // ID
          pid: undefined,   // 父ID
          status: undefined, // 状态
          listOrder: undefined, // 排序
          name: undefined,  // 名称
          permissions: []   // 权限数组
        }
      },
      // 表单校验
      rules: {
        name: [
          {required: true, message: "名称不能为空", trigger: "blur"}
        ],
        moduleId: [
          {required: true, message: "内容模型不能为空", trigger: "change"}
        ]
      },
      // 初始化 tableData 对象
      tableData: {
        param: {}
      }
    });
    // 打开弹窗
    const openDialog = (row?: CmsCategoryInfoData, parentId?: number) => {
      console.log('打开弹窗:', row, parentId);
      listCmsCategory({pageSize: 1000, pageNum: 1}).then((res: any) => {
        let list = res.data.list ?? [];
        list.forEach((item: any) => {
          item["disabled"] = item.type !== "channel" ?? false;
        });
        cmsCategoryChannelOptions.value = proxy.handleTree(res.data.list || [], "id", "parentId");
      });
      cmsCategoryChange("list");
      resetForm();
      state.formData.parentId = parentId;

      // 获取导航位置字典数据

      // 设置继承模版默认选择第二个选项
      if (props.isInheritOptions && props.isInheritOptions.length > 1) {
        state.formData.isInherit = props.isInheritOptions[1].value;
      }

      listModulesInfo({pageNum: 1, pageSize: 100}).then((res: any) => {
        if (res.data && res.data.list) {
          modulesList.value = res.data.list;
        }
      });
      roleList();
      getCmsCategoryItemsParentId();
      getModulesInfoItemsModuleId();

      if (row) {
        // 修改分类
        // 检查 row.id 是否存在且有效
        if (row.id !== undefined && row.id !== null) {
          // 修改调用 getCmsCategory 接口
          getCmsCategory(row.id).then((res: any) => {
            if (!res.data) {
              ElMessage.error('获取分类数据失败: 接口返回数据为空');
              closeDialog();
              return;
            }

            const data = res.data;
            //单图地址赋值
            imageUrlThumb.value = data.thumb ? proxy.getUpFileUrl(data.thumb) : "";
            data.banner = data.banner ? JSON.parse(data.banner) : [];

            // 确保状态值是字符串类型
            data.status = data.status !== undefined && data.status !== null ? data.status.toString() : "1";

            if (data.linkedPermissions) {
              rolePermissionsList.value = mergePermissionsToRole(rolePermissionsList.value, data.linkedPermissions);
            }

            // 将接口返回的数据赋值给表单
            state.formData = data;

            // 使用 nextTick 确保在 DOM 更新后再设置状态值
            nextTick(() => {
              // 再次确保状态值是字符串类型
              if (state.formData && state.formData.status !== undefined) {
                state.formData.status = state.formData.status !== null ? state.formData.status.toString() : "1";
              }

              // 如果有类型信息，调用 cmsCategoryChange
              if (state.formData && state.formData.type) {
                cmsCategoryChange(state.formData.type);
              }
            });
          }).catch((error) => {
            ElMessage.error('获取分类数据失败: ' + (error.message || error));
            closeDialog();
          });
        } else {
          ElMessage.error('无效的分类 ID');
          closeDialog();
        }
      } else {
        // 新增分类
        resetForm();
        state.formData.parentId = parentId;

        // 使用 nextTick 确保在 DOM 更新后再设置状态值
        nextTick(() => {
          // 确保状态值是字符串类型
          if (state.formData && state.formData.status !== undefined) {
            state.formData.status = state.formData.status !== null ? state.formData.status.toString() : "1";
          }

          // 设置默认类型为 list
          cmsCategoryChange("list");
        });
      }
      state.isShowDialog = true;
    };
    // 合并权限数据到角色树
    const mergePermissionsToRole = (roles: any[], permissions: any[]) => {

      // 确保 roles 不为 undefined
      if (!roles) {
        roles = [];
      }
      // 创建权限映射，key为categoryId，value为权限数组
      const permissionMap = new Map();
      permissions.forEach(item => {
        permissionMap.set(item.roleId, item.permissions ? item.permissions.split(',').filter(Boolean) : []);
      });

      const mergePerm = (list: any[]) => {
        list.forEach(item => {
          // 从权限映射中获取对应角色的权限
          item.permissions = permissionMap.get(item.id) || [];
          if (item.children && item.children.length > 0) {
            mergePerm(item.children);
          }
        });
      };

      mergePerm(roles);
      return roles;
    };
    // 关闭弹窗
    const closeDialog = () => {
      state.isShowDialog = false;
    };
    // 取消
    const onCancel = () => {
      closeDialog();
    };
    const cmsCategoryChange = (val: any) => {
      templateFileList.value = [];
      contentTemplateFileList.value = [];

      // 根据类型动态设置验证规则
      const formWrap = unref(formRef) as any;
      if (formWrap) {
        // 如果类型为 list（发布栏目），则内容模型必选
        if (val === 'list') {
          // 添加对 moduleId 的验证
          state.rules.moduleId = [
            {required: true, message: "内容模型不能为空", trigger: "change"}
          ];
        } else {
          // 其他类型不需要验证 moduleId
          state.rules.moduleId = [];
        }
        // 重新验证表单
        nextTick(() => {
          formWrap.clearValidate(['moduleId']);
        });
      }

      if (val !== "jump") {
        listTemplateFiles("/" + val).then((res: any) => {
          templateFileList.value = res.data?.list ?? [];
        });
        if (val === "list") {
          listTemplateFiles("/content").then((res: any) => {
            contentTemplateFileList.value = res.data?.list ?? [];
          });
        }
      }
    };

    const roleList = () => {
      const data: Array<any> = [];
      // 确保 state.tableData.param 存在
      if (!state.tableData.param) {
        state.tableData.param = {};
      }
      getRoleList(state.tableData.param).then(res => {
        const list = res.data.list ?? [];
        list.map((item: any) => {
          data.push({
            id: item.id,
            pid: item.pid,
            status: item.status,
            listOrder: item.listOrder,
            name: item.name,
            permissions: item.permissions ? item.permissions.split(",") : [] // 将权限字符串转换为数组
          });
        });
        rolePermissionsList.value = proxy.handleTree(data ?? [], "id", "pid", "children", true);
      }).catch(err => {
        ElMessage.error('获取角色列表失败');
      });
    };

    const onSubmit = () => {
      // 处理角色权限数据
      const formatRolePermissions = () => {
        // 创建一个数组来存储格式化后的权限数据
        const formattedPermissions: any[] = [];

        // 递归处理角色树
        const processRoles = (roles: any[]) => {
          if (!roles || roles.length === 0) return;

          roles.forEach(role => {
            // 只添加有权限的角色
            if (role.permissions && role.permissions.length > 0) {
              formattedPermissions.push({
                roleId: role.id,
                permissions: role.permissions.join(',') // 将权限数组转为逗号分隔的字符串
              });
            }

            // 处理子角色
            if (role.children && role.children.length > 0) {
              processRoles(role.children);
            }
          });
        };

        processRoles(rolePermissionsList.value);
        return formattedPermissions;
      };

      const formWrap = unref(formRef) as any;
      if (!formWrap) return;
      formWrap.validate((valid: boolean) => {
        if (valid) {
          // 额外验证：当类型为 list 时，内容模型必选
          if (state.formData.type === 'list' && (!state.formData.moduleId || state.formData.moduleId === 0)) {
            ElMessage.error('发布栏目必须选择内容模型');
            return;
          }

          state.loading = true;

          // 将格式化后的权限数据添加到表单数据中
          const formData = { ...state.formData };
          formData.permissions = formatRolePermissions();

          if (!formData.id || formData.id === 0) {
            // 添加
            // 修改调用 addCmsCategory 接口
            addCmsCategory(formData).then((res: any) => {
              ElMessage.success("添加成功");
              closeDialog(); // 关闭弹窗
              emit("cmsCategoryList");
            }).catch((error: any) => {
              ElMessage.error("添加失败: " + (error.message || error));
            }).finally(() => {
              state.loading = false;
            });
          } else {
            // 修改
            // 修改调用 updateCmsCategory 接口
            updateCmsCategory(formData).then((res: any) => {
              ElMessage.success("修改成功");
              closeDialog(); // 关闭弹窗
              emit("cmsCategoryList");
            }).catch((error: any) => {
              ElMessage.error("修改失败: " + (error.message || error));
            }).finally(() => {
              state.loading = false;
            });
          }
        }
      });
    };
    const resetForm = () => {
      state.formData = {
        id: undefined,
        name: undefined,
        type: "list",
        seoTitle: undefined,
        keywords: undefined,
        description: undefined,
        createdAt: undefined,
        updatedAt: undefined,
        deletedAt: undefined,
        content: undefined,
        parentId: props.parentId,
        moduleId: undefined,
        sort: 0,
        template: "list.html",
        contentTemplate: "content.html",
        isInherit: undefined, // 将在后面设置默认值
        thumb: undefined,
        banner: [],
        status: "1",
        jumpUrl: undefined,
        alias: undefined,
        pageTemplate: "page.html",
        channelTemplate: "channel.html",
        linkedCmsCategoryCmsCategory: {
          id: undefined,    // ID
          name: undefined    // 名称
        },
        linkedCmsCategoryModulesInfo: {
          id: undefined,    // ID
          name: undefined    // 模型名称
        }
      };
    };
    //关联cms_category表选项
    const getCmsCategoryItemsParentId = () => {
      emit("getCmsCategoryItemsParentId");
    };
    //关联modules_info表选项
    const getModulesInfoItemsModuleId = () => {
      emit("getModulesInfoItemsModuleId");
    };
    //单图上传缩略图
    const handleAvatarSuccessThumb: UploadProps["onSuccess"] = (res, file) => {
      if (res.code === 0) {
        imageUrlThumb.value = URL.createObjectURL(file.raw!);
        state.formData.thumb = res.data.path;
      } else {
        ElMessage.error(res.msg);
      }
      upLoadingThumb.value = false;
    };
    const beforeAvatarUploadThumb = () => {
      upLoadingThumb.value = true;
      return true;
    };
    const setUpData = () => {
      return {token: getToken()};
    };
    const setUpImgListBanner = (data: any) => {
      state.formData.banner = data;
    };
    const setEditContent = (data: string) => {
      state.formData.content = data;
    };

    // 新增递归遍历角色树的辅助函数
    const traverseRoles = (roles: any[], callback: (role: any) => void) => {
      roles.forEach(role => {
        callback(role);
        if (role.children && role.children.length > 0) {
          traverseRoles(role.children, callback);
        }
      });
    };

    // 修改toggleAllPermissions方法
    const toggleAllPermissions = () => {
      traverseRoles(rolePermissionsList.value, (role) => {
        if (state.formData.type === 'channel') {
          role.permissions = role.permissions.includes('list') ? [] : ['list'];
        } else if (state.formData.type === 'list') {
          const allPermissions = permissionOptions.value.map(option => option.value);
          // 反转当前权限选择
          role.permissions = allPermissions.filter(p => !role.permissions.includes(p));
        }
      });
    };

    const toggleAllForRow = (row) => {
        if (state.formData.type === 'channel') {
            row.permissions = row.permissions.includes('list') ? [] : ['list'];
        } else {
            const all = permissionOptions.value.map(opt => opt.value);
            row.permissions = all.filter(p => !row.permissions.includes(p));
        }
    };

    return {
      cmsCategoryChange,
      proxy,
      openDialog,
      closeDialog,
      onCancel,
      onSubmit,
      menuRef,
      formRef,
      getCmsCategoryItemsParentId,
      getModulesInfoItemsModuleId,
      //图片上传地址
      imageUrlThumb,
      //上传加载
      upLoadingThumb,
      handleAvatarSuccessThumb,
      beforeAvatarUploadThumb,
      setUpData,
      setUpImgListBanner,
      baseURL,
      modulesList,
      setEditContent,
      cmsCategoryChannelOptions,
      templateFileList,
      contentTemplateFileList,
      CmsCategoryTypeOptions,
      cmsToolbar: reactive(cmsToolbar),
      permissionOptions,
      rolePermissionsList,
      onSubmit,
      toggleAllPermissions,
      toggleAllForRow,
      cms_nav_position,
      ...toRefs(state)
    };
  }
});
</script>
<style scoped>
.cms-cmsCategory-edit :deep(.avatar-uploader .avatar) {
  width: 128px;
  height: 128px;
  display: block;
}

.cms-cmsCategory-edit :deep(.avatar-uploader .el-upload) {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.cms-cmsCategory-edit :deep(.avatar-uploader .el-upload:hover) {
  border-color: var(--el-color-primary);
}

.cms-cmsCategory-edit :deep(.el-icon.avatar-uploader-icon) {
  font-size: 28px;
  color: #8c939d;
  width: 128px;
  height: 128px;
  text-align: center;
}
</style>