/*
==========================================================================
GFast自动生成菜单SQL,只生成一次,按需修改.
生成日期：2021-07-26 11:07:30
生成路径: data/gen_sql/demo/demo_gen_class_menu.sql
生成人：
==========================================================================
 */

-- 删除原有数据
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/plugins/mail/mail';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/plugins/mail/mail/list';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/plugins/mail/mail/add';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/plugins/mail/mail/get';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/plugins/mail/mail/edit';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/plugins/mail/mail/delete';

/* SYNC DB : temp */
INSERT INTO `sys_auth_rule`(`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES ('0', 'api/v1/plugins/mail/mail', '邮件配置', 'iconfont icon-fuwenbenkuang', '', '', '0', '0', '0', '/plugins/mail/mail', 'layout/routerView/parent', '0', 'sys_admin', '0', '0', '1', '', '0', '', NULL, '2023-11-08 17:27:19');
SELECT @parentId := LAST_INSERT_ID();
INSERT INTO `sys_auth_rule`(`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId,'api/v1/plugins/mail/mail/list', '配置列表', 'ele-Fold', '', '', '1', '0', '0', '/plugins/mail/mail/list', 'plugins/mail/mail/list/index', '0', 'sys_admin', '0', '0', '1', '', '0', '', NULL, '2023-11-08 17:27:01');
SELECT @parentId := LAST_INSERT_ID();
INSERT INTO `sys_auth_rule`(`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/plugins/mail/mail/add', '邮件配置添加', '', '', '邮件配置添加', '2', '0', '0', '', '', '0', 'sys_admin', '0', '0', '1', '', '0', '', NULL, NULL);
INSERT INTO `sys_auth_rule`(`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId,'api/v1/plugins/mail/mail/get', '邮件配置查询', '', '', '邮件配置查询', '2', '0', '0', '', '', '0', 'sys_admin', '0', '0', '1', '', '0', '', NULL, NULL);
INSERT INTO `sys_auth_rule`(`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/plugins/mail/mail/edit', '邮件配置修改', '', '', '邮件配置修改', '2', '0', '0', '', '', '0', 'sys_admin', '0', '0', '1', '', '0', '', NULL, NULL);
INSERT INTO `sys_auth_rule`(`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId,'api/v1/plugins/mail/mail/delete', '邮件配置删除', '', '', '邮件配置删除', '2', '0', '0', '', '', '0', 'sys_admin', '0', '0', '1', '', '0', '', NULL, NULL);


