import{d as we,r as n,f as Ce,X as Ve,ag as d,c as C,o as f,P as a,H as l,a as V,M as h,L as r,I as De,O as B,a6 as $,G as K,Q as Se,J as xe,n as G}from"./@vue-C21YZbHS.js";import{f as Ie,r as Te,a as Ne,h as Re}from"./index-BvZSjrda.js";import{a as Q}from"./element-plus-CUmVNDWO.js";import{a as Ue}from"./index-BGmsw1a8.js";import"./lodash-es-S0Y0Up6J.js";import"./@vueuse-D_5IXmcI.js";import"./@element-plus-_Cc-TEQX.js";import"./@popperjs-D_chPuIy.js";import"./@ctrl-r5W6hzzQ.js";import"./dayjs-CepoAVPu.js";import"./@intlify-D8kAWvSi.js";import"./async-validator-9PlIezaS.js";import"./memoize-one-BdPwpGay.js";import"./normalize-wheel-es-BQoi3Ox2.js";import"./@floating-ui-DwceP2Gb.js";import"./pinia-DwJf82dV.js";import"./vue-router-BDT9dCQ7.js";import"./js-cookie-Cz0CWeBA.js";import"./nprogress-Scw6VIZr.js";import"./axios-B62VicFi.js";import"./qs-D41HS0uK.js";import"./side-channel-DytTmCZg.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-DbsE5JW_.js";import"./crypto-js-oM26lpET.js";import"./side-channel-list-C_7Uk4RP.js";import"./side-channel-map-BUokNT8A.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-bSkmVQJL.js";import"./vue-i18n-BkLk9Ti1.js";import"./vue-iNFchrNl.js";import"./vue-clipboard3-Dr8V5xR7.js";import"./clipboard-DzJOpNE6.js";import"./mitt-DJ65BbbF.js";import"./vue-grid-layout-CIPjP0XN.js";import"./vue-simple-uploader-j6sX1TKo.js";import"./vue-ueditor-wrap-CJ5gjsd3.js";const Ae={class:"system-sysJob-edit"},Le={class:"ml10"},Be={class:"option-con"},Ke={key:1},Me={class:"dialog-footer"},Oe={class:"dialog-footer"},Ee=we({name:"dataScope",__name:"dataScope",emits:["getRoleList"],setup(He,{expose:X,emit:j}){const q=j,D=n(),S=n(!1),{proxy:x}=Ce(),b=n(!1),M=n(!0),O=n(!1),I=n([]);n([]);const _=n(),W=n(),g=n(!1),E=n("0"),T=n(0),Y=n({children:"children",label:"deptName"}),H=n([{value:1,label:"全部"},{value:3,label:"本部门"},{value:4,label:"本部门及以下"},{value:5,label:"仅本人"},{value:2,label:"自定义"}]),u=n([]),c=n([]),p=Ve({roleId:void 0,roleName:"",authData:[{menuId:"0",scope:"0",deptIds:[]}]}),U=n([]),y=n([]),Z=o=>{let e=I.value;for(let s=0;s<e.length;s++)_.value.store.nodesMap[e[s].deptId].expanded=o},ee=o=>{_.value.setCheckedNodes(o?I.value:[])},te=o=>{g.value=!!o},le=()=>{p.authData=[],D.value.getSelectionRows().map(e=>{let s=e.id;p.authData.push({menuId:s,scope:u.value[s],deptIds:c.value[s]??[]})}),Ie(p).then(e=>{e.code===0?(Q.success("设置成功"),b.value=!1,q("getRoleList")):Q.error("设置失败")})},oe=()=>{re()},ae=()=>{c.value[T.value]=N(),A()},ne=()=>{N(),c.value[T.value]=N(),x.findChildrenByPid(T.value,y.value).map(e=>{c.value[e.id]=N()}),A()},A=()=>{g.value=!1,S.value=!1};X({openDialog:o=>{b.value=!0,Te().then(e=>{I.value=e.data.depts||[]}),ue(),o&&(Ne(o.id).then(e=>{e.data.role&&(p.roleName=e.data.role.name,p.roleId=e.data.role.id)}),se(o.id))}});const de=o=>{G(()=>{var e;(e=_.value)==null||e.setCheckedKeys([],!1),c.value[o]&&_.value.setCheckedKeys(c.value[o],!0)})},se=o=>{Re(o).then(e=>{U.value=x.handleTree(e.data.rules??[],"id","pid"),y.value=x.flattenTree(U.value);const s=[],i=[],R=[];e.data.dataScope&&(e.data.dataScope.map(m=>{s[m.menuId]=m.dataScope,i[m.menuId]=m.deptIds}),u.value=s,c.value=i,y.value.map(m=>{u.value[m.id]&&R.push(m)}),G(()=>{R.map(m=>{D.value.toggleRowSelection(m,!0)})}))})},re=()=>{b.value=!1},N=()=>_.value.getCheckedKeys(),ue=()=>{p.roleId=void 0,p.roleName="",p.authData=[],g.value=!1,u.value=[],c.value=[]},pe=o=>{},ie=o=>{y.value.map(e=>{u.value[e.id]=o})},me=(o,e)=>{const s=x.findChildrenByPid(e,y.value);y.value.some(i=>i.id==e?(s.unshift(i),!0):!1),s.map(i=>{D.value.toggleRowSelection(i,!0)}),s.map(i=>{u.value[i.id]=o})},ce=o=>{S.value=!0,T.value=o,de(o)},ve=o=>c.value[o]?c.value[o].length:0;return(o,e)=>{const s=d("el-alert"),i=d("el-input"),R=d("el-form-item"),m=d("el-form"),k=d("el-table-column"),fe=d("SvgIcon"),_e=d("el-tag"),P=d("el-radio"),z=d("el-radio-group"),ge=d("el-badge"),ye=d("el-link"),he=d("el-table"),w=d("el-button"),F=d("el-dialog"),L=d("el-checkbox"),J=d("el-col"),be=d("el-tree"),ke=d("el-row");return f(),C("div",Ae,[a(F,{title:"数据权限设置",modelValue:b.value,"onUpdate:modelValue":e[2]||(e[2]=t=>b.value=t),width:"90%","append-to-body":"","close-on-click-modal":!1},{footer:l(()=>[V("div",Me,[a(w,{type:"primary",onClick:le},{default:l(()=>e[11]||(e[11]=[r("确 定")])),_:1}),a(w,{onClick:oe},{default:l(()=>e[12]||(e[12]=[r("取 消")])),_:1})])]),default:l(()=>[a(s,{title:"注：此功能界面需要与代码查询条件配合使用，并非所有接口都需设置数据权限，多用于业务模块！",closable:!1,type:"warning",style:{"margin-bottom":"12px"}}),a(m,{ref_key:"formRef",ref:W,model:p,"label-width":"80px"},{default:l(()=>[a(R,{label:"角色名称"},{default:l(()=>[a(i,{modelValue:p.roleName,"onUpdate:modelValue":e[0]||(e[0]=t=>p.roleName=t),disabled:!0},null,8,["modelValue"])]),_:1})]),_:1},8,["model"]),a(he,{ref_key:"tableRef",ref:D,data:U.value,style:{width:"100%"},"row-key":"path","tree-props":{children:"children",hasChildren:"hasChildren"},onSelectionChange:pe},{default:l(()=>[a(k,{type:"selection",width:"55"}),a(k,{label:"菜单名称"},{default:l(t=>[a(fe,{name:t.row.icon},null,8,["name"]),V("span",Le,h(t.row.title),1)]),_:1}),a(k,{label:"api接口"},{default:l(t=>[V("span",null,h(t.row.name),1)]),_:1}),a(k,{label:"类型","show-overflow-tooltip":"",width:"80"},{default:l(t=>[a(_e,{type:t.row.menuType===0?"danger":t.row.menuType===1?"success":"warning",size:"small"},{default:l(()=>[r(h(t.row.menuType===0?"目录":t.row.menuType===1?"菜单":"按钮"),1)]),_:2},1032,["type"])]),_:1}),a(k,{label:"操作",width:"600"},{header:l(()=>[a(z,{modelValue:E.value,"onUpdate:modelValue":e[1]||(e[1]=t=>E.value=t),onChange:ie},{default:l(()=>[(f(!0),C(B,null,$(H.value,t=>(f(),C(B,{key:t.value},[t.value.toString()!="2"?(f(),K(P,{key:0,value:t.value},{default:l(()=>[r(h(t.label),1)]),_:2},1032,["value"])):xe("",!0)],64))),128))]),_:1},8,["modelValue"])]),default:l(t=>[V("div",Be,[a(z,{modelValue:u.value[t.row.id],"onUpdate:modelValue":v=>u.value[t.row.id]=v},{default:l(()=>[(f(!0),C(B,null,$(H.value,v=>(f(),K(P,{key:v.value,value:v.value,onChange:ze=>me(v.value,t.row.id)},{default:l(()=>[v.value.toString()=="2"&&u.value[t.row.id]=="2"?(f(),K(ge,{key:0,type:"success",value:ve(t.row.id),max:99,class:"item","show-zero":!1},{default:l(()=>[r(h(v.label),1)]),_:2},1032,["value"])):(f(),C("span",Ke,h(v.label),1))]),_:2},1032,["value","onChange"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"]),De(a(ye,{type:"primary",onClick:v=>ce(t.row.id),underline:!1},{default:l(()=>e[10]||(e[10]=[r("选择部门")])),_:2},1032,["onClick"]),[[Se,u.value[t.row.id]&&u.value[t.row.id]=="2"]])])]),_:1})]),_:1},8,["data"])]),_:1},8,["modelValue"]),a(F,{title:"选择部门",modelValue:S.value,"onUpdate:modelValue":e[9]||(e[9]=t=>S.value=t),width:"800px","append-to-body":"","close-on-click-modal":!1},{footer:l(()=>[V("div",Oe,[a(w,{type:"primary",onClick:ae},{default:l(()=>e[16]||(e[16]=[r("确 定")])),_:1}),a(w,{type:"success",onClick:ne},{default:l(()=>e[17]||(e[17]=[r("确定并应用到子菜单")])),_:1}),a(w,{onClick:A},{default:l(()=>e[18]||(e[18]=[r("取 消")])),_:1})])]),default:l(()=>[a(ke,{gutter:35},{default:l(()=>[a(J,{xs:24,sm:24,md:24,lg:24,xl:24},{default:l(()=>[a(L,{modelValue:M.value,"onUpdate:modelValue":e[3]||(e[3]=t=>M.value=t),onChange:e[4]||(e[4]=t=>Z(t))},{default:l(()=>e[13]||(e[13]=[r("展开/折叠")])),_:1},8,["modelValue"]),a(L,{modelValue:O.value,"onUpdate:modelValue":e[5]||(e[5]=t=>O.value=t),onChange:e[6]||(e[6]=t=>ee(t))},{default:l(()=>e[14]||(e[14]=[r("全选/全不选")])),_:1},8,["modelValue"]),a(L,{modelValue:g.value,"onUpdate:modelValue":e[7]||(e[7]=t=>g.value=t),onChange:e[8]||(e[8]=t=>te(t))},{default:l(()=>e[15]||(e[15]=[r("父子联动")])),_:1},8,["modelValue"])]),_:1}),a(J,{xs:24,sm:24,md:24,lg:24,xl:24,class:"mb20"},{default:l(()=>[a(be,{class:"tree-border",data:I.value,"show-checkbox":"","default-expand-all":"",ref_key:"deptRef",ref:_,"node-key":"deptId","check-strictly":!g.value,props:Y.value},null,8,["data","check-strictly","props"])]),_:1})]),_:1})]),_:1},8,["modelValue"])])}}}),Mt=Ue(Ee,[["__scopeId","data-v-590e4156"]]);export{Mt as default};
