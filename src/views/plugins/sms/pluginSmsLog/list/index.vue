<template>
  <div class="sms-pluginSmsLog-container">
    <el-card shadow="hover">
        <div class="sms-pluginSmsLog-search mb15">
            <el-form :model="tableData.param" ref="queryRef" :inline="true" label-width="100px">
            <el-row>
                <el-col :span="6">
                  <el-form-item label="模板id" prop="templateid">
                    <el-input
                        v-model="tableData.param.templateid"
                        placeholder="请输入模板id"
                        clearable
                        @keyup.enter.native="pluginSmsLogList"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6" >
                  <el-form-item label="电话号码" prop="mobiles">
                    <el-input
                        v-model="tableData.param.mobiles"
                        placeholder="请输入电话号码"
                        clearable
                        @keyup.enter.native="pluginSmsLogList"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="发送日期" prop="createdAt">
                    <el-date-picker
                        clearable  style="width: 200px"
                        v-model="tableData.param.createdAt"
                        format="YYYY-MM-DD HH:mm:ss"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        type="datetime"
                        placeholder="选择发送日期"
                    ></el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="6" >
                  <el-form-item>
                    <el-button type="primary"  @click="pluginSmsLogList"><el-icon><ele-Search /></el-icon>搜索</el-button>
                    <el-button  @click="resetQuery(queryRef)"><el-icon><ele-Refresh /></el-icon>重置</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button
                  type="primary"
                  @click="handleAdd"
                  v-auth="'api/v1/sms/pluginSmsLog/add'"
                ><el-icon><ele-Plus /></el-icon>新增</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                  type="success"
                  :disabled="single"
                  @click="handleUpdate(null)"
                  v-auth="'api/v1/sms/pluginSmsLog/edit'"
                ><el-icon><ele-Edit /></el-icon>修改</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                  type="danger"
                  :disabled="multiple"
                  @click="handleDelete(null)"
                  v-auth="'api/v1/sms/pluginSmsLog/delete'"
                ><el-icon><ele-Delete /></el-icon>删除</el-button>
              </el-col>
            </el-row>
        </div>
        <el-table v-loading="loading" :data="tableData.data" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="ID" align="center" prop="id"
            min-width="150px"
             />
          <el-table-column label="短信平台的类型" align="center" prop="smsType"
            min-width="150px"
             />
          <el-table-column label="短信的类型" align="center" prop="msgType"
            min-width="150px"
             />
          <el-table-column label="模板id" align="center" prop="templateid"
            min-width="150px"
             />
          <el-table-column label="电话号码" align="center" prop="mobiles"
            min-width="150px"
             />
          <el-table-column label="模板参数" align="center" prop="params"
            min-width="150px"
             />
          <el-table-column label="发送日期" align="center" prop="createdAt"
            min-width="150px"
            >
            <template #default="scope">
                <span>{{ proxy.parseTime(scope.row.createdAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding" min-width="200px" fixed="right">
            <template #default="scope">
              <el-button
                type="primary"
                link
                @click="handleView(scope.row)"
                v-auth="'api/v1/sms/pluginSmsLog/get'"
              ><el-icon><ele-View /></el-icon>详情</el-button>
              <el-button
                type="primary"
                link
                @click="handleUpdate(scope.row)"
                v-auth="'api/v1/sms/pluginSmsLog/edit'"
              ><el-icon><ele-EditPen /></el-icon>修改</el-button>
              <el-button
                type="primary"
                link
                @click="handleDelete(scope.row)"
                v-auth="'api/v1/sms/pluginSmsLog/delete'"
              ><el-icon><ele-DeleteFilled /></el-icon>删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
            v-show="tableData.total>0"
            :total="tableData.total"
            v-model:page="tableData.param.pageNum"
            v-model:limit="tableData.param.pageSize"
            @pagination="pluginSmsLogList"
        />
    </el-card>
    <ApiV1PluginsSmsPluginSmsLogEdit
       ref="editRef"
       @pluginSmsLogList="pluginSmsLogList"
    ></ApiV1PluginsSmsPluginSmsLogEdit>
    <ApiV1PluginsSmsPluginSmsLogDetail
      ref="detailRef"
      @pluginSmsLogList="pluginSmsLogList"
    ></ApiV1PluginsSmsPluginSmsLogDetail>
  </div>
</template>
<script setup lang="ts">
import {ItemOptions} from "/@/api/items";
import {toRefs, reactive, onMounted, ref, defineComponent, computed,getCurrentInstance,toRaw} from 'vue';
import {ElMessageBox, ElMessage, FormInstance} from 'element-plus';
import {
    listPluginSmsLog,
    getPluginSmsLog,
    delPluginSmsLog,
    addPluginSmsLog,
    updatePluginSmsLog,
} from "/@/api/plugins/sms/pluginSmsLog";
import {
    PluginSmsLogTableColumns,
    PluginSmsLogInfoData,
    PluginSmsLogTableDataState,
} from "/@/views/plugins/sms/pluginSmsLog/list/component/model"
import ApiV1PluginsSmsPluginSmsLogEdit from "/@/views/plugins/sms/pluginSmsLog/list/component/edit.vue"
import ApiV1PluginsSmsPluginSmsLogDetail from "/@/views/plugins/sms/pluginSmsLog/list/component/detail.vue"
defineOptions({ name: "ApiV1PluginsSmsPluginSmsLogList"})
const {proxy} = <any>getCurrentInstance()
const loading = ref(false)
const queryRef = ref()
const editRef = ref();
const detailRef = ref();
// 是否显示所有搜索选项
const showAll =  ref(false)
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple =ref(true)
const word = computed(()=>{
    if(showAll.value === false) {
        //对文字进行处理
        return "展开搜索";
    } else {
        return "收起搜索";
    }
})
// 字典选项数据
const {
} = proxy.useDict(
)
const state = reactive<PluginSmsLogTableDataState>({
    ids:[],
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
            smsType: undefined,
            msgType: undefined,
            templateid: undefined,
            mobiles: undefined,
            createdAt: undefined,
            dateRange: []
        },
    },
});
const { tableData} = toRefs(state);
// 页面加载时
onMounted(() => {
    initTableData();
});
// 初始化表格数据
const initTableData = () => {
    pluginSmsLogList()
};
/** 重置按钮操作 */
const resetQuery = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.resetFields()
    pluginSmsLogList()
};
// 获取列表数据
const pluginSmsLogList = ()=>{
  loading.value = true
  listPluginSmsLog(state.tableData.param).then((res:any)=>{
    let list = res.data.list??[];
    state.tableData.data = list;
    state.tableData.total = res.data.total;
    loading.value = false
  })
};
const toggleSearch = () => {
    showAll.value = !showAll.value;
}
// 多选框选中数据
const handleSelectionChange = (selection:Array<PluginSmsLogInfoData>) => {
    state.ids = selection.map(item => item.id)
    single.value = selection.length!=1
    multiple.value = !selection.length
}
const handleAdd =  ()=>{
    editRef.value.openDialog()
}
const handleUpdate = (row: PluginSmsLogTableColumns|null) => {
    if(!row){
        row = state.tableData.data.find((item:PluginSmsLogTableColumns)=>{
            return item.id ===state.ids[0]
        }) as PluginSmsLogTableColumns
    }
    editRef.value.openDialog(toRaw(row));
};
const handleDelete = (row: PluginSmsLogTableColumns|null) => {
    let msg = '你确定要删除所选数据？';
    let id:number[] = [] ;
    if(row){
    msg = `此操作将永久删除数据，是否继续?`
    id = [row.id]
    }else{
    id = state.ids
    }
    if(id.length===0){
        ElMessage.error('请选择要删除的数据。');
        return
    }
    ElMessageBox.confirm(msg, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            delPluginSmsLog(id).then(()=>{
                ElMessage.success('删除成功');
                pluginSmsLogList();
            })
        })
        .catch(() => {});
}
const handleView = (row:PluginSmsLogTableColumns)=>{
    detailRef.value.openDialog(toRaw(row));
}
</script>
<style lang="scss" scoped>
    .colBlock {
        display: block;
    }
    .colNone {
        display: none;
    }
    .ml-2{margin: 3px;}
</style>
