import{l as $,d as N}from"./ucenterConfig-BQReylwC.js";import I from"./add-5jyTX8O5.js";import T from"./edit-DqbxzEGJ.js";import z from"./detail-DXu7rLhb.js";import{a as A,E as M}from"./element-plus-CUmVNDWO.js";import{d as q,f as Q,r as d,k as G,X as H,h as X,ad as j,ab as J,ag as a,aq as B,c as K,o as C,P as e,H as i,a as O,I as _,G as h,L as U,Q as W}from"./@vue-C21YZbHS.js";import{a as Y}from"./index-BGmsw1a8.js";import"./lodash-es-S0Y0Up6J.js";import"./@vueuse-D_5IXmcI.js";import"./@element-plus-_Cc-TEQX.js";import"./@popperjs-D_chPuIy.js";import"./@ctrl-r5W6hzzQ.js";import"./dayjs-CepoAVPu.js";import"./@intlify-D8kAWvSi.js";import"./async-validator-9PlIezaS.js";import"./memoize-one-BdPwpGay.js";import"./normalize-wheel-es-BQoi3Ox2.js";import"./@floating-ui-DwceP2Gb.js";import"./pinia-DwJf82dV.js";import"./vue-router-BDT9dCQ7.js";import"./js-cookie-Cz0CWeBA.js";import"./nprogress-Scw6VIZr.js";import"./axios-B62VicFi.js";import"./qs-D41HS0uK.js";import"./side-channel-DytTmCZg.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-DbsE5JW_.js";import"./crypto-js-oM26lpET.js";import"./side-channel-list-C_7Uk4RP.js";import"./side-channel-map-BUokNT8A.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-bSkmVQJL.js";import"./vue-i18n-BkLk9Ti1.js";import"./vue-iNFchrNl.js";import"./vue-clipboard3-Dr8V5xR7.js";import"./clipboard-DzJOpNE6.js";import"./mitt-DJ65BbbF.js";import"./vue-grid-layout-CIPjP0XN.js";import"./vue-simple-uploader-j6sX1TKo.js";import"./vue-ueditor-wrap-CJ5gjsd3.js";const Z=q({name:"apiV1UcenterUcenterConfigList",components:{apiV1UcenterUcenterConfigAdd:I,apiV1UcenterUcenterConfigEdit:T,apiV1UcenterUcenterConfigDetail:z},setup(){const{proxy:t}=Q(),n=d(!1),k=d(),b=d(),v=d(),L=d(),f=d(!1),c=d(!0),m=d(!0),D=G(()=>f.value===!1?"展开搜索":"收起搜索");t.useDict();const l=H({ids:[],tableData:{data:[],total:0,loading:!1,param:{pageNum:1,pageSize:10,dateRange:[]}}});X(()=>{w()});const w=()=>{g()},s=o=>{o&&(o.resetFields(),g())},g=()=>{n.value=!0,$(l.tableData.param).then(o=>{let r=o.data.list??[];l.tableData.data=r,l.tableData.total=o.data.total,n.value=!1})};return{proxy:t,addRef:b,editRef:v,detailRef:L,showAll:f,loading:n,single:c,multiple:m,word:D,queryRef:k,resetQuery:s,ucenterConfigList:g,toggleSearch:()=>{f.value=!f.value},handleSelectionChange:o=>{l.ids=o.map(r=>r.id),c.value=o.length!=1,m.value=!o.length},handleAdd:()=>{b.value.openDialog()},handleUpdate:o=>{o||(o=l.tableData.data.find(r=>r.id===l.ids[0])),v.value.openDialog(J(o))},handleDelete:o=>{let r="你确定要删除所选数据？",p=[];if(o?(r="此操作将永久删除数据，是否继续?",p=[o.id]):p=l.ids,p.length===0){A.error("请选择要删除的数据。");return}M.confirm(r,"提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(()=>{N(p).then(()=>{A.success("删除成功"),g()})}).catch(()=>{})},...j(l)}}}),x={class:"ucenter-ucenterConfig-container"},ee={class:"ucenter-ucenterConfig-search mb15"};function te(t,n,k,b,v,L){const f=a("ele-Plus"),c=a("el-icon"),m=a("el-button"),D=a("el-col"),l=a("ele-Delete"),w=a("el-row"),s=a("el-table-column"),g=a("ele-EditPen"),V=a("ele-DeleteFilled"),y=a("el-table"),E=a("pagination"),S=a("el-card"),R=a("apiV1UcenterUcenterConfigAdd"),o=a("apiV1UcenterUcenterConfigEdit"),r=a("apiV1UcenterUcenterConfigDetail"),p=B("auth"),F=B("loading");return C(),K("div",x,[e(S,{shadow:"hover"},{default:i(()=>[O("div",ee,[e(w,{gutter:10,class:"mb8"},{default:i(()=>[e(D,{span:1.5},{default:i(()=>[_((C(),h(m,{type:"primary",onClick:t.handleAdd},{default:i(()=>[e(c,null,{default:i(()=>[e(f)]),_:1}),n[3]||(n[3]=U(" 新增 "))]),_:1},8,["onClick"])),[[p,"api/v1/ucenter/ucenterConfig/add"]])]),_:1}),e(D,{span:1.5},{default:i(()=>[_((C(),h(m,{disabled:t.multiple,type:"danger",onClick:n[0]||(n[0]=u=>t.handleDelete(null))},{default:i(()=>[e(c,null,{default:i(()=>[e(l)]),_:1}),n[4]||(n[4]=U(" 删除 "))]),_:1},8,["disabled"])),[[p,"api/v1/ucenter/ucenterConfig/delete"]])]),_:1})]),_:1})]),_((C(),h(y,{border:"",stripe:"",data:t.tableData.data,onSelectionChange:t.handleSelectionChange,class:"small-padding"},{default:i(()=>[e(s,{type:"selection",width:"55"}),e(s,{label:"ID",width:"80px",prop:"id"}),e(s,{label:"名称",width:"250px",prop:"name"}),e(s,{label:"值","min-width":"250px",prop:"value"}),e(s,{label:"提示信息","min-width":"100px",prop:"tips"}),e(s,{align:"center",fixed:"right",label:"操作",width:"160px"},{default:i(u=>[_((C(),h(m,{link:"",type:"primary",onClick:P=>t.handleUpdate(u.row)},{default:i(()=>[e(c,null,{default:i(()=>[e(g)]),_:1}),n[5]||(n[5]=U(" 修改 "))]),_:2},1032,["onClick"])),[[p,"api/v1/ucenter/ucenterConfig/edit"]]),_((C(),h(m,{link:"",type:"primary",onClick:P=>t.handleDelete(u.row)},{default:i(()=>[e(c,null,{default:i(()=>[e(V)]),_:1}),n[6]||(n[6]=U(" 删除 "))]),_:2},1032,["onClick"])),[[p,"api/v1/ucenter/ucenterConfig/delete"]])]),_:1})]),_:1},8,["data","onSelectionChange"])),[[F,t.loading]]),_(e(E,{limit:t.tableData.param.pageSize,"onUpdate:limit":n[1]||(n[1]=u=>t.tableData.param.pageSize=u),page:t.tableData.param.pageNum,"onUpdate:page":n[2]||(n[2]=u=>t.tableData.param.pageNum=u),total:t.tableData.total,onPagination:t.ucenterConfigList},null,8,["limit","page","total","onPagination"]),[[W,t.tableData.total>0]])]),_:1}),e(R,{ref:"addRef",onUcenterConfigList:t.ucenterConfigList},null,8,["onUcenterConfigList"]),e(o,{ref:"editRef",onUcenterConfigList:t.ucenterConfigList},null,8,["onUcenterConfigList"]),e(r,{ref:"detailRef",onUcenterConfigList:t.ucenterConfigList},null,8,["onUcenterConfigList"])])}const xe=Y(Z,[["render",te],["__scopeId","data-v-e7327f9d"]]);export{xe as default};
