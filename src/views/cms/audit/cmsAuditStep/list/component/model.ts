export interface CmsAuditStepTableColumns {    
    id:number;  //    
    title:string;  // 步骤名称    
    step:number;  // 步骤    
    type:string;  // 类别    
    createdAt:string;  // 创建时间    
}


export interface CmsAuditStepInfoData {    
    id:number|undefined;        //    
    title:string|undefined; // 步骤名称    
    step:number|undefined; // 步骤    
    type:string|undefined; // 类别    
    createdAt:string|undefined; // 创建时间    
    updatedAt:string|undefined; // 更新时间    
}


export interface CmsAuditStepTableDataState {
    ids:any[];
    tableData: {
        data: Array<CmsAuditStepTableColumns>;
        total: number;
        loading: boolean;
        param: {
            pageNum: number;
            pageSize: number;            
            id: number|undefined;            
            title: string|undefined;            
            step: number|undefined;            
            type: string|undefined;            
            createdAt: string|undefined;            
            dateRange: string[];
        };
    };
}


export interface CmsAuditStepEditState{
    loading:boolean;
    isShowDialog: boolean;
    formData:CmsAuditStepInfoData;
    rules: object;
}