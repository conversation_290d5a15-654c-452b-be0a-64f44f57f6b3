// ==========================================================================
// GFast自动生成router操作代码。
// 生成日期：2025-05-07 08:21:11
// 生成路径: internal/app/cms/audit/router/cms_audit.go
// 生成人：zhanghui
// desc:文章审核
// company:云南奇讯科技有限公司
// ==========================================================================

package router

import (
	"context"

	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/internal/app/cms/audit/controller"
)

func (router *Router) BindCmsAuditController(ctx context.Context, group *ghttp.RouterGroup) {
	group.Group("/cmsAudit", func(group *ghttp.RouterGroup) {
		group.Bind(
			controller.CmsAudit,
		)
	})
}
