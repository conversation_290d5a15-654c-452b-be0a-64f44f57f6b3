package model

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	model2 "github.com/tiger1103/gfast/v3/internal/app/cms/model"
	"github.com/tiger1103/gfast/v3/internal/app/system/model"
)

// CmsArticleInfoRes is the golang structure for table cms_article.
type ArticleInfoRes struct {
	Id            uint64                               `orm:"id,primary" json:"id"`                // ID
	Title         string                               `orm:"title" json:"title"`                  // 标题
	SeoTitle      string                               `orm:"seo_title" json:"seoTitle"`           // SEO标题
	CategoryId    uint                                 `orm:"category_id" json:"categoryId"`       // 分类
	ModuleId      uint                                 `orm:"module_id" json:"moduleId"`           // 模型
	Keywords      string                               `orm:"keywords" json:"keywords"`            // 关键字
	Description   string                               `orm:"description" json:"description"`      // 描述
	Hits          int                                  `orm:"hits" json:"hits"`                    // 点击数
	Flag          string                               `orm:"flag" json:"flag"`                    // 推荐属性
	CommentCount  int                                  `orm:"comment_count" json:"commentCount"`   // 评论数
	FavoriteCount int                                  `orm:"favorite_count" json:"favoriteCount"` // 收藏数
	LikeCount     int                                  `orm:"like_count" json:"likeCount"`         // 点赞数
	Thumb         string                               `orm:"thumb" json:"thumb"`                  // 缩略图
	Template      string                               `orm:"template" json:"template"`            // 模板
	OriginUrl     string                               `orm:"origin_url" json:"originUrl"`         // 来源地址
	OriginTitle   string                               `orm:"origin_title" json:"originTitle"`     // 来源名称
	Summary       string                               `orm:"summary" json:"summary"`              // 来源名称
	Content       string                               `json:"content"`                            // 文章内容
	IsJump        int                                  `json:"isJump"`                             // 是否跳转
	AllowComment  int                                  `json:"allowComment"`                       // 允许评论
	JumpUrl       string                               `json:"jumpUrl"`                            // 跳跳链接
	Url           string                               `json:"url"`
	Attr          []string                             `json:"attr"`
	UserId        uint64                               `json:"userId"`                             // 发布人ID
	PublishedAt   *gtime.Time                          `orm:"published_at" json:"publishedAt"`     // 发布日期
	Category      *model2.LinkedCmsCategoryCmsCategory `orm:"with:id=category_id" json:"category"` // 分类信息
	UserInfo      *model.SysUserSimpleRes              `orm:"with:id=user_id" json:"userInfo"`
	ModulesData   g.Map                                `json:"modulesData"` // 模型内容
}

// ArticleSearchRes 列表返回结果
type ArticleSearchRes struct {
	commonApi.ListRes
	List []*ArticleListRes `json:"list"`
}

type ArticleListRes struct {
	Id            uint64                               `json:"id"`
	Title         string                               `json:"title"`
	SeoTitle      string                               `json:"seoTitle"`     // Seo标题
	Description   string                               `json:"description"`  // Seo描述
	AllowComment  int                                  `json:"allowComment"` // 允许评论
	Keywords      string                               `json:"keywords"`     // Seo关键字
	CategoryId    uint                                 `json:"categoryId"`
	ModuleId      uint                                 `json:"moduleId"`
	Hits          int                                  `json:"hits"`
	Flag          string                               `json:"flag"`
	Attr          []string                             `json:"attr"`
	CommentCount  int                                  `json:"commentCount"`
	FavoriteCount int                                  `json:"favoriteCount"` // 收藏数
	LikeCount     int                                  `json:"likeCount"`     // 点赞数
	Thumb         string                               `json:"thumb"`
	UserId        uint64                               `json:"userId"`
	OriginTitle   string                               `json:"originTitle"` // 来源标题
	OriginUrl     string                               `json:"originUrl"`   // 来源地址
	Summary       string                               `json:"summary"`     // 摘要
	Url           string                               `json:"url"`
	IsJump        int                                  `json:"isJump"`
	JumpUrl       string                               `json:"jumpUrl"`
	PublishedAt   *gtime.Time                          `json:"publishedAt"`
	Category      *model2.LinkedCmsCategoryCmsCategory `orm:"with:id=category_id" json:"category"` // 分类信息
	UserInfo      *model.SysUserSimpleRes              `orm:"with:id=user_id" json:"userInfo"`
}

type LinkedArticleInfoRes struct {
	Id          uint64                               `json:"id"`
	Title       string                               `json:"title"`
	CategoryId  uint                                 `json:"categoryId"`
	Hits        int                                  `json:"hits"`
	Thumb       string                               `json:"thumb"`
	Summary     string                               `json:"summary"`
	Category    *model2.LinkedCmsCategoryCmsCategory `orm:"with:id=category_id" json:"category"` // 分类信息
	UserInfo    *model.SysUserSimpleRes              `orm:"with:id=user_id" json:"userInfo"`
	PublishedAt *gtime.Time                          `json:"publishedAt"`
	Url         string                               `json:"url"`
	IsJump      int                                  `json:"isJump"`
	JumpUrl     string                               `json:"jumpUrl"`
}
