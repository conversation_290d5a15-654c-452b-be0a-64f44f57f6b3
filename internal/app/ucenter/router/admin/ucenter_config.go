// ==========================================================================
// GFast自动生成router操作代码。
// 生成日期：2023-03-09 16:48:33
// 生成路径: internal/app/ucenter/router/ucenter_config.go
// 生成人：yxf
// desc:用户中心配置
// company:云南奇讯科技有限公司
// ==========================================================================

package admin

import (
	"context"
	"github.com/gogf/gf/v2/net/ghttp"
	adminController "github.com/tiger1103/gfast/v3/internal/app/ucenter/controller/admin"
)

func (router *Router) BindUCenterConfigController(ctx context.Context, group *ghttp.RouterGroup) {
	group.Group("/ucenterConfig", func(group *ghttp.RouterGroup) {
		group.Bind(
			adminController.UCenterConfig,
		)
	})
}
