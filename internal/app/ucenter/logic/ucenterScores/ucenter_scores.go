// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2023-03-09 16:50:38
// 生成路径: internal/app/ucenter/logic/ucenter_scores.go
// 生成人：yxf
// desc:用户积分
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	apiAdmin "github.com/tiger1103/gfast/v3/api/v1/ucenter/admin"

	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/internal/app/ucenter/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ucenter/model"
	"github.com/tiger1103/gfast/v3/internal/app/ucenter/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ucenter/service"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterUCenterScores(New())
}

func New() *sUCenterScores {
	return &sUCenterScores{}
}

type sUCenterScores struct{}

func (s *sUCenterScores) List(ctx context.Context, req *apiAdmin.UCenterScoresSearchReq) (listRes *apiAdmin.UCenterScoresSearchRes, err error) {
	listRes = new(apiAdmin.UCenterScoresSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.UCenterScores.Ctx(ctx).WithAll()
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id desc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.UCenterScoresInfoRes
		err = m.Fields(apiAdmin.UCenterScoresSearchRes{}).Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.UCenterScoresListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.UCenterScoresListRes{
				Id:          v.Id,
				MemberId:    v.MemberId,
				Score:       v.Score,
				DataSrc:     v.DataSrc,
				Description: v.Description,
				Type:        v.Type,
				CreatedAt:   v.CreatedAt,
			}
		}
	})
	return
}

func (s *sUCenterScores) GetById(ctx context.Context, id uint64) (res *model.UCenterScoresInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.UCenterScores.Ctx(ctx).WithAll().Where(dao.UCenterScores.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sUCenterScores) Add(ctx context.Context, req *apiAdmin.UCenterScoresAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.UCenterScores.Ctx(ctx).Insert(do.UCenterScores{
			MemberId:    req.MemberId,
			Score:       req.Score,
			DataSrc:     req.DataSrc,
			Description: req.Description,
			Type:        req.Type,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sUCenterScores) Edit(ctx context.Context, req *apiAdmin.UCenterScoresEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.UCenterScores.Ctx(ctx).WherePri(req.Id).Update(do.UCenterScores{
			MemberId:    req.MemberId,
			Score:       req.Score,
			DataSrc:     req.DataSrc,
			Description: req.Description,
			Type:        req.Type,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sUCenterScores) Delete(ctx context.Context, ids []uint64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.UCenterScores.Ctx(ctx).Delete(dao.UCenterScores.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
