<template>
  <div class="system-modulesInfo-container">
    <el-card shadow="hover">
      <div class="system-modulesInfo-search mb15">
        <el-form ref="queryRef" :inline="true" :model="tableData.param" label-width="80px">
          <el-row>
            <el-form-item label="模型名称" prop="name">
              <el-input
                  v-model="tableData.param.name"
                  clearable
                  placeholder="请输入模型名称"
                  @keyup.enter.native="modulesInfoList"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="modulesInfoList">
                <el-icon>
                  <ele-Search/>
                </el-icon>
                搜索
              </el-button>
              <el-button @click="resetQuery(queryRef)">
                <el-icon>
                  <ele-Refresh/>
                </el-icon>
                重置
              </el-button>
            </el-form-item>
          </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
                v-auth="'api/v1/system/modulesInfo/add'"
                type="primary"
                @click="handleAdd"
            >
              <el-icon>
                <ele-Plus/>
              </el-icon>
              新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                v-auth="'api/v1/system/modulesInfo/delete'"
                :disabled="multiple"
                type="danger"
                @click="handleDelete(null)"
            >
              <el-icon>
                <ele-Delete/>
              </el-icon>
              删除
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                type="success"
                @click="modulesInfoList"
            >
              <el-icon>
                <ele-Refresh/>
              </el-icon>
              刷新
            </el-button>
          </el-col>
        </el-row>
      </div>
      <el-table v-loading="loading" :data="tableData.data" border @selection-change="handleSelectionChange">
        <el-table-column align="center" type="selection" width="55"/>
        <el-table-column align="center" label="ID" prop="id" width="80px"/>
        <el-table-column label="模型名称" min-width="100px" prop="name"/>
        <el-table-column label="数据表名" min-width="100px" prop="tableName"/>
        <el-table-column align="center" class-name="small-padding" fixed="right" label="操作" width="260px">
          <template #default="scope">
            <el-button v-auth="'api/v1/system/modulesInfo/edit'" link type="primary" @click="handleUpdate(scope.row)">
              修改
            </el-button>
            <el-button v-auth="'api/v1/system/modulesInfo/edit'" link type="primary" @click="handleFieldEdit(scope.row)">
              字段设置
            </el-button>
            <el-button v-auth="'api/v1/system/modulesInfo/edit'" link type="primary" @click="handleCreateModule(scope.row)">
              生成模型
            </el-button>
            <el-button v-auth="'api/v1/system/modulesInfo/delete'" link type="primary" @click="handleDelete(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
          v-show="tableData.total>0"
          v-model:limit="tableData.param.pageSize"
          v-model:page="tableData.param.pageNum"
          :total="tableData.total"
          @pagination="modulesInfoList"
      />
    </el-card>
    <apiV1SystemModulesInfoEdit
        ref="editRef"
        @modulesInfoList="modulesInfoList"
    ></apiV1SystemModulesInfoEdit>
  </div>
</template>
<script lang="ts">
import {computed, defineComponent, getCurrentInstance, onMounted, reactive, ref, toRaw, toRefs} from "vue";
import {ElMessage, ElMessageBox, FormInstance} from "element-plus";
import {createModule, delModulesInfo, listModulesInfo} from "/@/api/system/modulesInfo";
import {ModulesInfoInfoData, ModulesInfoTableColumns, ModulesInfoTableDataState} from "/@/views/system/modulesInfo/list/component/model";
import apiV1SystemModulesInfoEdit from "/@/views/system/modulesInfo/list/component/edit.vue";
import {useRouter} from "vue-router";

export default defineComponent({
  name: "apiV1SystemModulesInfoList",
  components: {
    apiV1SystemModulesInfoEdit,
  },
  setup() {
    const router = useRouter()
    const {proxy} = <any>getCurrentInstance();
    const loading = ref(false);
    const queryRef = ref();
    const editRef = ref();
    // 是否显示所有搜索选项
    const showAll = ref(false);
    // 非单个禁用
    const single = ref(true);
    // 非多个禁用
    const multiple = ref(true);
    const word = computed(() => {
      if (showAll.value === false) {
        //对文字进行处理
        return "展开搜索";
      } else {
        return "收起搜索";
      }
    });
    // 字典选项数据
    const {} = proxy.useDict(
    );
    const state = reactive<ModulesInfoTableDataState>({
      ids: [],
      tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
          pageNum: 1,
          pageSize: 10,
          id: undefined,
          name: undefined,
          sort: undefined,
          tableName: undefined,
          createdAt: undefined,
          status: undefined,
          fields: undefined
        }
      }
    });
    // 页面加载时
    onMounted(() => {
      initTableData();
    });
    // 初始化表格数据
    const initTableData = () => {
      modulesInfoList();
    };
    /** 重置按钮操作 */
    const resetQuery = (formEl: FormInstance | undefined) => {
      if (!formEl) return;
      formEl.resetFields();
      modulesInfoList();
    };
    const handleCreateModule = (row:ModulesInfoTableColumns) => {
      ElMessageBox.confirm("生成模型会备份并删除原表数据，是否确认？","警告",{
        cancelButtonText:"取消",
        confirmButtonText:"确认",
        type:"warning"
      }).then(()=>{
        createModule(row.id).then(()=>{
          ElMessage.success("操作成功");
        }).catch(()=>{})
      }).catch(()=>{})
    }

    const handleFieldEdit = (row:ModulesInfoTableColumns) => {
      // todo 编辑字段
      router.push("/system/modulesField/list?id="+row.id,)
    }
    // 获取列表数据
    const modulesInfoList = () => {
      loading.value = true;
      listModulesInfo(state.tableData.param).then((res: any) => {
        let list = res.data.list ?? [];
        state.tableData.data = list;
        state.tableData.total = res.data.total;
        loading.value = false;
      });
    };
    const toggleSearch = () => {
      showAll.value = !showAll.value;
    };
    // 多选框选中数据
    const handleSelectionChange = (selection: Array<ModulesInfoInfoData>) => {
      state.ids = selection.map(item => item.id);
      single.value = selection.length != 1;
      multiple.value = !selection.length;
    };
    const handleAdd = () => {
      editRef.value.openDialog();
    };
    const handleUpdate = (row: ModulesInfoTableColumns) => {
      if (!row) {
        row = state.tableData.data.find((item: ModulesInfoTableColumns) => {
          return item.id === state.ids[0];
        }) as ModulesInfoTableColumns;
      }
      editRef.value.openDialog(toRaw(row));
    };
    const handleDelete = (row: ModulesInfoTableColumns) => {
      let msg = "你确定要删除所选数据？";
      let id: number[] = [];
      if (row) {
        msg = `此操作将永久删除数据，是否继续?`;
        id = [row.id];
      } else {
        id = state.ids;
      }
      if (id.length === 0) {
        ElMessage.error("请选择要删除的数据。");
        return;
      }
      ElMessageBox.confirm(msg, "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      })
          .then(() => {
            delModulesInfo(id).then(() => {
              ElMessage.success("删除成功");
              modulesInfoList();
            });
          })
          .catch(() => {
          });
    };

    return {
      proxy,
      editRef,
      showAll,
      loading,
      single,
      multiple,
      word,
      queryRef,
      resetQuery,
      modulesInfoList,
      toggleSearch,
      handleSelectionChange,
      handleAdd,
      handleUpdate,
      handleDelete,
      handleFieldEdit,
      handleCreateModule,
      ...toRefs(state)
    };
  }
});
</script>
<style lang="scss" scoped>
.colBlock {
  display: block;
}

.colNone {
  display: none;
}
</style>
