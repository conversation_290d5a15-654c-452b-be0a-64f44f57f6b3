import{a as F}from"./cmsCategory-MP-V5q2B.js";import{p as x}from"./cmsArticle-DSySRWLJ.js";import{a as y}from"./element-plus-CUmVNDWO.js";import{d as A,f as B,r as h,X as N,ad as O,ag as u,c as v,o as b,P as n,H as l,a as m,D as P,L as k,J as V,M as T,V as M}from"./@vue-C21YZbHS.js";import{a as q}from"./index-BGmsw1a8.js";import"./lodash-es-S0Y0Up6J.js";import"./@vueuse-D_5IXmcI.js";import"./@element-plus-_Cc-TEQX.js";import"./@popperjs-D_chPuIy.js";import"./@ctrl-r5W6hzzQ.js";import"./dayjs-CepoAVPu.js";import"./@intlify-D8kAWvSi.js";import"./async-validator-9PlIezaS.js";import"./memoize-one-BdPwpGay.js";import"./normalize-wheel-es-BQoi3Ox2.js";import"./@floating-ui-DwceP2Gb.js";import"./pinia-DwJf82dV.js";import"./vue-router-BDT9dCQ7.js";import"./js-cookie-Cz0CWeBA.js";import"./nprogress-Scw6VIZr.js";import"./axios-B62VicFi.js";import"./qs-D41HS0uK.js";import"./side-channel-DytTmCZg.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-DbsE5JW_.js";import"./crypto-js-oM26lpET.js";import"./side-channel-list-C_7Uk4RP.js";import"./side-channel-map-BUokNT8A.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-bSkmVQJL.js";import"./vue-i18n-BkLk9Ti1.js";import"./vue-iNFchrNl.js";import"./vue-clipboard3-Dr8V5xR7.js";import"./clipboard-DzJOpNE6.js";import"./mitt-DJ65BbbF.js";import"./vue-grid-layout-CIPjP0XN.js";import"./vue-simple-uploader-j6sX1TKo.js";import"./vue-ueditor-wrap-CJ5gjsd3.js";const z=A({name:"PushDialog",setup(){const{proxy:s}=B(),a=h(),p=h([]),_=h(null),r=h([]),t=N({loading:!1,isShowDialog:!1,formData:{ids:[],categoryIds:[],sourceCategoryId:void 0},rules:{categoryIds:[{required:!0,message:"请选择目标栏目",trigger:"change"}]}}),g=()=>{if(!t.formData.sourceCategoryId){p.value=[];return}F({categoryId:t.formData.sourceCategoryId,permission:"push"}).then(e=>{const o=e.data.list||[];o.forEach(d=>{d.isSelected=!1,d.id===t.formData.sourceCategoryId&&(d.disabled=!0)}),p.value=s.handleTree(o,"id","parentId")})},D=(e,o)=>{I(),t.formData.ids=e,o?(t.formData.sourceCategoryId=o,g()):y.warning("缺少源栏目 ID，无法获取目标栏目"),t.isShowDialog=!0},c=()=>{t.isShowDialog=!1},f=()=>{c()},I=()=>{t.formData={ids:[],categoryIds:[],sourceCategoryId:void 0},r.value=[]},i=e=>e&&e.type==="list"&&r.value.includes(e.id),C=(e,o)=>{var d,S,E,$;if(!(!o||o.type!=="list")&&o.id!==t.formData.sourceCategoryId){if(e)r.value.includes(o.id)||(r.value.push(o.id),(d=a.value)==null||d.setCurrentRow(o));else{const R=r.value.indexOf(o.id);R!==-1&&(r.value.splice(R,1),((E=(S=a.value)==null?void 0:S.getCurrentRow())==null?void 0:E.id)===o.id&&(($=a.value)==null||$.setCurrentRow(null)))}t.formData.categoryIds=[...r.value]}};return{treeTableRef:a,cmsCategoryOptions:p,selectedCategory:_,selectedCategoryIds:r,isRowSelected:i,toggleRowSelection:C,handleCategorySelect:e=>{if(e&&e.type==="list"){const o=i(e);C(!o,e),console.log(`点击行: ${e.name}, ID: ${e.id}, 当前状态: ${o}, 切换为: ${!o}`),console.log("选中的 ID 列表:",r.value)}},handleCheckboxClick:e=>{if(e&&e.type==="list"){const o=i(e);C(!o,e),console.log(`点击复选框: ${e.name}, ID: ${e.id}, 当前状态: ${o}, 切换为: ${!o}`),console.log("选中的 ID 列表:",r.value)}},openDialog:D,closeDialog:c,onCancel:f,onSubmit:()=>{if(t.loading=!0,!t.formData.categoryIds||t.formData.categoryIds.length===0){y.error("请选择目标栏目"),t.loading=!1;return}const e={ids:t.formData.ids,categoryIds:t.formData.categoryIds,sourceCategoryId:t.formData.sourceCategoryId};x(e).then(()=>{y.success("推送成功"),c()}).catch(()=>{y.error("推送失败")}).finally(()=>{t.loading=!1})},...O(t)}}}),H={class:"cms-article-push"},J={class:"push-category-container"},L={key:0,class:"disabled-text"},U=["onClick"],X={class:"dialog-footer"};function j(s,a,p,_,r,t){const g=u("el-table-column"),D=u("el-checkbox"),c=u("el-table"),f=u("el-button"),I=u("el-dialog");return b(),v("div",H,[n(I,{modelValue:s.isShowDialog,"onUpdate:modelValue":a[0]||(a[0]=i=>s.isShowDialog=i),"close-on-click-modal":!1,"destroy-on-close":!0,width:"600px"},{header:l(()=>a[1]||(a[1]=[m("div",null,"文章推送",-1)])),footer:l(()=>[m("div",X,[n(f,{type:"primary",loading:s.loading,onClick:s.onSubmit},{default:l(()=>a[3]||(a[3]=[k("确 定")])),_:1},8,["loading","onClick"]),n(f,{onClick:s.onCancel},{default:l(()=>a[4]||(a[4]=[k("取 消")])),_:1},8,["onClick"])])]),default:l(()=>[m("div",J,[a[2]||(a[2]=m("div",{class:"push-title"},"选择目标栏目",-1)),n(c,{ref:"treeTableRef",data:s.cmsCategoryOptions,"row-key":"id",border:"","default-expand-all":"","tree-props":{children:"children",hasChildren:"hasChildren"},"highlight-current-row":"",onRowClick:s.handleCategorySelect},{default:l(()=>[n(g,{prop:"name",label:"栏目名称"},{default:l(({row:i})=>[m("span",{class:P({"disabled-row":i.id===s.formData.sourceCategoryId})},[k(T(i.name)+" ",1),i.id===s.formData.sourceCategoryId?(b(),v("span",L,"(当前栏目)")):V("",!0)],2)]),_:1}),n(g,{label:"选择",width:"100",align:"center"},{default:l(({row:i})=>[i.type==="list"?(b(),v("div",{key:0,onClick:M(C=>s.handleCheckboxClick(i),["stop"])},[n(D,{"model-value":s.isRowSelected(i),disabled:i.id===s.formData.sourceCategoryId},null,8,["model-value","disabled"])],8,U)):V("",!0)]),_:1})]),_:1},8,["data","onRowClick"])])]),_:1},8,["modelValue"])])}const Xe=q(z,[["render",j],["__scopeId","data-v-5a56aab8"]]);export{Xe as default};
