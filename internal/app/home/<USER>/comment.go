// ==========================================================================
// desc: Home Article
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/encoding/ghtml"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/text/gregex"
	"github.com/tiger1103/gfast/v3/api/v1/home"
	"github.com/tiger1103/gfast/v3/internal/app/cms/dao"
	cmsModel "github.com/tiger1103/gfast/v3/internal/app/cms/model"
	"github.com/tiger1103/gfast/v3/internal/app/home/<USER>"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterComment(NewComment())
}

func NewComment() *sComment {
	return &sComment{}
}

type sComment struct{}

func (s *sComment) List(ctx context.Context, req *home.CommentSearchListReq) (res *home.CommentSearchListRes, err error) {
	res = new(home.CommentSearchListRes)
	g.Try(ctx, func(ctx context.Context) {
		m := dao.CmsComment.Ctx(ctx).Where("status=1")
		if req.ArticleId != "" {
			m = m.Where(dao.CmsComment.Columns().ArticleId+" = ?", req.ArticleId)
		} else {
			m = m.Where(dao.CmsComment.Columns().ArticleId + " > 0")
		}
		if req.MemberId != "" {
			m = m.Where("member_id=?", req.MemberId)
		} else {
			m = m.Where("pid", 0)
		}
		res.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取评论总数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		res.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id desc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		err = m.WithAll().Page(req.PageNum, req.PageSize).Order(order).Scan(&res.List)
		liberr.ErrIsNil(ctx, err, "获取评论列表失败")
	})
	g.View()
	return
}

func (s *sComment) Add(ctx context.Context, req *home.CommentAddReq) (err error) {
	// 提取表情
	matchFace, _ := gregex.MatchAllString(`face\/(\w+).gif`, req.Content)
	for _, faceStr := range matchFace {
		req.Content, _ = gregex.ReplaceString(`<img.*?src="\/static\/images\/`+faceStr[0]+`".*?>`, "###"+faceStr[1]+"###", req.Content)
	}
	req.Content = ghtml.StripTags(req.Content)
	_, err = dao.CmsComment.Ctx(ctx).Insert(req)
	if err != nil {
		return
	}
	err = dao.CmsComment.UpCommentCount(ctx, req.ArticleId)
	return
}

func (s *sComment) Delete(ctx context.Context, req *home.CommentDeleteReq) (err error) {
	var commentInfo *cmsModel.CmsCommentListRes
	dao.CmsComment.Ctx(ctx).Where("id", req.Id).WithAll().Scan(&commentInfo)
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		_, err2 := dao.CmsComment.Ctx(ctx).Where("id=? and member_id=?", req.Id, req.MemberId).Delete()
		_, err2 = dao.CmsComment.Ctx(ctx).Where("pid=?", req.Id).Delete()
		if commentInfo != nil {
			dao.CmsComment.UpCommentCount(ctx, commentInfo.ArticleId)
		}
		return err2
	})

	return
}
