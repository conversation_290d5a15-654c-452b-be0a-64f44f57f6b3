@-webkit-keyframes rotating {
    0% {-webkit-transform: rotate(0deg);transform: rotate(0deg)}
    to {-webkit-transform: rotate(1turn);transform: rotate(1turn)}
}
@keyframes rotating {
    0% {-webkit-transform: rotate(0deg);transform: rotate(0deg)}
    to {-webkit-transform: rotate(1turn);transform: rotate(1turn)}
}
html {-webkit-text-size-adjust: 100%;font-size: 10px;-webkit-tap-highlight-color: transparent}
body {font-family: var(--theme-font-family);font-size: 14px;line-height: 1.42857;color: #262626;-webkit-font-smoothing: antialiased;-moz-osx-font-smoothing: grayscale}
details, main {display: block}
h1 {margin: .67em 0}
hr {-webkit-box-sizing: content-box;box-sizing: content-box;height: 0;overflow: visible;margin-top: 20px;margin-bottom: 20px;border: 0;border-top: 1px solid #eee}
code, kbd, pre, samp {font-family: SFMono-Regular, Liberation Mono, Consolas, Menlo, monospace, Microsoft Yahei;font-size: 1em}
a {background-color: transparent;color: var(--theme-color);text-decoration: none}
abbr[title] {-webkit-text-decoration: underline dotted;text-decoration: underline dotted}
b, strong {font-weight: bolder}
sub, sup {font-size: 75%;line-height: 0;position: relative;vertical-align: baseline}
sub {bottom: -.25em}
sup {top: -.5em}
img {border-style: none;vertical-align: middle}
body, button, input, select, textarea {margin: 0}
optgroup {font-family: inherit;font-size: 100%;line-height: 1.15;margin: 0}
button, input {overflow: visible}
button, select {text-transform: none}
[type=button], [type=reset], [type=submit], button {-webkit-appearance: button}
[type=button]::-moz-focus-inner, [type=reset]::-moz-focus-inner, [type=submit]::-moz-focus-inner, button::-moz-focus-inner {border-style: none;padding: 0}
[type=button]:-moz-focusring, [type=reset]:-moz-focusring, [type=submit]:-moz-focusring, button:-moz-focusring {outline: 1px dotted ButtonText}
fieldset {margin: 0;min-width: 0}
[type=checkbox], [type=radio], legend {-webkit-box-sizing: border-box;box-sizing: border-box}
legend {display: table;max-width: 100%;white-space: normal}
progress {vertical-align: baseline}
textarea {overflow: auto}
[type=checkbox], [type=radio] {padding: 0}
[type=number]::-webkit-inner-spin-button, [type=number]::-webkit-outer-spin-button {height: auto}
[type=search] {-webkit-appearance: textfield;outline-offset: -2px}
[type=search]::-webkit-search-decoration {-webkit-appearance: none}
::-webkit-file-upload-button {-webkit-appearance: button;font: inherit}
summary {display: list-item}
[hidden], template {display: none}
*, :after, :before, input[type=search] {-webkit-box-sizing: border-box;box-sizing: border-box}
button, input, select, textarea {font-family: inherit;font-size: inherit;line-height: inherit}
a:hover {color: var(--theme-hover);text-decoration: none}
a:focus, input[type=checkbox]:focus, input[type=file]:focus, input[type=radio]:focus {outline: 5px auto -webkit-focus-ring-color;outline-offset: -1px}
figure {margin: 0}
[role=button] {cursor: pointer}
.h1, .h2, .h3, .h4, .h5, .h6, h1, h2, h3, h4, h5, h6 {font-family: inherit;font-weight: 500;line-height: 1.1;color: inherit}
.h1 .small, .h1 small, .h2 .small, .h2 small, .h3 .small, .h3 small, .h4 .small, .h4 small, .h5 .small, .h5 small, .h6 .small, .h6 small, h1 .small, h1 small, h2 .small, h2 small, h3 .small, h3 small, h4 .small, h4 small, h5 .small, h5 small, h6 .small, h6 small {font-weight: 400;line-height: 1;color: #777}
.h1, .h2, .h3, h1, h2, h3 {margin-top: 20px;margin-bottom: 10px}
.h1 .small, .h1 small, .h2 .small, .h2 small, .h3 .small, .h3 small, h1 .small, h1 small, h2 .small, h2 small, h3 .small, h3 small {font-size: 65%}
.h4, .h5, .h6, h4, h5, h6 {margin-top: 10px;margin-bottom: 10px}
.h4 .small, .h4 small, .h5 .small, .h5 small, .h6 .small, .h6 small, h4 .small, h4 small, h5 .small, h5 small, h6 .small, h6 small {font-size: 75%}
.h1, h1 {font-size: 36px}
.h2, h2 {font-size: 30px}
.h3, h3 {font-size: 24px}
.h4, h4 {font-size: 18px}
.h5, h5 {font-size: 14px}
.h6, h6 {font-size: 12px}
p {margin: 0 0 10px}
.lead {margin-bottom: 20px;font-size: 16px;font-weight: 300;line-height: 1.4}
@media (min-width: 768px) {
    .lead {font-size: 21px}
}
.small, small {font-size: 85%}
.mark, mark {background-color: #fff6e6;padding: .2em}
.text-left {text-align: left}
.text-right {text-align: right}
.text-center {text-align: center}
.text-justify {text-align: justify}
.text-nowrap {white-space: nowrap}
.text-lowercase {text-transform: lowercase}
.initialism, .text-uppercase {text-transform: uppercase}
.text-capitalize {text-transform: capitalize}
.text-muted {color: #777}
.text-primary {color: var(--theme-color)}
a.text-primary:focus, a.text-primary:hover {color: var(--theme-color)}
.text-success {color: #29ad00}
a.text-success:focus, a.text-success:hover {color: #29ad00}
.text-info {color: #00aae7}
a.text-info:focus, a.text-info:hover {color: #00aae7}
.text-warning {color: #ffa400}
a.text-warning:focus, a.text-warning:hover {color: #ffa400}
.text-danger {color: #fa5555}
a.text-danger:focus, a.text-danger:hover {color: #fa5555}
.bg-primary {color: #fff;background-color: var(--theme-color)}
a.bg-primary:focus, a.bg-primary:hover {background-color: var(--theme-color)}
.bg-success {background-color: #eaf7e6}
a.bg-success:focus, a.bg-success:hover {background-color: #eaf7e6}
.bg-info {background-color: #e6f7fd}
a.bg-info:focus, a.bg-info:hover {background-color: #e6f7fd}
.bg-warning {background-color: #fff6e6}
a.bg-warning:focus, a.bg-warning:hover {background-color: #fff6e6}
.bg-danger {background-color: #fee}
a.bg-danger:focus, a.bg-danger:hover {background-color: #fee}
.page-header {padding-bottom: 9px;margin: 40px 0 20px;border-bottom: 1px solid #eee}
dl, ol, ul {margin-top: 0;margin-bottom: 10px}
ol ol, ol ul, ul ol, ul ul {margin-bottom: 0}
.list-inline, .list-unstyled {padding-left: 0;list-style: none}
.list-inline {margin-left: -5px}
.list-inline > li {display: inline-block;padding-left: 5px;padding-right: 5px}
dl {margin-bottom: 20px}
dd, dt {line-height: 1.42857}
dt {font-weight: 700}
dd {margin-left: 0}
.dl-horizontal dd:after, .dl-horizontal dd:before {content: " ";display: table}
.dl-horizontal dd:after {clear: both}
@media (min-width: 768px) {
    .dl-horizontal dt {float: left;width: 160px;clear: left;text-align: right;overflow: hidden;-o-text-overflow: ellipsis;text-overflow: ellipsis;white-space: nowrap}
    .dl-horizontal dd {margin-left: 180px}
}
abbr[data-original-title], abbr[title] {cursor: help;border-bottom: 1px dotted #777}
.initialism {font-size: 90%}
blockquote {padding: 10px 20px;margin: 0 0 20px;font-size: 17.5px;border-left: 5px solid #eee}
.modal-body p:last-child, blockquote ol:last-child, blockquote p:last-child, blockquote ul:last-child {margin-bottom: 0}
blockquote .small, blockquote footer, blockquote small {display: block;font-size: 80%;line-height: 1.42857;color: #777}
blockquote .small:before, blockquote footer:before, blockquote small:before {content: "\2014 \00A0"}
.blockquote-reverse, blockquote.pull-right {padding-right: 15px;padding-left: 0;border-right: 5px solid #eee;border-left: 0;text-align: right}
.blockquote-reverse .small:before, .blockquote-reverse footer:before, .blockquote-reverse small:before, blockquote.pull-right .small:before, blockquote.pull-right footer:before, blockquote.pull-right small:before {content: ""}
.blockquote-reverse .small:after, .blockquote-reverse footer:after, .blockquote-reverse small:after, blockquote.pull-right .small:after, blockquote.pull-right footer:after, blockquote.pull-right small:after {content: "\00A0 \2014"}
address {margin-bottom: 20px;font-style: normal;line-height: 1.42857}
.container, .page-no-sidebar.member-lostpassword .member-lp-process {margin-right: auto;margin-left: auto;padding-left: 10px;padding-right: 10px}
.container:after, .container:before, .page-no-sidebar.member-lostpassword .member-lp-process:after, .page-no-sidebar.member-lostpassword .member-lp-process:before {content: " ";display: table}
.container:after, .page-no-sidebar.member-lostpassword .member-lp-process:after {clear: both}
@media (min-width: 768px) {
    .container, .page-no-sidebar.member-lostpassword .member-lp-process {width: 750px}
}
@media (min-width: 992px) {
    .container, .page-no-sidebar.member-lostpassword .member-lp-process {width: 970px}
}
@media (min-width: 1240px) {
    .container, .page-no-sidebar.member-lostpassword .member-lp-process {width: 1200px}
}
.container-fluid {margin-right: auto;margin-left: auto;padding-left: 10px;padding-right: 10px}
.container-fluid:after, .container-fluid:before {content: " ";display: table}
.container-fluid:after {clear: both}
.row {display: -webkit-box;display: -webkit-flex;display: -ms-flexbox;display: flex;-webkit-flex-wrap: wrap;-ms-flex-wrap: wrap;flex-wrap: wrap;margin-left: -10px;margin-right: -10px}
[class*=col-lg-], [class*=col-md-], [class*=col-sm-], [class*=col-xs-] {position: relative;width: 100%;min-height: 1px;padding-left: 10px;padding-right: 10px}
.col-xs-1, .col-xs-10, .col-xs-11, .col-xs-12, .col-xs-13, .col-xs-14, .col-xs-15, .col-xs-16, .col-xs-17, .col-xs-18, .col-xs-19, .col-xs-2, .col-xs-20, .col-xs-21, .col-xs-22, .col-xs-23, .col-xs-24, .col-xs-3, .col-xs-4, .col-xs-5, .col-xs-6, .col-xs-7, .col-xs-8, .col-xs-9 {-webkit-flex-shrink: 0;-ms-flex-negative: 0;flex-shrink: 0}
.col-xs-1 {width: 4.16667%}
.col-xs-2 {width: 8.33333%}
.col-xs-3 {width: 12.5%}
.col-xs-4 {width: 16.66667%}
.col-xs-5 {width: 20.83333%}
.col-xs-6 {width: 25%}
.col-xs-7 {width: 29.16667%}
.col-xs-8 {width: 33.33333%}
.col-xs-9 {width: 37.5%}
.col-xs-10 {width: 41.66667%}
.col-xs-11 {width: 45.83333%}
.col-xs-12 {width: 50%}
.col-xs-13 {width: 54.16667%}
.col-xs-14 {width: 58.33333%}
.col-xs-15 {width: 62.5%}
.col-xs-16 {width: 66.66667%}
.col-xs-17 {width: 70.83333%}
.col-xs-18 {width: 75%}
.col-xs-19 {width: 79.16667%}
.col-xs-20 {width: 83.33333%}
.col-xs-21 {width: 87.5%}
.col-xs-22 {width: 91.66667%}
.col-xs-23 {width: 95.83333%}
.col-xs-24 {width: 100%}
.col-xs-offset-0 {margin-left: 0}
.col-xs-offset-1 {margin-left: 4.16667%}
.col-xs-offset-2 {margin-left: 8.33333%}
.col-xs-offset-3 {margin-left: 12.5%}
.col-xs-offset-4 {margin-left: 16.66667%}
.col-xs-offset-5 {margin-left: 20.83333%}
.col-xs-offset-6 {margin-left: 25%}
.col-xs-offset-7 {margin-left: 29.16667%}
.col-xs-offset-8 {margin-left: 33.33333%}
.col-xs-offset-9 {margin-left: 37.5%}
.col-xs-offset-10 {margin-left: 41.66667%}
.col-xs-offset-11 {margin-left: 45.83333%}
.col-xs-offset-12 {margin-left: 50%}
.col-xs-offset-13 {margin-left: 54.16667%}
.col-xs-offset-14 {margin-left: 58.33333%}
.col-xs-offset-15 {margin-left: 62.5%}
.col-xs-offset-16 {margin-left: 66.66667%}
.col-xs-offset-17 {margin-left: 70.83333%}
.col-xs-offset-18 {margin-left: 75%}
.col-xs-offset-19 {margin-left: 79.16667%}
.col-xs-offset-20 {margin-left: 83.33333%}
.col-xs-offset-21 {margin-left: 87.5%}
.col-xs-offset-22 {margin-left: 91.66667%}
.col-xs-offset-23 {margin-left: 95.83333%}
.col-xs-offset-24 {margin-left: 100%}
@media (min-width: 768px) {
    .col-sm-1, .col-sm-10, .col-sm-11, .col-sm-12, .col-sm-13, .col-sm-14, .col-sm-15, .col-sm-16, .col-sm-17, .col-sm-18, .col-sm-19, .col-sm-2, .col-sm-20, .col-sm-21, .col-sm-22, .col-sm-23, .col-sm-24, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9 {-webkit-flex-shrink: 0;-ms-flex-negative: 0;flex-shrink: 0}
    .col-sm-1 {width: 4.16667%}
    .col-sm-2 {width: 8.33333%}
    .col-sm-3 {width: 12.5%}
    .col-sm-4 {width: 16.66667%}
    .col-sm-5 {width: 20.83333%}
    .col-sm-6 {width: 25%}
    .col-sm-7 {width: 29.16667%}
    .col-sm-8 {width: 33.33333%}
    .col-sm-9 {width: 37.5%}
    .col-sm-10 {width: 41.66667%}
    .col-sm-11 {width: 45.83333%}
    .col-sm-12 {width: 50%}
    .col-sm-13 {width: 54.16667%}
    .col-sm-14 {width: 58.33333%}
    .col-sm-15 {width: 62.5%}
    .col-sm-16 {width: 66.66667%}
    .col-sm-17 {width: 70.83333%}
    .col-sm-18 {width: 75%}
    .col-sm-19 {width: 79.16667%}
    .col-sm-20 {width: 83.33333%}
    .col-sm-21 {width: 87.5%}
    .col-sm-22 {width: 91.66667%}
    .col-sm-23 {width: 95.83333%}
    .col-sm-24 {width: 100%}
    .col-sm-offset-0 {margin-left: 0}
    .col-sm-offset-1 {margin-left: 4.16667%}
    .col-sm-offset-2 {margin-left: 8.33333%}
    .col-sm-offset-3 {margin-left: 12.5%}
    .col-sm-offset-4 {margin-left: 16.66667%}
    .col-sm-offset-5 {margin-left: 20.83333%}
    .col-sm-offset-6 {margin-left: 25%}
    .col-sm-offset-7 {margin-left: 29.16667%}
    .col-sm-offset-8 {margin-left: 33.33333%}
    .col-sm-offset-9 {margin-left: 37.5%}
    .col-sm-offset-10 {margin-left: 41.66667%}
    .col-sm-offset-11 {margin-left: 45.83333%}
    .col-sm-offset-12 {margin-left: 50%}
    .col-sm-offset-13 {margin-left: 54.16667%}
    .col-sm-offset-14 {margin-left: 58.33333%}
    .col-sm-offset-15 {margin-left: 62.5%}
    .col-sm-offset-16 {margin-left: 66.66667%}
    .col-sm-offset-17 {margin-left: 70.83333%}
    .col-sm-offset-18 {margin-left: 75%}
    .col-sm-offset-19 {margin-left: 79.16667%}
    .col-sm-offset-20 {margin-left: 83.33333%}
    .col-sm-offset-21 {margin-left: 87.5%}
    .col-sm-offset-22 {margin-left: 91.66667%}
    .col-sm-offset-23 {margin-left: 95.83333%}
    .col-sm-offset-24 {margin-left: 100%}
}
@media (min-width: 992px) {
    .col-md-1, .col-md-10, .col-md-11, .col-md-12, .col-md-13, .col-md-14, .col-md-15, .col-md-16, .col-md-17, .col-md-18, .col-md-19, .col-md-2, .col-md-20, .col-md-21, .col-md-22, .col-md-23, .col-md-24, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9 {-webkit-flex-shrink: 0;-ms-flex-negative: 0;flex-shrink: 0}
    .col-md-1 {width: 4.16667%}
    .col-md-2 {width: 8.33333%}
    .col-md-3 {width: 12.5%}
    .col-md-4 {width: 16.66667%}
    .col-md-5 {width: 20.83333%}
    .col-md-6 {width: 25%}
    .col-md-7 {width: 29.16667%}
    .col-md-8 {width: 33.33333%}
    .col-md-9 {width: 37.5%}
    .col-md-10 {width: 41.66667%}
    .col-md-11 {width: 45.83333%}
    .col-md-12 {width: 50%}
    .col-md-13 {width: 54.16667%}
    .col-md-14 {width: 58.33333%}
    .col-md-15 {width: 62.5%}
    .col-md-16 {width: 66.66667%}
    .col-md-17 {width: 70.83333%}
    .col-md-18 {width: 75%}
    .col-md-19 {width: 79.16667%}
    .col-md-20 {width: 83.33333%}
    .col-md-21 {width: 87.5%}
    .col-md-22 {width: 91.66667%}
    .col-md-23 {width: 95.83333%}
    .col-md-24 {width: 100%}
    .col-md-offset-0 {margin-left: 0}
    .col-md-offset-1 {margin-left: 4.16667%}
    .col-md-offset-2 {margin-left: 8.33333%}
    .col-md-offset-3 {margin-left: 12.5%}
    .col-md-offset-4 {margin-left: 16.66667%}
    .col-md-offset-5 {margin-left: 20.83333%}
    .col-md-offset-6 {margin-left: 25%}
    .col-md-offset-7 {margin-left: 29.16667%}
    .col-md-offset-8 {margin-left: 33.33333%}
    .col-md-offset-9 {margin-left: 37.5%}
    .col-md-offset-10 {margin-left: 41.66667%}
    .col-md-offset-11 {margin-left: 45.83333%}
    .col-md-offset-12 {margin-left: 50%}
    .col-md-offset-13 {margin-left: 54.16667%}
    .col-md-offset-14 {margin-left: 58.33333%}
    .col-md-offset-15 {margin-left: 62.5%}
    .col-md-offset-16 {margin-left: 66.66667%}
    .col-md-offset-17 {margin-left: 70.83333%}
    .col-md-offset-18 {margin-left: 75%}
    .col-md-offset-19 {margin-left: 79.16667%}
    .col-md-offset-20 {margin-left: 83.33333%}
    .col-md-offset-21 {margin-left: 87.5%}
    .col-md-offset-22 {margin-left: 91.66667%}
    .col-md-offset-23 {margin-left: 95.83333%}
    .col-md-offset-24 {margin-left: 100%}
}
@media (min-width: 1240px) {
    .col-lg-1, .col-lg-10, .col-lg-11, .col-lg-12, .col-lg-13, .col-lg-14, .col-lg-15, .col-lg-16, .col-lg-17, .col-lg-18, .col-lg-19, .col-lg-2, .col-lg-20, .col-lg-21, .col-lg-22, .col-lg-23, .col-lg-24, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9 {-webkit-flex-shrink: 0;-ms-flex-negative: 0;flex-shrink: 0}
    .col-lg-1 {width: 4.16667%}
    .col-lg-2 {width: 8.33333%}
    .col-lg-3 {width: 12.5%}
    .col-lg-4 {width: 16.66667%}
    .col-lg-5 {width: 20.83333%}
    .col-lg-6 {width: 25%}
    .col-lg-7 {width: 29.16667%}
    .col-lg-8 {width: 33.33333%}
    .col-lg-9 {width: 37.5%}
    .col-lg-10 {width: 41.66667%}
    .col-lg-11 {width: 45.83333%}
    .col-lg-12 {width: 50%}
    .col-lg-13 {width: 54.16667%}
    .col-lg-14 {width: 58.33333%}
    .col-lg-15 {width: 62.5%}
    .col-lg-16 {width: 66.66667%}
    .col-lg-17 {width: 70.83333%}
    .col-lg-18 {width: 75%}
    .col-lg-19 {width: 79.16667%}
    .col-lg-20 {width: 83.33333%}
    .col-lg-21 {width: 87.5%}
    .col-lg-22 {width: 91.66667%}
    .col-lg-23 {width: 95.83333%}
    .col-lg-24 {width: 100%}
    .col-lg-offset-0 {margin-left: 0}
    .col-lg-offset-1 {margin-left: 4.16667%}
    .col-lg-offset-2 {margin-left: 8.33333%}
    .col-lg-offset-3 {margin-left: 12.5%}
    .col-lg-offset-4 {margin-left: 16.66667%}
    .col-lg-offset-5 {margin-left: 20.83333%}
    .col-lg-offset-6 {margin-left: 25%}
    .col-lg-offset-7 {margin-left: 29.16667%}
    .col-lg-offset-8 {margin-left: 33.33333%}
    .col-lg-offset-9 {margin-left: 37.5%}
    .col-lg-offset-10 {margin-left: 41.66667%}
    .col-lg-offset-11 {margin-left: 45.83333%}
    .col-lg-offset-12 {margin-left: 50%}
    .col-lg-offset-13 {margin-left: 54.16667%}
    .col-lg-offset-14 {margin-left: 58.33333%}
    .col-lg-offset-15 {margin-left: 62.5%}
    .col-lg-offset-16 {margin-left: 66.66667%}
    .col-lg-offset-17 {margin-left: 70.83333%}
    .col-lg-offset-18 {margin-left: 75%}
    .col-lg-offset-19 {margin-left: 79.16667%}
    .col-lg-offset-20 {margin-left: 83.33333%}
    .col-lg-offset-21 {margin-left: 87.5%}
    .col-lg-offset-22 {margin-left: 91.66667%}
    .col-lg-offset-23 {margin-left: 95.83333%}
    .col-lg-offset-24 {margin-left: 100%}
}
fieldset, legend {padding: 0;border: 0}
legend {margin-bottom: 20px;font-size: 21px;line-height: inherit;color: #262626;border-bottom: 1px solid #e5e5e5}
input[type=file] {display: block}
input[type=range], legend {display: block;width: 100%}
select[multiple], select[size], textarea.form-control {height: auto}
.form-control, output {display: block;font-size: 14px;line-height: 1.42857;color: #555}
output {padding-top: 7px}
.form-control {width: 100%;height: 34px;padding: 6px 12px;background-color: #fff;background-image: none;border: 1px solid #dcdfe6;border-radius: 2px;-webkit-transition: border-color .15s ease-in-out;-o-transition: border-color ease-in-out .15s;transition: border-color .15s ease-in-out}
.form-control:focus {border-color: var(--theme-color);outline: 0;-webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px var(--theme-color);box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px var(--theme-color)}
.form-control::-moz-placeholder {color: #999;opacity: 1}
.form-control:-ms-input-placeholder {color: #999}
.form-control::-webkit-input-placeholder {color: #999}
.form-control::-ms-expand {border: 0;background-color: transparent}
.form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {background-color: #eee;opacity: 1}
.form-control[disabled], fieldset[disabled] .form-control {cursor: not-allowed}
input[type=search] {-webkit-appearance: none}
@media screen and (-webkit-min-device-pixel-ratio: 0) {
    input[type=date].form-control, input[type=datetime-local].form-control, input[type=month].form-control, input[type=time].form-control {line-height: 34px}
    .input-group-sm input[type=date], .input-group-sm input[type=datetime-local], .input-group-sm input[type=month], .input-group-sm input[type=time], input[type=date].input-sm, input[type=datetime-local].input-sm, input[type=month].input-sm, input[type=time].input-sm {line-height: 30px}
    .input-group-lg input[type=date], .input-group-lg input[type=datetime-local], .input-group-lg input[type=month], .input-group-lg input[type=time], input[type=date].input-lg, input[type=datetime-local].input-lg, input[type=month].input-lg, input[type=time].input-lg {line-height: 46px}
}
.form-group {margin-bottom: 15px}
.checkbox, .radio {position: relative;display: block;margin-top: 10px;margin-bottom: 10px}
.checkbox label, .checkbox-inline, .radio label, .radio-inline {padding-left: 20px;margin-bottom: 0;font-weight: 400;cursor: pointer}
.checkbox label, .radio label {min-height: 20px}
.checkbox input[type=checkbox], .checkbox-inline input[type=checkbox], .radio input[type=radio], .radio-inline input[type=radio] {position: absolute;margin-left: -20px;margin-top: 4px \9}
.checkbox + .checkbox, .radio + .radio {margin-top: -5px}
.checkbox-inline, .radio-inline {position: relative;display: inline-block;vertical-align: middle}
.checkbox-inline + .checkbox-inline, .radio-inline + .radio-inline {margin-top: 0;margin-left: 10px}
.checkbox-inline.disabled, .checkbox.disabled label, .radio-inline.disabled, .radio.disabled label, fieldset[disabled] .checkbox label, fieldset[disabled] .checkbox-inline, fieldset[disabled] .radio label, fieldset[disabled] .radio-inline, fieldset[disabled] input[type=checkbox], fieldset[disabled] input[type=radio], input[type=checkbox].disabled, input[type=checkbox][disabled], input[type=radio].disabled, input[type=radio][disabled] {cursor: not-allowed}
.has-success .checkbox, .has-success .checkbox-inline, .has-success .control-label, .has-success .help-block, .has-success .radio, .has-success .radio-inline, .has-success.checkbox label, .has-success.checkbox-inline label, .has-success.radio label, .has-success.radio-inline label {color: #29ad00}
.has-success .form-control {border-color: #29ad00;-webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075)}
.has-success .form-control:focus {border-color: #1d7a00;-webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px #4cff14;box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px #4cff14}
.has-success .input-group-addon {color: #29ad00;border-color: #29ad00;background-color: #eaf7e6}
.has-success .form-control-feedback {color: #29ad00}
.has-warning .checkbox, .has-warning .checkbox-inline, .has-warning .control-label, .has-warning .help-block, .has-warning .radio, .has-warning .radio-inline, .has-warning.checkbox label, .has-warning.checkbox-inline label, .has-warning.radio label, .has-warning.radio-inline label {color: #ffa400}
.has-warning .form-control {border-color: #ffa400;-webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075)}
.has-warning .form-control:focus {border-color: #cc8300;-webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px #ffc866;box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px #ffc866}
.has-warning .input-group-addon {color: #ffa400;border-color: #ffa400;background-color: #fff6e6}
.has-warning .form-control-feedback {color: #ffa400}
.has-error .checkbox, .has-error .checkbox-inline, .has-error .control-label, .has-error .help-block, .has-error .radio, .has-error .radio-inline, .has-error.checkbox label, .has-error.checkbox-inline label, .has-error.radio label, .has-error.radio-inline label {color: #fa5555}
.has-error .form-control {border-color: #fa5555;-webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075)}
.has-error .form-control:focus {border-color: #f92323;-webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px #fdb8b8;box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px #fdb8b8}
.has-error .input-group-addon {color: #fa5555;border-color: #fa5555;background-color: #fee}
.has-error .form-control-feedback {color: #fa5555}
.has-feedback label ~ .form-control-feedback {top: 25px}
.has-feedback label.sr-only ~ .form-control-feedback {top: 0}
.help-block {display: block;margin-top: 5px;margin-bottom: 10px;color: #666}
@media (min-width: 768px) {
    .form-inline .form-group {display: inline-block;margin-bottom: 0;vertical-align: middle}
    .form-inline .form-control {display: inline-block;width: auto;vertical-align: middle}
    .form-inline .form-control-static {display: inline-block}
    .form-inline .input-group {display: inline-table;vertical-align: middle}
    .form-inline .input-group .form-control, .form-inline .input-group .input-group-addon, .form-inline .input-group .input-group-btn {width: auto}
    .form-inline .input-group > .form-control {width: 100%}
    .form-inline .control-label {margin-bottom: 0;vertical-align: middle}
    .form-inline .checkbox, .form-inline .radio {display: inline-block;margin-top: 0;margin-bottom: 0;vertical-align: middle}
    .form-inline .checkbox label, .form-inline .radio label {padding-left: 0}
    .form-inline .checkbox input[type=checkbox], .form-inline .radio input[type=radio] {position: relative;margin-left: 0}
    .form-inline .has-feedback .form-control-feedback {top: 0}
}
.form-horizontal .checkbox, .form-horizontal .checkbox-inline, .form-horizontal .radio, .form-horizontal .radio-inline {margin-top: 0;margin-bottom: 0;padding-top: 7px}
.form-horizontal .checkbox, .form-horizontal .radio {min-height: 27px}
.form-horizontal .form-group {display: -webkit-box;display: -webkit-flex;display: -ms-flexbox;display: flex;-webkit-flex-wrap: wrap;-ms-flex-wrap: wrap;flex-wrap: wrap;margin-left: -10px;margin-right: -10px}
@media (min-width: 768px) {
    .form-horizontal .control-label {text-align: right;margin-bottom: 0;padding-top: 7px}
}
.form-horizontal .has-feedback .form-control-feedback {right: 10px}
.btn {display: inline-block;margin-bottom: 0;font-weight: 500;text-align: center;vertical-align: middle;-ms-touch-action: manipulation;touch-action: manipulation;cursor: pointer;color: #5a5a5a;background: #fff;border: 1px solid #dcdfe6;white-space: nowrap;padding: 9px 20px;font-size: 14px;line-height: 16px;border-radius: 4px;-webkit-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none}
.btn.active, .btn:active, .btn:focus, .btn:hover {position: relative;z-index: 0;color: var(--theme-color);outline: 0;border-color: transparent}
.btn.active:before, .btn:active:before, .btn:focus:before, .btn:hover:before {pointer-events: none;position: absolute;z-index: 1;left: -1px;top: -1px;right: -1px;bottom: -1px;content: "";background: 0 0;border: 1px solid var(--theme-color);opacity: .3;border-radius: inherit}
.btn.active:after, .btn:active:after, .btn:focus:after, .btn:hover:after {pointer-events: none;position: absolute;z-index: -1;left: -1px;top: -1px;right: -1px;bottom: -1px;content: "";background: var(--theme-color);opacity: .1;border-radius: inherit}
.btn.active, .btn:active {outline: 0;border-color: var(--theme-color)}
.btn.disabled, .btn[disabled], fieldset[disabled] .btn {cursor: not-allowed;opacity: .65;filter: alpha(opacity=65);-webkit-box-shadow: none;box-shadow: none}
.btn.loading {position: relative}
.btn.loading > .wi-loader {position: relative;display: inline-block;margin-right: 3px;font-size: 16px;color: inherit;vertical-align: top;-webkit-animation: rotating 2s linear infinite;animation: rotating 2s linear infinite}
.btn.loading:after {position: absolute;z-index: 0;left: -1px;right: -1px;top: -1px;bottom: -1px;content: "";border-radius: inherit;background-color: rgba(255, 255, 255, .35)}
.btn.loading, .btn.loading:after, a.btn.disabled, fieldset[disabled] a.btn {pointer-events: none}
.btn.btn-default {color: #333;background-color: #fff;border-color: #ccc;outline: 0;-webkit-transition: all .1s ease-out 0s;-o-transition: all .1s ease-out 0s;transition: all .1s ease-out 0s}
.btn.btn-default.active, .btn.btn-default:active, .btn.btn-default:focus, .btn.btn-default:hover {color: #333}
.btn.btn-default.active:before, .btn.btn-default:active:before, .btn.btn-default:focus:before, .btn.btn-default:hover:before {border: 0}
.btn.btn-default.active:after, .btn.btn-default:active:after, .btn.btn-default:focus:after, .btn.btn-default:hover:after {background: rgba(255, 255, 255, .1);opacity: 1}
.btn.btn-default.active, .btn.btn-default:active {border-color: #fff}
.btn.btn-default.active:after, .btn.btn-default:active:after {background: rgba(255, 255, 255, .2)}
.btn.btn-default.disabled.focus, .btn.btn-default.disabled:focus, .btn.btn-default.disabled:hover, .btn.btn-default[disabled].focus, .btn.btn-default[disabled]:focus, .btn.btn-default[disabled]:hover, fieldset[disabled] .btn.btn-default.focus, fieldset[disabled] .btn.btn-default:focus, fieldset[disabled] .btn.btn-default:hover {background-color: #fff;border-color: #ccc}
.btn.btn-default .badge {color: #fff;background-color: #333}
.btn.btn-primary {color: #fff;background-color: var(--theme-color);border-color: var(--theme-color);outline: 0;-webkit-transition: all .1s ease-out 0s;-o-transition: all .1s ease-out 0s;transition: all .1s ease-out 0s}
.btn.btn-primary.active, .btn.btn-primary:active, .btn.btn-primary:focus, .btn.btn-primary:hover {color: #fff}
.btn.btn-primary.active:before, .btn.btn-primary:active:before, .btn.btn-primary:focus:before, .btn.btn-primary:hover:before {border: 0}
.btn.btn-primary.active:after, .btn.btn-primary:active:after, .btn.btn-primary:focus:after, .btn.btn-primary:hover:after {background: rgba(255, 255, 255, .1);opacity: 1}
.btn.btn-primary.active, .btn.btn-primary:active {border-color: var(--theme-color)}
.btn.btn-primary.active:after, .btn.btn-primary:active:after {background: rgba(255, 255, 255, .2)}
.btn.btn-primary.disabled.focus, .btn.btn-primary.disabled:focus, .btn.btn-primary.disabled:hover, .btn.btn-primary[disabled].focus, .btn.btn-primary[disabled]:focus, .btn.btn-primary[disabled]:hover, fieldset[disabled] .btn.btn-primary.focus, fieldset[disabled] .btn.btn-primary:focus, fieldset[disabled] .btn.btn-primary:hover {background-color: var(--theme-color);border-color: var(--theme-color)}
.btn.btn-primary .badge {color: var(--theme-color);background-color: #fff}
.btn.btn-success {color: #fff;background-color: #29ad00;border-color: #29ad00;outline: 0;-webkit-transition: all .1s ease-out 0s;-o-transition: all .1s ease-out 0s;transition: all .1s ease-out 0s}
.btn.btn-success.active, .btn.btn-success:active, .btn.btn-success:focus, .btn.btn-success:hover {color: #fff}
.btn.btn-success.active:before, .btn.btn-success:active:before, .btn.btn-success:focus:before, .btn.btn-success:hover:before {border: 0}
.btn.btn-success.active:after, .btn.btn-success:active:after, .btn.btn-success:focus:after, .btn.btn-success:hover:after {background: rgba(255, 255, 255, .1);opacity: 1}
.btn.btn-success.active, .btn.btn-success:active {border-color: #29ad00}
.btn.btn-success.active:after, .btn.btn-success:active:after {background: rgba(255, 255, 255, .2)}
.btn.btn-success.disabled.focus, .btn.btn-success.disabled:focus, .btn.btn-success.disabled:hover, .btn.btn-success[disabled].focus, .btn.btn-success[disabled]:focus, .btn.btn-success[disabled]:hover, fieldset[disabled] .btn.btn-success.focus, fieldset[disabled] .btn.btn-success:focus, fieldset[disabled] .btn.btn-success:hover {background-color: #29ad00;border-color: #29ad00}
.btn.btn-success .badge {color: #29ad00;background-color: #fff}
.btn.btn-info {color: #fff;background-color: #00aae7;border-color: #00aae7;outline: 0;-webkit-transition: all .1s ease-out 0s;-o-transition: all .1s ease-out 0s;transition: all .1s ease-out 0s}
.btn.btn-info.active, .btn.btn-info:active, .btn.btn-info:focus, .btn.btn-info:hover {color: #fff}
.btn.btn-info.active:before, .btn.btn-info:active:before, .btn.btn-info:focus:before, .btn.btn-info:hover:before {border: 0}
.btn.btn-info.active:after, .btn.btn-info:active:after, .btn.btn-info:focus:after, .btn.btn-info:hover:after {background: rgba(255, 255, 255, .1);opacity: 1}
.btn.btn-info.active, .btn.btn-info:active {border-color: #00aae7}
.btn.btn-info.active:after, .btn.btn-info:active:after {background: rgba(255, 255, 255, .2)}
.btn.btn-info.disabled.focus, .btn.btn-info.disabled:focus, .btn.btn-info.disabled:hover, .btn.btn-info[disabled].focus, .btn.btn-info[disabled]:focus, .btn.btn-info[disabled]:hover, fieldset[disabled] .btn.btn-info.focus, fieldset[disabled] .btn.btn-info:focus, fieldset[disabled] .btn.btn-info:hover {background-color: #00aae7;border-color: #00aae7}
.btn.btn-info .badge {color: #00aae7;background-color: #fff}
.btn.btn-warning {color: #fff;background-color: #ffa400;border-color: #ffa400;outline: 0;-webkit-transition: all .1s ease-out 0s;-o-transition: all .1s ease-out 0s;transition: all .1s ease-out 0s}
.btn.btn-warning.active, .btn.btn-warning:active, .btn.btn-warning:focus, .btn.btn-warning:hover {color: #fff}
.btn.btn-warning.active:before, .btn.btn-warning:active:before, .btn.btn-warning:focus:before, .btn.btn-warning:hover:before {border: 0}
.btn.btn-warning.active:after, .btn.btn-warning:active:after, .btn.btn-warning:focus:after, .btn.btn-warning:hover:after {background: rgba(255, 255, 255, .1);opacity: 1}
.btn.btn-warning.active, .btn.btn-warning:active {border-color: #ffa400}
.btn.btn-warning.active:after, .btn.btn-warning:active:after {background: rgba(255, 255, 255, .2)}
.btn.btn-warning.disabled.focus, .btn.btn-warning.disabled:focus, .btn.btn-warning.disabled:hover, .btn.btn-warning[disabled].focus, .btn.btn-warning[disabled]:focus, .btn.btn-warning[disabled]:hover, fieldset[disabled] .btn.btn-warning.focus, fieldset[disabled] .btn.btn-warning:focus, fieldset[disabled] .btn.btn-warning:hover {background-color: #ffa400;border-color: #ffa400}
.btn.btn-warning .badge {color: #ffa400;background-color: #fff}
.btn.btn-danger {color: #fff;background-color: #fa5555;border-color: #fa5555;outline: 0;-webkit-transition: all .1s ease-out 0s;-o-transition: all .1s ease-out 0s;transition: all .1s ease-out 0s}
.btn.btn-danger.active, .btn.btn-danger:active, .btn.btn-danger:focus, .btn.btn-danger:hover {color: #fff}
.btn.btn-danger.active:before, .btn.btn-danger:active:before, .btn.btn-danger:focus:before, .btn.btn-danger:hover:before {border: 0}
.btn.btn-danger.active:after, .btn.btn-danger:active:after, .btn.btn-danger:focus:after, .btn.btn-danger:hover:after {background: rgba(255, 255, 255, .1);opacity: 1}
.btn.btn-danger.active, .btn.btn-danger:active {border-color: #fa5555}
.btn.btn-danger.active:after, .btn.btn-danger:active:after {background: rgba(255, 255, 255, .2)}
.btn.btn-danger.disabled.focus, .btn.btn-danger.disabled:focus, .btn.btn-danger.disabled:hover, .btn.btn-danger[disabled].focus, .btn.btn-danger[disabled]:focus, .btn.btn-danger[disabled]:hover, fieldset[disabled] .btn.btn-danger.focus, fieldset[disabled] .btn.btn-danger:focus, fieldset[disabled] .btn.btn-danger:hover {background-color: #fa5555;border-color: #fa5555}
.btn.btn-danger .badge {color: #fa5555;background-color: #fff}
.btn-lg {padding: 11px 20px;font-size: 14px;line-height: 16px;border-radius: 4px}
.btn-sm, .btn-xs {padding: 8px 15px;font-size: 12px;line-height: 14px;border-radius: 3px}
.btn-sm.loading > .wi-loader, .btn-xs.loading > .wi-loader {font-size: 14px}
.btn-xs {padding: 6px 15px}
.btn-round {border-radius: 30px}
.btn-block {display: block;width: 100%}
.alert > p + p, .btn-block + .btn-block {margin-top: 5px}
input[type=button].btn-block, input[type=reset].btn-block, input[type=submit].btn-block {width: 100%}
.fade {opacity: 0;-webkit-transition: opacity .15s linear;-o-transition: opacity .15s linear;transition: opacity .15s linear}
.fade.in {opacity: 1}
.collapse {display: none}
.collapse.in {display: block}
tr.collapse.in {display: table-row}
tbody.collapse.in {display: table-row-group}
.collapsing {position: relative;height: 0;overflow: hidden;-webkit-transition-property: height, visibility;-o-transition-property: height, visibility;transition-property: height, visibility;-webkit-transition-duration: .35s;-o-transition-duration: .35s;transition-duration: .35s;-webkit-transition-timing-function: ease;-o-transition-timing-function: ease;transition-timing-function: ease}
.caret {display: inline-block;width: 0;height: 0;margin-left: 2px;vertical-align: middle;border-top: 4px dashed;border-top: 4px solid \9;border-right: 4px solid transparent;border-left: 4px solid transparent}
.dropdown, .dropup {position: relative}
.dropdown-toggle:focus {outline: 0}
.dropdown-menu {position: absolute;top: 100%;left: 0;z-index: 1000;display: none;float: left;min-width: 160px;padding: 5px 0;margin: 2px 0 0;list-style: none;font-size: 14px;text-align: left;background-color: #fff;border: 1px solid #ccc;border: 1px solid rgba(0, 0, 0, .15);border-radius: 2px;-webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, .175);box-shadow: 0 6px 12px rgba(0, 0, 0, .175);background-clip: padding-box}
.dropdown-menu.pull-right {right: 0;left: auto}
.dropdown-menu .divider {height: 1px;margin: 9px 0;overflow: hidden;background-color: #e5e5e5}
.dropdown-menu > li > a {display: block;padding: 3px 20px;clear: both;font-weight: 400;line-height: 1.42857;color: #262626;white-space: nowrap}
.dropdown-menu > li > a:focus, .dropdown-menu > li > a:hover {text-decoration: none;color: #191919;background-color: #f5f5f5}
.dropdown-menu > .active > a, .dropdown-menu > .active > a:focus, .dropdown-menu > .active > a:hover {text-decoration: none;outline: 0}
.dropdown-menu > .disabled > a, .dropdown-menu > .disabled > a:focus, .dropdown-menu > .disabled > a:hover {color: #777}
.dropdown-menu > .disabled > a:focus, .dropdown-menu > .disabled > a:hover {text-decoration: none;background-color: transparent;background-image: none;filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);cursor: not-allowed}
.open > .dropdown-menu {display: block}
.open > a {outline: 0}
.dropdown-menu-right {left: auto;right: 0}
.dropdown-menu-left {left: 0;right: auto}
.dropdown-header {display: block;padding: 3px 20px;font-size: 12px;line-height: 1.42857;color: #777;white-space: nowrap}
.dropdown-backdrop {position: fixed;left: 0;right: 0;bottom: 0;top: 0;z-index: 990}
.pull-right > .dropdown-menu {right: 0;left: auto}
.dropup .caret, .navbar-fixed-bottom .dropdown .caret {border-top: 0;border-bottom: 4px dashed;border-bottom: 4px solid \9;content: ""}
.dropup .dropdown-menu, .navbar-fixed-bottom .dropdown .dropdown-menu {top: auto;bottom: 100%;margin-bottom: 2px}
@media (min-width: 768px) {
    .navbar-right .dropdown-menu {right: 0;left: auto}
    .navbar-right .dropdown-menu-left {left: 0;right: auto}
}
.nav {margin-bottom: 0;padding-left: 0;list-style: none}
.nav:after, .nav:before {content: " ";display: table}
.nav:after {clear: both}
.nav > li, .nav > li > a {position: relative;display: block}
.nav > li > a {padding: 10px 15px}
.nav > li > a:focus, .nav > li > a:hover {text-decoration: none;background-color: #eee}
.nav > li.disabled > a {color: #777}
.nav > li.disabled > a:focus, .nav > li.disabled > a:hover {color: #777;text-decoration: none;background-color: transparent;cursor: not-allowed}
.nav .open > a, .nav .open > a:focus, .nav .open > a:hover {background-color: #eee;border-color: var(--theme-color)}
.nav .nav-divider {height: 1px;margin: 9px 0;overflow: hidden;background-color: #e5e5e5}
.nav > li > a > img {max-width: none}
.nav-tabs {border-bottom: 1px solid #ddd}
.nav-tabs > li {float: left;margin-bottom: -1px}
.nav-tabs > li > a {margin-right: 2px;line-height: 1.42857;border: 1px solid transparent;border-radius: 2px 2px 0 0}
.nav-tabs > li > a:hover {border-color: #eee #eee #ddd}
.nav-tabs > li.active > a, .nav-tabs > li.active > a:focus, .nav-tabs > li.active > a:hover {color: #555;background-color: #fff;border: 1px solid;border-color: #ddd #ddd transparent;cursor: default}
.nav-pills > li {float: left}
.nav-pills > li > a {border-radius: 2px}
.nav-pills > li + li {margin-left: 2px}
.nav-pills > li.active > a, .nav-pills > li.active > a:focus, .nav-pills > li.active > a:hover {color: #fff;background-color: var(--theme-color)}
.nav-justified > li, .nav-stacked > li, .nav-tabs.nav-justified > li {float: none}
.nav-stacked > li + li {margin-top: 2px;margin-left: 0}
.nav-justified, .nav-tabs.nav-justified {width: 100%}
.nav-justified > li > a, .nav-tabs.nav-justified > li > a {text-align: center;margin-bottom: 5px}
.nav-justified > .dropdown .dropdown-menu {top: auto;left: auto}
@media (min-width: 768px) {
    .nav-justified > li, .nav-tabs.nav-justified > li {display: table-cell;width: 1%}
    .nav-justified > li > a, .nav-tabs.nav-justified > li > a {margin-bottom: 0}
}
.nav-tabs-justified, .nav-tabs.nav-justified {border-bottom: 0}
.nav-tabs-justified > li > a, .nav-tabs.nav-justified > li > a {margin-right: 0;border-radius: 2px}
.nav-tabs-justified > .active > a, .nav-tabs-justified > .active > a:focus, .nav-tabs-justified > .active > a:hover, .nav-tabs.nav-justified > .active > a, .nav-tabs.nav-justified > .active > a:focus, .nav-tabs.nav-justified > .active > a:hover {border: 1px solid #ddd}
@media (min-width: 768px) {
    .nav-tabs-justified > li > a, .nav-tabs.nav-justified > li > a {border-bottom: 1px solid #ddd;border-radius: 2px 2px 0 0}
    .nav-tabs-justified > .active > a, .nav-tabs-justified > .active > a:focus, .nav-tabs-justified > .active > a:hover, .nav-tabs.nav-justified > .active > a, .nav-tabs.nav-justified > .active > a:focus, .nav-tabs.nav-justified > .active > a:hover {border-bottom-color: #fff}
}
.tab-content > .tab-pane, ul.page-numbers > li.next span, ul.page-numbers > li.prev span, ul.pagination > li.next span, ul.pagination > li.prev span {display: none}
.navbar-brand > img, .tab-content > .active {display: block}
.nav-tabs .dropdown-menu {margin-top: -1px;border-top-right-radius: 0;border-top-left-radius: 0}
.navbar {position: relative;min-height: 50px;margin-bottom: 20px;border: 1px solid transparent}
.navbar:after, .navbar:before {content: " ";display: table}
.navbar:after {clear: both}
@media (min-width: 992px) {
    .navbar {border-radius: 2px}
}
.navbar-header:after, .navbar-header:before {content: " ";display: table}
.navbar-header:after {clear: both}
@media (min-width: 992px) {
    .navbar-header {float: left}
}
.navbar-collapse {overflow-x: visible;padding-right: 10px;padding-left: 10px;border-top: 1px solid transparent;-webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, .1);box-shadow: inset 0 1px 0 rgba(255, 255, 255, .1);-webkit-overflow-scrolling: touch}
.navbar-collapse:after, .navbar-collapse:before {content: " ";display: table}
.navbar-collapse:after {clear: both}
.navbar-collapse.in {overflow-y: auto}
@media (min-width: 992px) {
    .navbar-collapse {width: auto;border-top: 0;-webkit-box-shadow: none;box-shadow: none}
    .navbar-collapse.collapse {display: block !important;height: auto !important;padding-bottom: 0;overflow: visible !important}
    .navbar-collapse.in {overflow-y: visible}
    .navbar-fixed-bottom .navbar-collapse, .navbar-fixed-top .navbar-collapse, .navbar-static-top .navbar-collapse {padding-left: 0;padding-right: 0}
}
.navbar-fixed-bottom .navbar-collapse, .navbar-fixed-top .navbar-collapse {max-height: 340px}
@media (max-device-width: 480px) and (orientation: landscape) {
    .navbar-fixed-bottom .navbar-collapse, .navbar-fixed-top .navbar-collapse {max-height: 200px}
}
.container-fluid > .navbar-collapse, .container-fluid > .navbar-header, .container > .navbar-collapse, .container > .navbar-header, .page-no-sidebar.member-lostpassword .member-lp-process > .navbar-collapse, .page-no-sidebar.member-lostpassword .member-lp-process > .navbar-header {margin-right: -10px;margin-left: -10px}
@media (min-width: 992px) {
    .container-fluid > .navbar-collapse, .container-fluid > .navbar-header, .container > .navbar-collapse, .container > .navbar-header, .page-no-sidebar.member-lostpassword .member-lp-process > .navbar-collapse, .page-no-sidebar.member-lostpassword .member-lp-process > .navbar-header {margin-right: 0;margin-left: 0}
}
.navbar-static-top {z-index: 1000;border-width: 0 0 1px}
@media (min-width: 992px) {
    .navbar-static-top {border-radius: 0}
}
.navbar-fixed-bottom, .navbar-fixed-top {position: fixed;right: 0;left: 0;z-index: 1030}
@media (min-width: 992px) {
    .navbar-fixed-bottom, .navbar-fixed-top {border-radius: 0}
}
.navbar-fixed-top {top: 0;border-width: 0 0 1px}
.navbar-fixed-bottom {bottom: 0;margin-bottom: 0;border-width: 1px 0 0}
.navbar-brand {float: left;padding: 15px 10px;font-size: 18px;line-height: 20px;height: 50px}
.navbar-brand:focus, .navbar-brand:hover {text-decoration: none}
@media (min-width: 992px) {
    .navbar > .container .navbar-brand, .navbar > .container-fluid .navbar-brand, .page-no-sidebar.member-lostpassword .navbar > .member-lp-process .navbar-brand {margin-left: -10px}
}
.navbar-toggle {position: relative;float: right;margin-right: 10px;padding: 9px 10px;margin-top: 8px;margin-bottom: 8px;background-color: transparent;background-image: none;border: 1px solid transparent;border-radius: 2px}
.navbar-toggle:focus {outline: 0}
.navbar-toggle .icon-bar {display: block;width: 22px;height: 2px;border-radius: 1px}
.navbar-toggle .icon-bar + .icon-bar {margin-top: 4px}
@media (min-width: 992px) {
    .navbar-toggle {display: none}
}
.navbar-nav {margin: 7.5px -10px}
.navbar-nav > li > a {padding-top: 10px;padding-bottom: 10px;line-height: 20px}
@media (max-width: 767px) {
    .navbar-nav .open .dropdown-menu {position: static;float: none;width: auto;margin-top: 0;background-color: transparent;border: 0;-webkit-box-shadow: none;box-shadow: none}
    .navbar-nav .open .dropdown-menu .dropdown-header, .navbar-nav .open .dropdown-menu > li > a {padding: 5px 15px 5px 25px}
    .navbar-nav .open .dropdown-menu > li > a {line-height: 20px}
    .navbar-nav .open .dropdown-menu > li > a:focus, .navbar-nav .open .dropdown-menu > li > a:hover {background-image: none}
}
@media (min-width: 992px) {
    .navbar-nav {float: left;margin: 0}
    .navbar-nav > li {float: left}
    .navbar-nav > li > a {padding-top: 15px;padding-bottom: 15px}
}
.navbar-form {padding: 10px;border-top: 1px solid transparent;border-bottom: 1px solid transparent;-webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, .1), 0 1px 0 rgba(255, 255, 255, .1);box-shadow: inset 0 1px 0 rgba(255, 255, 255, .1), 0 1px 0 rgba(255, 255, 255, .1);margin: 8px -10px}
@media (min-width: 768px) {
    .navbar-form .form-group {display: inline-block;margin-bottom: 0;vertical-align: middle}
    .navbar-form .form-control {display: inline-block;width: auto;vertical-align: middle}
    .navbar-form .form-control-static {display: inline-block}
    .navbar-form .input-group {display: inline-table;vertical-align: middle}
    .navbar-form .input-group .form-control, .navbar-form .input-group .input-group-addon, .navbar-form .input-group .input-group-btn {width: auto}
    .navbar-form .input-group > .form-control {width: 100%}
    .navbar-form .control-label {margin-bottom: 0;vertical-align: middle}
    .navbar-form .checkbox, .navbar-form .radio {display: inline-block;margin-top: 0;margin-bottom: 0;vertical-align: middle}
    .navbar-form .checkbox label, .navbar-form .radio label {padding-left: 0}
    .navbar-form .checkbox input[type=checkbox], .navbar-form .radio input[type=radio] {position: relative;margin-left: 0}
    .navbar-form .has-feedback .form-control-feedback {top: 0}
}
@media (max-width: 767px) {
    .navbar-form .form-group {margin-bottom: 5px}
    .navbar-form .form-group:last-child {margin-bottom: 0}
}
@media (min-width: 992px) {
    .navbar-form {width: auto;border: 0;margin-left: 0;margin-right: 0;padding-top: 0;padding-bottom: 0;-webkit-box-shadow: none;box-shadow: none}
}
.navbar-nav > li > .dropdown-menu {margin-top: 0;border-top-right-radius: 0;border-top-left-radius: 0}
.navbar-fixed-bottom .navbar-nav > li > .dropdown-menu {margin-bottom: 0;border-top-right-radius: 2px;border-top-left-radius: 2px;border-bottom-right-radius: 0;border-bottom-left-radius: 0}
.navbar-btn {margin-top: 8px;margin-bottom: 8px}
.navbar-btn.btn-sm {margin-top: 10px;margin-bottom: 10px}
.navbar-btn.btn-xs {margin-top: 14px;margin-bottom: 14px}
.navbar-text {margin-top: 15px;margin-bottom: 15px}
@media (min-width: 992px) {
    .navbar-text {float: left;margin-left: 10px;margin-right: 10px}
    .navbar-left {float: left !important}
    .navbar-right {float: right !important;margin-right: -10px}
    .navbar-right ~ .navbar-right {margin-right: 0}
}
.navbar-default {background-color: #f8f8f8;border-color: #e7e7e7}
.navbar-default .navbar-brand, .navbar-default .navbar-nav > li > a, .navbar-default .navbar-text {color: #777}
.navbar-default .navbar-brand:focus, .navbar-default .navbar-brand:hover {color: #5e5e5e;background-color: transparent}
.navbar-default .navbar-nav > li > a:focus, .navbar-default .navbar-nav > li > a:hover {color: #333;background-color: transparent}
.navbar-default .navbar-nav > .active > a, .navbar-default .navbar-nav > .active > a:focus, .navbar-default .navbar-nav > .active > a:hover {color: #555;background-color: #e7e7e7}
.navbar-default .navbar-nav > .disabled > a, .navbar-default .navbar-nav > .disabled > a:focus, .navbar-default .navbar-nav > .disabled > a:hover {color: #ccc;background-color: transparent}
.navbar-default .navbar-toggle {border-color: #ddd}
.navbar-default .navbar-toggle:focus, .navbar-default .navbar-toggle:hover {background-color: #ddd}
.navbar-default .navbar-toggle .icon-bar {background-color: #888}
.navbar-default .navbar-collapse, .navbar-default .navbar-form {border-color: #e7e7e7}
.navbar-default .navbar-nav > .open > a, .navbar-default .navbar-nav > .open > a:focus, .navbar-default .navbar-nav > .open > a:hover {background-color: #e7e7e7;color: #555}
@media (max-width: 767px) {
    .navbar-default .navbar-nav .open .dropdown-menu > li > a {color: #777}
    .navbar-default .navbar-nav .open .dropdown-menu > li > a:focus, .navbar-default .navbar-nav .open .dropdown-menu > li > a:hover {color: #333;background-color: transparent}
    .navbar-default .navbar-nav .open .dropdown-menu > .active > a, .navbar-default .navbar-nav .open .dropdown-menu > .active > a:focus, .navbar-default .navbar-nav .open .dropdown-menu > .active > a:hover {color: #555;background-color: #e7e7e7}
    .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a, .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:focus, .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:hover {color: #ccc;background-color: transparent}
}
.navbar-default .btn-link, .navbar-default .navbar-link {color: #777}
.navbar-default .navbar-link:hover {color: #333}
.navbar-default .btn-link:focus, .navbar-default .btn-link:hover {color: #333}
.navbar-default .btn-link[disabled]:focus, .navbar-default .btn-link[disabled]:hover, fieldset[disabled] .navbar-default .btn-link:focus, fieldset[disabled] .navbar-default .btn-link:hover {color: #ccc}
.navbar-inverse {background-color: #222;border-color: #090909}
.navbar-inverse .navbar-brand, .navbar-inverse .navbar-nav > li > a, .navbar-inverse .navbar-text {color: #9d9d9d}
.navbar-inverse .navbar-brand:focus, .navbar-inverse .navbar-brand:hover, .navbar-inverse .navbar-nav > li > a:focus, .navbar-inverse .navbar-nav > li > a:hover {color: #fff;background-color: transparent}
.navbar-inverse .navbar-nav > .active > a, .navbar-inverse .navbar-nav > .active > a:focus, .navbar-inverse .navbar-nav > .active > a:hover {color: #fff;background-color: #090909}
.navbar-inverse .navbar-nav > .disabled > a, .navbar-inverse .navbar-nav > .disabled > a:focus, .navbar-inverse .navbar-nav > .disabled > a:hover {color: #444;background-color: transparent}
.navbar-inverse .navbar-toggle {border-color: #333}
.navbar-inverse .navbar-toggle:focus, .navbar-inverse .navbar-toggle:hover {background-color: #333}
.navbar-inverse .navbar-toggle .icon-bar {background-color: #fff}
.navbar-inverse .navbar-collapse, .navbar-inverse .navbar-form {border-color: #101010}
.navbar-inverse .navbar-nav > .open > a, .navbar-inverse .navbar-nav > .open > a:focus, .navbar-inverse .navbar-nav > .open > a:hover {background-color: #090909;color: #fff}
@media (max-width: 767px) {
    .navbar-inverse .navbar-nav .open .dropdown-menu > .dropdown-header {border-color: #090909}
    .navbar-inverse .navbar-nav .open .dropdown-menu .divider {background-color: #090909}
    .navbar-inverse .navbar-nav .open .dropdown-menu > li > a {color: #9d9d9d}
    .navbar-inverse .navbar-nav .open .dropdown-menu > li > a:focus, .navbar-inverse .navbar-nav .open .dropdown-menu > li > a:hover {color: #fff;background-color: transparent}
    .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a, .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:focus, .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:hover {color: #fff;background-color: #090909}
    .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a, .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a:focus, .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a:hover {color: #444;background-color: transparent}
}
.navbar-inverse .btn-link, .navbar-inverse .navbar-link {color: #9d9d9d}
.navbar-inverse .navbar-link:hover {color: #fff}
.navbar-inverse .btn-link:focus, .navbar-inverse .btn-link:hover {color: #fff}
.navbar-inverse .btn-link[disabled]:focus, .navbar-inverse .btn-link[disabled]:hover, fieldset[disabled] .navbar-inverse .btn-link:focus, fieldset[disabled] .navbar-inverse .btn-link:hover {color: #444}
.breadcrumb {padding: 0;margin-bottom: 20px;list-style: none;font-size: 0;color: #5a5a5a}
.breadcrumb > li, ul.page-numbers > li, ul.pagination > li {display: inline-block;font-size: 12px;color: inherit;vertical-align: top;line-height: 1}
.breadcrumb > li a {color: inherit;line-height: inherit}
.breadcrumb > li a:hover {color: var(--theme-hover)}
.breadcrumb > li .wi {margin: 0 3px;opacity: .8;vertical-align: top}
.breadcrumb > .active {opacity: .9}
ul.page-numbers, ul.pagination {display: block;text-align: center;padding-left: 0 !important;margin: 0 !important}
ul.page-numbers > li, ul.pagination > li {padding: 0;font-size: 13px;line-height: 28px;cursor: pointer;-webkit-box-sizing: border-box;box-sizing: border-box;text-align: center;margin: 0 5px !important;color: #5a5a5a;background-color: #f4f4f5;min-width: 30px;border-radius: 2px}
@media (max-width: 767px) {
    ul.page-numbers > li, ul.pagination > li {display: none}
    ul.page-numbers > li.next, ul.page-numbers > li.next span, ul.page-numbers > li.prev, ul.page-numbers > li.prev span, ul.pagination > li.next, ul.pagination > li.next span, ul.pagination > li.prev, ul.pagination > li.prev span {display: inline-block}
}
ul.page-numbers > li > a, ul.page-numbers > li > span, ul.pagination > li > a, ul.pagination > li > span {display: block;padding: 0 8px;color: inherit}
ul.page-numbers > li .wi, ul.pagination > li .wi {font-size: 15px;vertical-align: top}
ul.page-numbers > li:hover, ul.pagination > li:hover {color: #fff;background: var(--theme-hover)}
ul.page-numbers > li.disabled, ul.pagination > li.disabled {color: #5a5a5a;background-color: #f4f4f5}
ul.page-numbers > li.disabled:hover, ul.pagination > li.disabled:hover {color: #5a5a5a;background-color: #f4f4f5;cursor: default}
ul.page-numbers > li.active, ul.pagination > li.active {color: #fff;background: var(--theme-color)}
ul.page-numbers .pagination-go, ul.pagination .pagination-go {position: relative;padding: 0;background: 0 0}
ul.page-numbers .pagination-go .pgo-input, ul.pagination .pagination-go .pgo-input {width: 66px;border: 1px solid #dcdfe6;line-height: 26px;padding: 0 28px 0 8px;background: #fff;border-radius: 2px}
ul.page-numbers .pagination-go .pgo-input:focus, ul.pagination .pagination-go .pgo-input:focus {outline: 0;border-color: var(--theme-hover)}
ul.page-numbers .pagination-go .pgo-btn, ul.pagination .pagination-go .pgo-btn {position: absolute;width: 28px;height: 28px;right: 0;top: 0;color: #5a5a5a;background: 0 0;border: 0;cursor: pointer}
ul.page-numbers .pagination-go .pgo-btn:hover, ul.pagination .pagination-go .pgo-btn:hover {color: var(--theme-hover)}
ul.page-numbers .pagination-go .pgo-btn:active, ul.page-numbers .pagination-go .pgo-btn:focus, ul.pagination .pagination-go .pgo-btn:active, ul.pagination .pagination-go .pgo-btn:focus {outline: 0;background: 0 0}
.label {display: inline;padding: .2em .6em .3em;font-size: 75%;font-weight: 700;line-height: 1;color: #fff;text-align: center;white-space: nowrap;vertical-align: baseline;border-radius: .25em}
.label:empty {display: none}
.btn .label {position: relative;top: -1px}
a.label:focus, a.label:hover {color: #fff;text-decoration: none;cursor: pointer}
.label-default {background-color: #777}
.label-default[href]:focus, .label-default[href]:hover {background-color: #777}
.label-primary {background-color: var(--theme-color)}
.label-primary[href]:focus, .label-primary[href]:hover {background-color: var(--theme-color)}
.label-success {background-color: #29ad00}
.label-success[href]:focus, .label-success[href]:hover {background-color: #29ad00}
.label-info {background-color: #00aae7}
.label-info[href]:focus, .label-info[href]:hover {background-color: #00aae7}
.label-warning {background-color: #ffa400}
.label-warning[href]:focus, .label-warning[href]:hover {background-color: #ffa400}
.label-danger {background-color: #fa5555}
.label-danger[href]:focus, .label-danger[href]:hover {background-color: #fa5555}
.alert {padding: 10px 15px;margin-bottom: 20px;border: 1px solid transparent;border-radius: 2px;overflow: hidden}
.alert h4 {margin-top: 0;color: inherit}
.alert .alert-link {font-weight: 700}
.alert > p, .alert > ul {margin-bottom: 0}
.alert-dismissable, .alert-dismissible {padding-right: 10px 15 px20}
.alert-dismissable .close, .alert-dismissible .close {position: relative;top: -2px;right: -21px;color: inherit}
.alert-success {background-color: #eaf7e6;border-color: #e7f6e2;color: #29ad00}
.alert-success hr {border-top-color: #d7f0cf}
.alert-success .alert-link {color: #1d7a00}
.alert-info {background-color: #e6f7fd;border-color: #e1f5fd;color: #00aae7}
.alert-info hr {border-top-color: #caeefb}
.alert-info .alert-link {color: #0084b4}
.alert-warning {background-color: #fff6e6;border-color: #fff4e1;color: #ffa400}
.alert-warning hr {border-top-color: #ffebc7}
.alert-warning .alert-link {color: #cc8300}
.alert-danger {background-color: #fee;border-color: #ffe9e9;color: #fa5555}
.alert-danger hr {border-top-color: #ffcfcf}
.alert-danger .alert-link {color: #f92323}
.panel {margin-bottom: 20px;background-color: #fff;border: 1px solid transparent;-webkit-box-sizing: border-box;box-sizing: border-box;-webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, .05);box-shadow: 0 1px 1px rgba(0, 0, 0, .05)}
.panel-body:after, .panel-body:before {content: " ";display: table}
.panel-body:after {clear: both}
.panel-heading {border-bottom: 1px solid transparent;border-top-right-radius: 1px;border-top-left-radius: 1px}
.panel-heading > .dropdown .dropdown-toggle, .panel-title, .panel-title > .small, .panel-title > .small > a, .panel-title > a, .panel-title > small, .panel-title > small > a {color: inherit}
.panel-footer {padding: 10px 15px;background-color: #f5f5f5;border-top: 1px solid #eee;border-bottom-right-radius: 1px;border-bottom-left-radius: 1px}
.panel > .list-group, .panel > .panel-collapse > .list-group {margin-bottom: 0}
.panel > .list-group .list-group-item, .panel > .panel-collapse > .list-group .list-group-item {border-width: 1px 0;border-radius: 0}
.panel > .list-group:first-child .list-group-item:first-child, .panel > .panel-collapse > .list-group:first-child .list-group-item:first-child {border-top: 0;border-top-right-radius: 1px;border-top-left-radius: 1px}
.panel > .list-group:last-child .list-group-item:last-child, .panel > .panel-collapse > .list-group:last-child .list-group-item:last-child {border-bottom: 0;border-bottom-right-radius: 1px;border-bottom-left-radius: 1px}
.panel > .panel-heading + .panel-collapse > .list-group .list-group-item:first-child {border-top-right-radius: 0;border-top-left-radius: 0}
.panel-heading + .list-group .list-group-item:first-child {border-top-width: 0}
.list-group + .panel-footer {border-top-width: 0}
.panel > .panel-collapse > .table, .panel > .table, .panel > .table-responsive > .table {margin-bottom: 0}
.panel > .panel-collapse > .table caption, .panel > .table caption, .panel > .table-responsive > .table caption {padding-left: 15px;padding-right: 15px}
.panel > .table-responsive:first-child > .table:first-child, .panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child, .panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child, .panel > .table:first-child, .panel > .table:first-child > tbody:first-child > tr:first-child, .panel > .table:first-child > thead:first-child > tr:first-child {border-top-left-radius: 1px;border-top-right-radius: 1px}
.panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child td:first-child, .panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child th:first-child, .panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child td:first-child, .panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child th:first-child, .panel > .table:first-child > tbody:first-child > tr:first-child td:first-child, .panel > .table:first-child > tbody:first-child > tr:first-child th:first-child, .panel > .table:first-child > thead:first-child > tr:first-child td:first-child, .panel > .table:first-child > thead:first-child > tr:first-child th:first-child {border-top-left-radius: 1px}
.panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child td:last-child, .panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child th:last-child, .panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child td:last-child, .panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child th:last-child, .panel > .table:first-child > tbody:first-child > tr:first-child td:last-child, .panel > .table:first-child > tbody:first-child > tr:first-child th:last-child, .panel > .table:first-child > thead:first-child > tr:first-child td:last-child, .panel > .table:first-child > thead:first-child > tr:first-child th:last-child {border-top-right-radius: 1px}
.panel > .table-responsive:last-child > .table:last-child, .panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child, .panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child, .panel > .table:last-child, .panel > .table:last-child > tbody:last-child > tr:last-child, .panel > .table:last-child > tfoot:last-child > tr:last-child {border-bottom-left-radius: 1px;border-bottom-right-radius: 1px}
.panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child td:first-child, .panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child th:first-child, .panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child td:first-child, .panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child th:first-child, .panel > .table:last-child > tbody:last-child > tr:last-child td:first-child, .panel > .table:last-child > tbody:last-child > tr:last-child th:first-child, .panel > .table:last-child > tfoot:last-child > tr:last-child td:first-child, .panel > .table:last-child > tfoot:last-child > tr:last-child th:first-child {border-bottom-left-radius: 1px}
.panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child td:last-child, .panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child th:last-child, .panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child td:last-child, .panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child th:last-child, .panel > .table:last-child > tbody:last-child > tr:last-child td:last-child, .panel > .table:last-child > tbody:last-child > tr:last-child th:last-child, .panel > .table:last-child > tfoot:last-child > tr:last-child td:last-child, .panel > .table:last-child > tfoot:last-child > tr:last-child th:last-child {border-bottom-right-radius: 1px}
.panel > .panel-body + .table, .panel > .panel-body + .table-responsive, .panel > .table + .panel-body, .panel > .table-responsive + .panel-body {border-top: 1px solid #ddd}
.panel > .table > tbody:first-child > tr:first-child td, .panel > .table > tbody:first-child > tr:first-child th {border-top: 0}
.panel > .table-bordered, .panel > .table-responsive > .table-bordered {border: 0}
.panel > .table-bordered > tbody > tr > td:first-child, .panel > .table-bordered > tbody > tr > th:first-child, .panel > .table-bordered > tfoot > tr > td:first-child, .panel > .table-bordered > tfoot > tr > th:first-child, .panel > .table-bordered > thead > tr > td:first-child, .panel > .table-bordered > thead > tr > th:first-child, .panel > .table-responsive > .table-bordered > tbody > tr > td:first-child, .panel > .table-responsive > .table-bordered > tbody > tr > th:first-child, .panel > .table-responsive > .table-bordered > tfoot > tr > td:first-child, .panel > .table-responsive > .table-bordered > tfoot > tr > th:first-child, .panel > .table-responsive > .table-bordered > thead > tr > td:first-child, .panel > .table-responsive > .table-bordered > thead > tr > th:first-child {border-left: 0}
.panel > .table-bordered > tbody > tr > td:last-child, .panel > .table-bordered > tbody > tr > th:last-child, .panel > .table-bordered > tfoot > tr > td:last-child, .panel > .table-bordered > tfoot > tr > th:last-child, .panel > .table-bordered > thead > tr > td:last-child, .panel > .table-bordered > thead > tr > th:last-child, .panel > .table-responsive > .table-bordered > tbody > tr > td:last-child, .panel > .table-responsive > .table-bordered > tbody > tr > th:last-child, .panel > .table-responsive > .table-bordered > tfoot > tr > td:last-child, .panel > .table-responsive > .table-bordered > tfoot > tr > th:last-child, .panel > .table-responsive > .table-bordered > thead > tr > td:last-child, .panel > .table-responsive > .table-bordered > thead > tr > th:last-child {border-right: 0}
.panel > .table-bordered > tbody > tr:first-child > td, .panel > .table-bordered > tbody > tr:first-child > th, .panel > .table-bordered > thead > tr:first-child > td, .panel > .table-bordered > thead > tr:first-child > th, .panel > .table-responsive > .table-bordered > tbody > tr:first-child > td, .panel > .table-responsive > .table-bordered > tbody > tr:first-child > th, .panel > .table-responsive > .table-bordered > thead > tr:first-child > td, .panel > .table-responsive > .table-bordered > thead > tr:first-child > th {border-bottom: 0}
.panel > .table-bordered > tbody > tr:last-child > td, .panel > .table-bordered > tbody > tr:last-child > th, .panel > .table-bordered > tfoot > tr:last-child > td, .panel > .table-bordered > tfoot > tr:last-child > th, .panel > .table-responsive > .table-bordered > tbody > tr:last-child > td, .panel > .table-responsive > .table-bordered > tbody > tr:last-child > th, .panel > .table-responsive > .table-bordered > tfoot > tr:last-child > td, .panel > .table-responsive > .table-bordered > tfoot > tr:last-child > th {border-bottom: 0}
.panel > .table-responsive {border: 0;margin-bottom: 0}
.panel-group {margin-bottom: 20px}
.panel-group .panel {margin-bottom: 0;border-radius: 2px}
.panel-group .panel + .panel {margin-top: 5px}
.panel-group .panel-heading {border-bottom: 0}
.panel-group .panel-heading + .panel-collapse > .list-group {border-top: 1px solid #eee}
.panel-group .panel-footer {border-top: 0}
.panel-group .panel-footer + .panel-collapse .panel-body {border-bottom: 1px solid #eee}
.panel-default {border-color: #eee}
.panel-default > .panel-heading {color: #262626;background-color: #f5f5f5;border-color: #eee}
.panel-default > .panel-heading + .panel-collapse > .panel-body {border-top-color: #eee}
.panel-default > .panel-heading .badge {color: #f5f5f5;background-color: #262626}
.panel-default > .panel-footer + .panel-collapse > .panel-body {border-bottom-color: #eee}
.panel-primary, .panel-primary > .panel-heading {border-color: var(--theme-color)}
.panel-primary > .panel-heading {color: #fff;background-color: var(--theme-color)}
.panel-primary > .panel-heading + .panel-collapse > .panel-body {border-top-color: var(--theme-color)}
.panel-primary > .panel-heading .badge {color: var(--theme-color);background-color: #fff}
.panel-primary > .panel-footer + .panel-collapse > .panel-body {border-bottom-color: var(--theme-color)}
.panel-success {border-color: #e7f6e2}
.panel-success > .panel-heading {color: #29ad00;background-color: #eaf7e6;border-color: #e7f6e2}
.panel-success > .panel-heading + .panel-collapse > .panel-body {border-top-color: #e7f6e2}
.panel-success > .panel-heading .badge {color: #eaf7e6;background-color: #29ad00}
.panel-success > .panel-footer + .panel-collapse > .panel-body {border-bottom-color: #e7f6e2}
.panel-info {border-color: #e1f5fd}
.panel-info > .panel-heading {color: #00aae7;background-color: #e6f7fd;border-color: #e1f5fd}
.panel-info > .panel-heading + .panel-collapse > .panel-body {border-top-color: #e1f5fd}
.panel-info > .panel-heading .badge {color: #e6f7fd;background-color: #00aae7}
.panel-info > .panel-footer + .panel-collapse > .panel-body {border-bottom-color: #e1f5fd}
.panel-warning {border-color: #fff4e1}
.panel-warning > .panel-heading {color: #ffa400;background-color: #fff6e6;border-color: #fff4e1}
.panel-warning > .panel-heading + .panel-collapse > .panel-body {border-top-color: #fff4e1}
.panel-warning > .panel-heading .badge {color: #fff6e6;background-color: #ffa400}
.panel-warning > .panel-footer + .panel-collapse > .panel-body {border-bottom-color: #fff4e1}
.panel-danger {border-color: #ffe9e9}
.panel-danger > .panel-heading {color: #fa5555;background-color: #fee;border-color: #ffe9e9}
.panel-danger > .panel-heading + .panel-collapse > .panel-body {border-top-color: #ffe9e9}
.panel-danger > .panel-heading .badge {color: #fee;background-color: #fa5555}
.panel-danger > .panel-footer + .panel-collapse > .panel-body {border-bottom-color: #ffe9e9}
.close {margin: 0;float: right;font-size: 18px;line-height: 1;color: #828282;opacity: .8}
.close:focus, .close:hover {color: var(--theme-hover);text-decoration: none;cursor: pointer}
button.close {padding: 0;cursor: pointer;background: 0 0;border: 0;-webkit-appearance: none;outline: 0}
.modal, .modal-open {overflow: hidden}
.modal {display: none;position: fixed;top: 0;right: 0;bottom: 0;left: 0;text-align: center;z-index: 1050;-webkit-overflow-scrolling: touch;outline: 0}
.modal.fade .modal-dialog {-webkit-transform: translateY(-25%);-ms-transform: translateY(-25%);transform: translateY(-25%);-webkit-transition: -webkit-transform .3s ease-out;-o-transition: -o-transform .3s ease-out;-o-transition: transform .3s ease-out;transition: transform .3s ease-out;transition: transform .3s ease-out, -webkit-transform .3s ease-out}
.modal.in .modal-dialog {-webkit-transform: translate(0);-ms-transform: translate(0);transform: translate(0)}
.modal-open .modal {overflow-x: hidden;overflow-y: auto}
.modal-dialog {position: relative;display: inline-block;width: auto;margin-top: 15vh;text-align: left}
@media (max-width: 767px) {
    .modal-dialog {width: 92%;margin-bottom: 30px}
}
.modal-content {position: relative;background-color: #fff;border-radius: 2px;-webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, .3);box-shadow: 0 1px 3px rgba(0, 0, 0, .3);background-clip: padding-box;outline: 0}
.modal-backdrop {position: fixed;top: 0;right: 0;bottom: 0;left: 0;z-index: 1040;background-color: #000}
.modal-backdrop.fade {opacity: 0;filter: alpha(opacity=0)}
.modal-backdrop.in {opacity: .5;filter: alpha(opacity=50)}
.modal-header {padding: 15px 15px 10px}
.modal-header:after, .modal-header:before {content: " ";display: table}
.modal-header:after {clear: both}
.modal-title {margin: 0;font-size: 18px;color: #262626;font-weight: 400;line-height: 1}
.modal-body {position: relative;padding: 15px;color: #5a5a5a}
.modal-footer {padding: 10px 15px 15px;text-align: right}
.modal-footer:after, .modal-footer:before {content: " ";display: table}
.modal-footer:after {clear: both}
.modal-footer .btn + .btn {margin-left: 5px;margin-bottom: 0}
.modal-footer .btn-group .btn + .btn {margin-left: -1px}
.modal-footer .btn-block + .btn-block {margin-left: 0}
.modal-scrollbar-measure {position: absolute;top: -9999px;width: 50px;height: 50px;overflow: scroll}
@media (min-width: 768px) {
    .modal-dialog {width: 600px}
    .modal-content {-webkit-box-shadow: 0 5px 15px rgba(0, 0, 0, .5);box-shadow: 0 5px 15px rgba(0, 0, 0, .5)}
    .modal-sm {width: 420px}
}
@media (min-width: 992px) {
    .modal-lg {width: 900px}
    .modal-lg .modal-header {padding: 20px 20px 10px}
    .modal-lg .modal-body {padding: 20px}
    .modal-lg .modal-footer {padding: 10px 20px 20px}
}
.tooltip {position: absolute;z-index: 1070;display: block;font-family: var(--theme-font-family);font-style: normal;font-weight: 400;letter-spacing: normal;line-break: auto;line-height: 1.42857;text-align: left;text-align: start;text-decoration: none;text-shadow: none;text-transform: none;white-space: normal;word-break: normal;word-spacing: normal;word-wrap: normal;font-size: 12px;opacity: 0;filter: alpha(opacity=0)}
.tooltip.in {opacity: .9;filter: alpha(opacity=90)}
.tooltip.top {margin-top: -3px;padding: 5px 0}
.tooltip.right {margin-left: 3px;padding: 0 5px}
.tooltip.bottom {margin-top: 3px;padding: 5px 0}
.tooltip.left {margin-left: -3px;padding: 0 5px}
.tooltip-inner {max-width: 200px;padding: 3px 8px;color: #fff;text-align: center;background-color: #000;border-radius: 3px}
.tooltip-arrow {position: absolute;width: 0;height: 0;border-color: transparent;border-style: solid}
.tooltip.top .tooltip-arrow {bottom: 0;left: 50%;margin-left: -5px;border-width: 5px 5px 0;border-top-color: #000}
.tooltip.top-left .tooltip-arrow, .tooltip.top-right .tooltip-arrow {bottom: 0;margin-bottom: -5px;border-width: 5px 5px 0;border-top-color: #000}
.tooltip.top-left .tooltip-arrow {right: 5px}
.tooltip.top-right .tooltip-arrow {left: 5px}
.tooltip.right .tooltip-arrow {top: 50%;left: 0;margin-top: -5px;border-width: 5px 5px 5px 0;border-right-color: #000}
.tooltip.left .tooltip-arrow {top: 50%;right: 0;margin-top: -5px;border-width: 5px 0 5px 5px;border-left-color: #000}
.tooltip.bottom .tooltip-arrow {top: 0;left: 50%;margin-left: -5px;border-width: 0 5px 5px;border-bottom-color: #000}
.tooltip.bottom-left .tooltip-arrow {top: 0;right: 5px;margin-top: -5px;border-width: 0 5px 5px;border-bottom-color: #000}
.tooltip.bottom-right .tooltip-arrow {top: 0;left: 5px;margin-top: -5px;border-width: 0 5px 5px;border-bottom-color: #000}
.popover {position: absolute;top: 0;left: 0;z-index: 1060;display: none;max-width: 276px;padding: 1px;font-family: var(--theme-font-family);font-style: normal;font-weight: 400;letter-spacing: normal;line-break: auto;line-height: 1.42857;text-align: left;text-align: start;text-decoration: none;text-shadow: none;text-transform: none;white-space: normal;word-break: normal;word-spacing: normal;word-wrap: normal;font-size: 14px;background-color: #fff;background-clip: padding-box;border: 1px solid #ccc;border: 1px solid rgba(0, 0, 0, .2);border-radius: 2px;-webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, .2);box-shadow: 0 5px 10px rgba(0, 0, 0, .2)}
.popover.top {margin-top: -10px}
.popover.right {margin-left: 10px}
.popover.bottom {margin-top: 10px}
.popover.left {margin-left: -10px}
.popover-title {margin: 0;padding: 8px 14px;font-size: 14px;background-color: #f7f7f7;border-bottom: 1px solid #ebebeb;border-radius: 1px 1px 0 0}
.popover-content {padding: 9px 14px}
.popover > .arrow, .popover > .arrow:after {position: absolute;display: block;width: 0;height: 0;border-color: transparent;border-style: solid}
.popover > .arrow {border-width: 11px}
.popover > .arrow:after {border-width: 10px;content: ""}
.popover.top > .arrow {left: 50%;margin-left: -11px;border-bottom-width: 0;border-top-color: #999;border-top-color: rgba(0, 0, 0, .25);bottom: -11px}
.popover.top > .arrow:after {content: " ";bottom: 1px;margin-left: -10px;border-bottom-width: 0;border-top-color: #fff}
.popover.right > .arrow {top: 50%;left: -11px;margin-top: -11px;border-left-width: 0;border-right-color: #999;border-right-color: rgba(0, 0, 0, .25)}
.popover.right > .arrow:after {content: " ";left: 1px;bottom: -10px;border-left-width: 0;border-right-color: #fff}
.popover.bottom > .arrow {left: 50%;margin-left: -11px;border-top-width: 0;border-bottom-color: #999;border-bottom-color: rgba(0, 0, 0, .25);top: -11px}
.popover.bottom > .arrow:after {content: " ";top: 1px;margin-left: -10px;border-top-width: 0;border-bottom-color: #fff}
.popover.left > .arrow {top: 50%;right: -11px;margin-top: -11px;border-right-width: 0;border-left-color: #999;border-left-color: rgba(0, 0, 0, .25)}
.popover.left > .arrow:after {content: " ";right: 1px;border-right-width: 0;border-left-color: #fff;bottom: -10px}
.carousel, .carousel-inner {position: relative}
.carousel-inner {overflow: hidden;width: 100%}
.carousel-inner > .item {display: none;position: relative;-webkit-transition: left .6s ease-in-out;-o-transition: .6s ease-in-out left;transition: left .6s ease-in-out}
.carousel-inner > .item > a > img, .carousel-inner > .item > img {display: block;max-width: 100%;height: auto;line-height: 1}
@media (-webkit-transform-3d),(transform-3d) {
    .carousel-inner > .item {-webkit-transition: -webkit-transform .6s ease-in-out;-o-transition: -o-transform .6s ease-in-out;-o-transition: transform .6s ease-in-out;transition: transform .6s ease-in-out;transition: transform .6s ease-in-out, -webkit-transform .6s ease-in-out;-webkit-backface-visibility: hidden;backface-visibility: hidden;-webkit-perspective: 1000px;perspective: 1000px}
    .carousel-inner > .item.active.right, .carousel-inner > .item.next {-webkit-transform: translate3d(100%, 0, 0);transform: translate3d(100%, 0, 0);left: 0}
    .carousel-inner > .item.active.left, .carousel-inner > .item.prev {-webkit-transform: translate3d(-100%, 0, 0);transform: translate3d(-100%, 0, 0);left: 0}
    .carousel-inner > .item.active, .carousel-inner > .item.next.left, .carousel-inner > .item.prev.right {-webkit-transform: translateZ(0);transform: translateZ(0);left: 0}
}
.carousel-inner > .active, .carousel-inner > .next, .carousel-inner > .prev {display: block}
.carousel-inner > .active, .carousel-inner > .next.left, .carousel-inner > .prev.right {left: 0}
.carousel-control, .carousel-inner > .next, .carousel-inner > .prev {position: absolute;top: 0;width: 100%}
.carousel-inner > .next {left: 100%}
.carousel-inner > .active.left, .carousel-inner > .prev {left: -100%}
.carousel-inner > .active.right {left: 100%}
.carousel-control {left: 0;bottom: 0;width: 15%;opacity: .5;filter: alpha(opacity=50);font-size: 20px;color: #fff;text-align: center;text-shadow: 0 1px 2px rgba(0, 0, 0, .6);background-color: transparent}
.carousel-control.left {background-image: -o-linear-gradient(left, rgba(0, 0, 0, .5) 0, rgba(0, 0, 0, .0001) 100%);background-image: linear-gradient(90deg, rgba(0, 0, 0, .5) 0, rgba(0, 0, 0, .0001));background-repeat: repeat-x;filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#80000000", endColorstr="#00000000", GradientType=1)}
.carousel-control.right {left: auto;right: 0;background-image: -o-linear-gradient(left, rgba(0, 0, 0, .0001) 0, rgba(0, 0, 0, .5) 100%);background-image: linear-gradient(90deg, rgba(0, 0, 0, .0001) 0, rgba(0, 0, 0, .5));background-repeat: repeat-x;filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#00000000", endColorstr="#80000000", GradientType=1)}
.carousel-control:focus, .carousel-control:hover {outline: 0;color: #fff;text-decoration: none;opacity: .9;filter: alpha(opacity=90)}
.carousel-control .glyphicon-chevron-left, .carousel-control .glyphicon-chevron-right, .carousel-control .icon-next, .carousel-control .icon-prev {position: absolute;top: 50%;margin-top: -10px;z-index: 5;display: inline-block}
.carousel-control .glyphicon-chevron-left, .carousel-control .icon-prev {left: 50%;margin-left: -10px}
.carousel-control .glyphicon-chevron-right, .carousel-control .icon-next {right: 50%;margin-right: -10px}
.carousel-control .icon-next, .carousel-control .icon-prev {width: 20px;height: 20px;line-height: 1;font-family: serif}
.carousel-control .icon-prev:before {content: "\2039"}
.carousel-control .icon-next:before {content: "\203a"}
.carousel-indicators {position: absolute;bottom: 10px;left: 50%;z-index: 15;width: 60%;margin-left: -30%;padding-left: 0;list-style: none;text-align: center}
.carousel-indicators li {display: inline-block;width: 10px;height: 10px;margin: 1px;text-indent: -999px;border: 1px solid #fff;border-radius: 10px;cursor: pointer;background-color: transparent}
.carousel-indicators .active {margin: 0;width: 12px;height: 12px;background-color: #fff}
.carousel-caption {position: absolute;left: 15%;right: 15%;bottom: 20px;z-index: 10;padding-top: 20px;padding-bottom: 20px;color: #fff;text-align: center;text-shadow: 0 1px 2px rgba(0, 0, 0, .6)}
.carousel-caption .btn {text-shadow: none}
@media screen and (min-width: 768px) {
    .carousel-control .glyphicon-chevron-left, .carousel-control .glyphicon-chevron-right, .carousel-control .icon-next, .carousel-control .icon-prev {width: 30px;height: 30px;margin-top: -10px;font-size: 30px}
    .carousel-control .glyphicon-chevron-left, .carousel-control .icon-prev {margin-left: -10px}
    .carousel-control .glyphicon-chevron-right, .carousel-control .icon-next {margin-right: -10px}
    .carousel-caption {left: 20%;right: 20%;padding-bottom: 30px}
    .carousel-indicators {bottom: 20px}
}
.clearfix:after, .clearfix:before {content: " ";display: table}
.clearfix:after {clear: both}
.center-block {display: block;margin-left: auto;margin-right: auto}
.pull-right {float: right !important}
.pull-left {float: left !important}
.hide {display: none !important}
.show {display: block !important}
.invisible {visibility: hidden}
.text-hide {font: 0/0 a;color: transparent;text-shadow: none;background-color: transparent;border: 0}
@-ms-viewport {
    width: device-width
}
.visible-lg, .visible-lg-block, .visible-lg-inline, .visible-lg-inline-block, .visible-md, .visible-md-block, .visible-md-inline, .visible-md-inline-block, .visible-sm, .visible-sm-block, .visible-sm-inline, .visible-sm-inline-block, .visible-xs, .visible-xs-block, .visible-xs-inline, .visible-xs-inline-block {display: none !important}
@media (max-width: 767px) {
    .visible-xs, .visible-xs-block {display: block !important}
    table.visible-xs {display: table !important}
    tr.visible-xs {display: table-row !important}
    td.visible-xs, th.visible-xs {display: table-cell !important}
    .visible-xs-inline {display: inline !important}
    .visible-xs-inline-block {display: inline-block !important}
}
@media (min-width: 768px) and (max-width: 991px) {
    .visible-sm, .visible-sm-block {display: block !important}
    table.visible-sm {display: table !important}
    tr.visible-sm {display: table-row !important}
    td.visible-sm, th.visible-sm {display: table-cell !important}
    .visible-sm-inline {display: inline !important}
    .visible-sm-inline-block {display: inline-block !important}
}
@media (min-width: 992px) and (max-width: 1239px) {
    .visible-md, .visible-md-block {display: block !important}
    table.visible-md {display: table !important}
    tr.visible-md {display: table-row !important}
    td.visible-md, th.visible-md {display: table-cell !important}
    .visible-md-inline {display: inline !important}
    .visible-md-inline-block {display: inline-block !important}
}
@media (min-width: 1240px) {
    .visible-lg, .visible-lg-block {display: block !important}
    table.visible-lg {display: table !important}
    tr.visible-lg {display: table-row !important}
    td.visible-lg, th.visible-lg {display: table-cell !important}
    .visible-lg-inline {display: inline !important}
    .visible-lg-inline-block {display: inline-block !important}
}
@media (max-width: 767px) {
    .hidden-xs {display: none !important}
}
@media (min-width: 768px) and (max-width: 991px) {
    .hidden-sm {display: none !important}
}
@media (min-width: 992px) and (max-width: 1239px) {
    .hidden-md {display: none !important}
}
@media (min-width: 1240px) {
    .hidden-lg {display: none !important}
}
.visible-print {display: none !important}
@media print {
    .visible-print {display: block !important}
    table.visible-print {display: table !important}
    tr.visible-print {display: table-row !important}
    td.visible-print, th.visible-print {display: table-cell !important}
}
.visible-print-block {display: none !important}
@media print {
    .visible-print-block {display: block !important}
}
.visible-print-inline {display: none !important}
@media print {
    .visible-print-inline {display: inline !important}
}
.visible-print-inline-block {display: none !important}
@media print {
    .visible-print-inline-block {display: inline-block !important}
    .hidden-print {display: none !important}
}
input[type=checkbox], input[type=radio] {margin: 4px 0 0;line-height: normal}
.tabs {margin-bottom: 15px}
.tabs .nav-tabs {padding: 0 !important;margin: 0 !important}
.tabs .nav-tabs > li {margin-bottom: -1px !important}
.tabs .nav-tabs a {border-radius: 0;color: #666;background-color: #f3f3f3;border: 1px solid #ddd}
.tabs .nav-tabs a:hover {background-color: #f9f9f9;border: 1px solid #ddd}
.tabs .nav-tabs .active a {color: #333}
.tab-content {border: 1px solid #ddd;border-top: 0;background: #fff}
.panel-body p, .tab-content p {margin-bottom: 10px !important;text-indent: 0 !important}
.alert p:last-child, .panel-body p:last-child, .tab-content p:last-child, .tabs-horizontal .nav-tabs > li:last-child {margin-bottom: 0 !important}
.tab-pane {padding: 15px;overflow: hidden}
.tabs-horizontal {display: table;width: 100%;table-layout: fixed;overflow: hidden}
.tabs-horizontal .nav-tabs {position: relative;z-index: 1;width: 120px;display: table-cell;border-bottom: 0}
.tabs-horizontal .nav-tabs li {margin-bottom: 2px;float: none}
.tabs-horizontal .nav-tabs a {display: block;margin-right: -1px;border: 1px solid #ddd}
.tabs-horizontal .nav-tabs a:hover {border: 1px solid #ddd}
.tabs-horizontal .nav-tabs .active a, .tabs-horizontal .nav-tabs .active a:active, .tabs-horizontal .nav-tabs .active a:focus, .tabs-horizontal .nav-tabs .active a:hover {border: 1px solid #ddd;border-right-color: #fff}
.tabs-horizontal .tab-wrap {display: table-cell;border: 1px solid #ddd;background: #fff}
.tabs-horizontal .tab-content {border: 0}
.panel {border-radius: 3px}
.panel-heading {padding: 10px 15px;border-radius: 0}
.panel-title {padding: 0 !important;margin: 0 !important;font-size: 16px !important;font-weight: 400;border: 0 !important}
.panel-body {padding: 15px}
.alert .fa-lg {float: left;font-size: 38px;line-height: 1}
.alert p {text-indent: 0 !important;margin-bottom: 10px !important;line-height: 1.46 !important}
.alert-content {padding-left: 52px;font-size: 14px;line-height: 1.4}
.content-body-content .row, .map-wrap {margin-bottom: 10px}
.map-wrap {position: relative}
.map-wrap img {max-width: none !important}
.map-wrap .map-address {text-indent: 0 !important}
@-webkit-keyframes TCaptcha-icon {
    0%, 50%, to {-webkit-transform: scale(1);transform: scale(1)}
    25% {-webkit-transform: scale(1.2);transform: scale(1.2)}
    75% {-webkit-transform: scale(.8);transform: scale(.8)}
}
@keyframes TCaptcha-icon {
    0%, 50%, to {-webkit-transform: scale(1);transform: scale(1)}
    25% {-webkit-transform: scale(1.2);transform: scale(1.2)}
    75% {-webkit-transform: scale(.8);transform: scale(.8)}
}
@-webkit-keyframes smartphoto-loader {
    0% {opacity: .4;-webkit-transform: rotate(0deg);transform: rotate(0deg)}
    50% {opacity: 1;-webkit-transform: rotate(180deg);transform: rotate(180deg)}
    to {opacity: .4;-webkit-transform: rotate(1turn);transform: rotate(1turn)}
}
@keyframes smartphoto-loader {
    0% {opacity: .4;-webkit-transform: rotate(0deg);transform: rotate(0deg)}
    50% {opacity: 1;-webkit-transform: rotate(180deg);transform: rotate(180deg)}
    to {opacity: .4;-webkit-transform: rotate(1turn);transform: rotate(1turn)}
}
@-webkit-keyframes smartphoto-appear {
    0% {display: none;opacity: 0}
    1% {display: block;opacity: 0}
    to {display: block;opacity: 1}
}
@keyframes smartphoto-appear {
    0% {display: none;opacity: 0}
    1% {display: block;opacity: 0}
    to {display: block;opacity: 1}
}
@-webkit-keyframes smartphoto-hide {
    0% {display: block;opacity: 1}
    99% {display: block;opacity: 0}
    to {display: none;opacity: 0}
}
@keyframes smartphoto-hide {
    0% {display: block;opacity: 1}
    99% {display: block;opacity: 0}
    to {display: none;opacity: 0}
}
.nav-pills .nav-link {
    border-radius: 0.25rem;
}

.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
    color: #fff;
    background-color: #007bff;
}