import o from"./edit-rZMaUJfE.js";import{d as r,ag as i,c as m,o as p,P as e}from"./@vue-C21YZbHS.js";import{a as s}from"./index-BGmsw1a8.js";import"./cmsArticle-DSySRWLJ.js";import"./index-BSsI7aMT.js";import"./vue-router-BDT9dCQ7.js";import"./cmsCategory-MP-V5q2B.js";import"./modulesField-KHeO3-pX.js";import"./index-D8PZCYUo.js";import"./lodash-BPJNOONf.js";import"./@intlify-D8kAWvSi.js";import"./element-plus-CUmVNDWO.js";import"./lodash-es-S0Y0Up6J.js";import"./@vueuse-D_5IXmcI.js";import"./@element-plus-_Cc-TEQX.js";import"./@popperjs-D_chPuIy.js";import"./@ctrl-r5W6hzzQ.js";import"./dayjs-CepoAVPu.js";import"./async-validator-9PlIezaS.js";import"./memoize-one-BdPwpGay.js";import"./normalize-wheel-es-BQoi3Ox2.js";import"./@floating-ui-DwceP2Gb.js";import"./index-Xig09u4c.js";import"./cmsTemplate-IDwSlQj3.js";import"./eventBus-BLgm5ZO3.js";import"./mitt-DJ65BbbF.js";import"./cmsTags-0YlQk5ji.js";import"./cmsFlags-B3rSyn5u.js";import"./pinia-DwJf82dV.js";import"./js-cookie-Cz0CWeBA.js";import"./nprogress-Scw6VIZr.js";import"./axios-B62VicFi.js";import"./qs-D41HS0uK.js";import"./side-channel-DytTmCZg.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-DbsE5JW_.js";import"./crypto-js-oM26lpET.js";import"./side-channel-list-C_7Uk4RP.js";import"./side-channel-map-BUokNT8A.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-bSkmVQJL.js";import"./vue-i18n-BkLk9Ti1.js";import"./vue-iNFchrNl.js";import"./vue-clipboard3-Dr8V5xR7.js";import"./clipboard-DzJOpNE6.js";import"./vue-grid-layout-CIPjP0XN.js";import"./vue-simple-uploader-j6sX1TKo.js";import"./vue-ueditor-wrap-CJ5gjsd3.js";const n=r({name:"apiV1CmsArticleEdit",components:{apiV1CmsCmsArticleEdit:o}});function a(c,d,l,f,_,C){const t=i("apiV1CmsCmsArticleEdit");return p(),m("div",null,[e(t,{ref:"editRef"},null,512)])}const xt=s(n,[["render",a]]);export{xt as default};
