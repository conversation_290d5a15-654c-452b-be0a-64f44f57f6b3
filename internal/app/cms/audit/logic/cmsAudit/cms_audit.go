// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-05-07 08:21:11
// 生成路径: internal/app/cms/audit/logic/cms_audit.go
// 生成人：zhanghui
// desc:文章审核
// company:云南奇讯科技有限公司
// ==========================================================================

package cmsAudit

import (
	"context"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/os/gtime"
	systemService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/libCms"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/cms/audit/dao"
	"github.com/tiger1103/gfast/v3/internal/app/cms/audit/model"
	"github.com/tiger1103/gfast/v3/internal/app/cms/audit/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/cms/audit/service"
	cmsDao "github.com/tiger1103/gfast/v3/internal/app/cms/dao"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterCmsAudit(New())
}

func New() service.ICmsAudit {
	return &sCmsAudit{}
}

type sCmsAudit struct{}

func (s *sCmsAudit) List(ctx context.Context, req *model.CmsAuditSearchReq) (listRes *model.CmsAuditSearchRes, err error) {
	listRes = new(model.CmsAuditSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.CmsAudit.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.CmsAudit.Columns().Id+" = ?", req.Id)
		}
		if req.ArticleId != "" {
			m = m.Where(dao.CmsAudit.Columns().ArticleId+" = ?", gconv.Uint(req.ArticleId))
		}
		if req.CategoryId != "" {
			m = m.Where(dao.CmsAudit.Columns().CategoryId+" = ?", gconv.Uint(req.CategoryId))
		}
		if req.UserId != "" {
			m = m.Where(dao.CmsAudit.Columns().UserId+" = ?", gconv.Uint(req.UserId))
		}
		if req.StepId != "" {
			m = m.Where(dao.CmsAudit.Columns().StepId+" = ?", gconv.Uint(req.StepId))
		}
		if req.NextStepId != "" {
			m = m.Where(dao.CmsAudit.Columns().NextStepId+" = ?", gconv.Int(req.NextStepId))
		}
		if req.Status != "" {
			m = m.Where(dao.CmsAudit.Columns().Status+" = ?", req.Status)
		}
		if req.Content != "" {
			m = m.Where(dao.CmsAudit.Columns().Content+" = ?", req.Content)
		}
		if req.Action != "" {
			m = m.Where(dao.CmsAudit.Columns().Action+" = ?", req.Action)
		}
		if req.AuditedAt != "" {
			m = m.Where(dao.CmsAudit.Columns().AuditedAt+" = ?", gconv.Time(req.AuditedAt))
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.CmsAudit.Columns().CreatedAt+" >=? AND "+dao.CmsAudit.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id desc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.CmsAuditListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.CmsAuditListRes, len(res))
		// 审核步骤
		cmsAuditStepMap, _ := service.CmsAuditStep().GetCmsAuditStepMap(ctx)
		for k, v := range res {
			listRes.List[k] = &model.CmsAuditListRes{
				Id:            v.Id,
				ArticleId:     v.ArticleId,
				CategoryId:    v.CategoryId,
				UserId:        v.UserId,
				StepId:        v.StepId,
				NextStepId:    v.NextStepId,
				Status:        v.Status,
				Content:       v.Content,
				Action:        v.Action,
				AuditedAt:     v.AuditedAt,
				CreatedAt:     v.CreatedAt,
				StepTitle:     cmsAuditStepMap[gconv.String(v.StepId)],
				NextStepTitle: cmsAuditStepMap[gconv.String(v.NextStepId)],
				UserInfo:      v.UserInfo,
				ArticleInfo:   v.ArticleInfo,
				CategoryInfo:  v.CategoryInfo,
				StepInfo:      v.StepInfo,
				PreviewUrl:    libCms.GetPreviewUrl(ctx, "show", v.ArticleInfo.Url, gconv.Int(v.ArticleId)),
			}
		}
	})
	return
}

func (s *sCmsAudit) GetById(ctx context.Context, id uint) (res *model.CmsAuditInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.CmsAudit.Ctx(ctx).WithAll().Where(dao.CmsAudit.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

// 审核
func (s *sCmsAudit) Audit(ctx context.Context, req *model.CmsAuditEditReq) (err error) {
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		err = g.Try(ctx, func(ctx context.Context) {
			row, err := dao.CmsAudit.Ctx(ctx).WherePri(req.Id).One()
			liberr.ErrIsNil(ctx, err, "获取审核数据失败")
			if row.IsEmpty() {
				liberr.ErrIsNil(ctx, err, "审核数据不存在")
			}
			if !s.authAudit(ctx, req.Id) {
				liberr.ErrIsNil(ctx, err, "没有审核权限")
			}
			if row["status"].String() == "1" && row["next_step_id"].String() == "0" {
				liberr.ErrIsNil(ctx, err, "已审核通过，不能重复审核")
			}
			if row["status"].String() == "2" && row["next_step_id"].String() == "0" {
				liberr.ErrIsNil(ctx, err, "已驳回，不能重复审核")
			}

			var steps []*model.CmsAuditStepListRes
			err = dao.CmsAuditStep.Ctx(ctx).Order("step asc").Scan(&steps)
			liberr.ErrIsNil(ctx, err, "获取审核步骤失败")
			cmsAuditStepMap := make(map[string]map[string]string, 0)
			for _, v := range steps {
				cmsAuditStepMap[gconv.String(v.Id)] = map[string]string{
					"title": v.Title,
					"step":  gconv.String(v.Step),
					"type":  v.Type,
				}
			}
			status := req.Status
			content := req.Content
			stepId := row["next_step_id"].Uint()

			userId := systemService.Context().GetUserId(ctx)
			//审核通过
			if status == "1" {
				content = ""
				//计算下个步骤
				nextStepId := "0"
				for k, v := range cmsAuditStepMap {
					if k == gconv.String(stepId) {
						if v["type"] != "end" {
							nextStepId = gconv.String(gconv.Uint(k) + 1)
							break
						}
					}
				}
				if nextStepId == "0" && cmsAuditStepMap[gconv.String(row["next_step_id"])]["type"] == "end" { //终审
					_, err = cmsDao.CmsArticle.Ctx(ctx).Where(cmsDao.CmsArticle.Columns().Id, row["article_id"]).Update(g.Map{
						cmsDao.CmsArticle.Columns().Status: 1,
					})
				} else {
					_, err = cmsDao.CmsArticle.Ctx(ctx).Where(cmsDao.CmsArticle.Columns().Id, row["article_id"]).Update(g.Map{
						cmsDao.CmsArticle.Columns().Status: 2,
					})
				}
				_, err = dao.CmsAudit.Ctx(ctx).WherePri(req.Id).Update(do.CmsAudit{
					UserId:     userId,
					StepId:     stepId,
					NextStepId: nextStepId,
					Status:     status,
					Content:    content,
					AuditedAt:  gtime.Now(),
				})
				liberr.ErrIsNil(ctx, err, "审核失败")
				// 保存审核记录
				_, err = dao.CmsAuditRecord.Ctx(ctx).Insert(do.CmsAuditRecord{
					AuditId:    req.Id,
					ArticleId:  row["article_id"].Uint(),
					CategoryId: row["category_id"].Uint(),
					UserId:     userId,
					StepId:     stepId,
					NextStepId: nextStepId,
					Status:     status,
					Content:    content,
					Action:     row["action"].String(),
					AuditedAt:  gtime.Now(),
				})
				liberr.ErrIsNil(ctx, err, "保存审核记录失败")

			} else { //驳回
				//计算下个步骤
				nextStepId := "0"
				//for k, v := range cmsAuditStepMap {
				//	if k == gconv.String(stepId) {
				//		if v["type"] == "first" {
				//			nextStepId = gconv.String(gconv.Uint(k))
				//			break
				//		}
				//	}
				//}
				// 文章状态置为驳回
				_, err = cmsDao.CmsArticle.Ctx(ctx).Where(cmsDao.CmsArticle.Columns().Id, row["article_id"]).Update(g.Map{
					cmsDao.CmsArticle.Columns().Status: 3,
				})

				_, err = dao.CmsAudit.Ctx(ctx).WherePri(req.Id).Update(do.CmsAudit{
					UserId:     userId,
					StepId:     "0",
					NextStepId: nextStepId,
					Status:     2, // 驳回
					Content:    content,
					AuditedAt:  gtime.Now(),
				})
				liberr.ErrIsNil(ctx, err, "审核失败")
				// 保存审核记录
				_, err = dao.CmsAuditRecord.Ctx(ctx).Insert(do.CmsAuditRecord{
					AuditId:    req.Id,
					ArticleId:  row["article_id"].Uint(),
					CategoryId: row["category_id"].Uint(),
					UserId:     userId,
					StepId:     row["next_step_id"].Uint(),
					NextStepId: nextStepId,
					Status:     2, // 驳回
					Content:    content,
					Action:     row["action"].String(),
					AuditedAt:  gtime.Now(),
				})
				liberr.ErrIsNil(ctx, err, "保存审核记录失败")
			}

		})
		return err
	})
	return
}

func (s *sCmsAudit) Delete(ctx context.Context, ids []uint) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.CmsAudit.Ctx(ctx).Delete(dao.CmsAudit.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

// 判断是否有审核权限

func (s *sCmsAudit) authAudit(ctx context.Context, id uint) (res bool) {
	res = false
	row, err := dao.CmsAudit.Ctx(ctx).WherePri(id).One()
	liberr.ErrIsNil(ctx, err, "获取审核数据失败")
	if row.IsEmpty() {
		return false
	}
	if libCms.IsSuperAdmin(ctx) {
		return true
	}
	stepId := row["next_step_id"].Uint()
	// 拥有权限的审核步骤
	var rows []*model.CmsAuditCategoryListRes
	err = dao.CmsAuditCategory.Ctx(ctx).WithAll().
		Where(dao.CmsAuditCategory.Columns().CategoryId, row["category_id"]).
		Scan(rows)
	liberr.ErrIsNil(ctx, err, "获取审核分类数据失败")
	for _, v := range rows {
		if v.AuditUser.StepId == stepId && v.AuditUser.UserId == gconv.Uint(systemService.Context().GetUserId(ctx)) {
			res = true
			break
		}
	}
	return

}
