/*
* @desc:短信发送测试
* @company:云南奇讯科技有限公司
* @Author: y<PERSON><PERSON><PERSON><PERSON><<EMAIL>>
* @Date:   2023/12/5 17:32
 */

package controller

import (
	"context"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/api/v1/plugins/sms"
	commonController "github.com/tiger1103/gfast/v3/internal/app/common/controller"
	"github.com/tiger1103/gfast/v3/internal/app/plugins/sms/dao"
	"github.com/tiger1103/gfast/v3/internal/app/plugins/sms/driver"
	"github.com/tiger1103/gfast/v3/internal/app/plugins/sms/driver/service"
)

var Sms = new(smsController)

type smsController struct {
	commonController.BaseController
}

func (c *smsController) SendVerify(ctx context.Context, req *sms.SendVerifyReq) (res *sms.SendVerifyRes, err error) {
	err = c.getCommonSms(ctx)
	if err != nil {
		return
	}
	err = driver.CommonSms.SendVerifyCode(&driver.SendSmsArgs{
		Mobiles: []string{req.Mobile},
		Ctx:     ctx,
	})
	return
}

func (c *smsController) SendContent(ctx context.Context, req *sms.SendContentReq) (res *sms.SendContentRes, err error) {
	err = driver.CommonSms.SendTemplateSms(&driver.SendSmsArgs{
		Mobiles: req.Mobile,
		Params:  req.Params,
		Ctx:     ctx,
	})
	return
}

func (c *smsController) CheckVerifyCode(ctx context.Context, req *sms.CheckVerifyReq) (res *sms.CheckVerifyRes, err error) {
	err = driver.CommonSms.CheckVerifyCode(ctx, req.Mobile, req.Code)
	return
}

func (c *smsController) getCommonSms(ctx context.Context) (err error) {
	list, err := dao.PluginSmsConfig.Ctx(ctx).Where("status", 1).One()
	if err != nil {
		return
	}
	if list.IsEmpty() {
		err = gerror.New("短信配置信息不存在")
		return
	}
	g.Log().Debug(ctx, list.Map()["sms_type"])
	switch list.Map()["sms_type"] {
	case "yunpian":
		driver.CommonSms = service.Yunpian
	case "aly":
		driver.CommonSms = service.Aly
	case "yunxin":
		driver.CommonSms = service.Yunxin
	case "ten":
		driver.CommonSms = service.Ten
	default:
		driver.CommonSms = service.Demo
	}
	return
}
