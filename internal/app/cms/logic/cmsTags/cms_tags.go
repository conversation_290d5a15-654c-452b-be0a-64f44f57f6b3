// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2023-10-17 14:50:36
// 生成路径: internal/app/cms/logic/cms_tags.go
// 生成人：yxf
// desc:CMS标签
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/internal/app/cms/dao"
	"github.com/tiger1103/gfast/v3/internal/app/cms/model"
	"github.com/tiger1103/gfast/v3/internal/app/cms/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/cms/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterCmsTags(New())
}

func New() service.ICmsTags {
	return &sCmsTags{}
}

type sCmsTags struct{}

func (s *sCmsTags) List(ctx context.Context, req *model.CmsTagsSearchReq) (listRes *model.CmsTagsSearchRes, err error) {
	listRes = new(model.CmsTagsSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.CmsTags.Ctx(ctx).WithAll()
		if req.Name != "" {
			m = m.Where(dao.CmsTags.Columns().Name+" like ?", "%"+req.Name+"%")
		}
		if len(req.TagIds) > 0 {
			m = m.WhereIn(dao.CmsTags.Columns().Id, req.TagIds)
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "sort ASC,id ASC"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.CmsTagsListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.CmsTagsListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.CmsTagsListRes{
				Id:        v.Id,
				Name:      v.Name,
				CreatedAt: v.CreatedAt,
				Sort:      v.Sort,
			}
		}
	})
	return
}

func (s *sCmsTags) GetById(ctx context.Context, id uint) (res *model.CmsTagsInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.CmsTags.Ctx(ctx).WithAll().Where(dao.CmsTags.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sCmsTags) Add(ctx context.Context, req *model.CmsTagsAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.CmsTags.Ctx(ctx).Insert(do.CmsTags{
			Name: req.Name,
			Sort: req.Sort,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sCmsTags) Edit(ctx context.Context, req *model.CmsTagsEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.CmsTags.Ctx(ctx).WherePri(req.Id).Update(do.CmsTags{
			Name: req.Name,
			Sort: req.Sort,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sCmsTags) Delete(ctx context.Context, ids []uint) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.CmsTags.Ctx(ctx).Delete(dao.CmsTags.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
