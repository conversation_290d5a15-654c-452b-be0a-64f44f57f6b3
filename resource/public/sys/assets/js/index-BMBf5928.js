import{d as q,f as P,r as b,X as Q,ad as G,h as H,ag as r,c as C,o as v,P as e,H as a,a as X,I as j,u as l,a4 as V,O as J,a6 as W,G as Z,L as c,Q as ee}from"./@vue-C21YZbHS.js";import{_ as te,g as oe,d as ae}from"./editConfig.vue_vue_type_script_setup_true_lang-BjBMWHgt.js";import{a as x,E as le}from"./element-plus-CUmVNDWO.js";import"./index-BGmsw1a8.js";import"./pinia-DwJf82dV.js";import"./vue-router-BDT9dCQ7.js";import"./js-cookie-Cz0CWeBA.js";import"./@element-plus-_Cc-TEQX.js";import"./nprogress-Scw6VIZr.js";import"./@intlify-D8kAWvSi.js";import"./axios-B62VicFi.js";import"./qs-D41HS0uK.js";import"./side-channel-DytTmCZg.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-DbsE5JW_.js";import"./crypto-js-oM26lpET.js";import"./side-channel-list-C_7Uk4RP.js";import"./side-channel-map-BUokNT8A.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-bSkmVQJL.js";import"./vue-i18n-BkLk9Ti1.js";import"./vue-iNFchrNl.js";import"./vue-clipboard3-Dr8V5xR7.js";import"./clipboard-DzJOpNE6.js";import"./mitt-DJ65BbbF.js";import"./vue-grid-layout-CIPjP0XN.js";import"./vue-simple-uploader-j6sX1TKo.js";import"./vue-ueditor-wrap-CJ5gjsd3.js";import"./lodash-es-S0Y0Up6J.js";import"./@vueuse-D_5IXmcI.js";import"./@popperjs-D_chPuIy.js";import"./@ctrl-r5W6hzzQ.js";import"./dayjs-CepoAVPu.js";import"./async-validator-9PlIezaS.js";import"./memoize-one-BdPwpGay.js";import"./normalize-wheel-es-BQoi3Ox2.js";import"./@floating-ui-DwceP2Gb.js";const ne={class:"system-dic-container"},ie={class:"system-user-search mb15"},ot=q({name:"apiV1SystemConfigList",__name:"index",setup(re){const{proxy:w}=P();b();const _=b(),D=b(),{sys_yes_no:y}=w.useDict("sys_yes_no"),d=Q({ids:[],tableData:{data:[],total:0,loading:!1,param:{dateRange:[],pageNum:1,pageSize:10,configName:"",configKey:"",configType:""}}}),{tableData:n}=G(d),N=()=>{s()},s=()=>{oe(d.tableData.param).then(i=>{d.tableData.data=i.data.list,d.tableData.total=i.data.total})},R=()=>{_.value.openDialog()},h=i=>{_.value.openDialog(i)},k=i=>{let t="你确定要删除所选数据？",m=[];if(i?(t=`此操作将永久删除用户：“${i.configName}”，是否继续?`,m=[i.configId]):m=d.ids,m.length===0){x.error("请选择要删除的数据。");return}le.confirm(t,"提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(()=>{ae(m).then(()=>{x.success("删除成功"),s()})}).catch(()=>{})};H(()=>{N()});const T=i=>{i&&(i.resetFields(),s())},z=i=>{d.ids=i.map(t=>t.configId)},S=i=>w.selectDictLabel(l(y),i.configType);return(i,t)=>{const m=r("el-input"),f=r("el-form-item"),K=r("el-option"),B=r("el-select"),L=r("el-date-picker"),U=r("ele-Search"),g=r("el-icon"),u=r("el-button"),Y=r("ele-Refresh"),$=r("ele-FolderAdd"),F=r("ele-Delete"),I=r("el-form"),p=r("el-table-column"),M=r("el-table"),O=r("pagination"),A=r("el-card");return v(),C("div",ne,[e(A,{shadow:"hover"},{default:a(()=>[X("div",ie,[e(I,{model:l(n).param,ref_key:"queryRef",ref:D,inline:!0,"label-width":"68px"},{default:a(()=>[e(f,{label:"参数名称",prop:"configName"},{default:a(()=>[e(m,{modelValue:l(n).param.configName,"onUpdate:modelValue":t[0]||(t[0]=o=>l(n).param.configName=o),placeholder:"请输入参数名称",clearable:"",onKeyup:V(s,["enter","native"])},null,8,["modelValue"])]),_:1}),e(f,{label:"参数键名",prop:"configKey"},{default:a(()=>[e(m,{modelValue:l(n).param.configKey,"onUpdate:modelValue":t[1]||(t[1]=o=>l(n).param.configKey=o),placeholder:"请输入参数键名",clearable:"",onKeyup:V(s,["enter","native"])},null,8,["modelValue"])]),_:1}),e(f,{label:"系统内置",prop:"configType",style:{width:"200px"}},{default:a(()=>[e(B,{modelValue:l(n).param.configType,"onUpdate:modelValue":t[2]||(t[2]=o=>l(n).param.configType=o),placeholder:"系统内置",clearable:"",style:{width:"240px"}},{default:a(()=>[(v(!0),C(J,null,W(l(y),o=>(v(),Z(K,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(f,{label:"创建时间",prop:"dateRange"},{default:a(()=>[e(L,{modelValue:l(n).param.dateRange,"onUpdate:modelValue":t[3]||(t[3]=o=>l(n).param.dateRange=o),style:{width:"240px"},"value-format":"YYYY-MM-DD",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},null,8,["modelValue"])]),_:1}),e(f,null,{default:a(()=>[e(u,{size:"default",type:"primary",class:"ml10",onClick:s},{default:a(()=>[e(g,null,{default:a(()=>[e(U)]),_:1}),t[8]||(t[8]=c(" 查询 "))]),_:1}),e(u,{size:"default",onClick:t[4]||(t[4]=o=>T(D.value))},{default:a(()=>[e(g,null,{default:a(()=>[e(Y)]),_:1}),t[9]||(t[9]=c(" 重置 "))]),_:1}),e(u,{size:"default",type:"success",class:"ml10",onClick:R},{default:a(()=>[e(g,null,{default:a(()=>[e($)]),_:1}),t[10]||(t[10]=c(" 新增参数 "))]),_:1}),e(u,{size:"default",type:"danger",class:"ml10",onClick:t[5]||(t[5]=o=>k(null))},{default:a(()=>[e(g,null,{default:a(()=>[e(F)]),_:1}),t[11]||(t[11]=c(" 删除参数 "))]),_:1})]),_:1})]),_:1},8,["model"])]),e(M,{data:l(n).data,style:{width:"100%"},onSelectionChange:z},{default:a(()=>[e(p,{type:"selection",width:"55",align:"center"}),e(p,{label:"参数主键",align:"center",prop:"configId"}),e(p,{label:"参数名称",align:"center",prop:"configName","show-overflow-tooltip":!0}),e(p,{label:"参数键名",align:"center",prop:"configKey","show-overflow-tooltip":!0}),e(p,{label:"参数键值",align:"center",prop:"configValue"}),e(p,{label:"系统内置",align:"center",prop:"configType",formatter:S}),e(p,{label:"备注",align:"center",prop:"remark","show-overflow-tooltip":!0}),e(p,{label:"创建时间",align:"center",prop:"createdAt",width:"180"}),e(p,{label:"操作",width:"200"},{default:a(o=>[e(u,{size:"small",text:"",type:"primary",onClick:E=>h(o.row)},{default:a(()=>t[12]||(t[12]=[c("修改")])),_:2},1032,["onClick"]),e(u,{size:"small",text:"",type:"primary",onClick:E=>k(o.row)},{default:a(()=>t[13]||(t[13]=[c("删除")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),j(e(O,{total:l(n).total,page:l(n).param.pageNum,"onUpdate:page":t[6]||(t[6]=o=>l(n).param.pageNum=o),limit:l(n).param.pageSize,"onUpdate:limit":t[7]||(t[7]=o=>l(n).param.pageSize=o),onPagination:s},null,8,["total","page","limit"]),[[ee,l(n).total>0]])]),_:1}),e(te,{ref_key:"editDicRef",ref:_,onDataList:s,sysYesNoOptions:l(y)},null,8,["sysYesNoOptions"])])}}});export{ot as default};
