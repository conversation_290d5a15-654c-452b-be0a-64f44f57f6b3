// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2023-03-09 16:50:38
// 生成路径: internal/app/ucenter/dao/ucenter_scores.go
// 生成人：yxf
// desc:用户积分
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ucenter/dao/internal"
)

// ucenterScoresDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type ucenterScoresDao struct {
	*internal.UCenterScoresDao
}

var (
	// UCenterScores is globally public accessible object for table tools_gen_table operations.
	UCenterScores = ucenterScoresDao{
		internal.NewUCenterScoresDao(),
	}
)

// Fill with you ideas below.
