// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-05-06 17:48:31
// 生成路径: internal/app/cms/audit/model/entity/cms_audit_step.go
// 生成人：zhanghui
// desc:审核步骤
// company:云南奇讯科技有限公司
// ==========================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// CmsAuditStep is the golang structure for table cms_audit_step.
type CmsAuditStep struct {
	gmeta.Meta `orm:"table:cms_audit_step"`
	Id         uint        `orm:"id,primary" json:"id"`        //
	Title      string      `orm:"title" json:"title"`          // 步骤名称
	Step       uint        `orm:"step" json:"step"`            // 步骤
	Type       string      `orm:"type" json:"type"`            // 类别
	CreatedAt  *gtime.Time `orm:"created_at" json:"createdAt"` // 创建时间
	UpdatedAt  *gtime.Time `orm:"updated_at" json:"updatedAt"` // 更新时间
}
