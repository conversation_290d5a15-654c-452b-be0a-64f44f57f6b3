// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2023-02-17 16:46:34
// 生成路径: internal/app/cms/service/cms_ad.go
// 生成人：yxf
// desc:广告
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"
	"github.com/tiger1103/gfast/v3/api/v1/cms"
	"github.com/tiger1103/gfast/v3/internal/app/cms/model"
)

type ICmsAd interface {
	List(ctx context.Context, req *cms.CmsAdSearchReq) (res *cms.CmsAdSearchRes, err error)
	GetById(ctx context.Context, Id uint) (res *model.CmsAdInfoRes, err error)
	Add(ctx context.Context, req *cms.CmsAdAddReq) (err error)
	Edit(ctx context.Context, req *cms.CmsAdEditReq) (err error)
	Delete(ctx context.Context, Id []uint) (err error)
}

var localCmsAd ICmsAd

func CmsAd() ICmsAd {
	if localCmsAd == nil {
		panic("implement not found for interface ICmsAd, forgot register?")
	}
	return localCmsAd
}

func RegisterCmsAd(i ICmsAd) {
	localCmsAd = i
}
