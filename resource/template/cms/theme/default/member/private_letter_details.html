<style>
    .letter-list {
        display: flex;
        flex-direction: column;
        margin-top: 50px;
    }
    .letter-item {
        display: flex;
        padding-bottom:20px;
    }
    .letter-item:not(:first-child){
        border-top: 1px solid #efefef;
        padding-top: 20px;
    }
    .letter-avatar {
        width: 60px;
        height: 60px;
        margin-right: 15px;
    }
    .letter-avatar img {
        width:100%;
        height:100%;
        border-radius: 50%;
    }
    .letter-right {
        width:100%;
    }
    .nickname {
        font-size: 16px;
        color: #333;
        font-weight: 500!important;
    }
    .letter-content {
        position: relative;
        padding-right: 50px;
        font-size: 14px;
        line-height: 24px;
        color: #999;
        overflow: hidden;
        -o-text-overflow: ellipsis;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .letter-bottom {
        display: flex;
        justify-content:space-between;
        font-size: 14px;
        color: #999;
    }
    .letter-mark {
        color: #1e70bf;
        cursor: pointer;
    }
    .letter-action {
        cursor: pointer;
    }
    #page {
        text-align: center;
    }
</style>
<div id="wrap">
    <div class="wrap container">
        <div class="main main-full">
            <article class="post-18 page type-page status-publish hentry" id="post-18">
                <div class="entry">
                    <div class="entry-content">
                        <div class="member-account-wrap">
                            <!-- 左侧导航 -->
                            ${include "widget/profile_left.html" .}
                            <div class="member-account-content">
                                <h2 class="member-account-title">与${.friendInfo.nickname}的私信</h2>
                                <form role="form" id="sendMessageForm" action="/ucenter/member/SendMessage"  method="post">
                                    <input type="hidden" name="toID" value="${.friendInfo.id}">
                                    <div class="form-group">
                                        <textarea name="content" class="form-control" rows="5" placeholder="请输入内容，最多250字"></textarea>
                                    </div>
                                    <div class="form-group">
                                        <div class="col-sm-10">
                                            <button type="submit" class="btn btn-default">发送私信</button>
                                        </div>
                                    </div>
                                </form>
                                <ul class="letter-list" id="letter-list"></ul>
                                <div id="page" style="margin-top:20px;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </article>
        </div>
    </div>
</div>

<script>
    const memberId  = ${.Session.memberInfo.Id}
    const friendId =  ${.friendInfo.id}
    const   limit = 10

    template.defaults.imports.friend = function (member, members) {
        if (currentUserId() === 0) {
            return {}
        }
        // console.log(member, members)
        var friendId = member.befrom == currentUserId() ? member.beto : member.befrom
        var result = _.find(members, {id: friendId})
        return result ? result : {}
    }

    template.defaults.imports.userInfo = function (id, members) {
        if (!_.isNumber(id) || id === 0) {
            return {}
        }
        var result = _.find(members, {id: id})
        return result ? result : {}
    }


    $(function() {

        // 发送私信
        $("#sendMessageForm").ajaxForm({
            clearForm: true,
            restForm: true,
            beforeSubmit: function (arr, obj) {
                // console.log(arr, obj)
                if($("textarea[name=content]", obj).val() === "") {
                    layer.msg("请输入内容", {icon: 5});
                    return false
                }
                layer.load()
                return true
            },
            success: function (data) {
                layer.closeAll()
                var {code, message} = data
                if (code === 0) {
                    layer.msg('提交成功!', {icon: 1});
                    refresh && refresh()
                    return
                }
                layer.msg(message, {icon: 5});
                return
            }
        })

        // 标记消息已读
        $.post(APIBASE+"/ucenter/member/privateLetterRead", {id: getFriendId()}).done(function (res) {
            var {code, message} = res
            if (code !== 0) {
                layer.msg(message, {icon: 5});
            }
        })


        layui.use('laypage', function () {
            var laypage = layui.laypage
            refresh()
            function refresh () {
                paging(1, limit).done(function (data) {
                    var {list, total, users} = data
                    renderList({list, users})

                    if ((total / limit) > 1 ) {
                        laypage.render({
                            elem: 'page' //注意，这里的 test1 是 ID，不用加 # 号
                            ,count: total //数据总数，从服务端得到
                            ,limit: limit
                            ,jump: function(obj, first){
                                if (!first) {
                                    paging(obj.curr, obj.limit).done(function (data) {
                                        var {list, users} = data
                                        renderList({list, users})
                                    })
                                }
                            }
                        });
                    }

                })
            }
            window.refresh = refresh
        })


        function renderInit () {
            $(".letter-action-delete").on("click", function () {
                // console.log($(this).data("id"))
                var id = $(this).data("id")
                layer.confirm('确定删除此条信息?', {icon: 3, title:'提示'}, function(index){
                    //do something
                    layer.close(index);
                    $.get(APIBASE+"ucenter/member/privateLetterDelSub", {id}).done(function (res) {
                        var {code, message} = res
                        if (code !== 0) {
                            return layer.msg(message, {icon: 5});
                        }
                        refresh && refresh()
                        return layer.msg("操作成功", {icon: 1});
                    })

                });

            })
        }

        function renderList (data) {
            var tpl = `
{{each list}}
<li class="letter-item">
    <div class="letter-avatar"><img src="{{userInfo($value.befrom, users).avatar || ''}}" alt=""></div>
    <div class="letter-right">
        <div class="nickname">{{userInfo($value.befrom, users).nickname || ''}}</div>
        <div class="letter-content">{{$value.content}}</div>
        <div class="letter-bottom"><div><span>{{$value.createdAt}}</span></div><div><span class="letter-action letter-action-delete" data-id="{{$value.id}}"><i class="layui-icon layui-icon-delete"></i> 删除 </span></div></div>
    </div>
</li>
{{/each}}
`
            $("#letter-list").html(template.render(tpl, data))
            renderInit()
        }

        function currentUserId () {
            return memberId ? memberId : 0
        }

        function getFriendId () {
            return friendId ? friendId : 0
        }

        function paging (page, limit) {
            var dtd = $.Deferred()
            $.get(APIBASE+"ucenter/member/privateLetterListByMark", {toID: getFriendId(), page: page, pageSize: limit}).done(function (res) {
                var {code, data, message} = res
                if (code !== 0) {
                    layer.msg(message, {icon: 5})
                    return dtd.reject(message)
                }
                return dtd.resolve(data)
            })
            return dtd.promise()
        }
    })


</script>