// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2023-01-30 14:15:09
// 生成路径: internal/app/system/logic/modules_info.go
// 生成人：yxf
// desc:模型信息
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"errors"
	"fmt"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/api/v1/system"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/internal/app/system/dao"
	"github.com/tiger1103/gfast/v3/internal/app/system/model"
	"github.com/tiger1103/gfast/v3/internal/app/system/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/liberr"
	"strings"
)

func init() {
	service.RegisterModulesInfo(New())
}

func New() *sModulesInfo {
	return &sModulesInfo{}
}

type sModulesInfo struct{}

func (s *sModulesInfo) List(ctx context.Context, req *system.ModulesInfoSearchReq) (listRes *system.ModulesInfoSearchRes, err error) {
	listRes = new(system.ModulesInfoSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.ModulesInfo.Ctx(ctx).WithAll()
		if req.Name != "" {
			m = m.Where(dao.ModulesInfo.Columns().Name+" like ?", "%"+req.Name+"%")
		}
		if req.Sort != "" {
			m = m.Where(dao.ModulesInfo.Columns().Sort+" = ?", gconv.Int(req.Sort))
		}
		if req.Status != "" {
			m = m.Where(dao.ModulesInfo.Columns().Status+" = ?", gconv.Int(req.Status))
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "sort asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.ModulesInfoInfoRes
		err = m.Fields(system.ModulesInfoSearchRes{}).Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.ModulesInfoListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.ModulesInfoListRes{
				Id:        v.Id,
				Name:      v.Name,
				Sort:      v.Sort,
				TableName: v.TableName,
				CreatedAt: v.CreatedAt,
				Status:    v.Status,
				Fields:    v.Fields,
			}
		}
	})
	return
}

func (s *sModulesInfo) GetById(ctx context.Context, id uint) (res *model.ModulesInfoInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.ModulesInfo.Ctx(ctx).WithAll().Where(dao.ModulesInfo.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sModulesInfo) Add(ctx context.Context, req *system.ModulesInfoAddReq) (err error) {
	if existTableName := s.IsExistTableName(ctx, req.TableName); existTableName {
		return errors.New("表名：" + req.TableName + "在数据库中已被使用")
	}
	count, _ := dao.ModulesInfo.Ctx(ctx).Count(g.Map{"table_name": req.TableName})
	if count > 0 {
		return errors.New("表名：" + req.TableName + "重复")
	}
	err = g.Try(ctx, func(ctx context.Context) {
		err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
			id, err := dao.ModulesInfo.Ctx(ctx).InsertAndGetId(do.ModulesInfo{
				Name:      req.Name,
				Sort:      req.Sort,
				TableName: req.TableName,
				Status:    req.Status,
				Fields:    req.Fields,
			})
			liberr.ErrIsNil(ctx, err, "添加失败")
			err = s.CreateModule(ctx, uint(id))
			return err
		})
		liberr.ErrIsNil(ctx, err, "创建失败")
	})
	return
}

// IsExistTableName 检查表是否存在
func (s *sModulesInfo) IsExistTableName(ctx context.Context, tableName string) bool {
	dbConfig := g.DB().GetConfig()
	switch dbConfig.Type {
	case "mysql":
		res, _ := g.DB().Query(ctx, "SHOW TABLES FROM `"+dbConfig.Name+"`")
		for _, item := range res {
			tName := item["Tables_in_"+dbConfig.Name].String()
			if tName == tableName {
				return true
			}
		}
	case "pgsql":
		res, _ := g.DB().Query(ctx, "SELECT table_name FROM information_schema.tables WHERE table_schema = ?", dbConfig.Namespace)
		for _, item := range res {
			tName := item["table_name"].String()
			if strings.EqualFold(tName, tableName) {
				return true
			}
		}
	}
	return false
}

func (s *sModulesInfo) Edit(ctx context.Context, req *system.ModulesInfoEditReq) (err error) {
	//if existTableName := s.IsExistTableName(ctx, req.TableName); existTableName {
	//	return errors.New("表名：" + req.TableName + "已存在")
	//}
	count, _ := dao.ModulesInfo.Ctx(ctx).Count(g.Map{"id<>?": req.Id, "table_name": req.TableName})
	if count > 0 {
		return errors.New("表名：" + req.TableName + "重复")
	}
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.ModulesInfo.Ctx(ctx).WherePri(req.Id).Update(do.ModulesInfo{
			Name:      req.Name,
			Sort:      req.Sort,
			TableName: req.TableName,
			Status:    req.Status,
			Fields:    req.Fields,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sModulesInfo) Delete(ctx context.Context, ids []uint) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
			for _, id := range ids {
				var moduleInfo *model.ModulesInfoInfoRes
				dao.ModulesInfo.Ctx(ctx).Where("id", id).Scan(&moduleInfo)
				// 删除生成的表
				if moduleInfo != nil {
					tx.Exec("DROP TABLE `" + moduleInfo.TableName + "`")
				}
			}
			// 删除modules_field内容
			_, err = dao.ModulesField.Ctx(ctx).Delete("module_id in (?)", ids)
			if err != nil {
				return err
			}
			_, err = dao.ModulesInfo.Ctx(ctx).Delete(dao.ModulesInfo.Columns().Id+" in (?)", ids)
			return err
		})
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

func (s *sModulesInfo) CreateModule(ctx context.Context, id uint) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		// 数据库配置
		dbConfig := g.DB().GetConfig()
		var moduleInfo *model.ModulesInfoInfoRes
		err = dao.ModulesInfo.Ctx(ctx).WithAll().WherePri(id).Scan(&moduleInfo)
		liberr.ErrIsNil(ctx, err, "模型信息错误")
		if existTableName := s.IsExistTableName(ctx, moduleInfo.TableName); existTableName {
			err = errors.New("表名：" + moduleInfo.TableName + "在数据库中已被使用")
			liberr.ErrIsNil(ctx, err)
		}
		if moduleInfo != nil {
			var moduleInfoFields []*model.ModulesFieldInfoRes
			err = dao.ModulesField.Ctx(ctx).WithAll().Where(dao.ModulesField.Columns().ModuleId, id).Order("sort asc").Scan(&moduleInfoFields)
			liberr.ErrIsNil(ctx, err, "模型信息错误")
			// 创建新表
			switch dbConfig.Type {
			case "mysql":
				fieldStr := []string{"`relation_id` BIGINT (20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '关联表ID'", "`relation_table` VARCHAR (100) NOT NULL DEFAULT '' COMMENT '关联表名'", "`deleted_at` DATETIME COMMENT '删除日期'"}
				fieldStr = append(fieldStr, "UNIQUE INDEX `unique` (`relation_id`, `relation_table`) COMMENT '唯一索引'")
				createTableSql := fmt.Sprintf("CREATE TABLE `%s` (%s)ENGINE=INNODB COMMENT='"+moduleInfo.Name+"';", dbConfig.Prefix+moduleInfo.TableName, strings.Join(fieldStr, ","))
				_, err = g.DB().Exec(ctx, createTableSql)
				liberr.ErrIsNil(ctx, err)
			case "pgsql":
				fieldStr := []string{"\"relation_id\" INT8 NOT NULL DEFAULT 0", "\"relation_table\" VARCHAR (100) COLLATE \"pg_catalog\".\"default\" NOT NULL ", "\"deleted_at\" timestamp(6)"}
				fieldStr = append(fieldStr, "CONSTRAINT \""+moduleInfo.TableName+"_relation_id_relation_table_key\" UNIQUE (\"relation_id\", \"relation_table\")")
				createTableSql := fmt.Sprintf("CREATE TABLE \"%s\" (%s);", dbConfig.Prefix+moduleInfo.TableName, strings.Join(fieldStr, ","))
				createTableSql = createTableSql + fmt.Sprintf("COMMENT ON TABLE \"%s\" IS '%s';", dbConfig.Prefix+moduleInfo.TableName, moduleInfo.Name)
				createTableSql = createTableSql + fmt.Sprintf("COMMENT ON COLUMN \"%s\".\"relation_id\" IS '关联表ID';", dbConfig.Prefix+moduleInfo.TableName)
				createTableSql = createTableSql + fmt.Sprintf("COMMENT ON COLUMN \"%s\".\"relation_table\" IS '关联表名';", dbConfig.Prefix+moduleInfo.TableName)
				_, err = g.DB().Exec(ctx, createTableSql)
				liberr.ErrIsNil(ctx, err)
			}
		} else {
			liberr.ErrIsNil(ctx, errors.New("模型不存在"))
		}
	})
	return
}
