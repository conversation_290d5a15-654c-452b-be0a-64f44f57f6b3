/*
* @desc:登录
* @company:云南奇讯科技有限公司
* @Author: yxf
* @Date:   2023/2/27 21:52
 */

package controller

import (
	"context"
	"errors"
	"github.com/gogf/gf/v2/crypto/gmd5"
	"github.com/gogf/gf/v2/encoding/gurl"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/gogf/gf/v2/util/gmode"
	"github.com/gogf/gf/v2/util/grand"
	"github.com/tiger1103/gfast/v3/api/v1/ucenter"
	"github.com/tiger1103/gfast/v3/api/v1/ucenter/admin"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	"github.com/tiger1103/gfast/v3/internal/app/plugins/sms/driver"
	ucenterConsts "github.com/tiger1103/gfast/v3/internal/app/ucenter/consts"
	"github.com/tiger1103/gfast/v3/internal/app/ucenter/model"
	"github.com/tiger1103/gfast/v3/internal/app/ucenter/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ucenter/service"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"time"
)

var (
	UCenterAccount = ucenterAccountController{}
)

type ucenterAccountController struct {
}

// Register
//
//	@Description: 前台用户注册
//	@receiver c
//	@param ctx
//	@param req
//	@return res
//	@return err
func (c *ucenterAccountController) Register(ctx context.Context, req *ucenter.UCenterRegisterReq) (res *ucenter.UCenterRegisterRes, err error) {
	// 检查用户是否已注册
	isExist := service.UCenterMembers().IsMemberExist(ctx, &ucenter.UCenterIsMemberExistReq{
		Mobile:   req.Mobile,
		Username: req.Username,
	})
	if isExist {
		return nil, errors.New("用户名或手机号已被使用")
	}
	// 使用手机注册
	if req.Mobile != "" {
		// 检查手机验证码
		err = driver.CommonSms.CheckVerifyCode(ctx, req.Mobile, req.MsgCode)
		if err != nil {
			return
		}
		req.Username = req.Mobile
		// 使用手机注册时不设置密码采用随机密码
		if req.Password == "" {
			req.Password = grand.S(8)
			req.Password2 = req.Password
		}
	} else {
		// 使用用户名注册
		verifyRes := commonService.Captcha().VerifyString(req.VerifyKey, req.VerifyCode)
		if !verifyRes {
			return nil, errors.New("验证码无效")
		}
	}
	uid, err := service.UCenterMembers().Register(ctx, req)
	if err == nil {
		res = &ucenter.UCenterRegisterRes{
			Username: req.Username,
			Password: req.Password,
		}
	}
	commonService.EventBus().Publish(ucenterConsts.UCENTER_REGISTERED, uid)
	return
}

// Login
//
//	@Description: 前台用户登录
//	@receiver c
//	@param ctx
//	@param req
//	@return res
//	@return err
func (c *ucenterAccountController) Login(ctx context.Context, req *ucenter.UCenterLoginReq) (res *ucenter.UCenterLoginRes, err error) {
	var (
		doMember   *do.UCenterMembers
		memberInfo *model.UCenterMembersInfoRes
	)
	r := g.RequestFromCtx(ctx)
	res = &ucenter.UCenterLoginRes{}
	//判断验证码是否正确
	debug := gmode.IsDevelop()
	if !debug {
		if !commonService.Captcha().VerifyString(req.VerifyKey, req.VerifyCode) {
			err = gerror.New("验证码输入错误")
			return
		}
	}
	ip := libUtils.GetClientIp(ctx)
	userAgent := libUtils.GetUserAgent(ctx)
	// 手机登录
	if req.Mobile != "" && req.RegType == "mobile" {
		err = driver.CommonSms.CheckVerifyCode(ctx, req.Mobile, req.MsgCode)
		if err != nil {
			return nil, err
		}
		doMember, err = service.UCenterMembers().GetUserByMobile(ctx, req.Mobile)
	} else {
		doMember, err = service.UCenterMembers().GetAdminUserByUsernamePassword(ctx, req)
	}
	if err != nil {
		// 保存登录失败的日志信息
		service.UCenterLoginLog().Add(ctx, &admin.UCenterLoginLogParams{
			Status:    0,
			Username:  req.Username,
			Ip:        ip,
			UserAgent: userAgent,
			Msg:       err.Error(),
			Module:    "系统后台",
		})
		return
	}
	err = service.UCenterMembers().UpdateLoginInfo(ctx, gconv.Uint64(doMember.Id), ip)
	if err != nil {
		return
	}
	// 报存登录成功的日志信息
	service.UCenterLoginLog().Add(ctx, &admin.UCenterLoginLogParams{
		Status:    1,
		Username:  gconv.String(doMember.Username),
		Ip:        ip,
		UserAgent: userAgent,
		Msg:       "登录成功",
		Module:    "系统后台",
	})

	key := gconv.String(doMember.Id) + "-" + gmd5.MustEncryptString(gconv.String(doMember.Username)) + gmd5.MustEncryptString(gconv.String(doMember.Password))
	if g.Cfg().MustGet(ctx, "gfToken.multiLogin").Bool() {
		key = gconv.String(doMember.Id) + "-" + gmd5.MustEncryptString(gconv.String(doMember.Username)) + gmd5.MustEncryptString(gconv.String(doMember.Password)+ip+userAgent)
	}
	memberInfo, err = service.UCenterMembers().GetById(ctx, gconv.Uint64(doMember.Id))
	token, err := service.GfToken().GenerateToken(ctx, key, memberInfo)
	if err != nil {
		g.Log().Error(ctx, err)
		err = gerror.New("登录失败，" + err.Error())
		return
	}
	if req.Remember {
		r.Cookie.SetCookie("member_token", gurl.Encode(token), r.Request.Host, "/", time.Hour*24*15)
	} else {
		r.Cookie.SetCookie("member_token", gurl.Encode(token), r.Request.Host, "/", 0)
	}
	res.Token = token
	return
}

// Logout
//
//	@Description: 前台用户登出
//	@receiver c
//	@param ctx
//	@param req
//	@return res
//	@return err
func (c *ucenterAccountController) Logout(ctx context.Context, req *ucenter.UCenterLogoutReq) (res *ucenter.UCenterLogoutRes, err error) {
	r := g.RequestFromCtx(ctx)
	token := service.GfToken().GetRequestToken(r)
	err = service.GfToken().RemoveToken(ctx, token)
	r.Session.Remove("memberId", "memberUuid", "memberInfo", "member_token")
	r.Cookie.Remove("member_token")
	return res, nil
}

// ChangePwd
//
//	@Description: 前端修改账号密码
//	@receiver c
//	@param ctx
//	@param req
//	@return res
//	@return err
func (c *ucenterAccountController) ChangePwd(ctx context.Context, req *ucenter.ChangePwdReq) (res *ucenter.ChangePwdRes, err error) {
	if req.NewPassword != req.NewPassword2 {
		err = errors.New("两次输入的密码不一致")
		return
	}
	memberId := service.Context().GetLoginMember(ctx).Id
	memberInfo, err := service.UCenterMembers().GetByIdWithAll(ctx, memberId)
	if memberInfo == nil {
		return nil, errors.New("用户信息错误")
	}
	passwordEncrypt := libUtils.EncryptPassword(req.OldPassword, gconv.String(memberInfo.Salt))
	if passwordEncrypt != gconv.String(memberInfo.Password) {
		return nil, errors.New("您输入的旧密码错误，请重试！")
	}
	newPwd := libUtils.EncryptPassword(req.NewPassword, gconv.String(memberInfo.Salt))
	err = service.UCenterMembers().ChangePwd(ctx, memberId, newPwd)
	return
}

// Forgot 找回密码API
func (c *ucenterAccountController) Forgot(ctx context.Context, req *ucenter.UCenterForgotReq) (res *ucenter.UCenterForgotRes, err error) {
	res = new(ucenter.UCenterForgotRes)
	res.ForgotKey, err = service.UCenterMembers().Forgot(ctx, req)
	return
}

// 重置密码API
func (c *ucenterAccountController) ResetPwd(ctx context.Context, req *ucenter.UCenterResetPwdReq) (res *ucenter.UCenterResetPwdRes, err error) {
	err = service.UCenterMembers().ForgotResetPwd(ctx, req)
	return
}
