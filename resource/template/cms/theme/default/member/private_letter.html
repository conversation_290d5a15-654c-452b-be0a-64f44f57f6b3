<style>
    .member-account-title {
        display: flex;
        justify-content: space-between;
    }
    .letter-list {
        display: flex;
        flex-direction: column;
    }
    .letter-item {
        display: flex;
        padding-bottom:20px;
    }
    .letter-item:not(:first-child){
        border-top: 1px solid #efefef;
        padding-top: 20px;
    }
    .letter-avatar {
        width: 60px;
        height: 60px;
        margin-right: 15px;
    }
    .letter-avatar img {
        width:100%;
        height:100%;
        border-radius: 50%;
    }
    .letter-right {
        width:100%;
    }
    .nickname {
        font-size: 16px;
        color: #333;
        font-weight: 500!important;
    }
    .letter-content {
        position: relative;
        padding-right: 50px;
        font-size: 14px;
        line-height: 24px;
        color: #999;
        overflow: hidden;
        -o-text-overflow: ellipsis;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .letter-bottom {
        display: flex;
        justify-content:space-between;
        font-size: 14px;
        color: #999;
    }
    .letter-mark {
        color: #1e70bf;
        cursor: pointer;
    }
    .letter-action {
        cursor: pointer;
    }
    #page {
        text-align: center;
    }
    .tips-content {
        color:#000;
        margin-top: 30px;
        min-width: 200px;
    }

    ul.tips-content li {
        cursor: pointer;
        height:32px;
        line-height:32px;
        text-indent: 10px;
    }
    ul.tips-content li:hover {
        background-color: #f1f1f1;
    }
    .my-layer .layui-layer-btn0 {
        border-color: #343434;
        background-color: #343434;
    }

</style>
<div id="wrap">
    <div class="wrap container">
        <div class="main main-full">
            <article class="post-18 page type-page status-publish hentry" id="post-18">
                <div class="entry">
                    <div class="entry-content">
                        <div class="member-account-wrap">
                            <!-- 左侧导航 -->
                            ${include "widget/profile_left.html" .}
                            <div class="member-account-content">
                                <h2 class="member-account-title">
                                    <span>${.title}</span>
                                    ${if eq .isSys 0}
                                    <button id="send-message" type="button" class="btn btn-primary btn-xs">发私信</button>
                                    ${end}
                                </h2>
                                <ul class="letter-list" id="letter-list"></ul>
                                <div id="page" style="margin-top:20px;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </article>
        </div>
    </div>
</div>

<script>
    const memberId  = ${.Session.memberInfo.Id}
    const isSys = ${.isSys}
    const limit = 10
    template.defaults.imports.friend = function (member, members) {
        if (currentUserId() === 0) {
            return {}
        }
        // console.log(member, members)
        var friendId = member.befrom == currentUserId() ? member.beto : member.befrom
        var result = _.find(members, {id: friendId})
        return result ? result : {}
    }

    template.defaults.imports.allCount = function (mark, allMessage) {
        if (!_.isArray(allMessage)) {
            return 0
        }
        var result = _.find(allMessage, {mark: mark})

        return result ? result.count : 0
    }

    template.defaults.imports.readCount = function (mark, readMessage) {
        if (!_.isArray(readMessage)) {
            return 0
        }
        var result = _.find(readMessage, {mark: mark})

        return result ? result.count : 0
    }

    layui.use('laypage', function(){
        var laypage = layui.laypage;

        refresh()
        function refresh () {
            paging(1, limit).done(function (data) {
                var {list ,total, users, allMessage, readMessage} = data

                renderList({list, users, allMessage, readMessage})
                //执行一个laypage实例
                if ((total / limit) > 1 ) {
                    laypage.render({
                        elem: 'page' //注意，这里的 test1 是 ID，不用加 # 号
                        ,count: total //数据总数，从服务端得到
                        ,limit: limit
                        ,jump: function(obj, first){
                            if (!first) {
                                paging(obj.curr, obj.limit).done(function(data) {
                                    var {list ,users, allMessage, readMessage} = data
                                    // $("#letter-list").html(template.render(tpl, {list, users, allMessage, readMessage}))

                                    renderList({list, users, allMessage, readMessage})
                                })
                            }
                        }
                    });
                }
            })
        }
        window.refresh = refresh

    });

    // 发送私信
    $("#send-message").click(function () {

        layer.open({
            type: 1,
            title: "发送私信",
            area: ['500px', '300px'],
            success: function (layero, index) {
                var fn = debounce()
                // 搜索
                $("input[name=nickname]", layero).on("input", function () {
                    fn(this, layero, $(this).val())
                })

                // 取消
                $(".cancel", layero).click(function () {
                    // layer.close(index)
                    layer.closeAll()
                    return false
                })

                // 发送私信
                $("#send-message-form", layero).ajaxForm({
                    beforeSubmit: function (arr, obj) {
                        if($("input[name=toID]", obj).val() == "") {
                            layer.msg("请输入对方昵称!", {icon: 5});
                            return false
                        }

                        if($("textarea[name=content]", obj).val() == "") {
                            layer.msg("请输入内容!", {icon: 5});
                            return false
                        }
                        return true
                    },
                    success: function (data) {
                        var {code, message} = data
                        if (code === 0) {
                            layer.closeAll()
                            refresh()
                            return
                        }
                        layer.msg(message, {icon: 5});
                        return
                    }
                })
            },
            end: function () {
                layer.closeAll()
            },
            content: `
                <div style="padding:20px;">
                    <form role="form" id="send-message-form" method="post"  action="/ucenter/member/SendMessage">
                      <div class="form-group">
                        <label for="name">发送给</label>
                        <input type="hidden" name="toID">
                        <input name="nickname" type="text" class="form-control" id="name" placeholder="请输入用户昵称" autocomplete="off">
                      </div>
                      <div class="form-group">
                        <label for="name">内容</label>
                        <textarea name="content" class="form-control" rows="3"></textarea>
                      </div>
                      <button type="submit" class="btn btn-primary">提 交</button>
                      <button class="btn btn-default cancel">取 消</button>
                    </form>
                </div>
        `});


        function debounce () {
            var tpl = `
                <ul class="tips-content">
                    {{each list}}
                    <li data-id="{{$value.id}}">{{$value.nickname}}</li>
                     {{/each}}
                </ul>
                `
                ,tpl2 = `
                    <div class="tips-content">抱歉找不到结果</div>
                `
            return _.debounce(function (dom, outlayero, nickname) {
                // console.log(data)
                if (nickname === "") {
                    return
                }
                $.get(APIBASE+"ucenter/member/findMember", {nickname: nickname}).done(function (res) {
                    var {code, data, message} = res
                    var content = tpl2
                    if (code  === 0 && _.isArray(data) && data.length > 0) {
                        content = template.render(tpl, {list: data})
                    }
                    layer.open({
                        type: 4,
                        shade: false,
                        tips: [3,"#fff"],
                        content: [content, dom],
                        success: function (layero, index) {
                            // 搜索结果选择
                            $("ul.tips-content li", layero).click(function () {
                                $('input[name=nickname]', outlayero).val("")
                                $('input[name=nickname]', outlayero).attr("placeholder", $(this).html())
                                $('input[name=toID]', outlayero).val($(this).data("id"))
                                layer.close(index)
                            })
                        }
                    });

                })

            }, 500)
        }

    })


    function renderInit () {
        //  回复
        $(".letter-action-reply").click(function () {
            var friendId = $(this).data("friendid")
            var friendName = $(this).data("friendname")
            layer.prompt({
                formType: 2,
                skin: 'my-layer',
                title: '发送私信给' + friendName,
                area: ['400px', '100px'] //自定义文本域宽高
            }, function(value, index, elem){
                // alert(value); //得到value
                $.post(APIBASE+"/ucenter/member/SendMessage", {toID: friendId, content: value}).done(function (res) {
                    var {code, message} = res
                    if (code === 0) {
                        layer.close(index);
                        layer.msg('提交成功!', {icon: 1});
                        return refresh && refresh()
                    }
                    return layer.msg(message, {icon: 5});

                })

            });

        })

        // 跳转聊天详情
        $(".letter-mark").click(function () {
            // console.log($(this).data("userid"))
            window.location.href = '${.cmsConfig.site_url}/member/privateLetterListDetails?id=' + $(this).data("userid")
        })

        // 删除聊天
        $(".letter-action-delete").click(function () {
            var friendId = $(this).data("friendid")
            // console.log(friendId)

            layer.confirm('确定删除此条信息?', {icon: 3, title:'提示'}, function(index){
                $.ajax({url:APIBASE+"/ucenter/member/privateLetterDel", type: 'DELETE', data: {friendId}}).done(function (res) {
                    var {code, message} = res
                    if (code !== 0) {
                        return layer.msg(message, {icon: 5});
                    }
                    refresh && refresh()
                    return layer.msg("操作成功", {icon: 1});
                })
            })

        })
    }

    function renderList (data) {
        var tpl = `
{{each list}}
 <li class="letter-item">
    <div class="letter-avatar"><img src="{{friend($value, users).avatar || ""}}" alt=""></div>
    <div class="letter-right">
        <div class="nickname">{{friend($value, users).nickname || ""}}</div>
        <div class="letter-content">{{$value.content}}</div>
        <div class="letter-bottom">
            <div>
                <span>{{$value.createdAt}}</span>
                <span class="letter-mark" data-userid="{{friend($value, users).id || 0}}"> 共 {{allCount($value.mark, allMessage)}} 条消息 / {{readCount($value.mark, readMessage)}}条未读 </span>
            </div>
            <div>
                <span class="letter-action letter-action-delete" data-friendid="{{friend($value, users).id || 0}}"><i class="layui-icon layui-icon-delete"></i> 删除 </span>
                <span class="letter-action letter-action-reply" data-friendid="{{friend($value, users).id || 0}}" data-friendname="{{friend($value, users).nickname || ""}}"><i class="layui-icon layui-icon-edit"></i> 回复 </span>
            </div>
        </div>
    </div>
</li>
{{/each}}
`
        $("#letter-list").html(template.render(tpl, data))
        renderInit()
    }

    function currentUserId () {
        return memberId ? memberId : 0
    }


    function getIsSys () {
        return isSys
    }

    function paging (page, limit) {
        var dtd = $.Deferred()
        $.get(APIBASE+"ucenter/member/privateLetterList", {page: page, pageSize: limit, isSys: getIsSys()}).done(function (res) {
            var {code, data, message}  = res
            if (code !== 0) {
                layer.msg(message, {icon: 5})
                return dtd.reject(message)
            }
            return dtd.resolve(data)
        })
        return dtd.promise()
    }
</script>