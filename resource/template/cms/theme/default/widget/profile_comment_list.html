<div class="profile-comments-list">
    <div id="comment_list_container"></div>
    <div id="comment_pagination"></div>
</div>
<script id="comment-list" type="text/html">
    <%if(list){%>
    <ul class="comments-list">
        <% for(var i=0;i < list.length;i++){ %>
        <li class="comment byuser odd alt thread-odd thread-alt depth-1 parent">
            <div class="comment-inner">
                <div class="comment-author vcard">
                    <img class="avatar avatar-60 photo" height="60" src="<%=list[i].member.avatar?list[i].member.avatar:'/static/images/avatar.jpg'%>" width="60">
                </div>
                <div class="comment-body">
                    <div class="nickname">
                        <a class="j-user-card" href="${.cmsConfig.site_url}/member/<%= list[i].member.id %>" target="_blank"><%= list[i].member.nickname %></a>
                        <span class="comment-time"><%= list[i].createdAt%></span>
                    </div>
                    <div class="comment-text">
                        <div>评论了: <a href="${.cmsConfig.site_url}/show/<%=list[i].article.id%>.html"><%=list[i].article.title%></a></div>
                        <div class="comment-text-content"><%=list[i].content %></div>
                    </div>
                </div>
                ${if eq .Session.memberInfo.Id .memberInfo.Id}
                <div class="reply reply-btn" style="visibility: unset">
                    <a href="javascript:void(0);" onclick="deleteComment(<%=list[i].id%>)"><i class="iconfont icon-delete"></i>删除</a>
                </div>
                ${end}
            </div>
            <div class="comment-respond comment-reply-container" id="comment-reply-<%=i%>"></div>
        </li>
        <% } %>
    </ul>
    <%}else{%>
    <div class="empty-content-empty">没有数据</div>
    <%}%>
</script>
<script id="comment-pagination-template" type="text/html">
    <ul class="pagination" >
        <li class="total">共<%=total%>条</li>
        <% if(page > 1){ %>
        <li class="prev">
            <a href="javascript:void(0)" onclick="getCommentList(1)" class="page-numbers"><i class="iconfont icon-left"></i></a>
        </li>
        <%}%>
        <% for(var i=1;i <= totalPage; i++) { %>
        <li class="<%= page===i?'active':''%>">
            <a href="javascript:void(0)" onclick="getCommentList(<%=i%>)" class="page-numbers"><%=i%></a>
        </li>
        <% } %>
        <% if(page < totalPage) { %>
        <li class="next">
            <a href="javascript:void(0)" onclick="getCommentList(<%=totalPage%>)" class="page-numbers"><i class="iconfont icon-right"></i></a>
        </li>
        <%}%>
    </ul>
</script>
<script>
    var commentPageSize = 5
    var totalCommentPage = 0
    var totalComment = 0
    //获取评论列表
    function getCommentList(page) {
        $.get(APIBASE+"home/comment/list", {pageNum:page,pageSize:commentPageSize,memberId:${.memberInfo.Id}}, function (res) {
            if (res.code === 0) {
                totalCommentPage = Math.ceil(res.data.total/commentPageSize)
                totalComment = res.data.total
                $("#comment_list_container").html(template("comment-list", res.data))
                if(res.data.total > commentPageSize) {
                    $("#comment_pagination").html(template("comment-pagination-template",{page:res.data.currentPage,totalPage:totalCommentPage,total:totalComment}))
                }
            }
        })
    }
    $(function (){
        getCommentList(1)
    })
</script>