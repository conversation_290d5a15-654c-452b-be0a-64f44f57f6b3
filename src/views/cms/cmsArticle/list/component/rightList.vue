<template>
  <div class="right-content">
    <!-- 原有的搜索表单和表格内容 -->
    <push-dialog ref="pushDialogRef"></push-dialog>
    <move-dialog ref="moveDialogRef"></move-dialog>
    <recycle-dialog ref="recycleDialogRef" @refresh-list="cmsArticleList"></recycle-dialog>
    <div class="cms-cmsArticle-search mb15">
      <el-form ref="queryRef" :inline="true" :model="tableData.param" label-width="40px">
        <el-row>
          <el-form-item label="标题" prop="title">
            <el-input v-model.trim="tableData.param.title" clearable placeholder="请输入标题" @keyup.enter.native="cmsArticleList"/>
          </el-form-item>
          <el-form-item label="属性" prop="flag">
            <el-select v-model="tableData.param.flag" clearable placeholder="选择属性" style="width: 100px;">
              <el-option
                v-for="item in cmsFlagsOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id.toString()"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="tableData.param.status" clearable placeholder="发布状态" style="width: 100px;">
              <el-option v-for="dict in cms_article_pub_type" :key="dict.value" :label="dict.label" :value="dict.value"/>
            </el-select>
          </el-form-item>
          <el-form-item label="排序" prop="orderBy">
            <el-select v-model="tableData.param.orderBy" clearable placeholder="排序" style="width: 100px;">
              <el-option v-for="order in orderByOptions" :key="order.value" :label="order.label" :value="order.value"/>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="cmsArticleList">
              <el-icon>
                <ele-Search/>
              </el-icon>
              搜索
            </el-button>
            <el-button @click="resetQuery(queryRef)">
              <el-icon>
                <ele-Refresh/>
              </el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-row>
      </el-form>
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button v-auth="'api/v1/cms/cmsArticle/add'" v-if="hasAddPermission" type="primary" @click="handleAdd">
            <el-icon>
              <ele-Plus/>
            </el-icon>
            新增
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button v-auth="'api/v1/cms/cmsArticle/delete'" v-if="hasDeletePermission" :disabled="multiple" type="danger" @click="handleDelete(null)">
            <el-icon>
              <ele-Delete/>
            </el-icon>
            删除
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button v-if="hasPushPermission" :disabled="multiple" type="primary" @click="handlePush(null)">
            <el-icon>
              <ele-CopyDocument/>
            </el-icon>
            推送
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button v-if="hasMovePermission" :disabled="multiple" type="primary" @click="handleMove(null)">
            <el-icon>
              <ele-DocumentRemove/>
            </el-icon>
            移动
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button v-if="hasPublishPermission" :disabled="multiple" type="primary" @click="handlePublish(null)">
            <el-icon>
              <ele-Checked/>
            </el-icon>
            发布
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button v-if="hasRecyclePermission" type="primary" @click="handleRecycle">
            <el-icon>
              <ele-Delete/>
            </el-icon>
            回收站
          </el-button>
        </el-col>
      </el-row>
    </div>
    <el-table v-loading="loading" :data="tableData.data" border @selection-change="handleSelectionChange" class="small-padding">
      <el-table-column align="center" type="selection" width="55"/>
      <el-table-column align="center" label="ID" prop="id" width="70px"/>
      <el-table-column align="center" label="封面" prop="thumb" width="100px">
        <template #default="{row}">
          <el-popover placement="right" trigger="hover" v-if="row.thumb">
            <img v-if="row.thumb" :src="proxy.getUpFileUrl(row.thumb)" style="max-width: 400px;max-height: 300px"/>
            <template #reference>
              <img v-if="row.thumb" :src="proxy.getUpFileUrl(row.thumb)" class="article-thumb" />
            </template>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column align="center" label="分类" prop="categoryId" width="140px">
        <template #default="{row}">
          <span v-if="row.category">{{row.category.name}}</span>
        </template>
      </el-table-column>
      <el-table-column  label="标题" min-width="100px" prop="title">
        <template #default="scope">
          <a :href="scope.row.url" target="_blank">{{ scope.row.title }}</a> <a :href="scope.row.previewUrl" target="_blank" style="color: blue; font-style: italic;">{{ scope.row.status !== '1' ? '预览' : '' }}</a>
        </template>
      </el-table-column>
      <el-table-column align="center" label="属性" prop="flag" width="120px">
        <template #default="{row}">
          <span v-if="row.attr">{{row.attr.join(",")}}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="权重" prop="weigh" width="60px">
        <template #default="{row}">
          {{row.weigh}}
        </template>
      </el-table-column>
      <el-table-column :formatter="statusFormat" align="center" label="状态" prop="status" width="70px"/>
      <el-table-column align="center" label="发布人" width="100px">
        <template #default="{row}">
          <span v-if="row.userInfo">{{row.userInfo.userNickname}}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="发布日期" prop="publishedAt" width="160px">
        <template #default="scope">
          <span>{{ scope.row.publishedAt }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="创建日期" prop="createdAt" width="100px">
        <template #default="scope">
          <span>{{ proxy.parseTime(scope.row.createdAt, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="100px">
        <template #default="scope">
          <el-button v-auth="'api/v1/cms/cmsArticle/edit'" v-if="hasEditPermission" link type="primary" @click="handleUpdate(scope.row)">
            修改
          </el-button>
          <el-button v-auth="'api/v1/cms/cmsArticle/delete'" v-if="hasDeletePermission" link type="primary" @click="handleDelete(scope.row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
        v-show="tableData.total>0"
        v-model:limit="tableData.param.pageSize"
        v-model:page="tableData.param.pageNum"
        :total="tableData.total"
        @pagination="cmsArticleList"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, getCurrentInstance, onMounted, onUnmounted, reactive, ref, toRefs, watch } from "vue";
import { ElMessage, ElMessageBox, FormInstance } from "element-plus";
import {delCmsArticle, listCmsArticle, publishCmsArticle} from "/src/api/cms/cmsArticle";
import {checkCmsCategoryPermission} from "/@/api/cms/cmsCategory";
import {listCmsFlagsByCategoryId} from "/@/api/cms/cmsFlags";
import { events } from "/@/eventBus";
import { CmsArticleInfoData, CmsArticleTableColumns, CmsArticleTableDataState } from "/src/views/cms/cmsArticle/list/component/model";
import { useRouter } from "vue-router";
import PushDialog from "./push.vue";
import MoveDialog from "./move.vue";
import RecycleDialog from "./recycle.vue";

interface TreeNodeData {
  type: string;
  name: string;
  id: number;
  children?: TreeNodeData[];
}

export default defineComponent({
  name: "RightList",
  components: {
    PushDialog,
    MoveDialog,
    RecycleDialog
  },
  props: {
    currentNode: {
      type: Object as () => TreeNodeData | null,
      required: true
    }
  },
  setup(props) {
    const { proxy } = <any>getCurrentInstance();
    const loading = ref(false);
    const queryRef = ref<FormInstance>();
    const router = useRouter();
    const cmsCategoryOptions = ref<TreeNodeData[]>([]);
    const cmsFlagsOptions = ref([]);
    const pushDialogRef = ref();
    const moveDialogRef = ref();
    const recycleDialogRef = ref();

    // 定义排序选项
    const orderByOptions = ref([
      { label: "ID降序", value: "id DESC" },
      { label: "权重降序", value: "weigh DESC" },
      { label: "发布日期降序", value: "published_at DESC" }
    ]);
    // 字典选项数据
    const {
      cms_article_pub_type
    } = proxy.useDict(
        "cms_article_pub_type"
    );
    const state = reactive<CmsArticleTableDataState>({
      ids: [],
      tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
          pageNum: 1,
          pageSize: 10,
          title: undefined,
          categoryId: undefined,
          flag: undefined,
          status: undefined,
          orderBy: "id DESC" // 默认按ID降序排列
        }
      }
    });

    // 定义 multiple 属性，初始值为 true，确保删除按钮在加载时不可点击
    const multiple = ref(true);

    // 权限检查相关变量
    const hasAddPermission = ref(false);
    const hasEditPermission = ref(false);
    const hasDeletePermission = ref(false);
    const hasPushPermission = ref(false);
    const hasMovePermission = ref(false);
    const hasPublishPermission = ref(false);
    const hasRecyclePermission = ref(false);

    // 检查权限的方法
    const checkPermission = async (categoryId: number | undefined, permission: string) => {
      if (!categoryId) return false;
      try {
        const res = await checkCmsCategoryPermission({ categoryId, permission });
        return res.data;
      } catch (error) {
        console.error('权限检查失败:', error);
        return false;
      }
    };

    // 更新所有权限
    const updateAllPermissions = async (categoryId: number | undefined) => {
      if (!categoryId) {
        hasAddPermission.value = false;
        hasEditPermission.value = false;
        hasDeletePermission.value = false;
        hasPushPermission.value = false;
        hasMovePermission.value = false;
        hasPublishPermission.value = false;
        hasRecyclePermission.value = false;
        return;
      }

      // 并行检查所有权限
      const [add, edit, del, push, move, publish,recycle] = await Promise.all([
        checkPermission(categoryId, 'add'),
        checkPermission(categoryId, 'edit'),
        checkPermission(categoryId, 'delete'),
        checkPermission(categoryId, 'push'),
        checkPermission(categoryId, 'move'),
        checkPermission(categoryId, 'publish'),
        checkPermission(categoryId, 'recycle')
      ]);

      hasAddPermission.value = add;
      hasEditPermission.value = edit;
      hasDeletePermission.value = del;
      hasPushPermission.value = push;
      hasMovePermission.value = move;
      hasPublishPermission.value = publish;
      hasRecyclePermission.value = recycle;
    };

    // 获取列表数据
    const cmsArticleList = () => {
      loading.value = true;
      if (props.currentNode) {
        state.tableData.param.categoryId = props.currentNode.id;
      }
      listCmsArticle(state.tableData.param).then((res: any) => {
        let list = res.data.list ?? [];
        state.tableData.data = list;
        state.tableData.total = res.data.total;
        loading.value = false;
      });
    };

    // 获取推荐属性数据
    const getFlagsData = (categoryId: number) => {
      if (!categoryId) {
        cmsFlagsOptions.value = [];
        return;
      }

      // 先清空当前数据
      cmsFlagsOptions.value = [];

      listCmsFlagsByCategoryId({ categoryId }).then((res: any) => {

        try {
          // 检查响应码是否为0（成功）
          if (res.code === 0 && res.data && Array.isArray(res.data.list)) {
            // 使用 data.list 数组
            cmsFlagsOptions.value = res.data.list || [];

            // 强制触发视图更新
            cmsFlagsOptions.value = [...cmsFlagsOptions.value];
          } else {
            cmsFlagsOptions.value = [];
          }
        } catch (err) {
          cmsFlagsOptions.value = [];
        }
      }).catch(error => {
        cmsFlagsOptions.value = [];
      });
    };

    // 监听当前节点变化
    watch(() => props.currentNode, (newNode) => {
      if (newNode) {
        state.tableData.param.categoryId = newNode.id;
        state.tableData.param.pageNum = 1;
        // 先获取推荐属性数据，再获取文章列表
        getFlagsData(newNode.id);
        // 更新权限
        updateAllPermissions(newNode.id);
        // 获取文章列表
        cmsArticleList();
      } else {
        // 清除权限
        updateAllPermissions(undefined);
        cmsFlagsOptions.value = [];
      }
    });

    // 初始化表格数据
    const initTableData = () => {
      cmsArticleList();
    };

    /** 重置按钮操作 */
    const resetQuery = (formEl: FormInstance | undefined) => {
      if (!formEl) return;
      formEl.resetFields();
      // 保持当前选中的分类
      if (props.currentNode) {
        state.tableData.param.categoryId = props.currentNode.id;
        // 获取推荐属性数据
        getFlagsData(props.currentNode.id);
      }
      // 重置后保持默认排序
      state.tableData.param.orderBy = "id DESC";
      cmsArticleList();
    };



    // 状态字典翻译
    const statusFormat = (row: CmsArticleTableColumns) => {
      return proxy.selectDictLabel(cms_article_pub_type.value, row.status);
    };

    // 多选框选中数据
    const handleSelectionChange = (selection: Array<CmsArticleInfoData>) => {
      state.ids = selection.map(item => item.id);
      // 更新 multiple 和 single 的值
      multiple.value = !selection.length;
      const single = selection.length !== 1;
      return { single, multiple: multiple.value };
    };

    const handleAdd = () => {
      // 如果有选中的分类节点，将其ID作为参数传递
      const query = props.currentNode ? { categoryId: props.currentNode.id } : {};
      router.push({
        path: "/cms/cmsArticle/list/add",
        query
      }).then(() => {
        // 刷新列表数据
        cmsArticleList();
      });
    };

    const handleUpdate = (row: CmsArticleTableColumns) => {
      if (row) {
        router.push({ path: "/cms/cmsArticle/list/edit", query: { id: row.id } }).then(() => {
          // 刷新列表数据
          cmsArticleList();
        });
      }
    };

    const handleDelete = (row: CmsArticleTableColumns | null) => {
      let msg = "你确定要删除所选数据？";
      let id: number[] = [];
      if (row) {
        msg = `此操作将永久删除数据，是否继续?`;
        id = [row.id];
      } else {
        id = state.ids;
      }
      if (id.length === 0) {
        ElMessage.error("请选择要删除的数据。");
        return;
      }
      ElMessageBox.confirm(msg, "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        delCmsArticle(id).then(() => {
          ElMessage.success("删除成功");
          cmsArticleList();
        });
      }).catch(() => {
      });
    };

    const handlePublish = (row: CmsArticleTableColumns | null) => {
      let msg = "你确定要发布所选数据？";
      let id: number[] = [];
      if (row) {
        msg = `是否继续?`;
        id = [row.id];
      } else {
        id = state.ids;
      }
      if (id.length === 0) {
        ElMessage.error("请选择要发布的数据。");
        return;
      }
      ElMessageBox.confirm(msg, "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        // 将状态设置为布尔值 true
        const publishData = {
          ids: id,
          status: true
        };
        publishCmsArticle(publishData).then(() => {
          ElMessage.success("发布成功");
          cmsArticleList();
        });
      }).catch(() => {
      });
    };

    // 处理推送按钮点击事件
    const handlePush = (row: CmsArticleTableColumns | null) => {
      let id: number[] = [];
      if (row) {
        id = [row.id];
      } else {
        id = state.ids;
      }
      if (id.length === 0) {
        ElMessage.error("请选择要推送的数据。");
        return;
      }
      // 获取当前栏目ID
      const currentCategoryId = props.currentNode ? props.currentNode.id : undefined;
      // 打开推送弹窗
      pushDialogRef.value.openDialog(id, currentCategoryId);
    };

    // 处理移动按钮点击事件
    const handleMove = (row: CmsArticleTableColumns | null) => {
      let id: number[] = [];
      if (row) {
        id = [row.id];
      } else {
        id = state.ids;
      }
      if (id.length === 0) {
        ElMessage.error("请选择要移动的数据。");
        return;
      }
      // 获取当前栏目ID
      const currentCategoryId = props.currentNode ? props.currentNode.id : undefined;
      // 打开移动弹窗
      moveDialogRef.value.openDialog(id, currentCategoryId);
    };

    // 处理回收站按钮点击事件
    const handleRecycle = () => {
      // 获取当前栏目ID
      const currentCategoryId = props.currentNode ? props.currentNode.id : undefined;

      if (!currentCategoryId) {
        ElMessage.warning("请选择一个栏目后再查看回收站");
        return;
      }

      // 打开回收站弹窗
      recycleDialogRef.value.openDialog(currentCategoryId);
    };

    onMounted(() => {
      // 监听文章列表刷新事件
      events.on("refreshArticleList", cmsArticleList);
      // 初始化权限和推荐属性数据
      if (props.currentNode) {
        // 先获取推荐属性数据
        getFlagsData(props.currentNode.id);
        // 再更新权限
        updateAllPermissions(props.currentNode.id);
      }
      // 初始化表格数据
      initTableData();
    });

    // 组件卸载时移除事件监听
    onUnmounted(() => {
      events.off("refreshArticleList", cmsArticleList);
    });

    return {
      proxy,
      loading,
      queryRef,
      resetQuery,
      cmsArticleList,
      statusFormat,
      cms_article_pub_type,
      cmsCategoryOptions,
      cmsFlagsOptions,
      handleSelectionChange,
      handleAdd,
      handleUpdate,
      handleDelete,
      handlePublish,
      handlePush,
      pushDialogRef,
      handleMove,
      moveDialogRef,
      handleRecycle,
      recycleDialogRef,
      // 权限相关变量
      hasAddPermission,
      hasEditPermission,
      hasDeletePermission,
      hasPushPermission,
      hasMovePermission,
      hasPublishPermission,
      hasRecyclePermission,
      orderByOptions, // 排序选项
      ...toRefs(state),
      multiple // 返回 multiple 属性
    };
  }
});
</script>

<style lang="scss" scoped>
.right-content {
  flex: 1;
  padding: 10px;
  overflow: auto;
}

.article-thumb {
  max-width: 90px;
  max-height: 50px;
}

.cms-cmsArticle-container a {
  text-decoration: none;
  color: inherit; /* 保持链接颜色不变 */
}

.cms-cmsArticle-container a:visited {
  color: inherit; /* 访问后的链接颜色不变 */
}
</style>