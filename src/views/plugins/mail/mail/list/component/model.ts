export interface PluginMailTableColumns {    
    id:number;  //    
    mType:string;  // 邮箱类型    
    user:string;  // 用户名称    
    pass:string;  // 密码    
    host:string;  // 主机地址    
    port:number;  // 端口    
    status:number;  // 状态 : 0 - 禁用 1 - 启用    
    remark:string;  //         <el-form-item label="备注" prop="remark">    
}


export interface PluginMailInfoData {    
    id:number|undefined;        //    
    mType:string|undefined; // 邮箱类型    
    user:string|undefined; // 用户名称    
    pass:string|undefined; // 密码    
    host:string|undefined; // 主机地址    
    port:number|undefined; // 端口    
    status:boolean; // 状态 : 0 - 禁用 1 - 启用    
    remark:string|undefined; //         <el-form-item label="备注" prop="remark">    
}


export interface PluginMailTableDataState {
    ids:any[];
    tableData: {
        data: Array<PluginMailTableColumns>;
        total: number;
        loading: boolean;
        param: {
            pageNum: number;
            pageSize: number;            
            id: number|undefined;            
            mType: string|undefined;            
            user: string|undefined;            
            pass: string|undefined;            
            host: string|undefined;            
            port: number|undefined;            
            status: number|undefined;            
            dateRange: string[];
        };
    };
}


export interface PluginMailEditState{
    loading:boolean;
    isShowDialog: boolean;
    formData:PluginMailInfoData;
    rules: object;
}