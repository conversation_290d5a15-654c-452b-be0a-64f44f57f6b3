import{d as D,r as k,l as z,a2 as R,ag as b,c as C,o as i,P as e,H as o,u as x,I as u,a as r,ak as m,O as F,a6 as $,G as w,M as y,L as V}from"./@vue-C21YZbHS.js";import{o as B}from"./type-1SxfzBug.js";import M from"./relationTable-C5d-CDOi.js";import{_ as N}from"./lodash-BPJNOONf.js";import{a as Q}from"./index-BGmsw1a8.js";import"./index-Bp56ifal.js";import"./element-plus-CUmVNDWO.js";import"./lodash-es-S0Y0Up6J.js";import"./@vueuse-D_5IXmcI.js";import"./@element-plus-_Cc-TEQX.js";import"./@popperjs-D_chPuIy.js";import"./@ctrl-r5W6hzzQ.js";import"./dayjs-CepoAVPu.js";import"./@intlify-D8kAWvSi.js";import"./async-validator-9PlIezaS.js";import"./memoize-one-BdPwpGay.js";import"./normalize-wheel-es-BQoi3Ox2.js";import"./@floating-ui-DwceP2Gb.js";import"./pinia-DwJf82dV.js";import"./vue-router-BDT9dCQ7.js";import"./js-cookie-Cz0CWeBA.js";import"./nprogress-Scw6VIZr.js";import"./axios-B62VicFi.js";import"./qs-D41HS0uK.js";import"./side-channel-DytTmCZg.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-DbsE5JW_.js";import"./crypto-js-oM26lpET.js";import"./side-channel-list-C_7Uk4RP.js";import"./side-channel-map-BUokNT8A.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-bSkmVQJL.js";import"./vue-i18n-BkLk9Ti1.js";import"./vue-iNFchrNl.js";import"./vue-clipboard3-Dr8V5xR7.js";import"./clipboard-DzJOpNE6.js";import"./mitt-DJ65BbbF.js";import"./vue-grid-layout-CIPjP0XN.js";import"./vue-simple-uploader-j6sX1TKo.js";import"./vue-ueditor-wrap-CJ5gjsd3.js";const q={class:"app-container"},P=["onUpdate:modelValue"],W=["onUpdate:modelValue"],G=["onUpdate:modelValue"],H=["onUpdate:modelValue"],j=["onUpdate:modelValue"],K=["onUpdate:modelValue"],A=["onUpdate:modelValue"],J=["onUpdate:modelValue"],X=["onUpdate:modelValue"],Y={style:{float:"left"}},Z={style:{float:"right",color:"#8492a6","font-size":"13px"}},ee=D({name:"genTableColumns",__name:"tableColumns",setup(le){const T=k(),L=k(),p=z("tableData"),E=k(document.documentElement.scrollHeight-300+"px"),g=k([]);R(()=>{B().then(s=>{g.value=s.data.dictType??[]})});const c=s=>{T.value.openDialog(s.columnId)},I=(s,n)=>{p.value.columns.map((v,d)=>{if(v.columnId===s){let t=N.cloneDeep(v);t.linkTableName=n.linkTableName,t.linkLabelId=n.linkLabelId,t.linkLabelName=n.linkLabelName,p.value.columns[d]=t}})},U=s=>{p.value.columns.map((n,v)=>{if(n.columnId===s.columnId){let d=N.cloneDeep(n);d.linkTableName="",d.linkLabelId="",d.linkLabelName="",p.value.columns[v]=d}})};return(s,n)=>{const v=b("el-alert"),d=b("el-table-column"),t=b("el-option"),h=b("el-select"),_=b("el-checkbox"),f=b("el-tag"),S=b("el-table"),O=b("el-card");return i(),C("div",q,[e(O,{shadow:"never",header:"字段信息配置"},{default:o(()=>[e(v,{title:"⚠️表字段中的updated_at、deleted_at的字段在此列表中已经隐藏",type:"warning","show-icon":""}),e(S,{ref_key:"tableColumnsRef",ref:L,data:x(p).columns,"row-key":"columnId","max-height":E.value,width:"100%"},{default:o(()=>[e(d,{label:"序号",type:"index",width:"50",fixed:""}),e(d,{label:"字段列名",prop:"columnName",width:"150",fixed:"","show-overflow-tooltip":!0}),e(d,{label:"字段描述",width:"150",fixed:""},{default:o(l=>[u(r("textarea",{class:"el-textarea__inner","onUpdate:modelValue":a=>l.row.columnComment=a},null,8,P),[[m,l.row.columnComment,void 0,{lazy:!0}]])]),_:1}),e(d,{"class-name":"allowDrag",label:"物理类型",prop:"columnType",width:"120","show-overflow-tooltip":!0}),e(d,{label:"go类型",width:"120"},{default:o(l=>[e(h,{modelValue:l.row.goType,"onUpdate:modelValue":a=>l.row.goType=a},{default:o(()=>[e(t,{label:"int",value:"int"}),e(t,{label:"uint",value:"uint"}),e(t,{label:"int64",value:"int64"}),e(t,{label:"uint64",value:"uint64"}),e(t,{label:"float64",value:"float64"}),e(t,{label:"string",value:"string"}),e(t,{label:"Time",value:"Time"}),e(t,{label:"byte",value:"byte"}),e(t,{label:"bool",value:"bool"})]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),e(d,{label:"TS类型",width:"120"},{default:o(l=>[e(h,{modelValue:l.row.tsType,"onUpdate:modelValue":a=>l.row.tsType=a},{default:o(()=>[e(t,{label:"string",value:"string"}),e(t,{label:"number",value:"number"}),e(t,{label:"boolean",value:"boolean"}),e(t,{label:"string[]",value:"string[]"}),e(t,{label:"number[]",value:"number[]"}),e(t,{label:"any",value:"any"}),e(t,{label:"tuple",value:"tuple"}),e(t,{label:"enum",value:"enum"})]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),e(d,{label:"go属性",width:"150"},{default:o(l=>[u(r("textarea",{class:"el-textarea__inner","onUpdate:modelValue":a=>l.row.goField=a},null,8,W),[[m,l.row.goField,void 0,{lazy:!0}]])]),_:1}),e(d,{label:"json属性",width:"150"},{default:o(l=>[u(r("textarea",{class:"el-textarea__inner","onUpdate:modelValue":a=>l.row.htmlField=a},null,8,G),[[m,l.row.htmlField,void 0,{lazy:!0}]])]),_:1}),e(d,{label:"编辑",width:"50"},{default:o(l=>[e(_,{modelValue:l.row.isEdit,"onUpdate:modelValue":a=>l.row.isEdit=a,disabled:l.row.isPk=="1"||l.row.columnName=="created_at"||l.row.columnName=="dept_id"||l.row.columnName=="created_by"||l.row.columnName=="updated_by"},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:1}),e(d,{label:"列表",width:"50"},{default:o(l=>[e(_,{modelValue:l.row.isList,"onUpdate:modelValue":a=>l.row.isList=a},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(d,{label:"列表固定",width:"50"},{default:o(l=>[e(_,{modelValue:l.row.isFixed,"onUpdate:modelValue":a=>l.row.isFixed=a},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(d,{label:"过长隐藏",width:"50"},{default:o(l=>[e(_,{modelValue:l.row.isOverflowTooltip,"onUpdate:modelValue":a=>l.row.isOverflowTooltip=a},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(d,{label:"详情",width:"50"},{default:o(l=>[e(_,{modelValue:l.row.isDetail,"onUpdate:modelValue":a=>l.row.isDetail=a},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(d,{label:"查询",width:"50"},{default:o(l=>[e(_,{modelValue:l.row.isQuery,"onUpdate:modelValue":a=>l.row.isQuery=a,disabled:l.row.htmlField==x(p).treeParentCode},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:1}),e(d,{label:"查询方式",width:"120"},{default:o(l=>[e(h,{modelValue:l.row.queryType,"onUpdate:modelValue":a=>l.row.queryType=a},{default:o(()=>[e(t,{label:"=",value:"EQ"}),e(t,{label:"!=",value:"NE"}),e(t,{label:">",value:"GT"}),e(t,{label:">=",value:"GTE"}),e(t,{label:"<",value:"LT"}),e(t,{label:"<=",value:"LTE"}),e(t,{label:"LIKE",value:"LIKE"}),e(t,{label:"BETWEEN",value:"BETWEEN"})]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),e(d,{label:"必填",width:"50"},{default:o(l=>[e(_,{modelValue:l.row.isRequired,"onUpdate:modelValue":a=>l.row.isRequired=a},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(d,{label:"显示类型",width:"140"},{default:o(l=>[e(h,{modelValue:l.row.htmlType,"onUpdate:modelValue":a=>l.row.htmlType=a,disabled:l.row.htmlField==x(p).treeParentCode},{default:o(()=>[e(t,{label:"文本框",value:"input"}),e(t,{label:"数字输入框",value:"inputNumber"}),e(t,{label:"文本域",value:"textarea"}),e(t,{label:"下拉单选框",value:"select"}),e(t,{label:"下拉多选框",value:"selects"}),e(t,{label:"树形单选框",value:"treeSelect"}),e(t,{label:"树形多选框",value:"treeSelects"}),e(t,{label:"单选框",value:"radio"}),e(t,{label:"开关",value:"switch"}),e(t,{label:"复选框",value:"checkbox"}),e(t,{label:"日期控件",value:"date"}),e(t,{label:"日期时间控件",value:"datetime"}),e(t,{label:"富文本",value:"richtext"}),e(t,{label:"单图上传",value:"imagefile"}),e(t,{label:"多图上传",value:"images"}),e(t,{label:"单文件上传",value:"file"}),e(t,{label:"多文件上传",value:"files"}),e(t,{label:"图片选择器",value:"imageSelector"}),e(t,{label:"附件选择器",value:"fileSelector"}),e(t,{label:"用户选择器（单选）",value:"userSelectorSingle"}),e(t,{label:"用户选择器（多选）",value:"userSelectorMultiple"}),e(t,{label:"部门选择器（单选）",value:"deptSelectorSingle"}),e(t,{label:"部门选择器（多选）",value:"deptSelectorMultiple"}),e(t,{label:"键值对",value:"keyValue"})]),_:2},1032,["modelValue","onUpdate:modelValue","disabled"])]),_:1}),e(d,{label:"列表排序",width:"100"},{default:o(l=>[u(r("textarea",{class:"el-textarea__inner","onUpdate:modelValue":a=>l.row.sortOrderList=a},null,8,H),[[m,l.row.sortOrderList,void 0,{lazy:!0}]])]),_:1}),e(d,{label:"详情排序",width:"100"},{default:o(l=>[u(r("textarea",{class:"el-textarea__inner","onUpdate:modelValue":a=>l.row.sortOrderDetail=a},null,8,j),[[m,l.row.sortOrderDetail,void 0,{lazy:!0}]])]),_:1}),e(d,{label:"编辑排序",width:"100"},{default:o(l=>[u(r("textarea",{class:"el-textarea__inner","onUpdate:modelValue":a=>l.row.sortOrderEdit=a},null,8,K),[[m,l.row.sortOrderEdit,void 0,{lazy:!0}]])]),_:1}),e(d,{label:"查询排序",width:"100"},{default:o(l=>[u(r("textarea",{class:"el-textarea__inner","onUpdate:modelValue":a=>l.row.sortOrderQuery=a},null,8,A),[[m,l.row.sortOrderQuery,void 0,{lazy:!0}]])]),_:1}),e(d,{label:"列表宽度",width:"100"},{default:o(l=>[u(r("textarea",{class:"el-textarea__inner","onUpdate:modelValue":a=>l.row.minWidth=a},null,8,J),[[m,l.row.minWidth,void 0,{lazy:!0}]])]),_:1}),e(d,{label:"详情占列",width:"100"},{default:o(l=>[u(r("textarea",{class:"el-textarea__inner","onUpdate:modelValue":a=>l.row.colSpan=a},null,8,X),[[m,l.row.colSpan,void 0,{lazy:!0}]])]),_:1}),e(d,{label:"字典类型",width:"160"},{default:o(l=>[e(h,{modelValue:l.row.dictType,"onUpdate:modelValue":a=>l.row.dictType=a,clearable:"",filterable:"",placeholder:"请选择"},{default:o(()=>[(i(!0),C(F,null,$(g.value,a=>(i(),w(t,{key:a.dictType,label:a.dictName,value:a.dictType},{default:o(()=>[r("span",Y,y(a.dictName),1),r("span",Z,y(a.dictType),1)]),_:2},1032,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),e(d,{label:"关联表",width:"160"},{default:o(l=>[l.row.linkTableName?(i(),w(f,{key:0,type:"success",onClick:a=>c(l.row),closable:"",onClose:a=>U(l.row)},{default:o(()=>[V(y(l.row.linkTableName),1)]),_:2},1032,["onClick","onClose"])):(i(),w(f,{key:1,onClick:a=>c(l.row)},{default:o(()=>n[0]||(n[0]=[V("点击选择")])),_:2},1032,["onClick"]))]),_:1}),e(d,{label:"关联表key",width:"150"},{default:o(l=>[l.row.linkLabelId?(i(),w(f,{key:0,type:"success",onClick:a=>c(l.row),closable:"",onClose:a=>U(l.row)},{default:o(()=>[V(y(l.row.linkLabelId),1)]),_:2},1032,["onClick","onClose"])):(i(),w(f,{key:1,onClick:a=>c(l.row)},{default:o(()=>n[1]||(n[1]=[V("点击选择")])),_:2},1032,["onClick"]))]),_:1}),e(d,{label:"关联表value",width:"150"},{default:o(l=>[l.row.linkLabelName?(i(),w(f,{key:0,type:"success",onClick:a=>c(l.row),closable:"",onClose:a=>U(l.row)},{default:o(()=>[V(y(l.row.linkLabelName),1)]),_:2},1032,["onClick","onClose"])):(i(),w(f,{key:1,onClick:a=>c(l.row)},{default:o(()=>n[2]||(n[2]=[V("点击选择")])),_:2},1032,["onClick"]))]),_:1})]),_:1},8,["data","max-height"])]),_:1}),e(M,{ref_key:"relationTableRef",ref:T,onOk:I},null,512)])}}}),Ze=Q(ee,[["__scopeId","data-v-83072a66"]]);export{Ze as default};
