export interface UcenterLevelTableColumns {    
    id:number;  // ID    
    name:string;  // 名称    
    description:string;  // 描述    
    listOrder:number;  // 排序    
}


export interface UcenterLevelInfoData {    
    id:number|undefined;        // ID    
    name:string|undefined; // 名称    
    description:string|undefined; // 描述    
    listOrder:number|undefined; // 排序    
}


export interface UcenterLevelTableDataState {
    ids:any[];
    tableData: {
        data: Array<UcenterLevelTableColumns>;
        total: number;
        loading: boolean;
        param: {
            pageNum: number;
            pageSize: number;            
            dateRange: string[];
        };
    };
}


export interface UcenterLevelEditState{
    loading:boolean;
    isShowDialog: boolean;
    formData:UcenterLevelInfoData;
    rules: object;
}