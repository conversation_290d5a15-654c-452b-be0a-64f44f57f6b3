<template>
  <div class="cms-cmsConfig-container">
    <el-card shadow="hover">
      <div class="cms-cmsConfig-search mb15">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-auth="'api/v1/cms/cmsConfig/add'" type="primary" @click="handleAdd">
              <el-icon>
                <ele-Plus/>
              </el-icon>
              新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-auth="'api/v1/cms/cmsConfig/delete'" :disabled="multiple" type="danger" @click="handleDelete(null)">
              <el-icon>
                <ele-Delete/>
              </el-icon>
              删除
            </el-button>
          </el-col>
        </el-row>
      </div>
      <el-table v-loading="loading" :data="tableData.data" border @selection-change="handleSelectionChange">
        <el-table-column align="center" type="selection" width="55"/>
        <el-table-column label="配置名称" min-width="100px" prop="title"/>
        <el-table-column label="调用标签" min-width="100px" prop="name"/>
        <el-table-column label="内容" min-width="100px" prop="value">
          <template #default="{row}">
            <span v-if="row.type==='text'">{{ row.value }}</span>
            <img v-else-if="row.type==='image'" :src="proxy.getUpFileUrl(row.value)" style="max-width: 80px;max-height: 40px;">
            <div v-else-if="row.type==='rich'" v-html="row.value"></div>
            <span v-else-if="row.type==='radio' || row.type==='select'">
              {{ getSettingLabel(row.value, row.setting) }}
            </span>
            <span v-else>{{ row.value }}</span>
          </template>
        </el-table-column>
        <el-table-column label="说明" min-width="100px" prop="description"/>
        <el-table-column align="center" class-name="small-padding" fixed="right" label="操作" width="160px">
          <template #default="scope">
            <el-button v-auth="'api/v1/cms/cmsConfig/edit'" link type="primary" @click="handleUpdate(scope.row)">
              <el-icon>
                <ele-EditPen/>
              </el-icon>
              修改
            </el-button>
            <el-button v-auth="'api/v1/cms/cmsConfig/delete'" link type="primary" @click="handleDelete(scope.row)">
              <el-icon>
                <ele-DeleteFilled/>
              </el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
          v-show="tableData.total>0"
          v-model:limit="tableData.param.pageSize"
          v-model:page="tableData.param.pageNum"
          :total="tableData.total"
          @pagination="cmsConfigList"
      />
    </el-card>
    <apiV1CmsCmsConfigEdit
        ref="editRef"
        @cmsConfigList="cmsConfigList"
    ></apiV1CmsCmsConfigEdit>
    <apiV1CmsCmsConfigAdd
        ref="addRef"
        @cmsConfigList="cmsConfigList"
    ></apiV1CmsCmsConfigAdd>
  </div>
</template>
<script lang="ts">
import {computed, defineComponent, getCurrentInstance, onMounted, reactive, ref, toRaw, toRefs} from "vue";
import {ElMessage, ElMessageBox} from "element-plus";
import {delCmsConfig, listCmsConfig} from "/@/api/cms/cmsConfig";
import {CmsConfigInfoData, CmsConfigTableColumns, CmsConfigTableDataState} from "/@/views/cms/cmsConfig/list/component/model";
import apiV1CmsCmsConfigEdit from "/@/views/cms/cmsConfig/list/component/edit.vue";
import apiV1CmsCmsConfigAdd from "/@/views/cms/cmsConfig/list/component/add.vue";

export default defineComponent({
  name: "apiV1CmsCmsConfigList",
  components: {
    apiV1CmsCmsConfigEdit,
    apiV1CmsCmsConfigAdd
  },
  setup() {
    const {proxy} = <any>getCurrentInstance();
    const loading = ref(false);
    const editRef = ref();
    const addRef = ref();
    // 是否显示所有搜索选项
    const showAll = ref(false);
    // 非单个禁用
    const single = ref(true);
    // 非多个禁用
    const multiple = ref(true);
    const word = computed(() => {
      if (showAll.value === false) {
        //对文字进行处理
        return "展开搜索";
      } else {
        return "收起搜索";
      }
    });
    // 字典选项数据
    const {} = proxy.useDict(
    );
    const state = reactive<CmsConfigTableDataState>({
      ids: [],
      tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
          pageNum: 1,
          pageSize: 10
        }
      }
    });
    // 页面加载时
    onMounted(() => {
      initTableData();
    });
    // 初始化表格数据
    const initTableData = () => {
      cmsConfigList();
    };
    // 获取列表数据
    const cmsConfigList = () => {
      loading.value = true;
      listCmsConfig(state.tableData.param).then((res: any) => {
        let list = res.data.list ?? [];
        state.tableData.data = list;
        state.tableData.total = res.data.total;
        loading.value = false;
      });
    };
    const toggleSearch = () => {
      showAll.value = !showAll.value;
    };
    // 多选框选中数据
    const handleSelectionChange = (selection: Array<CmsConfigInfoData>) => {
      state.ids = selection.map(item => item.id);
      single.value = selection.length != 1;
      multiple.value = !selection.length;
    };
    const handleAdd = () => {
      addRef.value.openDialog();
    };
    const handleUpdate = (row: CmsConfigTableColumns) => {
      if (!row) {
        row = state.tableData.data.find((item: CmsConfigTableColumns) => {
          return item.id === state.ids[0];
        }) as CmsConfigTableColumns;
      }
      editRef.value.openDialog(toRaw(row));
    };
    // 从 setting 中获取对应 value 的标签
    const getSettingLabel = (value: string, settingStr: string) => {
      if (!settingStr) return value;

      try {
        // 尝试解析 setting JSON 字符串
        const settingObj = JSON.parse(settingStr);

        // 返回对应的标签，如果没有则返回原始值
        return settingObj[value] || value;
      } catch (error) {
        console.error('解析 setting 失败:', error);
        return value;
      }
    };

    const handleDelete = (row: CmsConfigTableColumns) => {
      let msg = "你确定要删除所选数据？";
      let id: number[] = [];
      if (row) {
        msg = `此操作将永久删除数据，是否继续?`;
        id = [row.id];
      } else {
        id = state.ids;
      }
      if (id.length === 0) {
        ElMessage.error("请选择要删除的数据。");
        return;
      }
      ElMessageBox.confirm(msg, "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      })
          .then(() => {
            delCmsConfig(id).then(() => {
              ElMessage.success("删除成功");
              cmsConfigList();
            });
          })
          .catch(() => {
          });
    };
    return {
      proxy,
      editRef,
      addRef,
      showAll,
      loading,
      single,
      multiple,
      word,
      cmsConfigList,
      toggleSearch,
      handleSelectionChange,
      handleAdd,
      handleUpdate,
      handleDelete,
      getSettingLabel,
      ...toRefs(state)
    };
  }
});
</script>
<style lang="scss" scoped>
.colBlock {
  display: block;
}

.colNone {
  display: none;
}
</style>
