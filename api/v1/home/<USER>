/**
 * @Company: 云南奇讯科技有限公司
 * @Author: yxf
 * @Description:
 * @Date: 2024/1/31 16:56
 */

package home

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/cms/model"
)

// CmsAdSearchReq 分页请求参数
type CmsAdSearchReq struct {
	g.Meta   `path:"/list" tags:"cms前台接口/广告" method:"get" summary:"广告列表"`
	Name     string `p:"name"` //广告名称
	Id       string `p:"id"`
	Position string `p:"position"` //广告位置
	commonApi.PageReq
	commonApi.Author
}

// CmsAdSearchRes 列表返回结果
type CmsAdSearchRes struct {
	g.Meta `mime:"application/json"`
	commonApi.ListRes
	List []*model.CmsAdListRes `json:"list"`
}

// CmsAdGetReq 获取一条数据请求
type CmsAdGetReq struct {
	g.Meta `path:"/get" tags:"cms前台接口/广告" method:"get" summary:"获取广告信息"`
	commonApi.Author
	Id uint `p:"id" v:"required#主键必须"` //通过主键获取
}

// CmsAdGetRes 获取一条数据结果
type CmsAdGetRes struct {
	g.Meta `mime:"application/json"`
	*model.CmsAdInfoRes
}
