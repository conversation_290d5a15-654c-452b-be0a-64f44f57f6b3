// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2023-03-09 16:49:02
// 生成路径: internal/app/ucenter/controller/ucenter_members.go
// 生成人：yxf
// desc:用户中心
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"
	"errors"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/api/v1/ucenter"
	"github.com/tiger1103/gfast/v3/api/v1/ucenter/admin"
	"github.com/tiger1103/gfast/v3/internal/app/common/model"
	"github.com/tiger1103/gfast/v3/internal/app/ucenter/service"
)

type ucenterMessageController struct {
}

var UCenterMessage = new(ucenterMessageController)

// Add 增加用户消息
func (c *ucenterMessageController) Add(ctx context.Context, req *ucenter.UCenterMessageAddReq) (res *common.EmptyRes, err error) {
	memberId := service.Context().GetMemberId(ctx)
	if memberId == 0 {
		return nil, errors.New("请先登录")
	}
	err = service.UCenterMessage().Add(ctx, &admin.UCenterMessageAddReq{
		Sender:   memberId,
		Receiver: req.Receiver,
		Content:  req.Content,
	})
	return
}

// Reply 回复用户消息
func (c *ucenterMessageController) Reply(ctx context.Context, req *ucenter.UCenterMessageReplyReq) (res *common.EmptyRes, err error) {
	memberId := service.Context().GetMemberId(ctx)
	if memberId == 0 {
		return nil, errors.New("请先登录")
	}
	err = service.UCenterMessage().Add(ctx, &admin.UCenterMessageAddReq{
		Sender:   memberId,
		Receiver: req.Receiver,
		Content:  req.Content,
		Pid:      req.Pid,
	})
	return
}

// Get 获取用户消息
func (c *ucenterMessageController) Get(ctx context.Context, req *ucenter.UCenterMessageGetReq) (res *ucenter.UCenterMessageGetRes, err error) {
	memberId := service.Context().GetMemberId(ctx)
	if memberId == 0 {
		return nil, errors.New("请先登录")
	}
	messageRes, err := service.UCenterMessage().GetById(ctx, req.Id, memberId)
	if err == nil && messageRes != nil {
		err = gconv.Struct(messageRes, &res)
	}
	return
}

// Delete 删除用户消息
func (c *ucenterMessageController) Delete(ctx context.Context, req *ucenter.UCenterMessageDeleteReq) (res *ucenter.UCenterMessageDeleteRes, err error) {
	memberId := service.Context().GetMemberId(ctx)
	if memberId == 0 {
		return nil, errors.New("请先登录")
	}
	err = service.UCenterMessage().Delete(ctx, []uint64{req.Id}, memberId)
	return
}

// List 获取消息列表
func (c *ucenterMessageController) List(ctx context.Context, req *ucenter.UCenterMessageListReq) (res *ucenter.UCenterMessageListRes, err error) {
	memberId := service.Context().GetMemberId(ctx)
	if memberId == 0 {
		return nil, errors.New("请先登录")
	}
	listRes, err := service.UCenterMessage().List(ctx, &admin.UCenterMessageSearchReq{
		Keywords: req.Keywords,
		MsgType:  req.MsgType,
		PageReq: common.PageReq{
			PageReq: model.PageReq{
				DateRange: req.DateRange,
				PageNum:   req.PageNum,
				PageSize:  req.PageSize,
			},
		},
	}, memberId)
	res = &ucenter.UCenterMessageListRes{
		ListRes: common.ListRes{
			CurrentPage: listRes.CurrentPage,
			Total:       listRes.Total,
		},
		List: listRes.List,
	}
	return
}
