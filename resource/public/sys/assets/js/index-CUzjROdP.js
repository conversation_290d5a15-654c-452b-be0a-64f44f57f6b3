import{i as f}from"./getStyleSheets-CiOwvIk9.js";import{d as h,X as F,h as v,ad as g,ag as o,c as a,o as r,P as i,H as s,O as w,a6 as I,G as A,a as e,M as E}from"./@vue-C21YZbHS.js";import{a as x}from"./index-BGmsw1a8.js";import"./@element-plus-_Cc-TEQX.js";import"./pinia-DwJf82dV.js";import"./vue-router-BDT9dCQ7.js";import"./js-cookie-Cz0CWeBA.js";import"./nprogress-Scw6VIZr.js";import"./@intlify-D8kAWvSi.js";import"./axios-B62VicFi.js";import"./qs-D41HS0uK.js";import"./side-channel-DytTmCZg.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-DbsE5JW_.js";import"./crypto-js-oM26lpET.js";import"./side-channel-list-C_7Uk4RP.js";import"./side-channel-map-BUokNT8A.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-bSkmVQJL.js";import"./element-plus-CUmVNDWO.js";import"./lodash-es-S0Y0Up6J.js";import"./@vueuse-D_5IXmcI.js";import"./@popperjs-D_chPuIy.js";import"./@ctrl-r5W6hzzQ.js";import"./dayjs-CepoAVPu.js";import"./async-validator-9PlIezaS.js";import"./memoize-one-BdPwpGay.js";import"./normalize-wheel-es-BQoi3Ox2.js";import"./@floating-ui-DwceP2Gb.js";import"./vue-i18n-BkLk9Ti1.js";import"./vue-iNFchrNl.js";import"./vue-clipboard3-Dr8V5xR7.js";import"./clipboard-DzJOpNE6.js";import"./mitt-DJ65BbbF.js";import"./vue-grid-layout-CIPjP0XN.js";import"./vue-simple-uploader-j6sX1TKo.js";import"./vue-ueditor-wrap-CJ5gjsd3.js";const B=h({name:"pagesElement",setup(){const t=F({sheetsIconList:[]}),n=()=>{f.ele().then(m=>{t.sheetsIconList=m})};return v(()=>{n()}),{...g(t)}}}),C={class:"element-container"},L={class:"iconfont-warp"},S={class:"flex-margin"},$={class:"iconfont-warp-value"},k={class:"iconfont-warp-label mt10"};function y(t,n,m,D,b,G){const c=o("SvgIcon"),l=o("el-col"),u=o("el-row"),d=o("el-card");return r(),a("div",C,[i(d,{shadow:"hover",header:`element plus 字体图标(自动载入，增加了 ele- 前缀，使用时：ele-Aim)：${t.sheetsIconList.length}个`},{default:s(()=>[i(u,{class:"iconfont-row"},{default:s(()=>[(r(!0),a(w,null,I(t.sheetsIconList,(p,_)=>(r(),A(l,{xs:12,sm:8,md:6,lg:4,xl:2,key:_},{default:s(()=>[e("div",L,[e("div",S,[e("div",$,[i(c,{name:p,size:30},null,8,["name"])]),e("div",k,E(p),1)])])]),_:2},1024))),128))]),_:1})]),_:1},8,["header"])])}const $t=x(B,[["render",y],["__scopeId","data-v-112202b9"]]);export{$t as default};
