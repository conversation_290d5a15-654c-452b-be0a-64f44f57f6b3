// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2023-12-01 14:49:39
// 生成路径: internal/app/plugins/sms/logic/plugin_sms_config.go
// 生成人：gfast
// desc:短信配置
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/plugins/sms/dao"
	driverService "github.com/tiger1103/gfast/v3/internal/app/plugins/sms/driver/service"
	"github.com/tiger1103/gfast/v3/internal/app/plugins/sms/model"
	"github.com/tiger1103/gfast/v3/internal/app/plugins/sms/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/plugins/sms/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterPluginSmsConfig(New())
}

func New() service.IPluginSmsConfig {
	return &sPluginSmsConfig{}
}

type sPluginSmsConfig struct{}

func (s *sPluginSmsConfig) List(ctx context.Context, req *model.PluginSmsConfigSearchReq) (listRes *model.PluginSmsConfigSearchRes, err error) {
	listRes = new(model.PluginSmsConfigSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.PluginSmsConfig.Ctx(ctx).WithAll()
		if req.SmsType != "" {
			m = m.Where(dao.PluginSmsConfig.Columns().SmsType+" = ?", req.SmsType)
		}
		if req.Status != "" {
			m = m.Where(dao.PluginSmsConfig.Columns().Status+" = ?", gconv.Int(req.Status))
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.PluginSmsConfigListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.PluginSmsConfigListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.PluginSmsConfigListRes{
				Id:      v.Id,
				SmsType: v.SmsType,
				Remark:  v.Remark,
				Status:  v.Status,
			}
		}
	})
	return
}

func (s *sPluginSmsConfig) GetById(ctx context.Context, id uint) (res *model.PluginSmsConfigInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.PluginSmsConfig.Ctx(ctx).WithAll().Where(dao.PluginSmsConfig.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sPluginSmsConfig) Add(ctx context.Context, req *model.PluginSmsConfigAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.PluginSmsConfig.Ctx(ctx).Insert(do.PluginSmsConfig{
			SmsType: req.SmsType,
			Remark:  req.Remark,
			Status:  req.Status,
			Config:  req.Config,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sPluginSmsConfig) Edit(ctx context.Context, req *model.PluginSmsConfigEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.PluginSmsConfig.Ctx(ctx).WherePri(req.Id).Update(do.PluginSmsConfig{
			SmsType: req.SmsType,
			Remark:  req.Remark,
			Status:  req.Status,
			Config:  req.Config,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sPluginSmsConfig) Delete(ctx context.Context, ids []uint) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.PluginSmsConfig.Ctx(ctx).Delete(dao.PluginSmsConfig.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

func(s *sPluginSmsConfig)ChangeStatus(ctx context.Context,id uint,status int) (err error){
	err = g.Try(ctx, func(ctx context.Context) {
		if status == 1 {
			// 同时只能有一个短信平台生效
			_, err := dao.PluginSmsConfig.Ctx(ctx).Where("status", 1).Update(do.PluginSmsConfig{
				Status: 0,
			})
			liberr.ErrIsNil(ctx,err)
		}
		_, err = dao.PluginSmsConfig.Ctx(ctx).WherePri(id).Update(do.PluginSmsConfig{
			Status: status,
		})
		liberr.ErrIsNil(ctx,err)
		err = driverService.Sms.RegisterSmsService(ctx)
		liberr.ErrIsNil(ctx,err)
	})
	return
}
