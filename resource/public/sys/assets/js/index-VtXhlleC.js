import{s as U,a as B}from"./index-BGmsw1a8.js";import{E as W}from"./element-plus-CUmVNDWO.js";import{d as z,X as b,r as v,ag as r,c as E,o as N,P as o,H as t,a as y,L as c}from"./@vue-C21YZbHS.js";import"./pinia-DwJf82dV.js";import"./vue-router-BDT9dCQ7.js";import"./js-cookie-Cz0CWeBA.js";import"./@element-plus-_Cc-TEQX.js";import"./nprogress-Scw6VIZr.js";import"./@intlify-D8kAWvSi.js";import"./axios-B62VicFi.js";import"./qs-D41HS0uK.js";import"./side-channel-DytTmCZg.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-DbsE5JW_.js";import"./crypto-js-oM26lpET.js";import"./side-channel-list-C_7Uk4RP.js";import"./side-channel-map-BUokNT8A.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-bSkmVQJL.js";import"./vue-i18n-BkLk9Ti1.js";import"./vue-iNFchrNl.js";import"./vue-clipboard3-Dr8V5xR7.js";import"./clipboard-DzJOpNE6.js";import"./mitt-DJ65BbbF.js";import"./vue-grid-layout-CIPjP0XN.js";import"./vue-simple-uploader-j6sX1TKo.js";import"./vue-ueditor-wrap-CJ5gjsd3.js";import"./lodash-es-S0Y0Up6J.js";import"./@vueuse-D_5IXmcI.js";import"./@popperjs-D_chPuIy.js";import"./@ctrl-r5W6hzzQ.js";import"./dayjs-CepoAVPu.js";import"./async-validator-9PlIezaS.js";import"./memoize-one-BdPwpGay.js";import"./normalize-wheel-es-BQoi3Ox2.js";import"./@floating-ui-DwceP2Gb.js";const R=z({__name:"index",setup(h){const a=b({topic:"producer_topic_test",body:"测试消息队列内容",channel:"channel1"});b({topic:"producer_topic_test",channel:"channel1"});function w(){U({url:"/api/v1/mqueue/demo/produce",method:"post",data:{topic:a.topic,body:a.body}}).then(i=>{window.console.log(i)})}const V="ws://localhost:8808/",d=v("");let l=null;const n=v(!1);function x(){if(l)return;const i=V+"api/v1/mqueue/demo/subscribe?topic="+a.topic+"&channel="+a.channel;l=new WebSocket(i);try{l.onopen=function(){d.value+="WebSocket Server ["+i+`] 连接成功！\r
`,n.value=!0},l.onclose=function(){l&&(l.close(),l=null,n.value=!1),d.value+="WebSocket Server ["+i+`] 连接被服务器关闭！\r
`},l.onerror=function(){l&&(l.close(),l=null,n.value=!1),d.value+="WebSocket Server ["+i+`] 连接错误！\r
`},l.onmessage=function(e){window.console.log(e),d.value+=" > "+e.data+`\r
`}}catch(e){W.alert(e.message)}}function S(){l&&(l.close(),l=null,n.value=!1,d.value+=`已手动取消订阅！\r
`)}return(i,e)=>{const k=r("el-alert"),u=r("el-input"),p=r("el-form-item"),s=r("el-button"),f=r("el-form"),_=r("el-col"),g=r("el-row"),C=r("el-card");return N(),E("div",null,[o(C,null,{default:t(()=>[o(k,{type:"error",title:"消息队列测试，先生产一条消息再订阅，先订阅不存在的topic会失败",closable:!1}),o(g,{gutter:20,style:{"margin-top":"20px"}},{default:t(()=>[o(_,{span:10},{default:t(()=>[e[5]||(e[5]=y("div",{class:"my-title"},"生产者",-1)),o(f,{"label-width":80},{default:t(()=>[o(p,{label:"topic"},{default:t(()=>[o(u,{modelValue:a.topic,"onUpdate:modelValue":e[0]||(e[0]=m=>a.topic=m)},null,8,["modelValue"])]),_:1}),o(p,{label:"topic"},{default:t(()=>[o(u,{modelValue:a.body,"onUpdate:modelValue":e[1]||(e[1]=m=>a.body=m),type:"textarea",rows:6},null,8,["modelValue"])]),_:1}),o(p,null,{default:t(()=>[o(s,{type:"primary",size:"default",onClick:w},{default:t(()=>e[4]||(e[4]=[c("生产消息")])),_:1})]),_:1})]),_:1})]),_:1}),o(_,{span:14},{default:t(()=>[e[8]||(e[8]=y("div",{class:"my-title"},"消费者",-1)),o(f,{"label-width":80},{default:t(()=>[o(p,{label:"topic"},{default:t(()=>[o(u,{modelValue:a.topic,"onUpdate:modelValue":e[2]||(e[2]=m=>a.topic=m),disabled:n.value},null,8,["modelValue","disabled"])]),_:1}),o(p,{label:"channel"},{default:t(()=>[o(u,{modelValue:a.channel,"onUpdate:modelValue":e[3]||(e[3]=m=>a.channel=m),disabled:n.value},null,8,["modelValue","disabled"])]),_:1}),o(p,null,{default:t(()=>[o(s,{size:"default",type:"primary",onClick:x,disabled:n.value},{default:t(()=>e[6]||(e[6]=[c("订阅消费")])),_:1},8,["disabled"]),o(s,{size:"default",type:"primary",onClick:S,disabled:!n.value},{default:t(()=>e[7]||(e[7]=[c("取消订阅")])),_:1},8,["disabled"])]),_:1}),o(p,{label:"消费结果"},{default:t(()=>[o(u,{"model-value":d.value,type:"textarea",rows:12},null,8,["model-value"])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})])}}}),Be=B(R,[["__scopeId","data-v-d79e3371"]]);export{Be as default};
