-- 删除原有数据

-- 删除cms菜单
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/system/modulesInfo/list';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/system/modulesInfo/get';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/system/modulesInfo/add';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/system/modulesInfo/edit';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/system/modulesInfo/delete';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsConfig/list';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsConfig/get';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsConfig/add';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsConfig/edit';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsConfig/delete';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsTags/list';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsTags/get';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsTags/add';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsTags/edit';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsTags/delete';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsCategory/list';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsCategory/get';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsCategory/add';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsCategory/edit';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsCategory/delete';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsArticle';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsArticle/list';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsArticle/get';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsArticle/add';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsArticle/edit';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsArticle/addPage';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsArticle/editPage';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsArticle/delete';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/system/modulesField/list';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/system/modulesField/get';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/system/modulesField/add';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/system/modulesField/edit';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/system/modulesField/delete';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsAd';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsAd/list';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsAd/get';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsAd/add';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsAd/edit';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsAd/delete';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsAdPosition/list';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsAdPosition/get';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsAdPosition/add';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsAdPosition/edit';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsAdPosition/delete';

DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsComment/list';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsArticleComment/changeStatus';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsArticleComment/delete';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsCommentReply/changeStatus';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsCommentReply/delete';
DELETE FROM `sys_auth_rule` WHERE `name` = 'cmsFront';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/tagBuilder/builder';





-- 删除用户中心菜单
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/ucenter/ucenterConfig/list';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/ucenter/ucenterConfig/get';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/ucenter/ucenterConfig/add';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/ucenter/ucenterConfig/edit';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/ucenter/ucenterConfig/delete';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/ucenter/ucenterLevel/list';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/ucenter/ucenterLevel/get';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/ucenter/ucenterLevel/add';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/ucenter/ucenterLevel/edit';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/ucenter/ucenterLevel/delete';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/ucenter/ucenterMembers';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/ucenter/ucenterMembers/list';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/ucenter/ucenterMembers/get';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/ucenter/ucenterMembers/add';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/ucenter/ucenterMembers/edit';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/ucenter/ucenterMembers/delete';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/ucenter/ucenterScores/list';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/ucenter/ucenterScores/get';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/ucenter/ucenterScores/add';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/ucenter/ucenterScores/edit';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/ucenter/ucenterScores/delete';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/ucenter/ucenterMessage/list';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/ucenter/ucenterMessage/get';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/ucenter/ucenterMessage/add';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/ucenter/ucenterMessage/edit';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/ucenter/ucenterMessage/delete';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/ucenter/ucenterNotifications/list';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/ucenter/ucenterNotifications/get';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/ucenter/ucenterNotifications/add';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/ucenter/ucenterNotifications/edit';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/ucenter/ucenterNotifications/delete';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/ucenter/ucenterLoginLog';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/ucenter/ucenterLoginLog/list';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/ucenter/ucenterLoginLog/get';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/ucenter/ucenterLoginLog/add';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/ucenter/ucenterLoginLog/edit';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/ucenter/ucenterLoginLog/delete';

-- 删除分类IP访问限制
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsCategoryIpaccess/list';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsCategoryIpaccess/get';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsCategoryIpaccess/add';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsCategoryIpaccess/edit';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsCategoryIpaccess/delete';
-- 删除分类 分类权限
DELETE FROM `sys_auth_rule` WHERE  'name' = 'api/v1/cms/cmsCategory/savePermissions';

-- 删除文章访问统计
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsVisit';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsVisit/list';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsVisitLog/list';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsVisitLog/get';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsVisitLog/add';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsVisitLog/edit';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsVisitLog/delete';

-- 删除文章属性
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsFlags/list';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsFlags/get';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsFlags/add';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsFlags/edit';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/cms/cmsFlags/delete';


#模型管理菜单
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (35, 'api/v1/system/modulesInfo/list', '模型管理', 'ele-Fold', '', '', 1, 0, 0, '/system/modulesInfo/list', 'system/modulesInfo/list/index', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, '2023-01-31 09:48:21');

#按钮父级菜单ID
SELECT @parentId := LAST_INSERT_ID();

INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/system/modulesInfo/get', '模型信息查询', '', '', '模型信息查询', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/system/modulesInfo/add', '模型信息添加', '', '', '模型信息添加', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/system/modulesInfo/edit', '模型信息修改', '', '', '模型信息修改', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/system/modulesInfo/delete', '模型信息删除', '', '', '模型信息删除', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);

#模型字段
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (35, 'api/v1/system/modulesField/list', '模型字段', 'ele-Fold', '', '', 1, 0, 1, '/system/modulesField/list', 'system/modulesField/list/index', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, '2023-02-01 15:26:03');

#按钮父级菜单ID
SELECT @parentId := LAST_INSERT_ID();

INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/system/modulesField/get', '模型字段查询', '', '', '模型字段查询', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/system/modulesField/add', '模型字段添加', '', '', '模型字段添加', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/system/modulesField/edit', '模型字段修改', '', '', '模型字段修改', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/system/modulesField/delete', '模型字段删除', '', '', '模型字段删除', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);


#文章管理目录
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (0, 'api/v1/cms/cmsArticle', '文章管理', 'iconfont icon-fuwenbenkuang', '', '文章管理', 0, 0, 0, '/cms/cmsArticle', 'layout/routerView/parent', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);

#菜单父级目录ID
SELECT @parentIdDir := LAST_INSERT_ID();

#cms配置管理
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentIdDir, 'api/v1/cms/cmsConfig/list', '配置设置', 'ele-Setting', '', '', 1, 0, 0, '/cms/cmsConfig/list', 'cms/cmsConfig/list/index', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, '2023-02-08 10:21:00');

#按钮父级菜单ID
SELECT @parentId := LAST_INSERT_ID();

INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsConfig/get', 'CMS配置查询', '', '', 'CMS配置查询', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsConfig/add', 'CMS配置添加', '', '', 'CMS配置添加', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsConfig/edit', 'CMS配置修改', '', '', 'CMS配置修改', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsConfig/delete', 'CMS配置删除', '', '', 'CMS配置删除', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);

#标签管理
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentIdDir, 'api/v1/cms/cmsTags/list', '标签管理', 'ele-Cellphone', '', '', 1, 0, 0, '/cms/cmsTags/list', 'cms/cmsTags/list/index', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, '2023-02-08 10:21:25');

#按钮父级菜单ID
SELECT @parentId := LAST_INSERT_ID();

INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsTags/get', 'CMS标签查询', '', '', 'CMS标签查询', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsTags/add', 'CMS标签添加', '', '', 'CMS标签添加', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsTags/edit', 'CMS标签修改', '', '', 'CMS标签修改', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsTags/delete', 'CMS标签删除', '', '', 'CMS标签删除', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);

#文章分类
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentIdDir, 'api/v1/cms/cmsCategory/list', '文章分类', 'ele-Menu', '', '', 1, 0, 0, '/cms/cmsCategory/list', 'cms/cmsCategory/list/index', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, '2023-02-08 10:22:21');

#按钮父级菜单ID
SELECT @parentId := LAST_INSERT_ID();

INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsCategory/get', '文章分类查询', '', '', '文章分类查询', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsCategory/add', '文章分类添加', '', '', '文章分类添加', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsCategory/edit', '文章分类修改', '', '', '文章分类修改', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsCategory/delete', '文章分类删除', '', '', '文章分类删除', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsCategory/savePermissions', '文章分类权限设置', '', '', '文章分类权限设置', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);

#文章列表
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentIdDir, 'api/v1/cms/cmsArticle/list', '文章列表', 'ele-Fold', '', '文章列表', 1, 0, 0, '/cms/cmsArticle/list', 'cms/cmsArticle/list/index', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);

#按钮父级菜单ID
SELECT @parentId := LAST_INSERT_ID();

INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsArticle/get', '文章查询', '', '', '文章查询', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsArticle/addPage', '文章添加页面', 'ele-CirclePlus', '', '', 1, 0, 1, '/cms/cmsArticle/list/add', '/cms/cmsArticle/list/add', 0, 'sys_admin', 0, 0, 0, '', 0, '', NULL, '2023-10-27 09:55:29');
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsArticle/editPage', '文章修改页面', 'ele-Edit', '', '', 1, 0, 1, '/cms/cmsArticle/list/edit', '/cms/cmsArticle/list/edit', 0, 'sys_admin', 0, 0, 0, '', 0, '', NULL, '2023-10-27 09:56:21');
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `menu_type`, `is_cached`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsArticle/add', '文章添加按钮', '2', '1', '2024-02-23 11:49:32', '2024-02-23 11:51:15');
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `menu_type`, `is_cached`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsArticle/edit', '文章修改按钮', '2', '1', '2024-02-23 11:49:32', '2024-02-23 11:51:15');
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsArticle/delete', '文章删除', '', '', '文章删除', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);

#站内广告
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentIdDir, 'api/v1/cms/cmsAd', '站内广告', 'iconfont icon-fuwenbenkuang', '', '', 0, 0, 0, '/cms/cmsAd', 'layout/routerView/parent', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, '2023-03-02 09:25:55');

#按钮父级菜单ID
SELECT @parentId := LAST_INSERT_ID();

INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsAd/list', '广告列表', 'ele-Fold', '', '广告列表', 1, 0, 0, '/cms/cmsAd/list', 'cms/cmsAd/list/index', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsAd/get', '广告查询', '', '', '广告查询', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsAd/add', '广告添加', '', '', '广告添加', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsAd/edit', '广告修改', '', '', '广告修改', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsAd/delete', '广告删除', '', '', '广告删除', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);


#广告位置
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentIdDir, 'api/v1/cms/cmsAdPosition/list', '广告位置', 'ele-Fold', '', '', 1, 0, 0, '/cms/cmsAdPosition/list', 'cms/cmsAdPosition/list/index', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, '2023-03-02 09:26:27');

#按钮父级菜单ID
SELECT @parentId := LAST_INSERT_ID();

INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsAdPosition/get', '广告位置查询', '', '', '广告位置查询', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsAdPosition/add', '广告位置添加', '', '', '广告位置添加', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsAdPosition/edit', '广告位置修改', '', '', '广告位置修改', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsAdPosition/delete', '广告位置删除', '', '', '广告位置删除', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);


#文章评论菜单
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentIdDir, 'api/v1/cms/cmsComment/list', '文章评论管理', 'ele-ChatLineRound', '', '', 0, 0, 0, '/cms/cmsComment/list', 'cms/cmsComment/list/index', 0, '', 0, 0, 1, '', 0, '', '2023-10-16 11:26:41', '2023-10-16 11:26:41');
#文章评论按钮
SELECT @parentId := LAST_INSERT_ID();
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsArticleComment/changeStatus', '评论状态修改', '', '', '', 2, 0, 0, '', '', 0, '', 0, 0, 1, '', 0, '', '2023-10-17 10:40:57', '2023-10-17 10:41:27');
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsArticleComment/delete', '删除评论', '', '', '', 2, 0, 0, '', '', 0, '', 0, 0, 1, '', 0, '', '2023-10-17 10:50:45', '2023-10-17 10:50:45');
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsCommentReply/changeStatus', '评论回复状态修改', '', '', '', 2, 0, 0, '', '', 0, '', 0, 0, 1, '', 0, '', '2023-10-17 10:51:36', '2023-10-17 10:51:36');
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsCommentReply/delete', '删除评论回复内容', '', '', '', 2, 0, 0, '', '', 0, '', 0, 0, 1, '', 0, '', '2023-10-17 10:52:08', '2023-10-17 10:52:08');

#前台预览
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentIdDir, 'api/v1/cms/tagBuilder/builder', '模板标签生成器', 'iconfont icon-zhongduancanshuchaxun', '', '', 1, 0, 0, '/cms/tagBuilder/builder', 'cms/tagBuilder/builder', 0, '', 0, 0, 1, '', 0, '', '2024-02-06 11:06:10', '2024-02-06 11:07:03');

#模板标签
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentIdDir, 'cmsFront', '前台预览', 'iconfont icon-xianshimima', '', '', 1, 1000, 0, '/cms/view', 'layout/routerView/link', 1, '', 0, 0, 1, '', 0, 'http://localhost:8808/home', '2023-12-15 11:22:37', '2024-02-06 11:17:09');

#分类IP访问限制
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentIdDir, 'api/v1/cms/cmsCategoryIpaccess/list', '分类IP访问限制', 'ele-ChatLineRound', '', '', 1, 0, 0, '/cms/cmsCategoryIpaccess/list', 'cms/cmsCategoryIpaccess/list/index', 0, '', 0, 0, 1, '', 0, '', '2025-04-16 15:37:42', '2025-04-16 15:37:42');
SELECT @parentId := LAST_INSERT_ID();
#分类IP访问限制按钮
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsCategoryIpaccess/get', '分类IP访问限制查询', 'ele-ChatLineRound', '', '', 0, 0, 0, '/cms/cmsCategoryIpaccess/get', 'cms/cmsCategoryIpaccess/list/get', 0, '', 0, 0, 1, '', 0, '', '2025-04-16 15:37:42', '2025-04-16 15:37:42');
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsCategoryIpaccess/add', '分类IP访问限制添加', 'ele-ChatLineRound', '', '', 0, 0, 0, '', '', 0, '', 0, 0, 1, '', 0, '', '2025-04-16 15:37:42', '2025-04-16 15:37:42');
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsCategoryIpaccess/edit', '分类IP访问限制编辑', 'ele-ChatLineRound', '', '', 0, 0, 0, '', '', 0, '', 0, 0, 1, '', 0, '', '2025-04-16 15:37:42', '2025-04-16 15:37:42');
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsCategoryIpaccess/delete', '分类IP访问限制删除', 'ele-ChatLineRound', '', '', 0, 0, 0, '', '', 0, '', 0, 0, 1, '', 0, '', '2025-04-16 15:37:42', '2025-04-16 15:37:42');

#文章访问统计
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentIdDir, 'api/v1/cms/cmsVisit', '文章访问统计', 'iconfont icon-ico_shuju', '', '', 0, 0, 0, '/cms/cmsVisit', '', 0, '', 0, 0, 1, '', 0, '', '2025-04-16 15:37:42', '2025-04-16 15:37:42');
SELECT @parentId := LAST_INSERT_ID();
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsVisit/list', '访问统计', 'ele-Histogram', '', '', 1, 0, 0, '/cms/cmsVisit/list', 'cms/cmsVisit/list/index', 0, '', 0, 0, 1, '', 0, '', '2025-04-16 15:37:42', '2025-04-16 15:37:42');
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsVisitLog/list', '访问日志', 'ele-Fold', '', '', 1, 0, 0, '/cms/cmsVisitLog/list', 'cms/cmsVisitLog/list/index', 0, '', 0, 0, 1, '', 0, '', '2025-04-16 15:37:42', '2025-04-16 15:37:42');
SELECT @parentId := LAST_INSERT_ID();
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsVisitLog/get', '访问日志查询', '', '', '', 2, 0, 0, '', '', 0, '', 0, 0, 1, '', 0, '', '2025-04-16 15:37:42', '2025-04-16 15:37:42');
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsVisitLog/delete', '访问日志删除', '', '', '', 2, 0, 0, '', '', 0, '', 0, 0, 1, '', 0, '', '2025-04-16 15:37:42', '2025-04-16 15:37:42');

#文章推荐属性
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentIdDir, 'api/v1/cms/cmsFlags/list', '推荐属性管理', 'ele-ChatLineRound', '', '', 1, 0, 0, '/cms/cmsFlags/list', 'cms/cmsFlags/list/index', 0, '', 0, 0, 1, '', 0, '', '2025-04-16 15:37:42', '2025-04-16 15:37:42');
SELECT @parentId := LAST_INSERT_ID();
#文章推荐属性按钮
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsFlags/get', '推荐属性查询', 'ele-ChatLineRound', '', '', 0, 0, 0, '/cms/cmsFlags/get', 'cms/cmsFlags/list/get', 2, '', 0, 0, 1, '', 0, '', '2025-04-16 15:37:42', '2025-04-16 15:37:42');
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsFlags/add', '推荐属性添加', 'ele-ChatLineRound', '', '', 2, 0, 0, '', '', 0, '', 0, 0, 1, '', 0, '', '2025-04-16 15:37:42', '2025-04-16 15:37:42');
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsFlags/edit', '推荐属性编辑', 'ele-ChatLineRound', '', '', 2, 0, 0, '', '', 0, '', 0, 0, 1, '', 0, '', '2025-04-16 15:37:42', '2025-04-16 15:37:42');
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentId, 'api/v1/cms/cmsFlags/delete', '推荐属性删除', 'ele-ChatLineRound', '', '', 2, 0, 0, '', '', 0, '', 0, 0, 1, '', 0, '', '2025-04-16 15:37:42', '2025-04-16 15:37:42');


#用户中心

#用户中心目录
INSERT INTO `sys_auth_rule` ( `pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES ( 0, 'api/v1/ucenter/ucenterMembers', '用户中心', 'iconfont icon-icon-', '', '', 0, 0, 0, '/ucenter/ucenterMembers', 'layout/routerView/parent', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, '2023-03-10 08:38:55');

#菜单父级目录ID
SELECT @parentIdDir := LAST_INSERT_ID();


#用户中心配置
INSERT INTO `sys_auth_rule` ( `pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES ( @parentIdDir, 'api/v1/ucenter/ucenterConfig/list', '用户中心配置', 'ele-Setting', '', '', 1, 0, 0, '/ucenter/ucenterConfig/list', 'ucenter/ucenterConfig/list/index', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, '2023-03-09 17:14:28');


#按钮父级菜单ID
SELECT @parentId := LAST_INSERT_ID();

INSERT INTO `sys_auth_rule` ( `pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES ( @parentId, 'api/v1/ucenter/ucenterConfig/get', '用户中心配置查询', '', '', '用户中心配置查询', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` ( `pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES ( @parentId, 'api/v1/ucenter/ucenterConfig/add', '用户中心配置添加', '', '', '用户中心配置添加', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` ( `pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES ( @parentId, 'api/v1/ucenter/ucenterConfig/edit', '用户中心配置修改', '', '', '用户中心配置修改', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` ( `pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES ( @parentId, 'api/v1/ucenter/ucenterConfig/delete', '用户中心配置删除', '', '', '用户中心配置删除', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);


#用户等级


INSERT INTO `sys_auth_rule` ( `pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES ( @parentIdDir, 'api/v1/ucenter/ucenterLevel/list', '用户等级', 'ele-Flag', '', '', 1, 0, 0, '/ucenter/ucenterLevel/list', 'ucenter/ucenterLevel/list/index', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, '2023-03-09 17:14:52');

#按钮父级菜单ID
SELECT @parentId := LAST_INSERT_ID();

INSERT INTO `sys_auth_rule` ( `pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES ( @parentId, 'api/v1/ucenter/ucenterLevel/get', '用户等级查询', '', '', '用户等级查询', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` ( `pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES ( @parentId, 'api/v1/ucenter/ucenterLevel/add', '用户等级添加', '', '', '用户等级添加', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` ( `pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES ( @parentId, 'api/v1/ucenter/ucenterLevel/edit', '用户等级修改', '', '', '用户等级修改', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` ( `pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES ( @parentId, 'api/v1/ucenter/ucenterLevel/delete', '用户等级删除', '', '', '用户等级删除', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);



#用户列表
INSERT INTO `sys_auth_rule` ( `pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES ( @parentIdDir, 'api/v1/ucenter/ucenterMembers/list', '用户列表', 'ele-Avatar', '', '', 1, 0, 0, '/ucenter/ucenterMembers/list', 'ucenter/ucenterMembers/list/index', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, '2023-03-09 17:16:31');

#按钮父级菜单ID
SELECT @parentId := LAST_INSERT_ID();


INSERT INTO `sys_auth_rule` ( `pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES ( @parentId, 'api/v1/ucenter/ucenterMembers/get', '用户中心查询', '', '', '用户中心查询', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` ( `pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES ( @parentId, 'api/v1/ucenter/ucenterMembers/add', '用户中心添加', '', '', '用户中心添加', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` ( `pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES ( @parentId, 'api/v1/ucenter/ucenterMembers/edit', '用户中心修改', '', '', '用户中心修改', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` ( `pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES ( @parentId, 'api/v1/ucenter/ucenterMembers/delete', '用户中心删除', '', '', '用户中心删除', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);



#用户积分明细
INSERT INTO `sys_auth_rule` ( `pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES ( @parentIdDir, 'api/v1/ucenter/ucenterScores/list', '用户积分明细', 'ele-Coin', '', '', 1, 0, 0, '/ucenter/ucenterScores/list', 'ucenter/ucenterScores/list/index', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, '2023-03-09 17:15:14');


#按钮父级菜单ID
SELECT @parentId := LAST_INSERT_ID();

INSERT INTO `sys_auth_rule` ( `pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES ( @parentId, 'api/v1/ucenter/ucenterScores/get', '用户积分查询', '', '', '用户积分查询', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` ( `pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES ( @parentId, 'api/v1/ucenter/ucenterScores/add', '用户积分添加', '', '', '用户积分添加', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` ( `pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES ( @parentId, 'api/v1/ucenter/ucenterScores/edit', '用户积分修改', '', '', '用户积分修改', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` ( `pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES ( @parentId, 'api/v1/ucenter/ucenterScores/delete', '用户积分删除', '', '', '用户积分删除', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);




#用户消息管理
INSERT INTO `sys_auth_rule` ( `pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES ( @parentIdDir, 'api/v1/ucenter/ucenterMessage/list', '用户消息管理', 'ele-CopyDocument', '', '', 1, 0, 0, '/ucenter/ucenterMessage/list', 'ucenter/ucenterMessage/list/index', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, '2023-03-09 17:15:29');


#按钮父级菜单ID
SELECT @parentId := LAST_INSERT_ID();

INSERT INTO `sys_auth_rule` ( `pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES ( @parentId, 'api/v1/ucenter/ucenterMessage/get', '用户消息查询', '', '', '用户消息查询', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` ( `pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES ( @parentId, 'api/v1/ucenter/ucenterMessage/add', '用户消息添加', '', '', '用户消息添加', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` ( `pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES ( @parentId, 'api/v1/ucenter/ucenterMessage/edit', '用户消息修改', '', '', '用户消息修改', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` ( `pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES ( @parentId, 'api/v1/ucenter/ucenterMessage/delete', '用户消息删除', '', '', '用户消息删除', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);



#用户通知管理
INSERT INTO `sys_auth_rule` ( `pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES ( @parentIdDir, 'api/v1/ucenter/ucenterNotifications/list', '用户通知管理', 'ele-Bell', '', '', 1, 0, 0, '/ucenter/ucenterNotifications/list', 'ucenter/ucenterNotifications/list/index', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, '2023-03-09 17:15:57');


#按钮父级菜单ID
SELECT @parentId := LAST_INSERT_ID();

INSERT INTO `sys_auth_rule` ( `pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES ( @parentId, 'api/v1/ucenter/ucenterNotifications/get', '用户通知查询', '', '', '用户通知查询', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` ( `pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES ( @parentId, 'api/v1/ucenter/ucenterNotifications/add', '用户通知添加', '', '', '用户通知添加', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` ( `pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES ( @parentId, 'api/v1/ucenter/ucenterNotifications/edit', '用户通知修改', '', '', '用户通知修改', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` ( `pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES ( @parentId, 'api/v1/ucenter/ucenterNotifications/delete', '用户通知删除', '', '', '用户通知删除', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);


#系统访问记录管理
INSERT INTO `sys_auth_rule` ( `pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES ( @parentIdDir, 'api/v1/ucenter/ucenterLoginLog/list', '系统访问记录列表', 'ele-Fold', '', '系统访问记录列表', 1, 0, 0, '/ucenter/ucenterLoginLog/list', 'ucenter/ucenterLoginLog/list/index', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);

#按钮父级菜单ID
SELECT @parentId := LAST_INSERT_ID();

INSERT INTO `sys_auth_rule` ( `pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES ( @parentId, 'api/v1/ucenter/ucenterLoginLog/get', '系统访问记录查询', '', '', '系统访问记录查询', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` ( `pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES ( @parentId, 'api/v1/ucenter/ucenterLoginLog/add', '系统访问记录添加', '', '', '系统访问记录添加', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` ( `pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES ( @parentId, 'api/v1/ucenter/ucenterLoginLog/edit', '系统访问记录修改', '', '', '系统访问记录修改', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` ( `pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES ( @parentId, 'api/v1/ucenter/ucenterLoginLog/delete', '系统访问记录删除', '', '', '系统访问记录删除', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);

-- 删除原有sms数据
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/plugins/sms/pluginSmsConfig';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/plugins/sms/pluginSmsConfig/list';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/plugins/sms/pluginSmsConfig/get';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/plugins/sms/pluginSmsConfig/add';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/plugins/sms/pluginSmsConfig/edit';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/plugins/sms/pluginSmsConfig/delete';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/plugins/sms/pluginSmsConfig/export';


DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/plugins/sms/pluginSmsLog';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/plugins/sms/pluginSmsLog/list';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/plugins/sms/pluginSmsLog/get';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/plugins/sms/pluginSmsLog/add';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/plugins/sms/pluginSmsLog/edit';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/plugins/sms/pluginSmsLog/delete';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/plugins/sms/pluginSmsLog/export';



INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (0, 'api/v1/plugins/sms/pluginSmsConfig', '短信配置', 'iconfont icon-fuwenbenkuang', '', '', 0, 0, 0, '/plugins/sms/pluginSmsConfig', 'layout/routerView/parent', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, '2023-12-06 11:10:27');

-- SMS菜单父目录ID
SELECT @parentSMSId := LAST_INSERT_ID();

INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentSMSId, 'api/v1/plugins/sms/pluginSmsConfig/list', '短信配置列表', 'ele-Fold', '', '短信配置列表', 1, 0, 0, '/plugins/sms/pluginSmsConfig/list', 'plugins/sms/pluginSmsConfig/list/index', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);

-- SMS按钮父目录ID
SELECT @parentSMSMenuId := LAST_INSERT_ID();

INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentSMSMenuId, 'api/v1/plugins/sms/pluginSmsConfig/get', '短信配置查询', '', '', '短信配置查询', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentSMSMenuId, 'api/v1/plugins/sms/pluginSmsConfig/add', '短信配置添加', '', '', '短信配置添加', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentSMSMenuId, 'api/v1/plugins/sms/pluginSmsConfig/edit', '短信配置修改', '', '', '短信配置修改', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentSMSMenuId, 'api/v1/plugins/sms/pluginSmsConfig/delete', '短信配置删除', '', '', '短信配置删除', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);


INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentSMSId, 'api/v1/plugins/sms/pluginSmsLog/list', '短信日志列表', 'ele-Fold', '', '', 1, 0, 0, '/plugins/sms/pluginSmsLog/list', 'plugins/sms/pluginSmsLog/list/index', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, '2023-12-06 11:10:41');

-- SMS按钮父目录ID
SELECT @parentSMSMenuId := LAST_INSERT_ID();

INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentSMSMenuId, 'api/v1/plugins/sms/pluginSmsLog/get', '短信日志查询', '', '', '短信日志查询', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentSMSMenuId, 'api/v1/plugins/sms/pluginSmsLog/add', '短信日志添加', '', '', '短信日志添加', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentSMSMenuId, 'api/v1/plugins/sms/pluginSmsLog/edit', '短信日志修改', '', '', '短信日志修改', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentSMSMenuId, 'api/v1/plugins/sms/pluginSmsLog/delete', '短信日志删除', '', '', '短信日志删除', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, NULL);


-- 删除原有mail数据
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/plugins/mail/mail';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/plugins/mail/mail/list';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/plugins/mail/mail/add';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/plugins/mail/mail/get';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/plugins/mail/mail/edit';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/plugins/mail/mail/delete';



INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (0, 'api/v1/plugins/mail/mail', '邮件配置', 'iconfont icon-fuwenbenkuang', '', '', 0, 0, 0, '/plugins/mail/mail', 'layout/routerView/parent', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, '2023-11-13 17:01:22');

-- email菜单父目录ID
SELECT @parentMailId := LAST_INSERT_ID();

INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentMailId, 'api/v1/plugins/mail/mail/list', '配置列表', 'ele-Fold', '', '', 1, 0, 0, '/plugins/mail/mail/list', 'plugins/mail/mail/list/index', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, '2023-11-13 17:10:10');

-- SMS按钮父目录ID
SELECT @parentMenuMailId := LAST_INSERT_ID();

INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentMenuMailId, 'api/v1/plugins/mail/mail/add', '邮件配置添加', '', '', '', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, '2023-11-13 17:01:55');
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentMenuMailId, 'api/v1/plugins/mail/mail/get', '邮件配置查询', '', '', '', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, '2023-11-13 17:02:03');
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentMenuMailId, 'api/v1/plugins/mail/mail/edit', '邮件配置修改', '', '', '', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, '2023-11-13 17:02:14');
INSERT INTO `sys_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `menu_type`, `weigh`, `is_hide`, `path`, `component`, `is_link`, `module_type`, `model_id`, `is_iframe`, `is_cached`, `redirect`, `is_affix`, `link_url`, `created_at`, `updated_at`) VALUES (@parentMenuMailId, 'api/v1/plugins/mail/mail/delete', '邮件配置删除', '', '', '', 2, 0, 0, '', '', 0, 'sys_admin', 0, 0, 1, '', 0, '', NULL, '2023-11-13 17:02:22');

