export interface CommentColumns {
    id: number;  // ID
    title: string;  // 标题
    categoryId: number;  // 分类
    moduleId: number;  // 模型
    hits: number;  // 点击数
    flag: string;  // 推荐属性
    commentCount: number;  // 评论数
    userId: number;  // 用户ID
    readPermissions: string;  // 阅读权限
    originTitle: string;  // 来源名称
    createdAt: string;  // 创建日期
    isJump: string; // 是否跳转链接
    status:number;//状态
}

export interface CmsArticleCommentState {
    ids: any[];
    tableData: {
        data: Array<CommentColumns>;
        total: number;
        loading: boolean;
        param: {
            pageNum: number;
            pageSize: number;
            title: string | undefined;
            categoryId: number | undefined;
            moduleId: number | undefined;
            flag: string | undefined;
            status: string | undefined;
            newsTitle:string|undefined;
            memberId:number|undefined;
            content:string|undefined;
        };
    };
}
