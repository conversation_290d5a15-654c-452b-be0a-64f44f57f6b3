import request from '/@/utils/request'
// 查询用户通知列表
export function listUcenterNotifications(query:object) {
  return request({
    url: '/api/v1/ucenter/admin/ucenterNotifications/list',
    method: 'get',
    params: query
  })
}
// 查询用户通知详细
export function getUcenterNotifications(id:number) {
  return request({
    url: '/api/v1/ucenter/admin/ucenterNotifications/get',
    method: 'get',
    params: {
      id: id.toString()
    }
  })
}
// 新增用户通知
export function addUcenterNotifications(data:object) {
  return request({
    url: '/api/v1/ucenter/admin/ucenterNotifications/add',
    method: 'post',
    data: data
  })
}
// 修改用户通知
export function updateUcenterNotifications(data:object) {
  return request({
    url: '/api/v1/ucenter/admin/ucenterNotifications/edit',
    method: 'put',
    data: data
  })
}
// 删除用户通知
export function delUcenterNotifications(ids:number[]) {
  return request({
    url: '/api/v1/ucenter/admin/ucenterNotifications/delete',
    method: 'delete',
    data:{
      ids:ids
    }
  })
}
