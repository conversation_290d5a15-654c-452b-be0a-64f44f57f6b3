// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2023-10-17 14:50:36
// 生成路径: internal/app/cms/service/cms_tags.go
// 生成人：yxf
// desc:CMS标签
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/cms/model"
)

type ICmsTags interface {
	List(ctx context.Context, req *model.CmsTagsSearchReq) (res *model.CmsTagsSearchRes, err error)
	GetById(ctx context.Context, Id uint) (res *model.CmsTagsInfoRes, err error)
	Add(ctx context.Context, req *model.CmsTagsAddReq) (err error)
	Edit(ctx context.Context, req *model.CmsTagsEditReq) (err error)
	Delete(ctx context.Context, Id []uint) (err error)
}

var localCmsTags ICmsTags

func CmsTags() ICmsTags {
	if localCmsTags == nil {
		panic("implement not found for interface ICmsTags, forgot register?")
	}
	return localCmsTags
}

func RegisterCmsTags(i ICmsTags) {
	localCmsTags = i
}
