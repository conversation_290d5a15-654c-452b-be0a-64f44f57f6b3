// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-04-16 15:37:42
// 生成路径: internal/app/cms/dao/internal/cms_category_ipaccess.go
// 生成人：zhanghui
// desc:分类IP访问限制
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// CmsCategoryIpaccessDao is the manager for logic model data accessing and custom defined data operations functions management.
type CmsCategoryIpaccessDao struct {
	table   string                     // Table is the underlying table name of the DAO.
	group   string                     // Group is the database configuration group name of current DAO.
	columns CmsCategoryIpaccessColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// CmsCategoryIpaccessColumns defines and stores column names for table cms_category_ipaccess.
type CmsCategoryIpaccessColumns struct {
	Id          string //
	Name        string // 名称
	CategoryIds string // 栏目IDS
	Iptable     string // IP表
	CreatedAt   string // 创建时间
	UpdatedAt   string // 更新时间
}

var cmsCategoryIpaccessColumns = CmsCategoryIpaccessColumns{
	Id:          "id",
	Name:        "name",
	CategoryIds: "category_ids",
	Iptable:     "iptable",
	CreatedAt:   "created_at",
	UpdatedAt:   "updated_at",
}

// NewCmsCategoryIpaccessDao creates and returns a new DAO object for table data access.
func NewCmsCategoryIpaccessDao() *CmsCategoryIpaccessDao {
	return &CmsCategoryIpaccessDao{
		group:   "default",
		table:   "cms_category_ipaccess",
		columns: cmsCategoryIpaccessColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *CmsCategoryIpaccessDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *CmsCategoryIpaccessDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *CmsCategoryIpaccessDao) Columns() CmsCategoryIpaccessColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *CmsCategoryIpaccessDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *CmsCategoryIpaccessDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *CmsCategoryIpaccessDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
