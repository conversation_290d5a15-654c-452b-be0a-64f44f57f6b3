// ==========================================================================
// GFast自动生成router操作代码。
// 生成日期：2023-01-31 09:33:48
// 生成路径: internal/app/cms/router/cms_category.go
// 生成人：yxf
// desc:文章分类
// company:云南奇讯科技有限公司
// ==========================================================================

package router

import (
	"context"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/internal/app/cms/controller"
)

func (router *Router) BindCmsCategoryController(ctx context.Context, group *ghttp.RouterGroup) {
	group.Group("/cmsCategory", func(group *ghttp.RouterGroup) {
		group.Bind(
			controller.CmsCategory,
		)
	})
}
