<template>
  <div class="right-content">
    <div class="cms-cmsCategory-search mb15">
      <el-form ref="queryRef" :inline="true" :model="tableData.param" label-width="80px">
        <el-row>
          <el-form-item label="分类名称" prop="name">
            <el-input v-model.trim="tableData.param.name" clearable placeholder="请输入名称" @keyup.enter.native="handleSearch"/>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><ele-Search/></el-icon>
              搜索
            </el-button>
            <el-button @click="resetQuery">
              <el-icon><ele-Refresh/></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-row>
      </el-form>
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button v-auth="'api/v1/cms/cmsCategory/add'" type="primary" @click="handleAdd" :disabled="addButtonDisabled">
            <el-icon><ele-Plus/></el-icon>
            新增
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button v-auth="'api/v1/cms/cmsCategory/delete'" :disabled="multiple" type="danger" @click="handleDelete(null)">
            <el-icon><ele-Delete /></el-icon>
            删除
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button v-auth="'api/v1/cms/cmsCategory/savePermissions'" type="primary" @click="handleOpenPermissionDialog">
            <el-icon><ele-Key /></el-icon>
            分类权限
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" @click="handleRefresh">
            <el-icon><ele-Refresh /></el-icon>
            刷新
          </el-button>
        </el-col>
      </el-row>
    </div>

    <el-table
      v-loading="loading"
      :data="tableData.data"
      @selection-change="handleSelectionChange"
      style="width: 100%"
      row-key="id"
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      default-expand-all
      v-if="!props.selectedCategory"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" prop="id" width="80" />
      <el-table-column label="分类名称" prop="name" :show-overflow-tooltip="true" tree-node>
        <template #default="scope">
          <span>
            {{ scope.row.name }}
            <el-tag v-if="scope.row.type === 'channel'" size="small" type="info">频道</el-tag>
            <el-tag v-else-if="scope.row.type === 'list'" size="small" type="success">列表</el-tag>
          </span>
        </template>
      </el-table-column>
      <el-table-column label="模型" align="center" width="120">
        <template #default="scope">
          <span v-if="scope.row.linkedModuleId && scope.row.linkedModuleId.name">
            {{ scope.row.linkedModuleId.name }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="排序" prop="sort" width="80" />
      <el-table-column label="状态" align="center" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.status === '1' ? 'success' : 'info'">
            {{ scope.row.status === '1' ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createdAt" width="180" />
      <el-table-column label="操作" align="center" width="240" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            type="primary"
            link
            @click="handleUpdate(scope.row)"
            v-auth="'api/v1/cms/cmsCategory/edit'"
          ><el-icon><ele-Edit /></el-icon>修改</el-button>
          <el-button v-if="scope.row.type==='channel'" v-auth="'api/v1/cms/cmsCategory/edit'" link type="primary" @click="handleAdd($event,scope.row.id)">
            增加子类
          </el-button>
          <el-button
            type="primary"
            link
            @click="handleDelete(scope.row)"
            v-auth="'api/v1/cms/cmsCategory/delete'"
          ><el-icon><ele-DeleteFilled /></el-icon>删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 当有选中分类时显示的普通表格 -->
    <el-table
      v-loading="loading"
      :data="tableData.data"
      @selection-change="handleSelectionChange"
      style="width: 100%"
      row-key="id"
      v-if="props.selectedCategory"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" prop="id" width="80" />
      <el-table-column label="分类名称" prop="name" :show-overflow-tooltip="true">
        <template #default="scope">
          <span>
            {{ scope.row.name }}
            <el-tag v-if="scope.row.type === 'channel'" size="small" type="info">频道</el-tag>
            <el-tag v-else-if="scope.row.type === 'list'" size="small" type="success">列表</el-tag>
          </span>
        </template>
      </el-table-column>
      <el-table-column label="模型" align="center" width="120">
        <template #default="scope">
          <span v-if="scope.row.linkedModuleId && scope.row.linkedModuleId.name">
            {{ scope.row.linkedModuleId.name }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="排序" prop="sort" width="80" />
      <el-table-column label="状态" align="center" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.status === '1' ? 'success' : 'info'">
            {{ scope.row.status === '1' ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createdAt" width="180" />
      <el-table-column label="操作" align="center" width="240" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            type="primary"
            link
            @click="handleUpdate(scope.row)"
            v-auth="'api/v1/cms/cmsCategory/edit'"
          ><el-icon><ele-Edit /></el-icon>修改</el-button>
          <el-button v-if="scope.row.type==='channel'" v-auth="'api/v1/cms/cmsCategory/edit'" link type="primary" @click="handleAdd($event,scope.row.id)">
            增加子类
          </el-button>
          <el-button
            type="primary"
            link
            @click="handleDelete(scope.row)"
            v-auth="'api/v1/cms/cmsCategory/delete'"
          ><el-icon><ele-DeleteFilled /></el-icon>删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 表格树不需要分页 -->

    <!-- 分类权限对话框 -->
    <el-dialog v-model="categoryPermissionDialogVisible" title="分类权限" width="600px">
      <el-form :model="categoryPermissionForm" label-width="100px">
        <el-form-item label="角色">
          <el-select v-model="categoryPermissionForm.roleId" placeholder="请选择角色" style="width: 100%">
            <el-option
              v-for="item in roleOptions"
              :key="item.id"
              :label="item.roleName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="分类权限">
          <el-tree
            ref="permissionTreeRef"
            :data="categoryPermissionTreeData"
            show-checkbox
            node-key="id"
            :props="{
              label: 'name',
              children: 'children'
            }"
            :default-checked-keys="checkedCategoryIds"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="categoryPermissionDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSavePermissions">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, toRefs, onMounted, getCurrentInstance, watch, computed } from 'vue';
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus';
import {
  listCmsCategory,
  getCmsCategory,
  delCmsCategory,
  addCmsCategory,
  updateCmsCategory,
  listCmsCategoryPermissions,
  saveCmsCategoryPermissions
} from '/@/api/cms/cmsCategory';
import { getRoleList } from '/@/api/system/role/index';

export default defineComponent({
  name: 'CmsCategoryRightList',
  props: {
    selectedCategory: {
      type: Object,
      default: () => null
    }
  },
  emits: ['refresh-tree', 'open-edit', 'open-permission'],
  setup(props, { emit }) {
    const { proxy } = <any>getCurrentInstance();
    const queryRef = ref<FormInstance>();
    const loading = ref(false);
    const multiple = ref(true);
    const single = ref(true);
    const ids = ref<number[]>([]);

    // 计算属性：判断新增按钮是否应该被禁用
    const addButtonDisabled = computed(() => {
      // 在树表格模式下，新增按钮始终可用
      return false;
    });

    // 表格数据
    const state = reactive({
      tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
          name: undefined,
          parentId: undefined
        }
      }
    });

    // 分类权限相关
    const categoryPermissionDialogVisible = ref(false);
    const categoryPermissionForm = reactive({
      roleId: undefined
    });
    const roleOptions = ref<any[]>([]);
    const categoryPermissionTreeData = ref<any[]>([]);
    const checkedCategoryIds = ref<number[]>([]);
    const permissionTreeRef = ref();

    // 注意: 新的 buildTreeData 方法已经实现在下面

    // 构建树形数据
    const buildTreeData = (list) => {
      // 使用 Map 优化查找性能
      const map = new Map();
      list.forEach(item => map.set(item.id, { ...item, children: [] }));

      const result = [];
      list.forEach(item => {
        const node = map.get(item.id);
        if (item.parentId && map.has(item.parentId)) {
          const parent = map.get(item.parentId);
          if (parent && node) {
            parent.children.push(node);
          }
        } else if (node) {
          result.push(node);
        }
      });

      return result;
    };

    // 获取列表数据
    const getList = () => {
      loading.value = true;
      // 构建请求参数
      const params = {
        ...state.tableData.param,
        // 如果有选中的分类，则添加 parentId 和 childNode 参数
        parentId: props.selectedCategory ? props.selectedCategory.id : undefined,
        childNode: 1 // 固定值为 1
      };

      console.log('请求参数:', params);
      // 使用不带分页参数的请求
      listCmsCategory(params).then(res => {
        if (res.code === 0) {
          let list = [];
          // 检查数据结构
          if (res.data && Array.isArray(res.data)) {
            list = res.data;
          } else if (res.data && res.data.list && Array.isArray(res.data.list)) {
            list = res.data.list;
          }

          // 如果有选中的分类，直接使用列表数据，不需要构建树
          if (props.selectedCategory) {
            state.tableData.data = list;
          } else {
            // 如果没有选中分类，则构建树形数据
            state.tableData.data = buildTreeData(list);
          }
          state.tableData.total = list.length || 0;
        } else {
          state.tableData.data = [];
          state.tableData.total = 0;
        }
      }).finally(() => {
        loading.value = false;
      });
    };

    // 页面加载时获取数据
    onMounted(() => {
      getList();
    });

    // 监听选中的分类变化
    watch(() => props.selectedCategory, (newVal) => {
      console.log('选中分类变化:', newVal);
      // 重新获取数据
      getList();
    }, { immediate: true });

    // 搜索
    const handleSearch = () => {
      // 如果有搜索条件，则过滤树形数据
      if (state.tableData.param.name) {
        // 重新获取数据并过滤
        getList();
      } else {
        // 如果没有搜索条件，直接获取全部数据
        getList();
      }
    };

    // 重置查询
    const resetQuery = () => {
      queryRef.value?.resetFields();
      state.tableData.param.name = undefined;
      // 重新获取全部数据
      getList();
    };

    // 刷新列表
    const refreshList = () => {
      getList();
    };

    // 处理刷新按钮点击
    const handleRefresh = () => {
      // 重置搜索条件
      queryRef.value?.resetFields();
      state.tableData.param.name = undefined;
      // 重新获取全部数据
      getList();
      // 触发刷新树事件
      emit('refresh-tree');
    };

    // 多选框选中数据
    const handleSelectionChange = (selection: any[]) => {
      ids.value = selection.map(item => item.id);
      multiple.value = !selection.length;
      single.value = selection.length !== 1;
    };

    // 新增
    const handleAdd = (evt?: Event, id?: number) => {
      // 如果传入了 id，说明是“增加子类”操作
      if (id) {
        console.log('增加子类, 父ID:', id);
        // 触发打开编辑组件事件，带上父分类 ID
        emit('open-edit', { parentId: id });
        return;
      }

      // 普通的新增操作
      // 如果没有选中的分类，则父ID为0（根节点）
      const parentId = 0;
      // 触发打开编辑组件事件，带上父分类 ID
      emit('open-edit', { parentId });
    };

    // 修改
    const handleUpdate = (row: any) => {
      if (!row) {
        // 如果没有传入行数据，则使用选中的行
        if (ids.value.length !== 1) {
          ElMessage.warning('请选择一条要修改的数据');
          return;
        }
        row = state.tableData.data.find((item: any) => item.id === ids.value[0]);
      }
      // 触发打开编辑组件事件，带上父分类 ID 和行数据
      emit('open-edit', { parentId: row.parentId, row });
    };

    // 删除
    const handleDelete = (row: any) => {
      const categoryIds = row ? [row.id] : ids.value;
      if (!categoryIds || categoryIds.length === 0) {
        ElMessage.warning('请选择要删除的数据');
        return;
      }

      ElMessageBox.confirm('确认要删除选中的数据吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delCmsCategory(categoryIds).then(() => {
          ElMessage.success('删除成功');
          // 删除成功后刷新列表
          getList();
          // 无论删除的是什么，都需要刷新树，因为树结构可能发生变化
          emit('refresh-tree');
        });
      });
    };

    // 获取角色列表
    const loadRoleList = () => {
      getRoleList({}).then(res => {
        roleOptions.value = res.data.list || [];
        if (roleOptions.value.length > 0) {
          categoryPermissionForm.roleId = roleOptions.value[0].id;
          getCategoryPermissionsByRole(categoryPermissionForm.roleId);
        }
      });
    };

    // 获取分类权限树
    const getCategoryPermissionTree = () => {
      listCmsCategory({}).then(res => {
        categoryPermissionTreeData.value = res.data || [];
      });
    };

    // 根据角色获取分类权限
    const getCategoryPermissionsByRole = (roleId: number) => {
      listCmsCategoryPermissions({ roleId: roleId }).then(res => {
        checkedCategoryIds.value = res.data || [];
      });
    };

    // 打开分类权限对话框
    const handleOpenPermissionDialog = () => {
      // 触发打开分类权限对话框事件
      emit('open-permission');
    };

    // 保存分类权限
    const handleSavePermissions = () => {
      if (!categoryPermissionForm.roleId) {
        ElMessage.warning('请选择角色');
        return;
      }

      const checkedKeys = permissionTreeRef.value.getCheckedKeys();
      const halfCheckedKeys = permissionTreeRef.value.getHalfCheckedKeys();
      const categoryIds = [...checkedKeys, ...halfCheckedKeys];

      saveCmsCategoryPermissions({
        roleId: categoryPermissionForm.roleId,
        categoryIds: categoryIds
      }).then(() => {
        ElMessage.success('保存成功');
        categoryPermissionDialogVisible.value = false;
      });
    };

    // 监听角色变化
    watch(() => categoryPermissionForm.roleId, (newVal) => {
      if (newVal) {
        getCategoryPermissionsByRole(newVal);
      }
    });

    // onMounted 已经在上面定义

    return {
      queryRef,
      loading,
      multiple,
      single,
      addButtonDisabled,
      ...toRefs(state),
      ids,
      handleSearch,
      resetQuery,
      refreshList,
      handleRefresh,
      handleSelectionChange,
      handleAdd,
      handleUpdate,
      handleDelete,
      // 分类权限相关
      categoryPermissionDialogVisible,
      categoryPermissionForm,
      roleOptions,
      categoryPermissionTreeData,
      checkedCategoryIds,
      permissionTreeRef,
      loadRoleList,
      getCategoryPermissionTree,
      handleOpenPermissionDialog,
      handleSavePermissions
    };
  }
});
</script>

<style scoped>
.right-content {
  flex: 1;
  padding: 0 10px;
  overflow: auto;
}

.cms-cmsCategory-search {
  margin-bottom: 15px;
}

.mb8 {
  margin-bottom: 8px;
}
</style>
