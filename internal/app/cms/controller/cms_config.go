// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2023-01-31 09:13:21
// 生成路径: internal/app/cms/controller/cms_config.go
// 生成人：yxf
// desc:CMS配置
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"
	"github.com/tiger1103/gfast/v3/api/v1/cms"
	"github.com/tiger1103/gfast/v3/internal/app/cms/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type cmsConfigController struct {
	systemController.BaseController
}

var CmsConfig = new(cmsConfigController)

// List 列表
func (c *cmsConfigController) List(ctx context.Context, req *cms.CmsConfigSearchReq) (res *cms.CmsConfigSearchRes, err error) {
	res, err = service.CmsConfig().List(ctx, req)
	return
}

// Get 获取CMS配置
func (c *cmsConfigController) Get(ctx context.Context, req *cms.CmsConfigGetReq) (res *cms.CmsConfigGetRes, err error) {
	res = new(cms.CmsConfigGetRes)
	res.CmsConfigInfoRes, err = service.CmsConfig().GetById(ctx, req.Id)
	return
}

// Add 添加CMS配置
func (c *cmsConfigController) Add(ctx context.Context, req *cms.CmsConfigAddReq) (res *cms.CmsConfigAddRes, err error) {
	err = service.CmsConfig().Add(ctx, req)
	return
}

// Edit 修改CMS配置
func (c *cmsConfigController) Edit(ctx context.Context, req *cms.CmsConfigEditReq) (res *cms.CmsConfigEditRes, err error) {
	err = service.CmsConfig().Edit(ctx, req)
	return
}

// Delete 删除CMS配置
func (c *cmsConfigController) Delete(ctx context.Context, req *cms.CmsConfigDeleteReq) (res *cms.CmsConfigDeleteRes, err error) {
	err = service.CmsConfig().Delete(ctx, req.Ids)
	return
}
