package logic

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/api/v1/cms"
	"github.com/tiger1103/gfast/v3/api/v1/home"
	"github.com/tiger1103/gfast/v3/internal/app/cms/dao"
	"github.com/tiger1103/gfast/v3/internal/app/home/<USER>"
	"github.com/tiger1103/gfast/v3/internal/app/home/<USER>"
	"github.com/tiger1103/gfast/v3/library/libCms"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterCategory(NewCategory())
}

func NewCategory() *sCategory { return &sCategory{} }

type sCategory struct{}

func (s *sCategory) List(ctx context.Context, req *home.CategorySearchReq) (listRes *home.CategorySearchRes, err error) {
	listRes = new(home.CategorySearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.CmsCategory.Ctx(ctx).WithAll()
		if req.Name != "" {
			m = m.Where(dao.CmsCategory.Columns().Name+" like ?", "%"+req.Name+"%")
		}
		if req.Status != "" {
			m = m.Where(dao.CmsCategory.Columns().Status+"=?", req.Status)
		}
		if req.ParentId != "" {
			m = m.Where(dao.CmsCategory.Columns().ParentId+"=?", req.ParentId)
		}
		if req.Type != "" {
			m = m.Where(dao.CmsCategory.Columns().Type+"=?", req.Type)
		}
		order := "sort asc, id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.CategoryInfoRes
		err = m.Fields(cms.CmsCategorySearchRes{}).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.CategoryListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.CategoryListRes{
				Id:             v.Id,
				Name:           v.Name,
				ParentId:       v.ParentId,
				Type:           v.Type,
				ModuleId:       v.ModuleId,
				LinkedModuleId: v.LinkedModuleId,
				Sort:           v.Sort,
				Thumb:          v.Thumb,
				Status:         v.Status,
				Alias:          v.Alias,
				JumpUrl:        v.JumpUrl,
				Url:            libCms.GetCmsUrl(v.Type, v.Url, gconv.Int(v.Id)),
			}
		}
	})
	return
}
func (s *sCategory) GetById(ctx context.Context, id uint) (res *model.CategoryInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.CmsCategory.Ctx(ctx).WithAll().Where(dao.CmsCategory.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}
