import{_ as O}from"./editMenu.vue_vue_type_script_setup_true_lang-Cy3I_ree.js";import{h as P,i as F}from"./index-BGmsw1a8.js";import{E as U,a as G}from"./element-plus-CUmVNDWO.js";import{d as J,r as V,X as Q,ad as X,f as j,a2 as K,ag as n,aq as W,c as Y,o as p,P as e,H as o,a as w,u as r,I as y,L as s,G as v,M as u,J as Z}from"./@vue-C21YZbHS.js";import"./index-Crq2syth.js";import"./getStyleSheets-CiOwvIk9.js";import"./@element-plus-_Cc-TEQX.js";import"./pinia-DwJf82dV.js";import"./vue-router-BDT9dCQ7.js";import"./js-cookie-Cz0CWeBA.js";import"./nprogress-Scw6VIZr.js";import"./@intlify-D8kAWvSi.js";import"./axios-B62VicFi.js";import"./qs-D41HS0uK.js";import"./side-channel-DytTmCZg.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-DbsE5JW_.js";import"./crypto-js-oM26lpET.js";import"./side-channel-list-C_7Uk4RP.js";import"./side-channel-map-BUokNT8A.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-bSkmVQJL.js";import"./vue-i18n-BkLk9Ti1.js";import"./vue-iNFchrNl.js";import"./vue-clipboard3-Dr8V5xR7.js";import"./clipboard-DzJOpNE6.js";import"./mitt-DJ65BbbF.js";import"./vue-grid-layout-CIPjP0XN.js";import"./vue-simple-uploader-j6sX1TKo.js";import"./vue-ueditor-wrap-CJ5gjsd3.js";import"./lodash-es-S0Y0Up6J.js";import"./@vueuse-D_5IXmcI.js";import"./@popperjs-D_chPuIy.js";import"./@ctrl-r5W6hzzQ.js";import"./dayjs-CepoAVPu.js";import"./async-validator-9PlIezaS.js";import"./memoize-one-BdPwpGay.js";import"./normalize-wheel-es-BQoi3Ox2.js";import"./@floating-ui-DwceP2Gb.js";const ee={class:"system-menu-container"},te={class:"system-menu-search mb15"},oe={class:"ml10"},et=J({name:"apiV1SystemAuthMenuList",__name:"index",setup(le){const h=V(),b=Q({queryParams:{title:"",component:""},menuTableData:[]}),{queryParams:d,menuTableData:z}=X(b),{proxy:c}=j(),{sys_show_hide:C}=c.useDict("sys_show_hide"),g=V("add"),k=a=>{g.value="add",h.value.openDialog(a)},B=a=>{g.value="edit",h.value.openDialog(a)},S=a=>{U.confirm(`此操作将永久删除菜单：“${a.title}”, 是否继续?`,"提示",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"}).then(()=>{F(a.id).then(()=>{G.success("删除成功"),c.$refs.editMenuRef.resetMenuSession(),_()})}).catch(()=>{})},$=a=>c.selectDictLabel(r(C),""+a.isHide);K(()=>{_()});const L=()=>{_()},_=()=>{P(b.queryParams).then(a=>{b.menuTableData=c.handleTree(a.data.rules??[],"id","pid")})};return(a,l)=>{const M=n("el-input"),T=n("el-form-item"),E=n("ele-Search"),x=n("el-icon"),m=n("el-button"),I=n("ele-FolderAdd"),R=n("el-form"),q=n("SvgIcon"),i=n("el-table-column"),A=n("el-tag"),H=n("el-table"),N=n("el-card"),f=W("auth");return p(),Y("div",ee,[e(N,{shadow:"hover"},{default:o(()=>[w("div",te,[e(R,{inline:!0},{default:o(()=>[e(T,{label:"菜单名称"},{default:o(()=>[e(M,{modelValue:r(d).title,"onUpdate:modelValue":l[0]||(l[0]=t=>r(d).title=t),placeholder:"请输入菜单名称",clearable:"",class:"w-50 m-2",size:"default"},null,8,["modelValue"])]),_:1}),e(T,{label:"组件路径"},{default:o(()=>[e(M,{modelValue:r(d).component,"onUpdate:modelValue":l[1]||(l[1]=t=>r(d).component=t),placeholder:"请输入组件路径",clearable:"",size:"default",class:"w-50 m-2"},null,8,["modelValue"])]),_:1}),e(T,null,{default:o(()=>[e(m,{size:"default",type:"primary",class:"ml10",onClick:L},{default:o(()=>[e(x,null,{default:o(()=>[e(E)]),_:1}),l[3]||(l[3]=s(" 查询 "))]),_:1}),y((p(),v(m,{size:"default",type:"success",class:"ml10",onClick:l[2]||(l[2]=t=>k(null))},{default:o(()=>[e(x,null,{default:o(()=>[e(I)]),_:1}),l[4]||(l[4]=s(" 新增菜单 "))]),_:1})),[[f,"api/v1/system/menu/add"]])]),_:1})]),_:1})]),e(H,{data:r(z),style:{width:"100%"},"row-key":"path","tree-props":{children:"children",hasChildren:"hasChildren"}},{default:o(()=>[e(i,{label:"菜单名称","show-overflow-tooltip":""},{default:o(t=>[e(q,{name:t.row.icon},null,8,["name"]),w("span",oe,u(t.row.title),1)]),_:1}),e(i,{prop:"path",label:"路由路径","show-overflow-tooltip":""}),e(i,{label:"组件路径","show-overflow-tooltip":""},{default:o(t=>[w("span",null,u(t.row.component),1)]),_:1}),e(i,{label:"api接口","show-overflow-tooltip":""},{default:o(t=>[w("span",null,u(t.row.name),1)]),_:1}),e(i,{label:"排序","show-overflow-tooltip":"",width:"80"},{default:o(t=>[s(u(t.row.weigh),1)]),_:1}),e(i,{label:"类型","show-overflow-tooltip":"",width:"80"},{default:o(t=>[e(A,{type:t.row.menuType===0?"danger":t.row.menuType===1?"success":"warning",size:"small"},{default:o(()=>[s(u(t.row.menuType===0?"目录":t.row.menuType===1?"菜单":"按钮"),1)]),_:2},1032,["type"])]),_:1}),e(i,{prop:"isHide",label:"显示状态",formatter:$,width:"120"}),e(i,{label:"操作",width:"240"},{default:o(t=>[t.row.menuType!==2?y((p(),v(m,{key:0,size:"small",text:"",type:"primary",onClick:D=>k(t.row)},{default:o(()=>l[5]||(l[5]=[s("新增")])),_:2},1032,["onClick"])),[[f,"api/v1/system/menu/add"]]):Z("",!0),y((p(),v(m,{size:"small",text:"",type:"primary",onClick:D=>B(t.row)},{default:o(()=>l[6]||(l[6]=[s("修改")])),_:2},1032,["onClick"])),[[f,"api/v1/system/menu/update"]]),y((p(),v(m,{size:"small",text:"",type:"primary",onClick:D=>S(t.row)},{default:o(()=>l[7]||(l[7]=[s("删除")])),_:2},1032,["onClick"])),[[f,"api/v1/system/menu/delete"]])]),_:1})]),_:1},8,["data"])]),_:1}),e(O,{ref_key:"editMenuRef",ref:h,onMenuList:_,visibleOptions:r(C),acType:g.value},null,8,["visibleOptions","acType"])])}}});export{et as default};
