/**
 * @Company: 云南奇讯科技有限公司
 * @Author: yxf
 * @Description:
 * @Version: 1.0.0
 * @Date: 2023/3/30 17:54
 */

package api

import (
	"context"
	"errors"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/api/v1/home"
	"github.com/tiger1103/gfast/v3/internal/app/home/<USER>"
	ucenterService "github.com/tiger1103/gfast/v3/internal/app/ucenter/service"
	"github.com/tiger1103/gfast/v3/library/libResponse"
)

type commentController struct {
}

var CommentController = new(commentController)

// List 评论列表
func (c *commentController) List(ctx context.Context, req *home.CommentSearchListReq) (res *home.CommentSearchListRes, err error) {
	res, err = service.Comment().List(ctx, req)
	return
}

// Add 添加文章评论
func (c *commentController) Add(ctx context.Context, req *home.CommentAddReq) (res *home.CommentAddRes, err error) {
	req.MemberId = ucenterService.Context().GetMemberId(ctx)
	err = service.Comment().Add(ctx, req)
	if err == nil {
		r := g.RequestFromCtx(ctx)
		articleInfo, _ := service.Article().GetById(ctx, req.ArticleId)
		commentCount := 0
		if articleInfo != nil {
			commentCount = articleInfo.CommentCount
		}
		libResponse.SusJson(true, r, "评论成功", g.Map{"commentCount": commentCount})
	}
	return
}

// Delete 删除文章评论
func (c *commentController) Delete(ctx context.Context, req *home.CommentDeleteReq) (res *home.CommentDeleteRes, err error) {
	req.MemberId = ucenterService.Context().GetMemberId(ctx)
	if req.MemberId <= 0 {
		return nil, errors.New("请先登录")
	}
	err = service.Comment().Delete(ctx, req)
	return
}
