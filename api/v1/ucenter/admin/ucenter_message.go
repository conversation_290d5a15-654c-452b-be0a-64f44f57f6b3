// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2023-03-09 16:51:57
// 生成路径: api/v1/ucenter/ucenter_message.go
// 生成人：yxf
// desc:用户消息相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ucenter/model"
)

// UCenterMessageSearchReq 分页请求参数
type UCenterMessageSearchReq struct {
	g.Meta   `path:"/list" tags:"ucenter后台/用户消息" method:"get" summary:"用户消息列表"`
	Keywords string `p:"keywords"`
	MsgType  string `p:"msgType"`
	commonApi.PageReq
	commonApi.Author
}

// UCenterMessageSearchRes 列表返回结果
type UCenterMessageSearchRes struct {
	g.Meta `mime:"application/json"`
	commonApi.ListRes
	List []*model.UCenterMessageListRes `json:"list"`
}

// UCenterMessageAddReq 添加操作请求参数
type UCenterMessageAddReq struct {
	g.Meta `path:"/add" tags:"ucenter后台/用户消息" method:"post" summary:"用户消息添加"`
	commonApi.Author
	Sender   uint64 `p:"sender" `
	Receiver uint64 `p:"receiver" `
	Content  string `p:"content" `
	Status   int    `p:"status" v:"required#状态:{0:未读,1:已读,2:已回复}不能为空"`
	Pid      uint64 `p:"pid" `
}

// UCenterMessageAddRes 添加操作返回结果
type UCenterMessageAddRes struct {
	commonApi.EmptyRes
}

// UCenterMessageEditReq 修改操作请求参数
type UCenterMessageEditReq struct {
	g.Meta `path:"/edit" tags:"ucenter后台/用户消息" method:"put" summary:"用户消息修改"`
	commonApi.Author
	Id       uint64 `p:"id" v:"required#主键ID不能为空"`
	Sender   uint64 `p:"sender" `
	Receiver uint64 `p:"receiver" `
	Content  string `p:"content" `
	Status   int    `p:"status" v:"required#状态:{0:未读,1:已读,2:已回复}不能为空"`
	Pid      uint64 `p:"pid" `
}

// UCenterMessageEditRes 修改操作返回结果
type UCenterMessageEditRes struct {
	commonApi.EmptyRes
}

// UCenterMessageGetReq 获取一条数据请求
type UCenterMessageGetReq struct {
	g.Meta `path:"/get" tags:"ucenter后台/用户消息" method:"get" summary:"获取用户消息信息"`
	commonApi.Author
	Id uint64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// UCenterMessageGetRes 获取一条数据结果
type UCenterMessageGetRes struct {
	g.Meta `mime:"application/json"`
	*model.UCenterMessageInfoRes
}

// UCenterMessageDeleteReq 删除数据请求
type UCenterMessageDeleteReq struct {
	g.Meta `path:"/delete" tags:"ucenter后台/用户消息" method:"delete" summary:"删除用户消息"`
	commonApi.Author
	Ids []uint64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// UCenterMessageDeleteRes 删除数据返回
type UCenterMessageDeleteRes struct {
	commonApi.EmptyRes
}
