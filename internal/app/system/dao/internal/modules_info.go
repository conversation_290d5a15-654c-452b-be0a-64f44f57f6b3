// ==========================================================================
// Code generated by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// ModulesInfoDao is the data access object for table modules_info.
type ModulesInfoDao struct {
	table   string             // table is the underlying table name of the DAO.
	group   string             // group is the database configuration group name of current DAO.
	columns ModulesInfoColumns // columns contains all the column names of Table for convenient usage.
}

// ModulesInfoColumns defines and stores column names for table modules_info.
type ModulesInfoColumns struct {
	Id        string // ID
	Name      string // 模型名称
	Sort      string // 排序
	TableName string // 数据表名
	CreatedAt string // 创建日期
	UpdatedAt string // 更新日期
	Status    string //
	Fields    string // 字段信息
}

// modulesInfoColumns holds the columns for table modules_info.
var modulesInfoColumns = ModulesInfoColumns{
	Id:        "id",
	Name:      "name",
	Sort:      "sort",
	TableName: "table_name",
	CreatedAt: "created_at",
	UpdatedAt: "updated_at",
	Status:    "status",
	Fields:    "fields",
}

// NewModulesInfoDao creates and returns a new DAO object for table data access.
func NewModulesInfoDao() *ModulesInfoDao {
	return &ModulesInfoDao{
		group:   "default",
		table:   "modules_info",
		columns: modulesInfoColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *ModulesInfoDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *ModulesInfoDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *ModulesInfoDao) Columns() ModulesInfoColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *ModulesInfoDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *ModulesInfoDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *ModulesInfoDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
