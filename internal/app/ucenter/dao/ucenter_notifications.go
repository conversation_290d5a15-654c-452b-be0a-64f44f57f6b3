// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2023-03-09 16:56:57
// 生成路径: internal/app/ucenter/dao/ucenter_notifications.go
// 生成人：yxf
// desc:用户通知
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/ucenter/dao/internal"
)

// ucenterNotificationsDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type ucenterNotificationsDao struct {
	*internal.UCenterNotificationsDao
}

var (
	// UCenterNotifications is globally public accessible object for table tools_gen_table operations.
	UCenterNotifications = ucenterNotificationsDao{
		internal.NewUCenterNotificationsDao(),
	}
)

// Fill with you ideas below.
