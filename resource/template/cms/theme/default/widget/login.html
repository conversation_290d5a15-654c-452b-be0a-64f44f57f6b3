<div class="member-form-wrap member-form-login">
    <div class="member-form-inner">
        <div class="member-form-head">
            <div class="member-form-head">
                <a class="member-form-logo" href="${.cmsConfig.site_url}">
                    <img src="/cms/default/images/cms_logo.png">
                </a>
            </div>
        </div>
        <ul class="member-form-tab">
            <li class="active"><a data-type="1" href="#">账号密码登录</a></li>
            <li><a data-type="2" href="#">手机号码登录</a></li>
        </ul>
        <form class="member-form j-member-form" id="login-form" method="post" action="">
            <div class="member-form-items">
                <div class="form-group login-item login-1">
                    <label>
                        <i class="iconfont icon-user hotnews-icon"></i>
                        <input class="form-input require" id="username" name="username" placeholder="请输入手机号码/电子邮箱/用户名" type="text">
                    </label>
                </div>
                <div class="form-group login-item login-1">
                    <label>
                        <i class="iconfont icon-lock hotnews-icon"></i>
                        <input class="form-input" id="password" name="password" placeholder="请输入登录密码" type="password">
                        <span class="show-password">
                            <i class="iconfont icon-eye-close hotnews-icon"></i>
                        </span>
                    </label>
                </div>
                <div class="form-group login-item login-2">
                    <label>
                        <i class="iconfont icon-phone hotnews-icon"></i>
                        <input class="form-input is-input require" data-label="手机号码" data-rule="mobile" id="mobile" name="mobile" placeholder="请输入手机号码" type="text">
                    </label>
                </div>
                <div class="form-group captcha">
                    <label>
                        <i class="iconfont icon-safetycertificate hotnews-icon"></i>
                        <input class="form-input require captcha-code" id="captcha_code" name="verifyCode" placeholder="请输入验证码" type="text">
                    </label>
                    <div class="captcha-img">
                        <img id="captcha_img" src="">
                        <input id="captcha_key" name="verifyKey" value="" type="hidden">
                    </div>
                </div>
                <div class="login-item login-2">
                    <div class="form-group sms-code">
                        <label>
                            <i class="iconfont icon-safetycertificate hotnews-icon"></i>
                            <input autocomplete="off" class="form-input is-input require" data-label="验证码" data-rule="sms_code:user_phone" id="msg_code" name="msgCode"
                                    placeholder="请输入短信验证码" type="text">
                        </label>
                        <div class="btn btn-lg send-sms-code j-send-sms-code" data-target="user_phone" id="sendMobileCode">发送验证码</div>
                    </div>
                </div>
            
            </div>
            <input type="hidden" id="modal" value="false">
            <input type="hidden" id="reg_type" name="reg_type" value="username">
            <div class="member-remember checkbox">
                <label>
                    <input id="remember" name="remember" type="checkbox" value="true">
                    记住我的登录状态
                </label>
                <a class="member-form-forgot" href="${.cmsConfig.site_url}/account/forgot">忘记密码？</a>
            </div>
            <button class="btn btn-primary btn-block btn-lg" type="submit">登录</button>
        </form>
        <div class="member-form-footer">
            <div class="member-form-social">
                <span>第三方帐号登录</span>
                <ul class="member-social-list">
                    <li class="social-item social-qq">
                        <a aria-label="QQ" data-original-title="QQ登录" data-placement="top" data-toggle="tooltip" href="" target="_blank" title="">
                            <i class="iconfont icon-QQ hotnews-icon"></i>
                        </a>
                    </li>
                    <li class="social-item social-wechat2">
                        <a aria-label="微信" data-original-title="微信登录" data-placement="top" data-toggle="tooltip" href="" target="_blank" title="">
                            <i class="iconfont icon-wechat-fill hotnews-icon"></i>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
        <div class="member-form-footer" style="display:block;text-align: center;">
            <span class="member-switch">还没有帐号？ <a href="${.cmsConfig.site_url}/account/register">立即注册</a></span>
            <span class="member-switch" style="margin-left: 20px;"><a href="${.cmsConfig.site_url}">返回首页</a></span>
        </div>
    </div>
</div>
<script src="${.cmsTheme}/libs/jquery.cookie-1.4.1.min.js"></script>
<script src="${.cmsTheme}/libs/jquery.querystring.js"></script>
<script src="${.cmsTheme}/js/member.js"></script>
<script>
    $(function () {
        var referer = decodeURIComponent($.queryString.get("referer"))
        if (referer==='undefined'){
            referer = ""
        }
        $("#login-form").on("submit", function () {
            $("#login-form").attr("action", APIBASE + "ucenter/account/login").ajaxSubmit({
                timeout: 10000,
                success: function (res) {
                    if (res.code === 0) {
                        // if($("input[name='remember']:checked").val()){
                        //     // 记住token
                        //     $.cookie("member_token",res.data.token,{expires:15,path: '/'})
                        // } else {
                        //     $.cookie("member_token",res.data.token,{path:'/'})
                        // }
                        layer.msg("登录成功，正在跳转...", {icon: 1})
                        setTimeout(function () {
                            window.location.href = referer || '${.cmsConfig.site_url}'
                        }, 2000)
                    } else {
                        layer.msg(res.message, {icon: 2})
                        $("#captcha_img").trigger('click')
                    }
                },
                error: function (err) {
                    console.log(err)
                }
            })
            return false
        })
    })
</script>