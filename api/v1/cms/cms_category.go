// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2023-01-31 09:33:48
// 生成路径: api/v1/cms/cms_category.go
// 生成人：yxf
// desc:文章分类相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package cms

import (
	"github.com/gogf/gf/v2/frame/g"

	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/cms/model"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// CmsCategorySearchReq 分页请求参数
type CmsCategorySearchReq struct {
	g.Meta    `path:"/list" tags:"cms后台/文章分类" method:"get" summary:"文章分类列表"`
	ChildNode bool   `p:"childNode"` // 包括子分类
	ParentId  string `p:"parentId"`  // 上级ID
	Name      string `p:"name"`      //名称
	Status    string `p:"status"`    // 状态
	Type      string `p:"type"`
	commonApi.PageReq
	commonApi.Author
}

// CmsCategorySearchRes 列表返回结果
type CmsCategorySearchRes struct {
	g.Meta `mime:"application/json"`
	commonApi.ListRes
	List []*model.CmsCategoryListRes `json:"list"`
}

// CmsCategoryAddReq 添加操作请求参数
type CmsCategoryAddReq struct {
	g.Meta `path:"/add" tags:"cms后台/文章分类" method:"post" summary:"文章分类添加"`
	commonApi.Author
	Name            string             `p:"name" v:"required#名称不能为空"`
	Type            string             `p:"type" `
	SeoTitle        string             `p:"seoTitle" `
	Keywords        string             `p:"keywords" `
	Description     string             `p:"description" `
	Content         string             `p:"content" `
	ParentId        uint               `p:"parentId" `
	ModuleId        uint               `p:"moduleId" `
	Sort            int                `p:"sort" `
	Template        string             `p:"template" `
	ContentTemplate string             `p:"contentTemplate" `
	IsInherit       string             `p:"isInherit" `
	Thumb           string             `p:"thumb" `
	Banner          []*comModel.UpFile `p:"banner" `
	Status          string             `p:"status" `
	PageTemplate    string             `p:"pageTemplate" `
	ChannelTemplate string             `p:"channelTemplate" `
	JumpUrl         string             `p:"jumpUrl" `
	Alias           string             `p:"alias" `
	Permissions     []*RolePermissions `p:"permissions"`
	Nav             string             `p:"nav"`
}

// CmsCategoryAddRes 添加操作返回结果
type CmsCategoryAddRes struct {
	commonApi.EmptyRes
}

// CmsCategoryEditReq 修改操作请求参数
type CmsCategoryEditReq struct {
	g.Meta `path:"/edit" tags:"cms后台/文章分类" method:"put" summary:"文章分类修改"`
	commonApi.Author
	Id              uint               `p:"id" v:"required#主键ID不能为空"`
	Name            string             `p:"name" v:"required#名称不能为空"`
	Type            string             `p:"type" `
	SeoTitle        string             `p:"seoTitle" `
	Keywords        string             `p:"keywords" `
	Description     string             `p:"description" `
	Content         string             `p:"content" `
	ParentId        uint               `p:"parentId" `
	ModuleId        uint               `p:"moduleId" `
	Sort            int                `p:"sort" `
	Template        string             `p:"template" `
	ContentTemplate string             `p:"contentTemplate" `
	IsInherit       string             `p:"isInherit" `
	Thumb           string             `p:"thumb" `
	Banner          []*comModel.UpFile `p:"banner" `
	Status          string             `p:"status" `
	PageTemplate    string             `p:"pageTemplate" `
	ChannelTemplate string             `p:"channelTemplate" `
	JumpUrl         string             `p:"jumpUrl" `
	Alias           string             `p:"alias" `
	Permissions     []*RolePermissions `p:"permissions"`
	Nav             string             `p:"nav"`
}

// CmsCategoryEditRes 修改操作返回结果
type CmsCategoryEditRes struct {
	commonApi.EmptyRes
}

// CmsCategoryGetReq 获取一条数据请求
type CmsCategoryGetReq struct {
	g.Meta `path:"/get" tags:"cms后台/文章分类" method:"get" summary:"获取文章分类信息"`
	commonApi.Author
	Id uint `p:"id" v:"required#主键必须"` //通过主键获取
}

// CmsCategoryGetRes 获取一条数据结果
type CmsCategoryGetRes struct {
	g.Meta `mime:"application/json"`
	*model.CmsCategoryInfoRes
}

// CmsCategoryDeleteReq 删除数据请求
type CmsCategoryDeleteReq struct {
	g.Meta `path:"/delete" tags:"cms后台/文章分类" method:"delete" summary:"删除文章分类"`
	commonApi.Author
	Ids []uint `p:"ids" v:"required#主键必须"` //通过主键删除
}

// CmsCategoryDeleteRes 删除数据返回
type CmsCategoryDeleteRes struct {
	commonApi.EmptyRes
}

// 该角色具有权限的分类
type CmsCategoryPermissionsReq struct {
	g.Meta `path:"/listPermissions" tags:"cms后台/分类权限" method:"get" summary:"文章分类权限"`
	RoleId uint `p:"roleId"` // 角色ID
}

// 分类权限结构
type CategoryPermissions struct {
	CategoryId  uint   `json:"categoryId"`
	Permissions string `json:"permissions"`
}

// 角色权限结构
type RolePermissions struct {
	RoleId      uint   `json:"roleId"`
	Permissions string `json:"Permissions"`
}

// 分类权限
type CmsCategoryPermissionsRes struct {
	g.Meta `mime:"application/json"`
	List   []*CategoryPermissions `json:"list"`
}

// 保存权限
type CmsCategorySavePermissionsReq struct {
	g.Meta      `path:"/savePermissions" tags:"cms后台/分类权限" method:"put" summary:"文章分类权限"`
	RoleId      uint                   `p:"roleId" v:"required#角色ID不能为空"`
	Permissions []*CategoryPermissions `p:"permissions"`
}

// 保存权限返回
type CmsCategorySavePermissionsRes struct {
	commonApi.EmptyRes
}

// 分类权限验证
type CmsCategoryCheckPermissionReq struct {
	g.Meta     `path:"/checkPermission" tags:"cms后台/分类权限" method:"get" summary:"分类权限验证"`
	CategoryId uint   `p:"categoryId"`
	Permission string `p:"permission"`
}

// 分类权限验证返回
type CmsCategoryCheckPermissionRes struct {
	g.Meta `mime:"application/json"`
	Res    bool `json:"res"`
}

// 根据分类id获取具有对应权限的同一模型的分类
type CmsCategoryGetPerCategorysReq struct {
	g.Meta     `path:"/getPerCatetorys" tags:"cms后台/分类权限" method:"get" summary:"根据分类id获取具有推送权限的同一模型的分类"`
	Permission string `p:"permission"`
	CategoryId uint   `p:"categoryId"`
}

type CmsCategoryGetPerCategorysRes struct {
	g.Meta `mime:"application/json"`
	List   []*model.CmsCategoryListRes `json:"list"`
}
