<div class="hotnews-profile-head">
    <div class="hotnews-ph-bg">
        <img id="user-front-cover" class="j-lazy" src='${SetStrDef (CorrectionPath .memberInfo.Cover) "/static/images/default_member_bg.jpg"}' style="display: inline;">
    </div>
    <div class="hotnews-ph-inner">
        <div class="hotnews-ph-user">
            <!-- 用户头像 -->
            <div class="hotnews-ph-avatar">
                <img id="user-avatar" alt="3326" class="avatar avatar-200 photo" src='${SetStrDef (CorrectionPath .memberInfo.Avatar) "/static/images/avatar.jpg"}' style="display: inline;" width="200">
                ${if eq .Session.memberInfo.Id .memberInfo.Id}
                <span class="edit-avatar" onclick="editAvatar(${.Session.memberInfo.Id})">
                    <i class="iconfont icon-camera hotnews-icon"></i>
                </span>
                ${end}
            </div>
            <h2 class="hotnews-ph-name">${.memberInfo.Nickname}<span class="user-group">${if .memberInfo.LinkedLevel}${.memberInfo.LinkedLevel.Name}${end}</span></h2>
            <div class="hotnews-ph-desc">${SetStrDef .memberInfo.Description "这个人很懒，什么都没有留下～"}</div>
            <div class="profile-stats">
                <div class="profile-stats-inner">
                    <div class="user-stats-item"><b>${.memberInfo.Follower}</b> <span>粉丝</span></div>
                </div>
            </div>

            ${if ne .Session.memberInfo.Id .memberInfo.Id}
            <button class="btn btn-xs btn-follow btn-primary" onclick="followMember(${.memberInfo.Id})" type="button">
                <i class="iconfont icon-plus hotnews-icon wi"></i>关注
            </button>
            <button class="btn btn-primary btn-xs btn-message" onclick="sendMessage(${.memberInfo.Id})" type="button">
                <i class="iconfont icon-mail hotnews-icon wi"></i>私信
            </button>
            ${end}

        </div>
        ${if eq .Session.memberInfo.Id .memberInfo.Id}
        <div class="hotnews-profile-action" onclick="editMemberCover()">
            <span class="hotnews-profile-setcover edit-cover" >
                <i class="iconfont icon-camera hotnews-icon wi"></i> 修改封面
            </span>
            <input id="hotnews_cropper_nonce" name="hotnews_cropper_nonce" type="hidden" value="349abbd3f5" />
        </div>
        ${end}
    </div>
</div>
<div id="send_message_dialog" style="display: none;padding: 10px;">
    <form id="send_message_form">
        <div class="member-account-input" style="width: 580px">
            <textarea class="is-input" maxlength="200" rows="5" placeholder="消息内容，不超过200字" id="message_content"></textarea>
            <input type="hidden" id="message_receiver" value="${.memberInfo.Id}">
            <input type="hidden" id="message_sender" value="${.Session.memberInfo.Id}">
        </div>
        <div style="text-align: right;">
            <button type="button" class="btn btn-primary" onclick="sendMessageSubmit()">发送</button>
            <button type="button" class="btn btn-default" style="margin-left: 10px;" onclick="layer.closeAll()">关闭</button>
        </div>
    </form>
</div>
<script>
    // 发送站内信
    function sendMessage(memberId) {
        var mid = $("#message_sender").val()
        if(!mid) {
            layer.msg("请先登录",{icon:2})
            return
        }
        $("#message_receiver").val(memberId)
        layer.open({
            type:1,
            title:'发送消息',
            content:$("#send_message_dialog"),
            area:['600px','230px']
        })
    }
    function sendMessageSubmit(){
        var content = $("#message_content",$("#send_message_form")).val()
        var receiver = $("#message_receiver",$("#send_message_form")).val()
        $.post(APIBASE+"ucenter/message/add",{content,receiver},function (res){
            if(res.code === 0) {
                layer.closeAll()
                layer.msg('发送成功',{icon:1})
                $("#message_content",$("#send_message_form")).val('')
            } else {
                layer.msg(res.message,{icon:2})
            }
        })
    }
</script>