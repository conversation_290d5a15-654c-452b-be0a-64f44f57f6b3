<template>
  <div class="cms-cmsArticle-edit">
    <el-card shadow="hover">
      <div style="max-width: 100%;">
        <el-form
          ref="formRef"
          :model="formData"
          :rules="rules"
          label-width="120px">
          <el-tabs model-value="base" type="border-card">
            <el-tab-pane label="基本信息" name="base">
              <div class="form-content">
                <!-- 左侧列 -->
                <div class="left-column">
                  <el-form-item label="文章分类" prop="categoryId">
                    <el-tree-select
                      style="width: 100%"
                      :check-strictly="false"
                      v-model="formData.categoryId"
                      :data="cmsCategoryOptions"
                      placeholder="请选择分类"
                      highlight-current
                      node-key="id"
                      :default-expanded-keys="[formData.categoryId]"
                      filterable
                      @change="handleCategoryChange"
                      :props="{ label: 'name',value: 'id',disabled:()=>true}">
                      <template #default="{ data}">
                        {{ data.name }}
                        <span v-if="data.type==='jump'" style="margin-left: 10px;">(跳转链接)</span>
                        <span v-if="data.type==='page'" style="margin-left: 10px;">(单页栏目)</span>
                      </template>
                    </el-tree-select>
                  </el-form-item>
                  <el-form-item v-if="!isEdit" label="发布到其它分类" prop="otherCategoryId">
                    <el-tree-select
                        style="width: 100%"
                        :check-strictly="false"
                        :data="cmsOtherCategoryOptions"
                        placeholder="请选择分类"
                        highlight-current
                        node-key="id"
                        filterable
                        :multiple=true
                        v-model="formData.otherCategoryId"
                        :props="{ label: 'name', value: 'id' }">
                      <template #default="{ data}">
                        {{ data.name }}
                      </template>
                    </el-tree-select>
                  </el-form-item>
                  <el-form-item label="文章标题" prop="title">
                    <el-input v-model.trim="formData.title" placeholder="请输入标题"/>
                  </el-form-item>
                  <el-form-item label="副标题" prop="subTitle">
                    <el-input v-model.trim="formData.subTitle" placeholder="请输入副标题"/>
                  </el-form-item>
                  <el-form-item label="推荐属性" prop="flag" style="margin-top: 10px;">
                      <el-checkbox-group v-model="formData.flag">
                        <el-checkbox
                          v-for="item in cmsFlagsOptions"
                          :key="item.id"
                          :value="item.id.toString()"
                        >{{ item.name }}
                        </el-checkbox>
                      </el-checkbox-group>
                  </el-form-item>
                  <el-form-item label="文章标签" prop="flag" style="margin-top: 10px;">
                    <el-checkbox-group v-model="formData.tags">
                      <el-checkbox
                        v-for="dict in cmsTagsOptions"
                        :key="dict.id"
                        :value="dict.id"
                      >{{ dict.name }}
                      </el-checkbox>
                    </el-checkbox-group>
                  </el-form-item>
                  <el-form-item label="外部链接" prop="isJump" style="margin-top: 10px;">
                    <el-radio-group v-model="formData.isJump">
                      <el-radio
                        v-for="dict in jumpOptions"
                        :key="dict.value"
                        :value="dict.value"
                      >{{ dict.label }}
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="跳转链接" prop="jumpUrl" v-show="formData.isJump==='1'">
                    <el-input v-model.trim="formData.jumpUrl" placeholder="请输入跳转链接"></el-input>
                  </el-form-item>
                  <el-form-item label="缩略图" prop="thumb">
                    <div class="thumbnail-container">
                      <el-upload
                        v-loading="upLoadingThumb"
                        :action="baseURL+'api/v1/system/upload/singleImg'"
                        :before-upload="beforeAvatarUploadThumb"
                        :data="setUpData()"
                        :on-success="handleAvatarSuccessThumb"
                        :show-file-list="false"
                        class="avatar-uploader"
                        name="file"
                      >
                        <img v-if="!proxy.isEmpty(imageUrlThumb)" :src="imageUrlThumb" class="avatar">
                        <el-icon v-else class="avatar-uploader-icon">
                          <ele-Plus/>
                        </el-icon>
                      </el-upload>
                      <div
                        v-if="!proxy.isEmpty(imageUrlThumb)"
                        class="delete-thumbnail-btn"
                        @click.stop="clearThumbnail"
                      >
                        <div class="delete-line"></div>
                      </div>
                    </div>
                  </el-form-item>
                  <el-form-item label="文章摘要" prop="summary">
                    <el-input v-model="formData.summary" :rows="4" placeholder="文章摘要内容，不超过200个字符" type="textarea"></el-input>
                  </el-form-item>
                  <el-form-item label="作者" prop="writer">
                    <el-input v-model.trim="formData.writer" placeholder="请输入作者"/>
                  </el-form-item>
                  <el-form-item label="文章内容" prop="content" v-show="formData.isJump==='0'">
                    <gf-ueditor v-model="formData.content" :tool-bars="cmsToolbar" :editorId="'cmsConfigValueEditor'+(new Date()).getTime()" @setEditContent="setEditContent"></gf-ueditor>
                  </el-form-item>
                  <el-form-item v-show="formData.isJump==='0'">
                    <template v-if="!formData.summary">
                      <el-checkbox v-model="autoExtract.summary">截取内容</el-checkbox>
                      <el-input-number v-model="autoExtract.summaryLength" :min="1" :max="200" size="small" style="width: 120px; margin: 0 5px;"></el-input-number>
                      字符至文章摘要
                    </template>
                    <template v-else>
                      <el-tooltip content="已有摘要，如需自动提取请先清除现有摘要" placement="top">
                        <span style="color: #909399;">截取内容至文章摘要（已有摘要）</span>
                      </el-tooltip>
                      <el-button type="text" size="small" @click="clearSummary" style="margin-left: 5px;">清除摘要</el-button>
                    </template>

                    <template v-if="!formData.thumb">
                      <el-checkbox v-model="autoExtract.thumbnail" style="margin-left: 20px;">截取内容第</el-checkbox>
                      <el-input-number v-model="autoExtract.thumbnailIndex" :min="1" :max="10" size="small" style="width: 120px; margin: 0 5px;"></el-input-number>
                      张图片为缩略图
                    </template>
                    <template v-else>
                      <el-tooltip content="已有缩略图，如需自动提取请先清除现有缩略图" placement="top">
                        <span style="margin-left: 20px; color: #909399;">截取内容第几张图片为缩略图（已有缩略图）</span>
                      </el-tooltip>
                    </template>
                  </el-form-item>
                </div>
                <!-- 右侧列 -->
                <div class="right-column">
                  <el-form-item v-for="item in moduleFields" :key="item.id" :label="item.label">
                    <modules-data-form-item v-model="item.content" :module-field="item" style="width: 100%"></modules-data-form-item>
                  </el-form-item>
                  <el-form-item label="权重" prop="weigh">
                    <el-input-number v-model="formData.weigh" :min="0" :precision="0" :step="1" placeholder="请输入权重值"></el-input-number>
                  </el-form-item>
                  <el-form-item label="发布日期">
                    <el-date-picker v-model="formData.publishedAt" type="datetime" value-format="YYYY-MM-DD HH:mm:ss"></el-date-picker>
                  </el-form-item>
                  <el-form-item label="发布状态" prop="status">
                    <el-radio-group v-model="formData.status" :disabled="!hasPublishPermission">
                      <el-radio
                        v-for="dict in cms_article_pub_type"
                        :key="dict.value"
                        :value="dict.value"
                      >{{ dict.label }}
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="更多设置" name="more">
              <el-form-item label="SEO标题" prop="seoTitle">
                <el-input v-model.trim="formData.seoTitle" placeholder="请输入SEO标题"/>
              </el-form-item>
              <el-form-item label="SEO关键字" prop="keywords">
                <el-input v-model.trim="formData.keywords" placeholder="请输入关键字"/>
              </el-form-item>
              <el-form-item label="SEO描述" prop="description">
                <el-input v-model="formData.description" placeholder="请输入描述" type="textarea"/>
              </el-form-item>
<!--              <el-form-item label="阅读权限" prop="readPermissions">-->
<!--                <el-input v-model.trim="formData.readPermissions" placeholder="请输入阅读权限"/>-->
<!--              </el-form-item>-->
              <el-form-item label="来源地址" prop="originUrl">
                <el-input v-model.trim="formData.originUrl" placeholder="请输入来源地址"/>
              </el-form-item>
              <el-form-item label="来源名称" prop="originTitle">
                <el-input v-model.trim="formData.originTitle" placeholder="请输入来源名称"/>
              </el-form-item>
<!--              <el-form-item label="允许评论">-->
<!--                <el-radio-group v-model="formData.allowComment">-->
<!--                  <el-radio :value="'1'">是</el-radio>-->
<!--                  <el-radio :value="'0'">否</el-radio>-->
<!--                </el-radio-group>-->
<!--              </el-form-item>-->
              <el-form-item label="显示模板" prop="template">
                <el-select v-model="formData.template" placeholder="请选择模板文件" style="width: 100%" clearable>
                  <el-option v-for="(item,index) in templateFileList" :key="index" :label="item.name" :value="item.name"/>
                </el-select>
              </el-form-item>
            </el-tab-pane>
          </el-tabs>
        </el-form>
        <div style="text-align: center;padding: 20px;">
          <el-button type="primary" @click="onSubmit">确 定</el-button>
          <el-button @click="onCancel">取 消</el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>
<script lang="ts">
import {defineComponent, getCurrentInstance, onMounted, reactive, ref, toRefs, unref} from "vue";
import {dayjs, ElMessage, UploadProps} from "element-plus";
import {addCmsArticle, getCmsArticle, updateCmsArticle} from "/@/api/cms/cmsArticle";
import {getToken} from "/@/utils/gfast";
import {CmsArticleEditState} from "/@/views/cms/cmsArticle/list/component/model";
import GfUeditor from "/@/components/ueditor/index.vue";
import {cmsToolbar} from "/@/components/ueditor/model";
import {useRoute} from "vue-router";
import {listCmsCategory, listCmsPerCategorys, checkCmsCategoryPermission} from "/@/api/cms/cmsCategory";
import {listModulesField} from "/@/api/system/modulesField";
import {ModulesFieldInfoData} from "/@/views/system/modulesField/list/component/model";
import ModulesDataFormItem from "/@/components/modulesData/forumItem.vue";
import {listTemplateFiles} from "/@/api/cms/cmsTemplate";
import {CmsTemplateFileData} from "/@/views/cms/cmsTemplate/list/component/model";
import {events} from "/@/eventBus";
import {listCmsTags} from "/@/api/cms/cmsTags";
import {listCmsFlagsByCategoryId} from "/@/api/cms/cmsFlags";

export default defineComponent({
  name: "apiV1CmsArticleEditComponent",
  components: {ModulesDataFormItem, GfUeditor},
  props: {},
  setup() {
    const formRef = ref();
    const isEdit = ref(false);
    const templateFileList = ref<CmsTemplateFileData[]>([]);
    const baseURL: string | undefined | boolean = import.meta.env.VITE_API_URL;
    const {proxy} = <any>getCurrentInstance();
    const route = useRoute();
    //图片上传地址
    const imageUrlThumb = ref("");
    //上传加载
    const upLoadingThumb = ref(false);
    const cmsCategoryOptions = ref([]);
    const cmsOtherCategoryOptions = ref([]);
    const cmsTagsOptions = ref([]);
    const cmsFlagsOptions = ref([]);
    const jumpOptions = [{label:'是',value:'1'},{label:'否',value:'0'}]
    const hasPublishPermission = ref(false);

    // 自动提取摘要和缩略图的配置
    const autoExtract = reactive({
      summary: true,        // 是否自动提取摘要
      summaryLength: 200,   // 提取摘要的字符长度
      thumbnail: true,     // 是否自动提取缩略图
      thumbnailIndex: 1     // 提取第几张图片作为缩略图
    });
    // 字典选项数据
    const {
      cms_article_attr,
      cms_article_pub_type
    } = proxy.useDict(
        "cms_article_attr",
        "cms_article_pub_type"
    );

    const state = reactive<CmsArticleEditState>({
      loading: false,
      isShowDialog: false,
      formData: {
        id: undefined,
        title: undefined,
        subTitle: undefined,
        writer: undefined,
        summary: undefined,
        seoTitle: undefined,
        categoryId: 0,
        moduleId: undefined,
        keywords: undefined,
        description: undefined,
        hits: undefined,
        flag: [],
        commentCount: undefined,
        thumb: undefined,
        template: undefined,
        status: "1",
        userId: undefined,
        readPermissions: undefined,
        originUrl: undefined,
        originTitle: undefined,
        createdAt: undefined,
        updatedAt: undefined,
        deletedAt: undefined,
        publishedAt: undefined,
        content: "",
        isJump: '0',
        allowComment: '1',
        jumpUrl: '',
        modulesData: {},
        tags:[],
        otherCategoryId: [],
        weigh: 0,
      },
      // 表单校验
      rules: {
        id: [
          {required: true, message: "ID不能为空", trigger: "blur"}
        ],
        status: [
          {required: true, message: "状态不能为空", trigger: "blur"}
        ],
        title: [
          {required: true, message: "文章标题不能为空", trigger: "blur"},
          {min: 1, max: 100, message: "标题长度在1-100个字符之间", trigger: "blur"}
        ],
        content: [
          {required: true, message: "文章内容不能为空", trigger: "blur"}
        ],
        categoryId: [
          {required: true, message: "请选择文章分类", trigger: "change"}
        ]
      }
    });
    let categoryList: any[] = [];
    // 打开弹窗
    const openDialog = () => {
      isEdit.value = false;
      resetForm();
      listCmsCategory({pageSize: 1000, pageNum: 1, status: 1}).then((res: any) => {
        let list = res.data.list ?? [];
        categoryList = list;
        list.forEach((item: any) => {
          if (!["list","channel"].includes(item.type)) {
            item["disabled"] = true;
          }
        });
        list = proxy.handleTree(res.data.list || [], "id", "parentId");
        cmsCategoryOptions.value = list;

        // 获取路由参数中的分类ID并触发事件
        const categoryId = route.query.categoryId;
        if (categoryId) {
          state.formData.categoryId = Number(categoryId);
          handleCategoryChange(Number(categoryId));
        }
      });

      //获取文章标签
      listCmsTags({pageSize: 1000, pageNum: 1}).then((res:any)=>{
        cmsTagsOptions.value = res.data.list??[];
      });

      if (route.query?.id) {
        isEdit.value = true;
        getCmsArticle(Number(route.query?.id)).then((res: any) => {
          const data = res.data;
          data.flag = data.flag.split(",");
          //单图地址赋值
          imageUrlThumb.value = data.thumb ? proxy.getUpFileUrl(data.thumb) : "";
          // 如果已有缩略图，禁用自动提取缩略图选项
          if (data.thumb) {
            autoExtract.thumbnail = false;
          }

          // 如果已有摘要，禁用自动提取摘要选项
          if (data.summary) {
            autoExtract.summary = false;
          }
          data.status = "" + data.status;
          data.isJump = ""+data.isJump;
          data.tags = data.tags?data.tags.map((item:any)=>{return item.tagsId}):[]
          state.formData = data;

          // 在获取文章数据后立即检查发布权限
          console.log('编辑模式，文章分类ID:', state.formData.categoryId);
          if (state.formData.categoryId) {
            checkPublishPermission(state.formData.categoryId);

            // 获取推荐属性数据
            listCmsFlagsByCategoryId({ categoryId: state.formData.categoryId }).then((res: any) => {
              console.log('编辑模式推荐属性数据:', res.data);
              if (res.data && Array.isArray(res.data.list) && res.data.list.length > 0) {
                cmsFlagsOptions.value = res.data.list;
              }
            }).catch(error => {
              console.error('获取推荐属性失败:', error);
            });
          }

          if (state.formData.moduleId! > 0) {
            // 获取模型信息
            listModulesField({pageSize: 200, pageNum: 1, moduleId: state.formData.moduleId}).then((res: any) => {
              res.data.list.forEach((item:any)=>{
                if(['checkbox','image','images','file','files'].includes(item.type)){
                  item['content'] = state.formData.modulesData[item.name]?state.formData.modulesData[item.name]:[]
                } else {
                  item['content'] = state.formData.modulesData[item.name]??""
                }
              });
              moduleFields.value = res.data.list ?? [];
            });
          }
        });
      }
      state.isShowDialog = true;
    };
    // 关闭弹窗
    const closeDialog = () => {
      state.isShowDialog = false;
      proxy.mittBus.emit("onCurrentContextmenuClick", Object.assign({}, {contextMenuClickId: 1, ...route}));
    };
    // 取消
    const onCancel = () => {
      closeDialog();
    };
    const setEditContent = (data: string) => {
      state.formData.content = data;
    };
    const moduleFields = ref<ModulesFieldInfoData[]>([]);
    // 提交
    const onSubmit = () => {
      const formWrap = unref(formRef) as any;
      if (!formWrap) return;
      formWrap.validate((valid: boolean) => {
        if (valid) {
          state.loading = true;
          // 获取动态模型的值
          state.formData.modulesData = {};
          moduleFields.value.forEach((item: ModulesFieldInfoData) => {
            state.formData.modulesData[item.name] = item.content;
          });

          // 确保没有发布权限时，状态固定为待审核(0)
          if (!hasPublishPermission.value) {
            state.formData.status = '0';
          }

          // 处理自动提取摘要
          // 只有当摘要为空时，才执行自动提取
          if (autoExtract.summary && state.formData.content && state.formData.isJump === '0' && !state.formData.summary) {
            // 先将HTML内容转换为纯文本
            let textContent = '';

            try {
              // 方法一：使用DOM操作去除HTML标记
              const tempDiv = document.createElement('div');
              tempDiv.innerHTML = state.formData.content;

              // 删除所有脚本和样式标签，防止执行不必要的代码
              const scripts = tempDiv.getElementsByTagName('script');
              const styles = tempDiv.getElementsByTagName('style');

              // 从后向前删除，避免索引变化
              for (let i = scripts.length - 1; i >= 0; i--) {
                scripts[i].parentNode?.removeChild(scripts[i]);
              }

              for (let i = styles.length - 1; i >= 0; i--) {
                styles[i].parentNode?.removeChild(styles[i]);
              }

              // 获取纯文本
              textContent = tempDiv.textContent || tempDiv.innerText || '';

              // 方法二：使用正则表达式作为备用方法
              if (!textContent) {
                textContent = state.formData.content.replace(/<[^>]+>/g, ''); // 移除所有HTML标签
              }
            } catch (error) {
              // 如果出错，使用简单的正则表达式方法
              console.error('提取纯文本时出错:', error);
              textContent = state.formData.content.replace(/<[^>]+>/g, '');
            }

            // 处理特殊字符和空白
            textContent = textContent
              .replace(/&nbsp;/g, ' ') // 替换HTML空格
              .replace(/\s+/g, ' ') // 将多个空白字符合并为一个
              .trim(); // 去除首尾空白

            // 截取指定长度的文本作为摘要
            state.formData.summary = textContent.substring(0, autoExtract.summaryLength);
          }

          // 处理自动提取缩略图
          // 只有当缩略图为空时，才执行自动提取
          if (autoExtract.thumbnail && state.formData.content && state.formData.isJump === '0' && !state.formData.thumb) {
            // 使用正则表达式提取所有图片标签
            const imgRegex = /<img[^>]+src="([^"]+)"[^>]*>/g;
            const imgMatches = [];
            let match;
            while ((match = imgRegex.exec(state.formData.content)) !== null) {
              imgMatches.push(match[1]);
            }

            // 如果有足够的图片，则提取指定索引的图片作为缩略图
            const index = autoExtract.thumbnailIndex - 1; // 转换为基于0的索引
            if (imgMatches.length > index) {
              state.formData.thumb = imgMatches[index];
              // 更新缩略图预览
              imageUrlThumb.value = state.formData.thumb;
            }
          }
          if (!state.formData.id || state.formData.id === 0) {
            //添加
            addCmsArticle(state.formData).then(() => {
              ElMessage.success("添加成功");
              events.emit("refreshArticleList")
              closeDialog(); // 关闭弹窗
            }).finally(() => {
              state.loading = false;
            });
          } else {
            //修改
            updateCmsArticle(state.formData).then(() => {
              ElMessage.success("修改成功");
              events.emit("refreshArticleList")
              closeDialog(); // 关闭弹窗
            }).finally(() => {
              state.loading = false;
            });
          }
        }
      });
    };
    const resetForm = () => {
      state.formData = {
        id: undefined,
        title: undefined,
        subTitle: undefined,
        writer: undefined,
        summary: undefined,
        seoTitle: undefined,
        categoryId: undefined,
        moduleId: undefined,
        keywords: undefined,
        description: undefined,
        hits: undefined,
        flag: [],
        commentCount: undefined,
        thumb: undefined,
        template: undefined,
        status: "1",
        userId: undefined,
        readPermissions: undefined,
        originUrl: undefined,
        originTitle: undefined,
        createdAt: undefined,
        updatedAt: undefined,
        deletedAt: undefined,
        publishedAt: dayjs().format("YYYY-MM-DD H:m:s"),
        content: "",
        isJump: '0',
        allowComment: '1',
        jumpUrl: '',
        modulesData: {},
        tags:[],
        weigh: 0
      };
    };
    //单图上传缩略图
    const handleAvatarSuccessThumb: UploadProps["onSuccess"] = (res, file) => {
      if (res.code === 0) {
        imageUrlThumb.value = URL.createObjectURL(file.raw!);
        state.formData.thumb = res.data.path;
        // 已有缩略图，禁用自动提取缩略图选项
        autoExtract.thumbnail = false;
      } else {
        ElMessage.error(res.msg);
      }
      upLoadingThumb.value = false;
    };

    // 清除缩略图
    const clearThumbnail = () => {
      imageUrlThumb.value = "";
      state.formData.thumb = undefined;
      // 如果之前禁用了自动提取缩略图选项，现在可以重新启用
      // 默认启用自动提取缩略图选项
      autoExtract.thumbnail = true;
      ElMessage.success("缩略图已清除");
    };

    // 清除摘要
    const clearSummary = () => {
      state.formData.summary = "";
      // 如果之前禁用了自动提取摘要选项，现在可以重新启用
      // 默认启用自动提取摘要选项
      autoExtract.summary = true;
      ElMessage.success("摘要已清除");
    };

    const beforeAvatarUploadThumb = () => {
      upLoadingThumb.value = true;
      return true;
    };
    const setUpData = () => {
      return {token: getToken()};
    };
    onMounted(() => {
      listTemplateFiles("/content").then((res: any) => {
        templateFileList.value = res.data?.list ?? [];
      });
      openDialog();
      // 获取路由参数中的分类ID
      const categoryId = route.query.categoryId;
      if (categoryId) {
        state.formData.categoryId = Number(categoryId);
        // 手动触发分类变更事件
        handleCategoryChange(Number(categoryId));
        // 检查发布权限
        checkPublishPermission(Number(categoryId));
      }
    });
    const checkPublishPermission = async (categoryId: any) => {
      // 处理 categoryId 可能是数组或对象的情况
      let actualCategoryId = categoryId;

      // 如果 categoryId 是数组或对象，尝试提取实际的 ID 值
      if (typeof categoryId === 'object' && categoryId !== null) {
        if (Array.isArray(categoryId) && categoryId.length > 0) {
          // 如果是数组，尝试获取第一个元素
          if (typeof categoryId[0] === 'object' && categoryId[0] !== null && 'fieldValue' in categoryId[0]) {
            actualCategoryId = categoryId[0].fieldValue;
          } else {
            actualCategoryId = categoryId[0];
          }
        } else if ('fieldValue' in categoryId) {
          // 如果是对象且包含 fieldValue 属性
          actualCategoryId = categoryId.fieldValue;
        }
      }

      // 如果处理后的 ID 仍然无效，则返回
      if (!actualCategoryId || actualCategoryId === undefined) {
        hasPublishPermission.value = false;
        return;
      }

      try {
        const res = await checkCmsCategoryPermission({ categoryId: actualCategoryId, permission: 'publish' });
        hasPublishPermission.value = res.data;
        if (!hasPublishPermission.value) {
          state.formData.status = '0'; // 如果没有发布权限，固定为待审核状态(0)
        } else if (!isEdit.value) {
          // 只在新增时设置默认值为已发布(1)
          state.formData.status = '1';
        }
      } catch (error) {
        hasPublishPermission.value = false;
        state.formData.status = '0'; // 出错时也设置为待审核状态
      }
    };

    const handleCategoryChange = (value: any) => {
      // 处理 value 可能是数组或对象的情况
      let actualValue = value;

      // 如果 value 是数组或对象，尝试提取实际的 ID 值
      if (typeof value === 'object' && value !== null) {
        if (Array.isArray(value) && value.length > 0) {
          // 如果是数组，尝试获取第一个元素
          if (typeof value[0] === 'object' && value[0] !== null && 'fieldValue' in value[0]) {
            actualValue = value[0].fieldValue;
          } else {
            actualValue = value[0];
          }
        } else if ('fieldValue' in value) {
          // 如果是对象且包含 fieldValue 属性
          actualValue = value.fieldValue;
        }
      }

      // 首先检查发布权限，无论是否找到匹配的分类
      checkPublishPermission(actualValue);

      // 根据分类ID获取推荐属性
      if (actualValue) {
        listCmsFlagsByCategoryId({ categoryId: actualValue }).then((res: any) => {
          console.log('推荐属性数据:', res.data);
          if (res.data && Array.isArray(res.data.list) && res.data.list.length > 0) {
            cmsFlagsOptions.value = res.data.list;
          } else {
            // 如果没有数据，使用默认的字典数据
            cmsFlagsOptions.value = [];
          }
        }).catch(error => {
          console.error('获取推荐属性失败:', error);
          cmsFlagsOptions.value = [];
        });
      } else {
        cmsFlagsOptions.value = [];
      }

      for (let item of categoryList) {
        if (item.id === actualValue) {
          state.formData.moduleId = item.moduleId;
          // 获取模型信息
          listModulesField({pageSize: 200, pageNum: 1, moduleId: item.moduleId}).then((res: any) => {
            const tmpList = res.data.list ?? [];
            tmpList.forEach((item:ModulesFieldInfoData)=>{
              item.content = state.formData.modulesData[item.name]??undefined
            })
            moduleFields.value = tmpList;
          });
          //发布到其他分类
          listCmsPerCategorys({categoryId: item.id, permission: "push"}).then((res: any) => {
            let list = res.data.list ?? [];
            list.forEach((item: any) => {
              if (!["list"].includes(item.type)) {
                item["disabled"] = true;
              }
              if (item.id === state.formData.categoryId) {
                item["disabled"] = true;
              }
            });
            list = proxy.handleTree(res.data.list || [], "id", "parentId");
            cmsOtherCategoryOptions.value = list;
          });
          break;
        }
      }
    };
    const handleOtherCategoryChange = (value: any) => {
      // 选择后将 otherCategoryId 设置为空
      state.formData.otherCategoryId = [];
    };

    return {
      formRef,
      isEdit,
      proxy,
      handleCategoryChange,
      openDialog,
      closeDialog,
      onCancel,
      onSubmit,
      moduleFields,
      //图片上传地址
      imageUrlThumb,
      //上传加载
      upLoadingThumb,
      handleAvatarSuccessThumb,
      clearThumbnail,
      beforeAvatarUploadThumb,
      setUpData,
      baseURL,
      setEditContent,
      cms_article_attr,
      cms_article_pub_type,
      cmsCategoryOptions,
      cmsOtherCategoryOptions,
      templateFileList,
      jumpOptions,
      cmsTagsOptions,
      cmsFlagsOptions,
      hasPublishPermission,
      autoExtract,
      cmsToolbar: reactive(cmsToolbar),
      handleOtherCategoryChange,
      ...toRefs(state)
    };
  }
});
</script>
<style scoped>
.cms-cmsArticle-edit :deep(.avatar-uploader .avatar) {
  max-width: 178px;
  max-height: 178px;
  display: block;
}

.thumbnail-container {
  position: relative;
  display: inline-block;
}

.delete-thumbnail-btn {
  position: absolute;
  top: -10px;
  right: -10px;
  z-index: 10;
  width: 20px;
  height: 20px;
  background-color: #f56c6c;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.3s;
}

.delete-thumbnail-btn:hover {
  transform: scale(1.1);
  background-color: #f56c6c;
}

.delete-line {
  width: 10px;
  height: 2px;
  background-color: white;
  border-radius: 1px;
}

.cms-cmsArticle-edit :deep(.avatar-uploader .el-upload) {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.cms-cmsArticle-edit :deep(.avatar-uploader .el-upload:hover) {
  border-color: var(--el-color-primary);
}

.cms-cmsArticle-edit :deep(.el-icon.avatar-uploader-icon) {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
}

.form-content {
  display: flex;
  gap: 20px;
}

.left-column {
  flex: 0 0 60%;
}

.right-column {
  flex: 0 0 38%;
}
</style>
