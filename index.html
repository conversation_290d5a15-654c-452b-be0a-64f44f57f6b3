<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>宿州学院 体育学院</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .navbar-custom {
            background-color: #d73527;
            padding: 0;
        }
        .navbar-custom .navbar-nav .nav-link {
            color: white;
            padding: 15px 20px;
            border-right: 1px solid rgba(255,255,255,0.2);
        }
        .navbar-custom .navbar-nav .nav-link:hover {
            background-color: rgba(255,255,255,0.1);
        }
        .header-top {
            background-color: #f8f9fa;
            padding: 10px 0;
        }
        .logo-section {
            display: flex;
            align-items: center;
        }
        .logo-text {
            margin-left: 15px;
        }
        .logo-text h1 {
            color: #d73527;
            font-size: 24px;
            margin: 0;
        }
        .logo-text h2 {
            color: #666;
            font-size: 16px;
            margin: 0;
        }
        .search-box {
            max-width: 300px;
        }
        .hero-section {
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 400"><rect fill="%23d73527" width="1200" height="400"/></svg>');
            height: 400px;
            background-size: cover;
            position: relative;
        }
        .hero-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(rgba(215,53,39,0.7), rgba(215,53,39,0.7));
        }
        .content-section {
            padding: 40px 0;
        }
        .section-title {
            color: #333;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #d73527;
        }
        .news-item {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .news-item:last-child {
            border-bottom: none;
        }
        .news-date {
            background-color: #f8f9fa;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            color: #666;
        }
        .news-title {
            color: #333;
            text-decoration: none;
            font-size: 14px;
        }
        .news-title:hover {
            color: #d73527;
        }
        .footer-section {
            background-color: #d73527;
            color: white;
            padding: 30px 0;
        }
        .gallery-item {
            margin-bottom: 20px;
        }
        .gallery-item img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <!-- 顶部信息栏 -->
    <div class="header-top">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <div class="logo-section">
                        <img src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='60' height='60'><circle fill='%23d73527' cx='30' cy='30' r='25'/><text x='30' y='35' text-anchor='middle' fill='white' font-size='12'>宿</text></svg>" alt="宿州学院">
                        <div class="logo-text">
                            <h1>宿州学院</h1>
                            <h2>Department of Physical Education</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="search-box">
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="请输入关键词">
                            <button class="btn btn-outline-secondary" type="button">
                                <i class="bi bi-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <div class="navbar-nav">
                <a class="nav-link" href="#">网站首页</a>
                <a class="nav-link" href="#">学院概况</a>
                <a class="nav-link" href="#">党建工作</a>
                <a class="nav-link" href="#">教学工作</a>
                <a class="nav-link" href="#">学科建设</a>
                <a class="nav-link" href="#">师资队伍</a>
                <a class="nav-link" href="#">人才培养</a>
                <a class="nav-link" href="#">科学研究</a>
                <a class="nav-link" href="#">群体工作</a>
                <a class="nav-link" href="#">信息公开</a>
                <a class="nav-link" href="#">下载中心</a>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="hero-section">
        <div class="hero-overlay d-flex align-items-center">
            <div class="container">
                <div class="row">
                    <div class="col-md-8">
                        <img src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='600' height='300'><rect fill='%23f4a261' width='600' height='300'/><text x='300' y='150' text-anchor='middle' fill='white' font-size='24'>学院建筑图片</text></svg>" alt="学院建筑" class="img-fluid rounded">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-section">
        <div class="container">
            <div class="row">
                <!-- 学院新闻 -->
                <div class="col-md-4">
                    <div class="section-title">学院新闻 <span class="float-end"><a href="#" class="text-decoration-none">More ></a></span></div>
                    <div class="news-item d-flex">
                        <img src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='120' height='80'><rect fill='%23e76f51' width='120' height='80'/></svg>" alt="新闻图片" class="me-3">
                        <div>
                            <a href="#" class="news-title">我院在2021年中国大学生健美操锦标赛中获得佳绩</a>
                        </div>
                    </div>
                </div>

                <!-- 通知公告 -->
                <div class="col-md-4">
                    <div class="section-title">通知公告 <span class="float-end"><a href="#" class="text-decoration-none">More ></a></span></div>
                    <div class="news-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="news-date">28</span>
                            <div class="flex-grow-1 ms-3">
                                <a href="#" class="news-title">2021年中国成人大学生乒乓球锦标赛...</a>
                                <div class="text-muted small">[10-28]</div>
                            </div>
                        </div>
                    </div>
                    <div class="news-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="news-date">28</span>
                            <div class="flex-grow-1 ms-3">
                                <a href="#" class="news-title">2021年中国成人大学生乒乓球锦标赛...</a>
                                <div class="text-muted small">[10-28]</div>
                            </div>
                        </div>
                    </div>
                    <div class="news-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="news-date">28</span>
                            <div class="flex-grow-1 ms-3">
                                <a href="#" class="news-title">2021年中国成人大学生乒乓球锦标赛...</a>
                                <div class="text-muted small">[10-28]</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 其他栏目 -->
                <div class="col-md-4">
                    <div class="section-title">重要工作 <span class="float-end"><a href="#" class="text-decoration-none">More ></a></span></div>
                    <div class="news-item">
                        <a href="#" class="news-title">大学体育教育改革与发展思路探讨（长沙站）</a>
                        <div class="text-muted small">2021-10-11</div>
                    </div>
                    <div class="news-item">
                        <a href="#" class="news-title">大学体育教育改革与发展思路探讨（长沙站）</a>
                        <div class="text-muted small">2021-10-11</div>
                    </div>
                </div>
            </div>

            <!-- 第二行内容 -->
            <div class="row mt-4">
                <div class="col-md-4">
                    <div class="section-title">教学工作 <span class="float-end"><a href="#" class="text-decoration-none">More ></a></span></div>
                    <div class="news-item">
                        <a href="#" class="news-title">大学体育教育改革与发展思路探讨（长沙站）</a>
                        <div class="text-muted small">2021-10-11</div>
                    </div>
                    <div class="news-item">
                        <a href="#" class="news-title">大学体育教育改革与发展思路探讨（长沙站）</a>
                        <div class="text-muted small">2021-10-11</div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="section-title">群体工作 <span class="float-end"><a href="#" class="text-decoration-none">More ></a></span></div>
                    <div class="news-item">
                        <a href="#" class="news-title">大学体育教育改革与发展思路探讨（长沙站）</a>
                        <div class="text-muted small">2021-10-11</div>
                    </div>
                    <div class="news-item">
                        <a href="#" class="news-title">大学体育教育改革与发展思路探讨（长沙站）</a>
                        <div class="text-muted small">2021-10-11</div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="section-title">精彩瞬间 <span class="float-end"><a href="#" class="text-decoration-none">More ></a></span></div>
                    <div class="row">
                        <div class="col-4 gallery-item">
                            <img src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='100' height='80'><rect fill='%23264653' width='100' height='80'/></svg>" alt="精彩瞬间1">
                        </div>
                        <div class="col-4 gallery-item">
                            <img src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='100' height='80'><rect fill='%232a9d8f' width='100' height='80'/></svg>" alt="精彩瞬间2">
                        </div>
                        <div class="col-4 gallery-item">
                            <img src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='100' height='80'><rect fill='%23e9c46a' width='100' height='80'/></svg>" alt="精彩瞬间3">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部信息 -->
    <div class="footer-section">
        <div class="container">
            <div class="row">
                <div class="col-md-8">
                    <p><strong>版权所有：宿州学院 体育学院</strong></p>
                    <p>电话：0557-2871950</p>
                    <p>E-mail：<EMAIL></p>
                </div>
                <div class="col-md-4">
                    <p><strong>技术支持：智慧校园</strong></p>
                    <p>邮编：234000</p>
                    <p>地址：安徽省宿州市教育园区（东区）</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
