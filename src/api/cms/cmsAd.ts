import request from '/@/utils/request'
// 查询广告列表
export function listCmsAd(query:object) {
  return request({
    url: '/api/v1/cms/cmsAd/list',
    method: 'get',
    params: query
  })
}
// 查询广告详细
export function getCmsAd(id:number) {
  return request({
    url: '/api/v1/cms/cmsAd/get',
    method: 'get',
    params: {
      id: id.toString()
    }
  })
}
// 新增广告
export function addCmsAd(data:object) {
  return request({
    url: '/api/v1/cms/cmsAd/add',
    method: 'post',
    data: data
  })
}
// 修改广告
export function updateCmsAd(data:object) {
  return request({
    url: '/api/v1/cms/cmsAd/edit',
    method: 'put',
    data: data
  })
}
// 删除广告
export function delCmsAd(ids:number[]) {
  return request({
    url: '/api/v1/cms/cmsAd/delete',
    method: 'delete',
    data:{
      ids:ids
    }
  })
}
// 关联cms_ad_position表选项
export function listCmsAdPosition(query:object){
   return request({
     url: '/api/v1/cms/cmsAdPosition/list',
     method: 'get',
     params: query
   })
}
