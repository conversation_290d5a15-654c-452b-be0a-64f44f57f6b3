<template>
  <div class="cms-cmsArticleComment-container">
    <el-card shadow="hover">
      <div class="cms-cmsArticleComment-search mb15">
        <el-form ref="queryForm" :inline="true" :model="tableData.param" label-width="68px">
          <el-form-item label="评论内容" prop="content">
            <el-input v-model="tableData.param.content" clearable placeholder="请输入内容" @keyup.enter.native="handleQuery"/>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="tableData.param.status" clearable placeholder="请选择状态">
              <el-option label="全部" value=""/>
              <el-option label="待审核" value="0"/>
              <el-option label="已审核" value="1"/>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">
              <el-icon>
                <ele-Search/>
              </el-icon>
              搜索
            </el-button>
            <el-button @click="resetQuery(queryForm)">
              <el-icon>
                <ele-Refresh/>
              </el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-auth="'cms/admin/cmsComment/delete'" :disabled="multiple" type="danger" @click="handleDelete(null)">
              <el-icon>
                <ele-Delete/>
              </el-icon>
              删除
            </el-button>
          </el-col>
        </el-row>
      </div>
      <el-table v-loading="tableData.loading" :data="tableData.data" border @selection-change="handleSelectionChange">
        <el-table-column align="center" type="selection" width="55"/>
        <el-table-column label="评论内容" prop="content">
          <template #default="scope">
            <div class="comment-article-title">文章:{{ scope.row.article.title }}(id:{{ scope.row.article.id }})</div>
            <div class="comment-member">
              用户：
              <span class="comment-member-nickname" v-if="scope.row.member">{{ scope.row.member.nickname }}(id:{{ scope.row.member.id }})</span>
              <span class="comment-member-nickname" v-else>未知用户</span>
              [{{ scope.row.createdAt }}]
            </div>
            <div v-html="faceUrlFix(scope.row.content)"></div>
            <div v-if="scope.row.children" class="comment-reply-list">
              <div v-for="(item, index) in scope.row.children" :key="index">
                <span class="comment-member-nickname" v-if="item.member">{{ item.member.nickname }}</span>
                <span class="comment-member-nickname" v-else>未知用户</span>
                回复了:
                <span class="comment-member-nickname" v-if="item.replyMember">{{ item.replyMember.nickname }}</span>
                <span class="comment-member-nickname" v-else>未知用户</span>
                : <span v-html="faceUrlFix(item.content)"></span>[{{
                  item.createdAt
                }}]
                <el-switch
                    size="default"
                    style="margin: 0 10px;"
                    v-model="item.status"
                    :active-value="1"
                    :inactive-value="0"
                    @change="replyStatusChange(item)"
                ></el-switch>
                <a class="comment-member-nickname" style="cursor: pointer" @click="handleDeleteCommentReply(item)">删除</a>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="审核状态" width="100px">
          <template #default="scope">
            <el-switch
                v-model="scope.row.status"
                :active-value="1"
                :inactive-value="0"
                @change="statusChange(scope.row)"
            ></el-switch>
          </template>
        </el-table-column>
        <el-table-column align="center" class-name="small-padding" label="操作" width="90px">
          <template #default="scope">
            <el-button v-auth="'cms/admin/cmsComment/delete'" link type="primary" @click="handleDelete(scope.row)">
              <el-icon>
                <ele-DeleteFilled/>
              </el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
          v-show="tableData.total>0"
          v-model:limit="tableData.param.pageSize"
          v-model:page="tableData.param.pageNum"
          :total="tableData.total"
          @pagination="getList"
      />
    </el-card>
  </div>
</template>
<script lang="ts">
import {changeCmsCommentReplyStatus, changeCmsCommentStatus, delCmsComment, delCmsCommentReply, listCmsComment} from "/@/api/cms/cmsComment"
import {defineComponent, onMounted, reactive, ref, toRefs} from "vue";
import {CmsArticleCommentState, CommentColumns} from "/@/views/cms/cmsComment/list/component/model";
import {ElMessage, ElMessageBox, FormInstance} from "element-plus";

export default defineComponent({
  name: "apiV1CmsCmsArticleCommentList",
  components: {},
  setup() {
    // 非单个禁用
    const single = ref(true);
    // 非多个禁用
    const multiple = ref(true);
    const queryForm = ref()
    const state = reactive<CmsArticleCommentState>({
      ids: [],
      tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
          pageNum: 1,
          pageSize: 10,
          title: undefined,
          categoryId: undefined,
          moduleId: undefined,
          flag: undefined,
          status: undefined,
          newsTitle: undefined,
          memberId: undefined,
          content: undefined,
        }
      }
    });
    onMounted(() => {
      getList()
    })
    const faceUrlFix = (content: string) => {
      return content.replaceAll(/src="\//g, "src=\"" + import.meta.env.VITE_API_URL + "/")
    }
    // 状态修改
    const statusChange = (row: CommentColumns) => {
      changeCmsCommentStatus(row.id, row.status).then((res: any) => {
        console.log(res)
      }).catch(function () {
        row.status = row.status === 0 ? 1 : 0
      })
    }
    /** 查询文章评论列表 */
    const getList = () => {
      state.tableData.loading = true
      listCmsComment(state.tableData.param).then((response: any) => {
        let list = response.data.list || []
        state.tableData.data = list
        state.tableData.total = response.data.total
        state.tableData.loading = false
      })
    }
    /** 搜索按钮操作 */
    const handleQuery = () => {
      state.tableData.param.pageNum = 1
      getList()
    }
    /** 重置按钮操作 */
    const resetQuery = (formEl: FormInstance | undefined) => {
      if (!formEl) return;
      formEl.resetFields();
      handleQuery()
    }
    // 多选框选中数据
    const handleSelectionChange = (selection: Array<CommentColumns>) => {
      state.ids = selection.map(item => item.id)
      single.value = selection.length != 1
      multiple.value = !selection.length
    }
    /** 删除按钮操作 */
    const handleDelete = (row: CommentColumns | null) => {
      let ids: number[] = []
      if (row) {
        ids = [row.id]
      } else {
        ids = state.ids
      }
      ElMessageBox.confirm('是否确认删除评论', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function () {
        return delCmsComment(ids)
      }).then(() => {
        getList()
        ElMessage.success('删除成功')
      }).catch(function () {
      })
    }
    /** 评论回复删除按钮操作 */
    const handleDeleteCommentReply = (item: CommentColumns) => {
      ElMessageBox.confirm('是否确认删除', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function () {
        return delCmsCommentReply(item.id)
      }).then(() => {
        getList()
        ElMessage.success('删除成功')
      }).catch(function () {
        ElMessage.error("操作失败")
      })
    }
    // 评论回复状态修改
    const replyStatusChange = (row: CommentColumns) => {
      changeCmsCommentReplyStatus(row.id, row.status).then((res: any) => {
        console.log(res)
      }).catch(function () {
        ElMessage.error("操作失败")
      })
    }
    return {
      faceUrlFix,
      statusChange,
      getList,
      handleQuery,
      queryForm,
      resetQuery,
      single,
      multiple,
      handleDelete,
      handleSelectionChange,
      handleDeleteCommentReply,
      replyStatusChange,
      ...toRefs(state)
    }
  }
})
</script>
<style scoped>
.comment-member-nickname {
  color: #1890ff;
  font-weight: bold;
}

.comment-reply-list {
  position: relative;
  margin: 15px 0 0 0px;
  padding: 10px;
  list-style: none;
  background: #f8f8f8;
  border-radius: 4px;
}

.comment-reply-list:before {
  position: absolute;
  left: 15px;
  top: -17px;
  border: 9px solid transparent;
  border-bottom: 10px solid #f8f8f8;
  content: "";
}

.comment-article-title {
  font-weight: bold;
  font-size: 14px;
}
</style>
