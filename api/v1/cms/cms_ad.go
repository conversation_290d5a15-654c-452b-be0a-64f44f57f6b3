// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2023-02-17 16:46:33
// 生成路径: api/v1/cms/cms_ad.go
// 生成人：yxf
// desc:广告相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package cms

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/cms/model"
)

// CmsAdSearchReq 分页请求参数
type CmsAdSearchReq struct {
	g.Meta   `path:"/list" tags:"cms后台/广告" method:"get" summary:"广告列表"`
	Name     string `p:"name"` //广告名称
	Id       string `p:"id"`
	Position string `p:"position"` //广告位置
	commonApi.PageReq
	commonApi.Author
}

// CmsAdSearchRes 列表返回结果
type CmsAdSearchRes struct {
	g.Meta `mime:"application/json"`
	commonApi.ListRes
	List []*model.CmsAdListRes `json:"list"`
}

// CmsAdAddReq 添加操作请求参数
type CmsAdAddReq struct {
	g.Meta `path:"/add" tags:"cms后台/广告" method:"post" summary:"广告添加"`
	commonApi.Author
	Name     string `p:"name" v:"required#广告名称不能为空"`
	Type     string `p:"type" `
	Code     string `p:"code" `
	Content  string `p:"content" `
	Url      string `p:"url" `
	Sort     int    `p:"sort" `
	Images   string `p:"images" `
	Status   int    `p:"status" `
	Position int    `p:"position" v:"required#广告位置不能为空"`
}

// CmsAdAddRes 添加操作返回结果
type CmsAdAddRes struct {
	commonApi.EmptyRes
}

// CmsAdEditReq 修改操作请求参数
type CmsAdEditReq struct {
	g.Meta `path:"/edit" tags:"cms后台/广告" method:"put" summary:"广告修改"`
	commonApi.Author
	Id       uint   `p:"id" v:"required#主键ID不能为空"`
	Name     string `p:"name" v:"required#广告名称不能为空"`
	Type     string `p:"type" `
	Code     string `p:"code" `
	Content  string `p:"content" `
	Url      string `p:"url" `
	Sort     int    `p:"sort" `
	Images   string `p:"images" `
	Status   int    `p:"status" `
	Position int    `p:"position" v:"required#广告位置不能为空"`
}

// CmsAdEditRes 修改操作返回结果
type CmsAdEditRes struct {
	commonApi.EmptyRes
}

// CmsAdGetReq 获取一条数据请求
type CmsAdGetReq struct {
	g.Meta `path:"/get" tags:"cms后台/广告" method:"get" summary:"获取广告信息"`
	commonApi.Author
	Id uint `p:"id" v:"required#主键必须"` //通过主键获取
}

// CmsAdGetRes 获取一条数据结果
type CmsAdGetRes struct {
	g.Meta `mime:"application/json"`
	*model.CmsAdInfoRes
}

// CmsAdDeleteReq 删除数据请求
type CmsAdDeleteReq struct {
	g.Meta `path:"/delete" tags:"cms后台/广告" method:"delete" summary:"删除广告"`
	commonApi.Author
	Ids []uint `p:"ids" v:"required#主键必须"` //通过主键删除
}

// CmsAdDeleteRes 删除数据返回
type CmsAdDeleteRes struct {
	commonApi.EmptyRes
}
