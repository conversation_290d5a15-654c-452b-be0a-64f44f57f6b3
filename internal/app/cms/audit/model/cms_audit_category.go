// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-05-07 08:31:55
// 生成路径: internal/app/cms/audit/model/cms_audit_category.go
// 生成人：gfast
// desc:栏目审核设置
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/util/gmeta"
	cmsModel "github.com/tiger1103/gfast/v3/internal/app/cms/model"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// CmsAuditCategoryInfoRes is the golang structure for table cms_audit_category.
type CmsAuditCategoryInfoRes struct {
	gmeta.Meta  `orm:"table:cms_audit_category"`
	Id          string `orm:"id,primary" json:"id" dc:"ID"`                 // ID
	AuditUserId uint   `orm:"audit_user_id" json:"auditUserId" dc:"审核人员ID"` // 审核人员ID
	CategoryId  uint   `orm:"category_id" json:"categoryId" dc:"栏目ID"`      // 栏目ID
	Step        uint   `orm:"step" json:"step" dc:"步骤"`                     // 步骤
	StepId      uint   `orm:"step_id" json:"stepId" dc:"步骤ID"`              // 步骤ID
	UserIds     string `orm:"user_ids" json:"userIds" dc:"用户IDS"`           // 用户IDS
}

type CmsAuditCategoryListRes struct {
	Id           string                       `json:"id" dc:"ID"`
	AuditUserId  uint                         `json:"auditUserId" dc:"审核人员ID"`
	CategoryId   uint                         `json:"categoryId" dc:"栏目ID"`
	UserNames    string                       `json:"userNames"`                              //用户信息
	StepInfo     *CmsAuditStepInfoRes         `json:"stepInfo"`                               //步骤信息
	CategoryInfo *cmsModel.CmsCategoryInfoRes `orm:"with:id=category_id" json:"categoryInfo"` //栏目信息
	AuditUser    *CmsAuditUserInfoRes         `orm:"with:id=audit_user_id" json:"auditUser"`  //审核人员
}

// CmsAuditCategorySearchReq 分页请求参数
type CmsAuditCategorySearchReq struct {
	comModel.PageReq
	Id          string `p:"id" dc:"ID"`                                                 //ID
	AuditUserId string `p:"auditUserId" v:"auditUserId@integer#审核人员ID需为整数" dc:"审核人员ID"` //审核人员ID
	CategoryId  string `p:"categoryId" v:"categoryId@integer#栏目ID需为整数" dc:"栏目ID"`       //栏目ID
}

// CmsAuditCategorySearchRes 列表返回结果
type CmsAuditCategorySearchRes struct {
	comModel.ListRes
	List []*CmsAuditCategoryListRes `json:"list"`
}

// CmsAuditCategoryAddReq 添加操作请求参数
type CmsAuditCategoryAddReq struct {
	AuditUserId uint   `p:"auditUserId"  dc:"审核人员ID"`
	CategoryId  uint   `p:"categoryId"  dc:"栏目ID"`
	UserIds     string `p:"userIds"  dc:"用户IDS"`
	Step        uint   `p:"step"  dc:"步骤"`
	StepId      uint   `p:"stepId"  dc:"步骤ID"`
}

// CmsAuditCategoryEditReq 修改操作请求参数
type CmsAuditCategoryEditReq struct {
	Id          string `p:"id" v:"required#主键ID不能为空" dc:"ID"`
	AuditUserId uint   `p:"auditUserId"  dc:"审核人员ID"`
	CategoryId  uint   `p:"categoryId"  dc:"栏目ID"`
	UserIds     string `p:"userIds"  dc:"用户IDS"`
	Step        uint   `p:"step"  dc:"步骤"`
	StepId      uint   `p:"stepId"  dc:"步骤ID"`
}
