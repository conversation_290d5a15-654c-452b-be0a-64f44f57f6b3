// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2023-02-01 15:22:21
// 生成路径: internal/app/system/dao/internal/modules_field.go
// 生成人：yxf
// desc:模型字段
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// ModulesFieldDao is the manager for logic model data accessing and custom defined data operations functions management.
type ModulesFieldDao struct {
	table   string              // Table is the underlying table name of the DAO.
	group   string              // Group is the database configuration group name of current DAO.
	columns ModulesFieldColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// ModulesFieldColumns defines and stores column names for table modules_field.
type ModulesFieldColumns struct {
	Id          string // ID
	Label       string // 字段名称
	Name        string // 调用名称
	Type        string // 类型
	Description string // 描述
	Default     string // 默认值
	Sort        string // 排序
	Options     string // 选项数据
	ModuleId    string // 模型ID
	FieldLength string // 字段长度
}

var modulesFieldColumns = ModulesFieldColumns{
	Id:          "id",
	Label:       "label",
	Name:        "name",
	Type:        "type",
	Description: "description",
	Default:     "default",
	Sort:        "sort",
	Options:     "options",
	ModuleId:    "module_id",
	FieldLength: "field_length",
}

// NewModulesFieldDao creates and returns a new DAO object for table data access.
func NewModulesFieldDao() *ModulesFieldDao {
	return &ModulesFieldDao{
		group:   "default",
		table:   "modules_field",
		columns: modulesFieldColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *ModulesFieldDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *ModulesFieldDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *ModulesFieldDao) Columns() ModulesFieldColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *ModulesFieldDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *ModulesFieldDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *ModulesFieldDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
