<template>
  <div class="ucenter-ucenterLevel-container">
    <el-card shadow="hover">
      <div class="ucenter-ucenterLevel-search mb15">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-auth="'api/v1/ucenter/ucenterLevel/add'" type="primary" @click="handleAdd">
              <el-icon>
                <ele-Plus/>
              </el-icon>
              新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-auth="'api/v1/ucenter/ucenterLevel/delete'" :disabled="multiple" type="danger" @click="handleDelete(null)">
              <el-icon>
                <ele-Delete/>
              </el-icon>
              删除
            </el-button>
          </el-col>
        </el-row>
      </div>
      <el-table v-loading="loading" :data="tableData.data" border stripe @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55"/>
        <el-table-column label="ID" width="80px" prop="id"/>
        <el-table-column label="名称" width="140px" prop="name"/>
        <el-table-column label="描述" min-width="100px" prop="description"/>
        <el-table-column label="排序" width="100px" prop="listOrder"/>
        <el-table-column class-name="small-padding" fixed="right" label="操作" width="160px">
          <template #default="scope">
            <el-button v-auth="'api/v1/ucenter/ucenterLevel/edit'" link type="primary" @click="handleUpdate(scope.row)">
              <el-icon>
                <ele-EditPen/>
              </el-icon>
              修改
            </el-button>
            <el-button v-auth="'api/v1/ucenter/ucenterLevel/delete'" link type="primary" @click="handleDelete(scope.row)">
              <el-icon>
                <ele-DeleteFilled/>
              </el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="tableData.total>0" v-model:limit="tableData.param.pageSize" v-model:page="tableData.param.pageNum" :total="tableData.total"
                  @pagination="ucenterLevelList"/>
    </el-card>
    <apiV1UcenterUcenterLevelAdd ref="addRef" @ucenterLevelList="ucenterLevelList"></apiV1UcenterUcenterLevelAdd>
    <apiV1UcenterUcenterLevelEdit ref="editRef" @ucenterLevelList="ucenterLevelList"></apiV1UcenterUcenterLevelEdit>
    <apiV1UcenterUcenterLevelDetail ref="detailRef" @ucenterLevelList="ucenterLevelList"></apiV1UcenterUcenterLevelDetail>
  </div>
</template>
<script lang="ts">
import {computed, defineComponent, getCurrentInstance, onMounted, reactive, ref, toRaw, toRefs} from "vue";
import {ElMessage, ElMessageBox, FormInstance} from "element-plus";
import {delUcenterLevel, listUcenterLevel} from "/@/api/ucenter/ucenterLevel";
import {UcenterLevelInfoData, UcenterLevelTableColumns, UcenterLevelTableDataState} from "/@/views/ucenter/ucenterLevel/list/component/model";
import apiV1UcenterUcenterLevelAdd from "/@/views/ucenter/ucenterLevel/list/component/add.vue";
import apiV1UcenterUcenterLevelEdit from "/@/views/ucenter/ucenterLevel/list/component/edit.vue";
import apiV1UcenterUcenterLevelDetail from "/@/views/ucenter/ucenterLevel/list/component/detail.vue";

export default defineComponent({
  name: "apiV1UcenterUcenterLevelList",
  components: {
    apiV1UcenterUcenterLevelAdd,
    apiV1UcenterUcenterLevelEdit,
    apiV1UcenterUcenterLevelDetail
  },
  setup() {
    const {proxy} = <any>getCurrentInstance();
    const loading = ref(false);
    const queryRef = ref();
    const addRef = ref();
    const editRef = ref();
    const detailRef = ref();
    // 是否显示所有搜索选项
    const showAll = ref(false);
    // 非单个禁用
    const single = ref(true);
    // 非多个禁用
    const multiple = ref(true);
    const word = computed(() => {
      if (showAll.value === false) {
        //对文字进行处理
        return "展开搜索";
      } else {
        return "收起搜索";
      }
    });
    // 字典选项数据
    const {} = proxy.useDict(
    );
    const state = reactive<UcenterLevelTableDataState>({
      ids: [],
      tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
          pageNum: 1,
          pageSize: 10,
          dateRange: []
        }
      }
    });
    // 页面加载时
    onMounted(() => {
      initTableData();
    });
    // 初始化表格数据
    const initTableData = () => {
      ucenterLevelList();
    };
    /** 重置按钮操作 */
    const resetQuery = (formEl: FormInstance | undefined) => {
      if (!formEl) return;
      formEl.resetFields();
      ucenterLevelList();
    };
    // 获取列表数据
    const ucenterLevelList = () => {
      loading.value = true;
      listUcenterLevel(state.tableData.param).then((res: any) => {
        let list = res.data.list ?? [];
        state.tableData.data = list;
        state.tableData.total = res.data.total;
        loading.value = false;
      });
    };
    const toggleSearch = () => {
      showAll.value = !showAll.value;
    };
    // 多选框选中数据
    const handleSelectionChange = (selection: Array<UcenterLevelInfoData>) => {
      state.ids = selection.map(item => item.id);
      single.value = selection.length != 1;
      multiple.value = !selection.length;
    };
    const handleAdd = () => {
      addRef.value.openDialog();
    };
    const handleUpdate = (row: UcenterLevelTableColumns) => {
      if (!row) {
        row = state.tableData.data.find((item: UcenterLevelTableColumns) => {
          return item.id === state.ids[0];
        }) as UcenterLevelTableColumns;
      }
      editRef.value.openDialog(toRaw(row));
    };
    const handleDelete = (row: UcenterLevelTableColumns) => {
      let msg = "你确定要删除所选数据？";
      let id: number[] = [];
      if (row) {
        msg = `此操作将永久删除数据，是否继续?`;
        id = [row.id];
      } else {
        id = state.ids;
      }
      if (id.length === 0) {
        ElMessage.error("请选择要删除的数据。");
        return;
      }
      ElMessageBox.confirm(msg, "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      })
          .then(() => {
            delUcenterLevel(id).then(() => {
              ElMessage.success("删除成功");
              ucenterLevelList();
            });
          })
          .catch(() => {
          });
    };
    const handleView = (row: UcenterLevelTableColumns) => {
      detailRef.value.openDialog(toRaw(row));
    };
    return {
      proxy,
      addRef,
      editRef,
      detailRef,
      showAll,
      loading,
      single,
      multiple,
      word,
      queryRef,
      resetQuery,
      ucenterLevelList,
      toggleSearch,
      handleSelectionChange,
      handleAdd,
      handleUpdate,
      handleDelete,
      ...toRefs(state)
    };
  }
});
</script>
<style lang="scss" scoped>
.colBlock {
  display: block;
}

.colNone {
  display: none;
}
</style>
