import{s}from"./index-BGmsw1a8.js";function o(t){return s({url:"/api/v1/system/sysJob/list",method:"get",params:t})}function a(t){return s({url:"/api/v1/system/sysJob/get",method:"get",params:{jobId:t.toString()}})}function r(t){return s({url:"/api/v1/system/sysJob/add",method:"post",data:t})}function u(t){return s({url:"/api/v1/system/sysJob/edit",method:"put",data:t})}function n(t){return s({url:"/api/v1/system/sysJob/delete",method:"delete",data:{jobIds:t}})}function d(t){return s({url:"/api/v1/system/sysJob/run",method:"put",data:{jobId:t}})}function i(t){return s({url:"/api/v1/system/sysJob/start",method:"put",data:{jobId:t}})}function y(t){return s({url:"/api/v1/system/sysJob/stop",method:"put",data:{jobId:t}})}function m(t){return s({url:"/api/v1/system/sysJob/logs",method:"get",params:t})}function p(t){return s({url:"/api/v1/system/sysJob/deleteLogs",method:"delete",data:{logIds:t}})}export{m as a,r as b,n as c,p as d,y as e,a as g,o as l,d as r,i as s,u};
