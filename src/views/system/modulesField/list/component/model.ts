export interface ModulesFieldTableColumns {
    id:number;  // ID
    label:string;  // 字段名称
    name:string;  // 调用名称
    type:string;  // 类型
    description:string;  // 描述
    default:string;  // 默认值
    sort:number;  // 排序
    options:string;  // 选项数据
    moduleId:number;  // 模型ID
    fieldLength:number;
}

export interface ModulesFieldInfoData {
    id:number|undefined;        // ID
    label:string|undefined; // 字段名称
    name:string; // 调用名称
    type:string|undefined; // 类型
    description:string|undefined; // 描述
    default:string|undefined; // 默认值
    sort:number|undefined; // 排序
    options:string|undefined; // 选项数据
    moduleId:number|undefined; // 模型ID
    fieldLength:number|undefined; // 模型ID
    content:string|undefined; // 内容
}

export interface ModulesFieldTableDataState {
    ids:any[];
    tableData: {
        data: Array<ModulesFieldTableColumns>;
        total: number;
        loading: boolean;
        param: {
            moduleId: number;
            pageNum: number;
            pageSize: number;
        };
    };
}

export interface ModulesFieldEditState{
    loading:boolean;
    isShowDialog: boolean;
    formData:ModulesFieldInfoData;
    rules: object;
}

export const modulesInfoFieldOptions = [
    {label: "单行文本", value: "text"},
    {label: "多行文本", value: "textarea"},
    {label: "单选框", value: "radio"},
    {label: "多选框", value: "checkbox"},
    {label: "富文本", value: "richtext"},
    {label: "单图片", value: "image"},
    {label: "多图片", value: "images"},
    {label: "单文件", value: "file"},
    {label: "多文件", value: "files"},
    {label: "下拉选择", value: "select"},
    {label: "数字", value: "number"},
    {label: "日期", value: "date"},
    {label: "日期时间", value: "datetime"}
]
