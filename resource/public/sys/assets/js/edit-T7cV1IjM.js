import{g as C,a as E,u as U}from"./ucenterNotifications-Cj4LEkOJ.js";import{a as b}from"./element-plus-CUmVNDWO.js";import{d as A,f as I,r as y,X as k,ad as w,u as S,ag as l,aq as h,c as _,o as f,P as r,H as i,O as L,a6 as R,G as $,L as c,a as q,I as O,M}from"./@vue-C21YZbHS.js";import{a as G}from"./index-BGmsw1a8.js";import"./lodash-es-S0Y0Up6J.js";import"./@vueuse-D_5IXmcI.js";import"./@element-plus-_Cc-TEQX.js";import"./@popperjs-D_chPuIy.js";import"./@ctrl-r5W6hzzQ.js";import"./dayjs-CepoAVPu.js";import"./@intlify-D8kAWvSi.js";import"./async-validator-9PlIezaS.js";import"./memoize-one-BdPwpGay.js";import"./normalize-wheel-es-BQoi3Ox2.js";import"./@floating-ui-DwceP2Gb.js";import"./pinia-DwJf82dV.js";import"./vue-router-BDT9dCQ7.js";import"./js-cookie-Cz0CWeBA.js";import"./nprogress-Scw6VIZr.js";import"./axios-B62VicFi.js";import"./qs-D41HS0uK.js";import"./side-channel-DytTmCZg.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-DbsE5JW_.js";import"./crypto-js-oM26lpET.js";import"./side-channel-list-C_7Uk4RP.js";import"./side-channel-map-BUokNT8A.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-bSkmVQJL.js";import"./vue-i18n-BkLk9Ti1.js";import"./vue-iNFchrNl.js";import"./vue-clipboard3-Dr8V5xR7.js";import"./clipboard-DzJOpNE6.js";import"./mitt-DJ65BbbF.js";import"./vue-grid-layout-CIPjP0XN.js";import"./vue-simple-uploader-j6sX1TKo.js";import"./vue-ueditor-wrap-CJ5gjsd3.js";const H=A({name:"apiV1UcenterUcenterNotificationsEdit",components:{},props:{typeOptions:{type:Array,default:()=>[]}},setup(o,{emit:e}){const{proxy:B}=I(),D=y(null),V=y(),t=k({loading:!1,isShowDialog:!1,formData:{id:void 0,type:void 0,content:void 0,status:!1,createdAt:void 0,memberId:void 0},rules:{id:[{required:!0,message:"ID不能为空",trigger:"blur"}],status:[{required:!0,message:"状态不能为空",trigger:"blur"}]}}),g=n=>{v(),n&&C(n.id).then(p=>{const m=p.data;m.type=""+m.type,t.formData=m}),t.isShowDialog=!0},s=()=>{t.isShowDialog=!1},u=()=>{s()},d=()=>{const n=S(D);n&&n.validate(p=>{p&&(t.loading=!0,!t.formData.id||t.formData.id===0?E(t.formData).then(()=>{b.success("添加成功"),s(),e("ucenterNotificationsList")}).finally(()=>{t.loading=!1}):U(t.formData).then(()=>{b.success("修改成功"),s(),e("ucenterNotificationsList")}).finally(()=>{t.loading=!1}))})},v=()=>{t.formData={id:void 0,type:void 0,content:void 0,status:!1,createdAt:void 0,memberId:void 0}};return{proxy:B,openDialog:g,closeDialog:s,onCancel:u,onSubmit:d,menuRef:V,formRef:D,...w(t)}}}),P={class:"ucenter-ucenterNotifications-edit"},T={class:"dialog-footer"};function W(o,e,B,D,V,t){const g=l("el-option"),s=l("el-select"),u=l("el-form-item"),d=l("el-input"),v=l("el-radio"),n=l("el-radio-group"),p=l("el-form"),m=l("el-button"),F=l("el-dialog"),N=h("drag");return f(),_("div",P,[r(F,{modelValue:o.isShowDialog,"onUpdate:modelValue":e[4]||(e[4]=a=>o.isShowDialog=a),width:"769px","close-on-click-modal":!1,"destroy-on-close":!0},{header:i(()=>[O((f(),_("div",null,[c(M((!o.formData.id||o.formData.id==0?"添加":"修改")+"用户通知"),1)])),[[N,[".ucenter-ucenterNotifications-edit .el-dialog",".ucenter-ucenterNotifications-edit .el-dialog__header"]]])]),footer:i(()=>[q("div",T,[r(m,{type:"primary",onClick:o.onSubmit},{default:i(()=>e[6]||(e[6]=[c("确 定")])),_:1},8,["onClick"]),r(m,{onClick:o.onCancel},{default:i(()=>e[7]||(e[7]=[c("取 消")])),_:1},8,["onClick"])])]),default:i(()=>[r(p,{ref:"formRef",model:o.formData,rules:o.rules,"label-width":"90px"},{default:i(()=>[r(u,{label:"通知类型",prop:"type"},{default:i(()=>[r(s,{modelValue:o.formData.type,"onUpdate:modelValue":e[0]||(e[0]=a=>o.formData.type=a),placeholder:"请选择通知类型"},{default:i(()=>[(f(!0),_(L,null,R(o.typeOptions,a=>(f(),$(g,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),r(u,{label:"内容",prop:"content"},{default:i(()=>[r(d,{modelValue:o.formData.content,"onUpdate:modelValue":e[1]||(e[1]=a=>o.formData.content=a),placeholder:"请输入内容"},null,8,["modelValue"])]),_:1}),r(u,{label:"状态",prop:"status"},{default:i(()=>[r(n,{modelValue:o.formData.status,"onUpdate:modelValue":e[2]||(e[2]=a=>o.formData.status=a)},{default:i(()=>[r(v,null,{default:i(()=>e[5]||(e[5]=[c("请选择字典生成")])),_:1})]),_:1},8,["modelValue"])]),_:1}),r(u,{label:"会员ID",prop:"memberId"},{default:i(()=>[r(d,{modelValue:o.formData.memberId,"onUpdate:modelValue":e[3]||(e[3]=a=>o.formData.memberId=a),placeholder:"请输入会员ID"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}const Me=G(H,[["render",W]]);export{Me as default};
