<div class="member-form-wrap member-form-login">
    <div class="member-form-inner">
        <div class="member-form-head">
            <div class="member-form-head">
                <a class="member-form-logo" href="${.cmsConfig.site_url}">
                    <img src="/cms/default/images/cms_logo.png">
                </a>
            </div>
        </div>
        <ul class="member-form-tab">
            <li class="active"><a data-type="1" href="#">普通账号注册</a></li>
            <li><a data-type="2" href="#">手机号码注册</a></li>
        </ul>
        <form class="member-form j-member-form" id="register-form" method="post" action="">
            <div class="member-form-items">
                <div class="form-group login-item login-1">
                    <label>
                        <i class="iconfont icon-user hotnews-icon"></i>
                        <input class="form-input require" id="username" name="username" placeholder="请输入手机号码/电子邮箱/用户名" type="text">
                    </label>
                </div>
                <div class="form-group login-item login-1">
                    <label>
                        <i class="iconfont icon-lock hotnews-icon"></i>
                        <input class="form-input" id="password" name="password" placeholder="请输入登录密码" type="password">
                    </label>
                </div>
                <div class="form-group login-item login-1">
                    <label>
                        <i class="iconfont icon-lock hotnews-icon"></i>
                        <input class="form-input" id="password2" name="password2" placeholder="确认登录密码" type="password">
                    </label>
                </div>
                <div class="form-group login-item login-2">
                    <label>
                        <i class="iconfont icon-phone hotnews-icon"></i>
                        <input class="form-input is-input require" data-label="手机号码" data-rule="mobile" id="mobile" name="mobile" placeholder="请输入手机号码" type="text">
                    </label>
                </div>
                <div class="form-group captcha">
                    <label>
                        <i class="iconfont icon-safetycertificate hotnews-icon"></i>
                        <input class="form-input require captcha-code" id="captcha_code" name="verifyCode" placeholder="请输入验证码" type="text">
                    </label>
                    <div class="captcha-img">
                        <img id="captcha_img" src="" style="cursor:pointer;">
                        <input id="captcha_key" name="verifyKey" value="" type="hidden">
                    </div>
                </div>
                <div class="login-item login-2">
                    <div class="form-group sms-code">
                        <label>
                            <i class="iconfont icon-safetycertificate hotnews-icon"></i>
                            <input autocomplete="off" class="form-input is-input require" data-label="验证码" data-rule="sms_code:user_phone" id="sms_code" name="msgCode" placeholder="请输入短信验证码"
                                    type="text">
                        </label>
                        <div class="btn btn-lg send-sms-code j-send-sms-code" data-target="user_phone" id="sendMobileCode">发送验证码</div>
                    </div>
                </div>
            </div>
            <input type="hidden" name="reg_type" id="reg_type" value="username">
            <input type="hidden" id="modal" value="false">
            <button class="btn btn-primary btn-block btn-lg" type="submit">注册</button>
        </form>
        <div class="member-form-footer" style="display:block;text-align: center;">
            <span class="member-switch">已有帐号？ <a href="${.cmsConfig.site_url}/account/login">立即登录</a></span>
            <span class="member-switch" style="margin-left: 20px;"><a href="${.cmsConfig.site_url}">返回首页</a></span>
        </div>
    </div>
</div>
<script src="${.cmsTheme}/libs/jquery.querystring.js"></script>
<script>
    $(function (){
        $("#register-form").on("submit",function (e){
            try {
                $("#register-form").attr("action",APIBASE+"ucenter/account/register").ajaxSubmit({
                    success:function (res){
                        if(res.code===0){
                            layer.msg("账号注册成功",{icon:1})
                            setTimeout(function (){
                                window.location.href = '${.cmsConfig.site_url}/account/login?referer='+$.queryString.get("referer")
                            },2000)
                        } else {
                            layer.msg(res.message,{icon:2})
                            $('#captcha_img').trigger('click')
                        }
                    },
                    error:function (err){
                        console.log(err)
                    }
                })
            }catch (e) {
                console.log(e)
                layer.error('发生错误')
            }
            return false
        })
    })
</script>