// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2023-02-17 16:46:34
// 生成路径: internal/app/cms/model/cms_ad.go
// 生成人：yxf
// desc:广告
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// CmsAdInfoRes is the golang structure for table cms_ad.
type CmsAdInfoRes struct {
	gmeta.Meta     `orm:"table:cms_ad"`
	Id             uint                      `orm:"id,primary" json:"id"`        // ID
	Name           string                    `orm:"name" json:"name"`            // 广告名称
	Type           string                    `orm:"type" json:"type"`            // 类型
	Content        string                    `orm:"content" json:"content"`      // 广告内容
	Url            string                    `orm:"url" json:"url"`              // 跳转链接
	CreatedAt      *gtime.Time               `orm:"created_at" json:"createdAt"` // 创建日期
	UpdatedAt      *gtime.Time               `orm:"updated_at" json:"updatedAt"` // 更新日期
	Sort           int                       `orm:"sort" json:"sort"`            // 排序
	Status         int                       `orm:"status" json:"status"`        // 状态
	Position       int                       `orm:"position" json:"position"`    // 广告位置
	LinkedPosition *LinkedCmsAdCmsAdPosition `orm:"with:id=position" json:"linkedPosition"`
}

type LinkedCmsAdCmsAdPosition struct {
	gmeta.Meta `orm:"table:cms_ad_position"`
	Id         uint   `orm:"id" json:"id"`     // ID
	Name       string `orm:"name" json:"name"` // 位置名称
}

type CmsAdListRes struct {
	Id             uint                      `json:"id"`
	Name           string                    `json:"name"`
	Type           string                    `json:"type"`
	Url            string                    `json:"url"`
	CreatedAt      *gtime.Time               `json:"createdAt"`
	Sort           int                       `json:"sort"`
	Content        string                    `json:"content"`
	Status         int                       `json:"status"`
	Position       int                       `json:"position"`
	LinkedPosition *LinkedCmsAdCmsAdPosition `orm:"with:id=position" json:"linkedPosition"`
}
