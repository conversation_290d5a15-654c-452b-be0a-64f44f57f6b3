// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-05-06 17:48:32
// 生成路径: internal/app/cms/audit/logic/cms_audit_step.go
// 生成人：zhanghui
// desc:审核步骤
// company:云南奇讯科技有限公司
// ==========================================================================

package cmsAuditStep

import (
	"context"
	"github.com/gogf/gf/v2/encoding/gjson"
	model3 "github.com/tiger1103/gfast/v3/internal/app/common/model"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/cms/audit/dao"
	"github.com/tiger1103/gfast/v3/internal/app/cms/audit/model"
	"github.com/tiger1103/gfast/v3/internal/app/cms/audit/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/cms/audit/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterCmsAuditStep(New())
}

func New() service.ICmsAuditStep {
	return &sCmsAuditStep{}
}

type sCmsAuditStep struct{}

func (s *sCmsAuditStep) List(ctx context.Context, req *model.CmsAuditStepSearchReq) (listRes *model.CmsAuditStepSearchRes, err error) {
	listRes = new(model.CmsAuditStepSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.CmsAuditStep.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.CmsAuditStep.Columns().Id+" = ?", req.Id)
		}
		if req.Title != "" {
			m = m.Where(dao.CmsAuditStep.Columns().Title+" = ?", req.Title)
		}
		if req.Step != "" {
			m = m.Where(dao.CmsAuditStep.Columns().Step+" = ?", gconv.Uint(req.Step))
		}
		if req.Type != "" {
			m = m.Where(dao.CmsAuditStep.Columns().Type+" = ?", req.Type)
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.CmsAuditStep.Columns().CreatedAt+" >=? AND "+dao.CmsAuditStep.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.CmsAuditStepListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.CmsAuditStepListRes, len(res))

		for k, v := range res {
			listRes.List[k] = &model.CmsAuditStepListRes{
				Id:        v.Id,
				Title:     v.Title,
				Step:      v.Step,
				Type:      v.Type,
				CreatedAt: v.CreatedAt,
			}
		}
	})
	return
}

func (s *sCmsAuditStep) GetById(ctx context.Context, id uint) (res *model.CmsAuditStepInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.CmsAuditStep.Ctx(ctx).WithAll().Where(dao.CmsAuditStep.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sCmsAuditStep) Add(ctx context.Context, req *model.CmsAuditStepAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.CmsAuditStep.Ctx(ctx).Insert(do.CmsAuditStep{
			Title: req.Title,
			Step:  req.Step,
			Type:  req.Type,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sCmsAuditStep) Edit(ctx context.Context, req *model.CmsAuditStepEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.CmsAuditStep.Ctx(ctx).WherePri(req.Id).Update(do.CmsAuditStep{
			Title: req.Title,
			Step:  req.Step,
			Type:  req.Type,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sCmsAuditStep) Delete(ctx context.Context, ids []uint) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.CmsAuditStep.Ctx(ctx).Delete(dao.CmsAuditStep.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

// 获取审核步骤对象
func (s *sCmsAuditStep) GetCmsAuditStepMap(ctx context.Context) (cmsAuditStepMap map[string]string, err error) {
	cmsAuditStepMap = make(map[string]string, 0)
	cache := commonService.Cache()
	cmsAuditStepMapCache := cache.Get(ctx, model.CmsAuditStepCacheKey.CmsAuditStepMapCache).MapStrStr()
	if cmsAuditStepMapCache != nil {
		err = gconv.Structs(cmsAuditStepMapCache, &cmsAuditStepMap)
		return
	}
	cmsAuditStepList, _ := s.List(ctx, &model.CmsAuditStepSearchReq{PageReq: model3.PageReq{PageSize: 1000, PageNum: 1}})
	if cmsAuditStepList == nil {
		return
	}
	for _, audit := range cmsAuditStepList.List {
		cmsAuditStepMap[gconv.String(audit.Id)] = audit.Title
	}
	cache.Set(ctx, model.CmsAuditStepCacheKey.CmsAuditStepMapCache, gjson.MustEncodeString(cmsAuditStepMap), 30*time.Second, model.CmsAuditStepCacheTag.CmsAuditStepMapCache)
	return
}
