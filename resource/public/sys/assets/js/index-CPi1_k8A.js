import{l as $,d as P}from"./ucenterNotifications-Cj4LEkOJ.js";import T from"./add-zCyuiXGi.js";import I from"./edit-T7cV1IjM.js";import M from"./detail-B4IiX_j4.js";import{a as R,E as z}from"./element-plus-CUmVNDWO.js";import{d as q,f as Q,r as s,k as G,X as H,h as X,ad as j,ab as J,ag as a,aq as E,c as K,o as _,P as i,H as l,a as F,I as g,G as D,L as U,M as O,Q as W}from"./@vue-C21YZbHS.js";import{a as Y}from"./index-BGmsw1a8.js";import"./lodash-es-S0Y0Up6J.js";import"./@vueuse-D_5IXmcI.js";import"./@element-plus-_Cc-TEQX.js";import"./@popperjs-D_chPuIy.js";import"./@ctrl-r5W6hzzQ.js";import"./dayjs-CepoAVPu.js";import"./@intlify-D8kAWvSi.js";import"./async-validator-9PlIezaS.js";import"./memoize-one-BdPwpGay.js";import"./normalize-wheel-es-BQoi3Ox2.js";import"./@floating-ui-DwceP2Gb.js";import"./pinia-DwJf82dV.js";import"./vue-router-BDT9dCQ7.js";import"./js-cookie-Cz0CWeBA.js";import"./nprogress-Scw6VIZr.js";import"./axios-B62VicFi.js";import"./qs-D41HS0uK.js";import"./side-channel-DytTmCZg.js";import"./es-errors-CFxpeikN.js";import"./object-inspect-DbsE5JW_.js";import"./crypto-js-oM26lpET.js";import"./side-channel-list-C_7Uk4RP.js";import"./side-channel-map-BUokNT8A.js";import"./get-intrinsic-CdQ0j820.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-Cv-yPkyD.js";import"./gopd-fcd2-aIC.js";import"./es-define-property-bDCdrV83.js";import"./has-symbols-BaUvM3gb.js";import"./get-proto-CEhLFpt-.js";import"./dunder-proto-BvNz4iDg.js";import"./call-bind-apply-helpers-CXPkwEps.js";import"./function-bind-BbkWVFrm.js";import"./hasown-C2NEVhna.js";import"./call-bound-B36HnitE.js";import"./side-channel-weakmap-bSkmVQJL.js";import"./vue-i18n-BkLk9Ti1.js";import"./vue-iNFchrNl.js";import"./vue-clipboard3-Dr8V5xR7.js";import"./clipboard-DzJOpNE6.js";import"./mitt-DJ65BbbF.js";import"./vue-grid-layout-CIPjP0XN.js";import"./vue-simple-uploader-j6sX1TKo.js";import"./vue-ueditor-wrap-CJ5gjsd3.js";const Z=q({name:"apiV1UcenterUcenterNotificationsList",components:{apiV1UcenterUcenterNotificationsAdd:T,apiV1UcenterUcenterNotificationsEdit:I,apiV1UcenterUcenterNotificationsDetail:M},setup(){const{proxy:t}=Q(),n=s(!1),w=s(),v=s(),y=s(),L=s(),c=s(!1),d=s(!0),m=s(!0),h=G(()=>c.value===!1?"展开搜索":"收起搜索"),{ucenter_notification_type:N}=t.useDict("ucenter_notification_type"),r=H({ids:[],tableData:{data:[],total:0,loading:!1,param:{username:"",pageNum:1,pageSize:10,dateRange:[]}}});X(()=>{p()});const p=()=>{u()},C=e=>{e&&(e.resetFields(),u())},u=()=>{n.value=!0,$(r.tableData.param).then(e=>{let o=e.data.list??[];r.tableData.data=o,r.tableData.total=e.data.total,n.value=!1})};return{proxy:t,addRef:v,editRef:y,detailRef:L,showAll:c,loading:n,single:d,multiple:m,word:h,queryRef:w,resetQuery:C,ucenterNotificationsList:u,toggleSearch:()=>{c.value=!c.value},typeFormat:e=>t.selectDictLabel(N.value,e.type),ucenter_notification_type:N,handleSelectionChange:e=>{r.ids=e.map(o=>o.id),d.value=e.length!=1,m.value=!e.length},handleAdd:()=>{v.value.openDialog()},handleUpdate:e=>{e||(e=r.tableData.data.find(o=>o.id===r.ids[0])),y.value.openDialog(J(e))},handleDelete:e=>{let o="你确定要删除所选数据？",f=[];if(e?(o="此操作将永久删除数据，是否继续?",f=[e.id]):f=r.ids,f.length===0){R.error("请选择要删除的数据。");return}z.confirm(o,"提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(()=>{P(f).then(()=>{R.success("删除成功"),u()})}).catch(()=>{})},...j(r)}}}),x={class:"ucenter-ucenterNotifications-container"},tt={class:"ucenter-ucenterNotifications-search mb15"};function et(t,n,w,v,y,L){const c=a("ele-Plus"),d=a("el-icon"),m=a("el-button"),h=a("el-col"),N=a("ele-Delete"),r=a("el-row"),p=a("el-table-column"),C=a("ele-DeleteFilled"),u=a("el-table"),V=a("pagination"),S=a("el-card"),A=a("apiV1UcenterUcenterNotificationsAdd"),B=a("apiV1UcenterUcenterNotificationsEdit"),k=a("apiV1UcenterUcenterNotificationsDetail"),b=E("auth"),e=E("loading");return _(),K("div",x,[i(S,{shadow:"hover"},{default:l(()=>[F("div",tt,[i(r,{gutter:10,class:"mb8"},{default:l(()=>[i(h,{span:1.5},{default:l(()=>[g((_(),D(m,{type:"primary",onClick:t.handleAdd},{default:l(()=>[i(d,null,{default:l(()=>[i(c)]),_:1}),n[3]||(n[3]=U(" 新增 "))]),_:1},8,["onClick"])),[[b,"api/v1/ucenter/ucenterNotifications/add"]])]),_:1}),i(h,{span:1.5},{default:l(()=>[g((_(),D(m,{disabled:t.multiple,type:"danger",onClick:n[0]||(n[0]=o=>t.handleDelete(null))},{default:l(()=>[i(d,null,{default:l(()=>[i(N)]),_:1}),n[4]||(n[4]=U(" 删除 "))]),_:1},8,["disabled"])),[[b,"api/v1/ucenter/ucenterNotifications/delete"]])]),_:1})]),_:1})]),g((_(),D(u,{stripe:"",border:"",data:t.tableData.data,onSelectionChange:t.handleSelectionChange,class:"small-padding"},{default:l(()=>[i(p,{type:"selection",width:"55"}),i(p,{label:"会员",width:"240px",prop:"memberId"},{default:l(({row:o})=>[U(O(o.member.username),1)]),_:1}),i(p,{label:"内容","min-width":"100px",prop:"content"}),i(p,{label:"创建日期",width:"170px",prop:"createdAt"},{default:l(o=>[F("span",null,O(t.proxy.parseTime(o.row.createdAt,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),i(p,{fixed:"right",label:"操作",width:"90px"},{default:l(o=>[g((_(),D(m,{link:"",type:"primary",onClick:f=>t.handleDelete(o.row)},{default:l(()=>[i(d,null,{default:l(()=>[i(C)]),_:1}),n[5]||(n[5]=U(" 删除 "))]),_:2},1032,["onClick"])),[[b,"api/v1/ucenter/ucenterNotifications/delete"]])]),_:1})]),_:1},8,["data","onSelectionChange"])),[[e,t.loading]]),g(i(V,{limit:t.tableData.param.pageSize,"onUpdate:limit":n[1]||(n[1]=o=>t.tableData.param.pageSize=o),page:t.tableData.param.pageNum,"onUpdate:page":n[2]||(n[2]=o=>t.tableData.param.pageNum=o),total:t.tableData.total,onPagination:t.ucenterNotificationsList},null,8,["limit","page","total","onPagination"]),[[W,t.tableData.total>0]])]),_:1}),i(A,{ref:"addRef",typeOptions:t.ucenter_notification_type,onUcenterNotificationsList:t.ucenterNotificationsList},null,8,["typeOptions","onUcenterNotificationsList"]),i(B,{ref:"editRef",typeOptions:t.ucenter_notification_type,onUcenterNotificationsList:t.ucenterNotificationsList},null,8,["typeOptions","onUcenterNotificationsList"]),i(k,{ref:"detailRef",typeOptions:t.ucenter_notification_type,onUcenterNotificationsList:t.ucenterNotificationsList},null,8,["typeOptions","onUcenterNotificationsList"])])}const xt=Y(Z,[["render",et],["__scopeId","data-v-c208f92e"]]);export{xt as default};
