// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2023-03-09 16:48:47
// 生成路径: internal/app/ucenter/model/ucenter_level.go
// 生成人：yxf
// desc:用户等级
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/util/gmeta"
)

// UCenterLevelInfoRes is the golang structure for table ucenter_level.
type UCenterLevelInfoRes struct {
	gmeta.Meta  `orm:"table:ucenter_level"`
	Id          uint   `orm:"id,primary" json:"id"`           // ID
	Name        string `orm:"name" json:"name"`               // 名称
	Description string `orm:"description" json:"description"` // 描述
	ListOrder   int    `orm:"list_order" json:"listOrder"`    // 排序
}

type UCenterLevelListRes struct {
	Id          uint   `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	ListOrder   int    `json:"listOrder"`
}
