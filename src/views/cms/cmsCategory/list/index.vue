<template>
  <div class="cms-cmsCategory-container">
    <el-card shadow="hover">
      <div class="cms-cmsCategory-content">
        <!-- 左侧树形 -->
        <LeftTree @node-click="handleNodeClick" ref="leftTreeRef" />

        <!-- 右侧表格 -->
        <RightList ref="rightListRef" :selected-category="selectedCategory" @refresh-tree="refreshTree" @open-edit="handleOpenEdit" @open-permission="handleOpenPermission" />
      </div>
    </el-card>

    <!-- 添加或修改分类对话框 -->
    <ApiV1CmsCmsCategoryEdit
      ref="editRef"
      :isInheritOptions="sys_yes_no"
      :statusOptions="sys_normal_disable"
      @cmsCategoryList="refreshList"
    />

    <!-- 分类权限对话框 -->
    <ApiV1CmsCmsCategoryPermission ref="permissionRef" />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, onMounted, getCurrentInstance } from 'vue';
import LeftTree from './component/leftTree.vue';
import RightList from './component/rightList.vue';
import ApiV1CmsCmsCategoryEdit from './component/edit.vue';
import ApiV1CmsCmsCategoryPermission from './component/permission.vue';

export default defineComponent({
  name: 'CmsCategoryList',
  components: {
    LeftTree,
    RightList,
    ApiV1CmsCmsCategoryEdit,
    ApiV1CmsCmsCategoryPermission
  },
  setup() {
    const { proxy } = getCurrentInstance() as any;

    // 字典选项数据
    const {
      sys_yes_no,
      sys_normal_disable
    } = proxy.useDict(
        "sys_yes_no",
        "sys_normal_disable"
    );

    const leftTreeRef = ref();
    const rightListRef = ref();
    const editRef = ref();
    const permissionRef = ref();
    const selectedCategory = ref(null);

    // 处理树节点点击
    const handleNodeClick = (data: any) => {
      selectedCategory.value = data;
    };

    // 刷新树
    const refreshTree = () => {
      leftTreeRef.value?.refreshTree();
    };

    // 刷新列表
    const refreshList = () => {
      rightListRef.value?.refreshList();
    };

    // 处理打开编辑组件事件
    const handleOpenEdit = (data: any) => {
      if (data.row) {
        // 修改
        editRef.value.openDialog(data.row, data.parentId);
      } else {
        // 新增
        editRef.value.openDialog(null, data.parentId);
      }
    };

    // 处理打开分类权限对话框事件
    const handleOpenPermission = () => {
      // 打开分类权限对话框
      permissionRef.value.openDialog();
    };

    onMounted(() => {
      // 初始化加载
    });

    return {
      leftTreeRef,
      rightListRef,
      editRef,
      permissionRef,
      selectedCategory,
      handleNodeClick,
      refreshTree,
      refreshList,
      handleOpenEdit,
      handleOpenPermission,
      sys_yes_no,
      sys_normal_disable
    };
  }
});
</script>

<style scoped>
.cms-cmsCategory-container {
  height: 100%;
  width: 100%;
}

.cms-cmsCategory-content {
  display: flex;
  height: calc(100vh - 150px);
  overflow: hidden;
}
</style>
