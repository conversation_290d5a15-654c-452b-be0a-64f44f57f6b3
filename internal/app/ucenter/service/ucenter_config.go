// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2023-03-09 16:48:33
// 生成路径: internal/app/ucenter/service/ucenter_config.go
// 生成人：yxf
// desc:用户中心配置
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"
	apiAdmin "github.com/tiger1103/gfast/v3/api/v1/ucenter/admin"
	"github.com/tiger1103/gfast/v3/internal/app/ucenter/model"
)

type IUCenterConfig interface {
	List(ctx context.Context, req *apiAdmin.UCenterConfigSearchReq) (res *apiAdmin.UCenterConfigSearchRes, err error)
	GetById(ctx context.Context, Id uint) (res *model.UCenterConfigInfoRes, err error)
	Add(ctx context.Context, req *apiAdmin.UCenterConfigAddReq) (err error)
	Edit(ctx context.Context, req *apiAdmin.UCenterConfigEditReq) (err error)
	Delete(ctx context.Context, Id []uint) (err error)
}

var localUCenterConfig IUCenterConfig

func UCenterConfig() IUCenterConfig {
	if localUCenterConfig == nil {
		panic("implement not found for interface IUCenterConfig, forgot register?")
	}
	return localUCenterConfig
}

func RegisterUCenterConfig(i IUCenterConfig) {
	localUCenterConfig = i
}
