// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2023-03-02 08:45:44
// 生成路径: api/v1/cms/cms_ad_position.go
// 生成人：yxf
// desc:广告位置相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package cms

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/cms/model"
)

// CmsAdPositionSearchReq 分页请求参数
type CmsAdPositionSearchReq struct {
	g.Meta `path:"/list" tags:"cms后台/广告位置" method:"get" summary:"广告位置列表"`
	commonApi.PageReq
	commonApi.Author
}

// CmsAdPositionSearchRes 列表返回结果
type CmsAdPositionSearchRes struct {
	g.Meta `mime:"application/json"`
	commonApi.ListRes
	List []*model.CmsAdPositionListRes `json:"list"`
}

// CmsAdPositionAddReq 添加操作请求参数
type CmsAdPositionAddReq struct {
	g.Meta `path:"/add" tags:"cms后台/广告位置" method:"post" summary:"广告位置添加"`
	commonApi.Author
	Name string `p:"name" v:"required#位置名称不能为空"`
}

// CmsAdPositionAddRes 添加操作返回结果
type CmsAdPositionAddRes struct {
	commonApi.EmptyRes
}

// CmsAdPositionEditReq 修改操作请求参数
type CmsAdPositionEditReq struct {
	g.Meta `path:"/edit" tags:"cms后台/广告位置" method:"put" summary:"广告位置修改"`
	commonApi.Author
	Id   uint   `p:"id" v:"required#主键ID不能为空"`
	Name string `p:"name" v:"required#位置名称不能为空"`
}

// CmsAdPositionEditRes 修改操作返回结果
type CmsAdPositionEditRes struct {
	commonApi.EmptyRes
}

// CmsAdPositionGetReq 获取一条数据请求
type CmsAdPositionGetReq struct {
	g.Meta `path:"/get" tags:"cms后台/广告位置" method:"get" summary:"获取广告位置信息"`
	commonApi.Author
	Id uint `p:"id" v:"required#主键必须"` //通过主键获取
}

// CmsAdPositionGetRes 获取一条数据结果
type CmsAdPositionGetRes struct {
	g.Meta `mime:"application/json"`
	*model.CmsAdPositionInfoRes
}

// CmsAdPositionDeleteReq 删除数据请求
type CmsAdPositionDeleteReq struct {
	g.Meta `path:"/delete" tags:"cms后台/广告位置" method:"delete" summary:"删除广告位置"`
	commonApi.Author
	Ids []uint `p:"ids" v:"required#主键必须"` //通过主键删除
}

// CmsAdPositionDeleteRes 删除数据返回
type CmsAdPositionDeleteRes struct {
	commonApi.EmptyRes
}
