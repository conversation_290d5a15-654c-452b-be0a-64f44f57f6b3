// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// CmsFavorite is the golang structure of table cms_favorite for DAO operations like Where/Data.
type CmsFavorite struct {
	g.Meta    `orm:"table:cms_favorite, do:true"`
	Id        interface{} // ID
	ArticleId interface{} // 文章ID
	MemberId  interface{} // 会员ID
	CreatedAt *gtime.Time // 创建日期
}
