import request from '/@/utils/request'
// 查询审核步骤列表
export function listCmsAuditStep(query:object) {
  return request({
    url: '/api/v1/cms/audit/cmsAuditStep/list',
    method: 'get',
    params: query
  })
}
// 查询审核步骤详细
export function getCmsAuditStep(id:number) {
  return request({
    url: '/api/v1/cms/audit/cmsAuditStep/get',
    method: 'get',
    params: {
      id: id.toString()
    }
  })
}
// 新增审核步骤
export function addCmsAuditStep(data:object) {
  return request({
    url: '/api/v1/cms/audit/cmsAuditStep/add',
    method: 'post',
    data: data
  })
}
// 修改审核步骤
export function updateCmsAuditStep(data:object) {
  return request({
    url: '/api/v1/cms/audit/cmsAuditStep/edit',
    method: 'put',
    data: data
  })
}
// 删除审核步骤
export function delCmsAuditStep(ids:number[]) {
  return request({
    url: '/api/v1/cms/audit/cmsAuditStep/delete',
    method: 'delete',
    data:{
      ids:ids
    }
  })
}
